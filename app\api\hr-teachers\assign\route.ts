import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { teacherId, classId } = body

    console.log('Assigning HR teacher:', { teacherId, classId })

    // Validate required fields
    if (!teacherId || !classId) {
      return NextResponse.json(
        { error: 'Teacher ID and Class ID are required' },
        { status: 400 }
      )
    }

    // Verify teacher exists in either Teacher table or User table with teacher role
    let teacher = await prisma.teacher.findUnique({
      where: { id: teacherId },
      select: { id: true, name: true, email: true, subject: true }
    })

    let teacherFromUser = null
    if (!teacher) {
      teacherFromUser = await prisma.user.findFirst({
        where: {
          id: teacherId,
          role: 'TEACHER',
          status: 'active'
        },
        select: { id: true, name: true, email: true }
      })
    }

    if (!teacher && !teacherFromUser) {
      return NextResponse.json(
        { error: 'Teacher not found in either Teacher table or User table with teacher role' },
        { status: 404 }
      )
    }

    // Use the found teacher data
    const teacherData = teacher || {
      id: teacherFromUser!.id,
      name: teacherFromUser!.name,
      email: teacherFromUser!.email,
      subject: null // Users might not have a subject field
    }

    // Verify class exists
    const classExists = await prisma.class.findUnique({
      where: { id: classId },
      select: { id: true, name: true, hrTeacherId: true }
    })

    if (!classExists) {
      return NextResponse.json(
        { error: 'Class not found' },
        { status: 404 }
      )
    }

    // Check if teacher is already assigned to another class as HR teacher
    // Check both traditional HR teacher assignments and TeacherAssignment records
    const existingHRAssignment = await prisma.class.findFirst({
      where: {
        hrTeacherId: teacherId,
        id: { not: classId } // Exclude the current class if updating
      },
      select: { id: true, name: true }
    })

    const existingTeacherAssignment = await prisma.teacherAssignment.findFirst({
      where: {
        teacherId: teacherId,
        isHRTeacher: true,
        classId: { not: classId } // Exclude the current class if updating
      },
      include: {
        class: {
          select: { id: true, name: true }
        }
      }
    })

    if (existingHRAssignment) {
      return NextResponse.json(
        {
          error: `Teacher is already assigned as HR teacher to class ${existingHRAssignment.name}`
        },
        { status: 400 }
      )
    }

    if (existingTeacherAssignment) {
      return NextResponse.json(
        {
          error: `Teacher is already assigned as HR teacher to class ${existingTeacherAssignment.class?.name}`
        },
        { status: 400 }
      )
    }

    // Handle HR teacher assignment based on source
    let updatedClass

    if (teacher) {
      // Teacher is from Teacher table - use the traditional HRTeacher model
      // First, create or update the HRTeacher record
      await prisma.hRTeacher.upsert({
        where: { classId: classId },
        update: { teacherId: teacherId },
        create: { teacherId: teacherId, classId: classId }
      })

      // Update the class hrTeacherId field
      updatedClass = await prisma.class.update({
        where: { id: classId },
        data: { hrTeacherId: teacherId },
        include: {
          hrTeacher: {
            select: {
              id: true,
              name: true,
              email: true,
              subject: true,
            }
          }
        }
      })
    } else {
      // Teacher is from User table - use TeacherAssignment model
      // Remove any existing HR teacher assignment for this class
      await prisma.teacherAssignment.deleteMany({
        where: { classId: classId, isHRTeacher: true }
      })

      // Create new HR teacher assignment
      await prisma.teacherAssignment.create({
        data: {
          teacherId: teacherId,
          classId: classId,
          isHRTeacher: true
        }
      })

      // Update class to clear hrTeacherId since we're using TeacherAssignment
      updatedClass = await prisma.class.update({
        where: { id: classId },
        data: { hrTeacherId: null },
        select: {
          id: true,
          name: true,
          hrTeacherId: true,
          totalStudents: true,
          totalSubjects: true
        }
      })
    }

    console.log(`Successfully assigned ${teacherData.name} as HR teacher to class ${updatedClass.name}`)

    return NextResponse.json({
      message: 'HR teacher assigned successfully',
      class: updatedClass,
      teacher: teacherData
    })

  } catch (error) {
    console.error('Error assigning HR teacher:', error)
    return NextResponse.json(
      { error: 'Failed to assign HR teacher' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const classId = searchParams.get('classId')

    if (!classId) {
      return NextResponse.json(
        { error: 'Class ID is required' },
        { status: 400 }
      )
    }

    console.log('Removing HR teacher from class:', classId)

    // Remove HR teacher from both possible sources
    // 1. Remove from HRTeacher table (for teachers from Teacher table)
    await prisma.hRTeacher.deleteMany({
      where: { classId: classId }
    })

    // 2. Remove from TeacherAssignment table (for teachers from User table)
    await prisma.teacherAssignment.deleteMany({
      where: { classId: classId, isHRTeacher: true }
    })

    // 3. Clear the hrTeacherId field in the class
    const updatedClass = await prisma.class.update({
      where: { id: classId },
      data: { hrTeacherId: null },
      select: {
        id: true,
        name: true,
        hrTeacherId: true,
      }
    })

    console.log(`Successfully removed HR teacher from class ${updatedClass.name}`)

    return NextResponse.json({
      message: 'HR teacher removed successfully',
      class: updatedClass
    })

  } catch (error) {
    console.error('Error removing HR teacher:', error)
    return NextResponse.json(
      { error: 'Failed to remove HR teacher' },
      { status: 500 }
    )
  }
}

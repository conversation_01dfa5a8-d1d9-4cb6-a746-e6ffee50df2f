const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function createTeacherUsers() {
  try {
    console.log('Connecting to database...')
    
    // Check if teacher users already exist
    const existingTeachers = await prisma.user.findMany({
      where: {
        role: 'TEACHER'
      }
    })
    
    if (existingTeachers.length > 0) {
      console.log(`Found ${existingTeachers.length} existing teacher users:`)
      existingTeachers.forEach(teacher => {
        console.log(`- ${teacher.name} (${teacher.email})`)
      })
      return
    }
    
    console.log('Creating sample teacher users...')
    const teacherPassword = await bcrypt.hash('teacher@123', 10)
    
    const teacherUsers = [
      {
        email: '<EMAIL>',
        password: teacherPassword,
        name: '<PERSON>',
        role: 'TEACHER',
        status: 'active'
      },
      {
        email: '<EMAIL>',
        password: teacherPassword,
        name: '<PERSON>',
        role: 'TEACHER',
        status: 'active'
      },
      {
        email: 'micha<PERSON>.<EMAIL>',
        password: teacherPassword,
        name: '<PERSON>',
        role: 'TEACHER',
        status: 'active'
      },
      {
        email: '<EMAIL>',
        password: teacherPassword,
        name: 'Emma Davis',
        role: 'TEACHER',
        status: 'active'
      },
      {
        email: '<EMAIL>',
        password: teacherPassword,
        name: '<PERSON> <PERSON>',
        role: 'TEACHER',
        status: 'active'
      }
    ]

    const createdUsers = await prisma.user.createMany({
      data: teacherUsers
    })
    
    console.log(`Successfully created ${createdUsers.count} teacher users`)
    
    // Verify creation
    const allTeachers = await prisma.user.findMany({
      where: {
        role: 'TEACHER'
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        status: true
      }
    })
    
    console.log('Created teacher users:')
    allTeachers.forEach(teacher => {
      console.log(`- ${teacher.name} (${teacher.email}) - ${teacher.status}`)
    })
    
  } catch (error) {
    console.error('Error creating teacher users:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createTeacherUsers()

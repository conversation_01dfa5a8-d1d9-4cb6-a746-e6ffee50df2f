import { useToast } from '../components/ui/use-toast'
import { CheckCircle, XCircle, AlertTriangle, Info } from 'lucide-react'

export function useNotification() {
  const { toast } = useToast()

  const showSuccess = (title: string, description?: string) => {
    toast({
      title: `✅ ${title}`,
      description,
      variant: "success" as any,
      duration: 4000,
    })
  }

  const showError = (title: string, description?: string) => {
    toast({
      title: `❌ ${title}`,
      description,
      variant: "destructive",
      duration: 6000,
    })
  }

  const showWarning = (title: string, description?: string) => {
    toast({
      title: `⚠️ ${title}`,
      description,
      variant: "warning" as any,
      duration: 5000,
    })
  }

  const showInfo = (title: string, description?: string) => {
    toast({
      title: `ℹ️ ${title}`,
      description,
      variant: "default",
      duration: 4000,
    })
  }

  // Professional class management notifications
  const classNotifications = {
    created: (className: string) => showSuccess(
      "Class Created Successfully",
      `Class ${className} has been created and is now available in the system.`
    ),
    
    updated: (className: string) => showSuccess(
      "Class Updated Successfully",
      `Class ${className} has been updated with the latest information.`
    ),
    
    deleted: (className: string) => showSuccess(
      "Class Deleted Successfully",
      `Class ${className} and all related data (subjects, teacher assignments) have been permanently removed from the system.`
    ),
    
    deleteError: (className: string, reason: string) => showError(
      "Cannot Delete Class",
      `Class ${className} cannot be deleted: ${reason}`
    ),
    
    networkError: () => showError(
      "Network Connection Error",
      "Unable to connect to the server. Please check your internet connection and try again."
    ),
    
    permissionError: () => showError(
      "Permission Denied",
      "You do not have the required permissions to perform this action. Please contact your administrator."
    ),
    
    subjectsFetchError: (className: string) => showWarning(
      "Subjects Loading Issue",
      `Unable to load subjects for class ${className}. Using default subjects instead.`
    ),
    
    studentCountUpdated: () => showSuccess(
      "Student Counts Updated",
      "All class student counts have been refreshed successfully."
    ),
    
    generalError: (action: string) => showError(
      "Operation Failed",
      `Failed to ${action}. Please try again or contact support if the problem persists.`
    ),

    validationError: (message: string) => showWarning(
      "Validation Error",
      message
    )
  }

  // Professional user management notifications
  const userNotifications = {
    roleUpdated: (userName: string, newRole: string) => showSuccess(
      "User Role Updated",
      `${userName}'s role has been successfully changed to ${newRole}.`
    ),
    
    statusUpdated: (userName: string, newStatus: string) => showSuccess(
      "User Status Updated",
      `${userName}'s status has been changed to ${newStatus}.`
    ),
    
    invalidRole: (role: string) => showError(
      "Invalid Role",
      `The role "${role}" is not supported by the system. Please select a valid role.`
    ),
    
    updateError: (userName: string) => showError(
      "Update Failed",
      `Failed to update ${userName}. Please try again.`
    )
  }

  // Professional database notifications
  const databaseNotifications = {
    connectionError: () => showError(
      "Database Connection Error",
      "Unable to connect to the database. Please try again later."
    ),
    
    initialized: () => showSuccess(
      "Database Initialized",
      "Database has been successfully initialized with all required tables."
    ),
    
    healthCheckPassed: () => showInfo(
      "Database Health Check",
      "All database tables and connections are functioning properly."
    ),
    
    healthCheckFailed: (errors: string[]) => showError(
      "Database Health Check Failed",
      `Issues found: ${errors.join(', ')}`
    )
  }

  return {
    showSuccess,
    showError,
    showWarning,
    showInfo,
    classNotifications,
    userNotifications,
    databaseNotifications
  }
}

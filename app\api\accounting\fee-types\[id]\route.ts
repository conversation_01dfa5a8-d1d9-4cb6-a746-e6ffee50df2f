import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET a specific fee type
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id
    
    const feeType = await prisma.feeType.findUnique({
      where: { id },
    })
    
    if (!feeType) {
      return NextResponse.json(
        { error: 'Fee type not found' },
        { status: 404 }
      )
    }
    
    return NextResponse.json(feeType)
  } catch (error) {
    console.error('Error fetching fee type:', error)
    return NextResponse.json(
      { error: 'Failed to fetch fee type' },
      { status: 500 }
    )
  }
}

// UPDATE a fee type
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id
    const data = await request.json()
    
    // Validate required fields
    if (!data.name || !data.amount || !data.frequency) {
      return NextResponse.json(
        { error: 'Name, amount, and frequency are required' },
        { status: 400 }
      )
    }
    
    // Check if fee type exists
    const existingFeeType = await prisma.feeType.findUnique({
      where: { id },
    })
    
    if (!existingFeeType) {
      return NextResponse.json(
        { error: 'Fee type not found' },
        { status: 404 }
      )
    }
    
    // Check if another fee type with the same name exists (excluding this one)
    const duplicateName = await prisma.feeType.findFirst({
      where: {
        name: data.name,
        id: { not: id },
      },
    })
    
    if (duplicateName) {
      return NextResponse.json(
        { error: 'Another fee type with this name already exists' },
        { status: 409 }
      )
    }
    
    // Update the fee type
    const updatedFeeType = await prisma.feeType.update({
      where: { id },
      data: {
        name: data.name,
        description: data.description || '',
        amount: parseFloat(data.amount),
        frequency: data.frequency,
        isActive: data.isActive !== undefined ? data.isActive : true,
      },
    })
    
    return NextResponse.json(updatedFeeType)
  } catch (error) {
    console.error('Error updating fee type:', error)
    return NextResponse.json(
      { error: 'Failed to update fee type' },
      { status: 500 }
    )
  }
}

// DELETE a fee type
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id
    
    // Check if fee type exists
    const existingFeeType = await prisma.feeType.findUnique({
      where: { id },
      include: {
        payments: {
          take: 1, // Just check if there are any payments
        },
      },
    })
    
    if (!existingFeeType) {
      return NextResponse.json(
        { error: 'Fee type not found' },
        { status: 404 }
      )
    }
    
    // Check if there are any payments associated with this fee type
    if (existingFeeType.payments.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete fee type with associated payments' },
        { status: 400 }
      )
    }
    
    // Delete the fee type
    await prisma.feeType.delete({
      where: { id },
    })
    
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting fee type:', error)
    return NextResponse.json(
      { error: 'Failed to delete fee type' },
      { status: 500 }
    )
  }
}

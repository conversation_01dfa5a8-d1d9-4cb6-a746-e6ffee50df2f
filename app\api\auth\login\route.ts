import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'

export async function POST(request: Request) {
  try {
    const { email, password } = await request.json()
    console.log('Login attempt for email:', email)

    // Test database connection first with retry mechanism
    let connectionAttempts = 0
    const maxAttempts = 3

    while (connectionAttempts < maxAttempts) {
      try {
        await prisma.$queryRaw`SELECT 1`
        console.log('Database connection verified')
        break
      } catch (dbError) {
        connectionAttempts++
        console.error(`Database connection attempt ${connectionAttempts} failed:`, dbError)

        if (connectionAttempts >= maxAttempts) {
          return NextResponse.json(
            { error: 'Database connection failed after multiple attempts. Please try again later.' },
            { status: 500 }
          )
        }

        // Wait a bit before retrying
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
    }

    // Find the user by email
    const user = await prisma.user.findUnique({
      where: { email },
    })

    console.log('User found:', user ? 'Yes' : 'No')

    if (!user) {
      return NextResponse.json(
        { error: 'Invalid credentials' },
        { status: 401 }
      )
    }

    // Check if account is locked or inactive
    if (user.status !== 'active') {
      console.log('User account is not active:', user.status)

      // Provide a more detailed message for inactive accounts
      let errorMessage = '';
      if (user.status === 'inactive') {
        errorMessage = 'Your account is pending approval. Please contact the administrator to activate your account and assign a role.';
      } else {
        errorMessage = `Your account is ${user.status}. Please contact an administrator.`;
      }

      return NextResponse.json(
        { error: errorMessage },
        { status: 401 }
      )
    }

    // Verify password
    let isValid = false

    try {
      // Special case for Super Admin
      if (user.role === 'SUPER_ADMIN') {
        // Allow login with provided password or with the actual password
        isValid = await bcrypt.compare(password, user.password)
      } else {
        // Regular password check for other users
        isValid = await bcrypt.compare(password, user.password)
      }

      console.log('Password valid:', isValid ? 'Yes' : 'No')

      if (!isValid) {
        return NextResponse.json(
          { error: 'Invalid credentials' },
          { status: 401 }
        )
      }

      // Update last login time
      try {
        await prisma.user.update({
          where: { id: user.id },
          data: { lastLogin: new Date() },
        })
      } catch (updateError) {
        console.error('Error updating last login time:', updateError)
        // Continue with login even if this fails
      }
    } catch (error) {
      console.error('Error comparing passwords:', error)
      return NextResponse.json(
        { error: 'Error validating credentials' },
        { status: 500 }
      )
    }

    // Generate JWT token
    const jwtSecret = process.env.JWT_SECRET || 'school_management_secure_jwt_secret_key_2023';

    const token = jwt.sign(
      {
        id: user.id,
        email: user.email,
        role: user.role,
        name: user.name
      },
      jwtSecret,
      {
        expiresIn: '7d', // Extended to 7 days for better user experience
        algorithm: 'HS256'
      }
    )

    // Create response with user data
    const response = NextResponse.json(
      {
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role
        },
        message: 'Login successful'
      },
      { status: 200 }
    )

    // Set the token cookie
    response.cookies.set('token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 7 * 24 * 60 * 60, // 7 days in seconds
      path: '/'
    })

    // Set a non-httpOnly cookie for client-side access
    response.cookies.set('auth_status', 'logged_in', {
      httpOnly: false,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 7 * 24 * 60 * 60, // 7 days in seconds
      path: '/'
    })

    // Set a cookie with the user role for middleware access control
    response.cookies.set('user_role', user.role, {
      httpOnly: false,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 7 * 24 * 60 * 60, // 7 days in seconds
      path: '/'
    })

    console.log('Login successful for user:', user.email)
    return response
  } catch (error) {
    console.error('Unexpected error during login:', error)

    // Check if it's a database connection error
    if (error instanceof Error && error.message.includes('Can\'t reach database server')) {
      return NextResponse.json(
        { error: 'Database connection failed. Please check your internet connection and try again.' },
        { status: 500 }
      )
    }

    // Check if it's a Prisma error
    if (error instanceof Error && error.message.includes('PrismaClient')) {
      return NextResponse.json(
        { error: 'Database error occurred. Please try again later.' },
        { status: 500 }
      )
    }

    return NextResponse.json(
      { error: 'Login failed. Please try again later.' },
      { status: 500 }
    )
  }
}
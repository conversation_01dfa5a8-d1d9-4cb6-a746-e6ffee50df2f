import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET all quick links
export async function GET() {
  try {
    const quickLinks = await prisma.quickLink.findMany({
      orderBy: {
        order: 'asc'
      }
    })
    
    return NextResponse.json(quickLinks)
  } catch (error) {
    console.error('Error fetching quick links:', error)
    return NextResponse.json({
      error: error.message || 'Failed to fetch quick links'
    }, { status: 500 })
  }
}

// POST a new quick link
export async function POST(request: Request) {
  try {
    const data = await request.json()
    
    // Validate required fields
    if (!data.title || !data.description || !data.icon || !data.url || !data.color || !data.borderColor || !data.hoverColor) {
      return NextResponse.json({
        error: 'All fields are required'
      }, { status: 400 })
    }
    
    // Get the highest order value to place the new quick link at the end
    const highestOrder = await prisma.quickLink.findFirst({
      orderBy: {
        order: 'desc'
      },
      select: {
        order: true
      }
    })
    
    const newOrder = highestOrder ? highestOrder.order + 1 : 0
    
    // Create the new quick link
    const quickLink = await prisma.quickLink.create({
      data: {
        title: data.title,
        description: data.description,
        icon: data.icon,
        url: data.url,
        color: data.color,
        borderColor: data.borderColor,
        hoverColor: data.hoverColor,
        isActive: data.isActive !== undefined ? data.isActive : true,
        order: newOrder
      }
    })
    
    return NextResponse.json(quickLink)
  } catch (error) {
    console.error('Error creating quick link:', error)
    return NextResponse.json({
      error: error.message || 'Failed to create quick link'
    }, { status: 500 })
  }
}

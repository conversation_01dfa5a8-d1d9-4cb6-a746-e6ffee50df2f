/**
 * Capitalizes the first letter of each word in a string and makes the rest lowercase
 * @param str The string to capitalize
 * @returns The capitalized string
 */
export function capitalizeFirstLetter(str: string): string {
  if (!str) return str;

  return str
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
}

/**
 * Formats a name with the first letter capitalized and the rest lowercase
 * @param name The name to format
 * @returns The formatted name
 */
export function formatName(name: string): string {
  return capitalizeFirstLetter(name);
}

/**
 * Formats a student name with the first letter capitalized and the rest lowercase
 * @param name The student name to format
 * @returns The formatted name
 */
export function formatStudentName(name: string): string {
  return formatName(name);
}

/**
 * Formats a teacher name with the first letter capitalized and the rest lowercase
 * @param name The teacher name to format
 * @returns The formatted name
 */
export function formatTeacherName(name: string): string {
  return formatName(name);
}

/**
 * Formats a user name with the first letter capitalized and the rest lowercase
 * @param name The user name to format
 * @returns The formatted name
 */
export function formatUserName(name: string): string {
  return formatName(name);
}

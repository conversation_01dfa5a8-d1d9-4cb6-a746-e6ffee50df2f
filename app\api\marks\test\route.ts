import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET test endpoint for marks functionality
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const className = searchParams.get('className') || '8A'
    const subject = searchParams.get('subject') || 'Arabic'
    const term = searchParams.get('term') || 'First Semester'

    console.log('Testing marks API with:', { className, subject, term })

    // First, let's see ALL marks in the database for this class
    const allMarksForClass = await prisma.mark.findMany({
      where: {
        className
      },
      select: {
        subject: true,
        term: true,
        className: true,
        id: true
      }
    })

    console.log(`Found ${allMarksForClass.length} total marks for class ${className}`)

    // Get unique subjects and terms
    const uniqueSubjects = [...new Set(allMarksForClass.map(m => m.subject))]
    const uniqueTerms = [...new Set(allMarksForClass.map(m => m.term))]

    console.log('Unique subjects in DB:', uniqueSubjects)
    console.log('Unique terms in DB:', uniqueTerms)

    // Test basic mark query
    const marks = await prisma.mark.findMany({
      where: {
        className,
        subject,
        term
      },
      include: {
        student: {
          select: {
            name: true,
            sid: true,
            gender: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    console.log(`Found ${marks.length} marks`)

    // Test all marks for the class
    const allClassMarks = await prisma.mark.findMany({
      where: {
        className
      },
      select: {
        subject: true,
        term: true
      },
      distinct: ['subject', 'term']
    })

    // Test all subjects for the class
    const classData = await prisma.class.findUnique({
      where: { name: className },
      select: {
        subjects: true
      }
    })

    // Get all unique subjects that have marks for this class and term
    const submittedSubjects = await prisma.mark.findMany({
      where: {
        className,
        term
      },
      select: {
        subject: true,
      },
      distinct: ['subject']
    })

    // Check subject counts
    const subjectCounts = await Promise.all(
      (classData?.subjects || []).map(async (subject) => {
        const count = await prisma.mark.count({
          where: {
            className,
            term,
            subject
          }
        })

        return {
          subject,
          marksCount: count,
          hasMarks: count > 0
        }
      })
    )

    return NextResponse.json({
      success: true,
      testParams: { className, subject, term },

      // Raw database data
      allMarksForClass: allMarksForClass.length,
      uniqueSubjectsInDB: uniqueSubjects,
      uniqueTermsInDB: uniqueTerms,

      // Specific query results
      marksFound: marks.length,
      marks: marks.map(mark => ({
        id: mark.id,
        studentName: mark.student.name,
        studentSid: mark.student.sid,
        subject: mark.subject,
        marks: mark.marks,
        term: mark.term,
        className: mark.className
      })),

      // Class configuration
      classSubjects: classData?.subjects || [],

      // Submitted subjects analysis
      submittedSubjects: submittedSubjects.map(s => s.subject),
      subjectCounts,

      // Detailed comparison
      comparison: {
        classSubjects: classData?.subjects || [],
        submittedSubjects: submittedSubjects.map(s => s.subject),
        exactMatches: (classData?.subjects || []).filter(subject =>
          submittedSubjects.some(s => s.subject === subject)
        ),
        caseInsensitiveMatches: (classData?.subjects || []).filter(subject =>
          submittedSubjects.some(s => s.subject.toLowerCase().trim() === subject.toLowerCase().trim())
        ),
        mismatches: (classData?.subjects || []).filter(subject =>
          !submittedSubjects.some(s => s.subject.toLowerCase().trim() === subject.toLowerCase().trim())
        )
      },

      message: 'Marks test completed successfully'
    })
  } catch (error) {
    console.error('Error in marks test:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 })
  }
}

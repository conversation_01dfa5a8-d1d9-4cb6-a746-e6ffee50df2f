import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { cookies } from 'next/headers'
import { verifyJWT } from '@/lib/jwt'

// GET student by SID
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const sid = searchParams.get('sid')

    if (!sid) {
      return NextResponse.json(
        { error: 'Student ID (SID) is required' },
        { status: 400 }
      )
    }

    // Verify authentication
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    }

    const decoded = await verifyJWT(token)
    
    // Only SUPER_ADMIN can search students by SID for parent linking
    if (decoded.role !== 'SUPER_ADMIN') {
      return NextResponse.json(
        { error: 'Forbidden - Only Super Admin can search students by SID' },
        { status: 403 }
      )
    }

    // Find student by SID
    const student = await prisma.student.findUnique({
      where: { sid },
      select: {
        id: true,
        sid: true,
        name: true,
        className: true,
        fatherName: true,
        gfName: true,
        age: true,
        gender: true,
        academicYear: true,
        parents: {
          include: {
            parent: {
              select: {
                id: true,
                name: true,
                email: true
              }
            }
          }
        }
      }
    })

    if (!student) {
      return NextResponse.json(
        { error: 'Student not found with the provided SID' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      student,
      message: 'Student found successfully'
    })
  } catch (error) {
    console.error('Error finding student by SID:', error)
    return NextResponse.json(
      { error: 'Failed to find student' },
      { status: 500 }
    )
  }
}

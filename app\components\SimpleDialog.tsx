'use client';

import React, { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import { X } from 'lucide-react';
import { Button } from './ui/button';

// A simple dialog component that doesn't use Radix UI's focus trapping
export function SimpleDialog({
  open,
  onOpenChange,
  children,
  title,
  description,
  footer,
  maxWidth = 'max-w-lg',
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  children: React.ReactNode;
  title?: React.ReactNode;
  description?: React.ReactNode;
  footer?: React.ReactNode;
  maxWidth?: string;
}) {
  // Use local state to track open state
  const [isOpen, setIsOpen] = useState(open);
  const [mounted, setMounted] = useState(false);

  // Sync with parent open state
  useEffect(() => {
    setIsOpen(open);
  }, [open]);

  // Handle mounting for portal
  useEffect(() => {
    setMounted(true);
  }, []);

  // Handle close
  const handleClose = () => {
    setIsOpen(false);
    onOpenChange(false);
  };

  // Handle escape key and body scroll lock
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        handleClose();
      }
    };

    if (isOpen) {
      // Lock body scroll
      document.body.style.overflow = 'hidden';
      window.addEventListener('keydown', handleEscape);
    } else {
      // Restore body scroll
      document.body.style.overflow = 'unset';
    }

    return () => {
      window.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  if (!isOpen || !mounted) return null;

  const modalContent = (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center p-4 animate-fadeIn">
      {/* Blurred Backdrop */}
      <div
        className="fixed inset-0 bg-black/60 backdrop-blur-sm transition-opacity duration-300"
        onClick={handleClose}
        aria-hidden="true"
      />

      {/* Dialog */}
      <div
        className={`relative bg-white dark:bg-gray-900 rounded-xl shadow-2xl border border-gray-200 dark:border-gray-700 ${maxWidth} w-full max-h-[90vh] overflow-auto p-6 transform transition-all duration-300 ease-out animate-slideUp`}
        role="dialog"
        aria-modal="true"
      >
        {/* Close button */}
        <Button
          variant="ghost"
          size="icon"
          className="absolute top-3 right-3 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-full transition-colors duration-200"
          onClick={handleClose}
        >
          <X className="h-5 w-5" />
          <span className="sr-only">Close</span>
        </Button>

        {/* Header */}
        {(title || description) && (
          <div className="mb-6 pr-10">
            {title && <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">{title}</h2>}
            {description && <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">{description}</p>}
          </div>
        )}
        
        {/* Content */}
        <div className="mb-6">
          {children}
        </div>
        
        {/* Footer */}
        {footer && (
          <div className="flex justify-end gap-2">
            {footer}
          </div>
        )}
      </div>
    </div>
  );

  return createPortal(modalContent, document.body);
}

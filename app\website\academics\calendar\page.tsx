"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { FaCalendarAlt, FaDownload, FaPrint, FaChevronDown, FaChevronUp } from 'react-icons/fa';
import { Button } from '@/app/components/ui/button';
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  TabsList,
  TabsTrigger,
} from '@/app/components/ui/tabs';
import { format, parseISO } from 'date-fns';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.5 },
  },
};

// Define interfaces for different types of calendar content
interface CalendarEvent {
  id: string;
  title: string;
  date: string;
  category: 'academic' | 'holiday' | 'exam' | 'staff' | 'student' | 'parent' | 'special';
  description: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface CalendarSettings {
  id: string;
  academicYear: string;
  currentSemester: number;
  updatedAt: string;
}

interface SemesterData {
  id: string;
  name: string;
  events: {
    date: string;
    event: string;
    category: string;
  }[];
}

interface CalendarItem {
  id: string;
  date: string;
  event: string;
  category: string;
}

// Calendar data
const academicYear = "2023-2024";

// Default semesters data (will be supplemented with API data)
const semesters: SemesterData[] = [
  {
    id: "1",
    name: "First Semester",
    events: [
      { date: "August 15, 2023", event: "Teacher Orientation & Preparation", category: "staff" },
      { date: "August 20, 2023", event: "New Student Orientation", category: "student" },
      { date: "August 21, 2023", event: "First Day of School", category: "academic" },
      { date: "September 4, 2023", event: "Labor Day (No School)", category: "holiday" },
      { date: "September 15, 2023", event: "Back to School Night", category: "parent" },
      { date: "October 9-13, 2023", event: "Fall Break (No School)", category: "holiday" },
      { date: "October 20, 2023", event: "End of First Quarter", category: "academic" },
      { date: "October 27, 2023", event: "Parent-Teacher Conferences", category: "parent" },
      { date: "November 10, 2023", event: "Veterans Day Observed (No School)", category: "holiday" },
      { date: "November 22-24, 2023", event: "Thanksgiving Break (No School)", category: "holiday" },
      { date: "December 11-15, 2023", event: "First Semester Final Exams", category: "academic" },
      { date: "December 15, 2023", event: "End of First Semester", category: "academic" },
      { date: "December 18, 2023 - January 5, 2024", event: "Winter Break (No School)", category: "holiday" },
    ]
  },
  {
    id: "2",
    name: "Second Semester",
    events: [
      { date: "January 8, 2024", event: "Teacher Professional Development Day (No Students)", category: "staff" },
      { date: "January 9, 2024", event: "Second Semester Begins", category: "academic" },
      { date: "January 15, 2024", event: "Martin Luther King Jr. Day (No School)", category: "holiday" },
      { date: "February 19, 2024", event: "Presidents' Day (No School)", category: "holiday" },
      { date: "March 8, 2024", event: "End of Third Quarter", category: "academic" },
      { date: "March 15, 2024", event: "Parent-Teacher Conferences", category: "parent" },
      { date: "March 25-29, 2024", event: "Spring Break (No School)", category: "holiday" },
      { date: "April 19, 2024", event: "Teacher Professional Development Day (No Students)", category: "staff" },
      { date: "May 20-24, 2024", event: "Second Semester Final Exams", category: "academic" },
      { date: "May 24, 2024", event: "Last Day of School", category: "academic" },
      { date: "May 27, 2024", event: "Memorial Day (No School)", category: "holiday" },
      { date: "May 30, 2024", event: "Graduation Ceremony", category: "special" },
      { date: "May 31, 2024", event: "Teacher Check-Out Day", category: "staff" },
    ]
  }
];

const importantDates = [
  { date: "August 21, 2023", event: "First Day of School", category: "academic" },
  { date: "October 20, 2023", event: "End of First Quarter", category: "academic" },
  { date: "December 15, 2023", event: "End of First Semester", category: "academic" },
  { date: "January 9, 2024", event: "Second Semester Begins", category: "academic" },
  { date: "March 8, 2024", event: "End of Third Quarter", category: "academic" },
  { date: "May 24, 2024", event: "Last Day of School", category: "academic" },
  { date: "May 30, 2024", event: "Graduation Ceremony", category: "special" },
];

const holidays = [
  { date: "September 4, 2023", event: "Labor Day (No School)", category: "holiday" },
  { date: "October 9-13, 2023", event: "Fall Break (No School)", category: "holiday" },
  { date: "November 10, 2023", event: "Veterans Day Observed (No School)", category: "holiday" },
  { date: "November 22-24, 2023", event: "Thanksgiving Break (No School)", category: "holiday" },
  { date: "December 18, 2023 - January 5, 2024", event: "Winter Break (No School)", category: "holiday" },
  { date: "January 15, 2024", event: "Martin Luther King Jr. Day (No School)", category: "holiday" },
  { date: "February 19, 2024", event: "Presidents' Day (No School)", category: "holiday" },
  { date: "March 25-29, 2024", event: "Spring Break (No School)", category: "holiday" },
  { date: "May 27, 2024", event: "Memorial Day (No School)", category: "holiday" },
];

const parentTeacherConferences = [
  { date: "October 27, 2023", event: "Fall Parent-Teacher Conferences", category: "parent" },
  { date: "March 15, 2024", event: "Spring Parent-Teacher Conferences", category: "parent" },
];

const examSchedule = [
  { date: "December 11-15, 2023", event: "First Semester Final Exams", category: "academic" },
  { date: "May 20-24, 2024", event: "Second Semester Final Exams", category: "academic" },
];

// Category colors
const getCategoryColor = (category: string) => {
  switch (category) {
    case 'academic':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';
    case 'holiday':
      return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';
    case 'parent':
      return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
    case 'staff':
      return 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300';
    case 'special':
      return 'bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300';
    case 'student':
      return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-300';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
  }
};

export default function CalendarPage() {
  const [expandedSemester, setExpandedSemester] = useState<string | null>("1"); // First semester expanded by default
  const [apiEvents, setApiEvents] = useState<CalendarEvent[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // State for all calendar data
  const [calendarData, setCalendarData] = useState<{
    settings: CalendarSettings | null;
    semesters: SemesterData[];
    importantDates: any[];
    holidays: any[];
    parentTeacherConferences: any[];
    examSchedule: any[];
    events: CalendarEvent[];
  }>({
    settings: null,
    semesters: [],
    importantDates: [],
    holidays: [],
    parentTeacherConferences: [],
    examSchedule: [],
    events: []
  });

  // Fetch all calendar data from the API
  useEffect(() => {
    const fetchCalendarData = async () => {
      try {
        setIsLoading(true);
        console.log('Fetching all calendar data from API...');
        const response = await fetch('/api/website-management/academic-calendar?dataType=all');

        if (response.ok) {
          const data = await response.json();
          console.log('Fetched all calendar data:', data);

          // Filter active events
          const activeEvents = data.events ? data.events.filter((event: CalendarEvent) => event.isActive) : [];

          setCalendarData({
            settings: data.settings || null,
            semesters: data.semesters || [],
            importantDates: data.importantDates || [],
            holidays: data.holidays || [],
            parentTeacherConferences: data.parentTeacherConferences || [],
            examSchedule: data.examSchedule || [],
            events: activeEvents
          });

          // Also set the API events for backward compatibility
          setApiEvents(activeEvents);
        } else {
          console.error('Failed to fetch calendar data:', response.status, response.statusText);
          const errorText = await response.text().catch(() => '');
          console.error('Error details:', errorText);

          // Fall back to default data
          setCalendarData({
            settings: { id: 'settings-1', academicYear: '2023-2024', currentSemester: 1, updatedAt: new Date().toISOString() },
            semesters: semesters,
            importantDates: importantDates,
            holidays: holidays,
            parentTeacherConferences: parentTeacherConferences,
            examSchedule: examSchedule,
            events: []
          });
        }
      } catch (error) {
        console.error('Error fetching calendar data:', error);

        // Fall back to default data
        setCalendarData({
          settings: { id: 'settings-1', academicYear: '2023-2024', currentSemester: 1, updatedAt: new Date().toISOString() },
          semesters: semesters,
          importantDates: importantDates,
          holidays: holidays,
          parentTeacherConferences: parentTeacherConferences,
          examSchedule: examSchedule,
          events: []
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchCalendarData();
  }, []);

  // Convert API events to the format used in the UI
  const apiEventsFormatted = apiEvents.map(event => ({
    date: format(parseISO(event.date), 'MMMM d, yyyy'),
    event: event.title,
    category: event.category
  }));

  // Use API semesters if available, otherwise use default semesters
  const updatedSemesters = calendarData.semesters.length > 0
    ? calendarData.semesters
    : [
        {
          ...semesters[0],
          events: [...semesters[0].events, ...apiEventsFormatted]
        },
        semesters[1]
      ];

  const toggleSemester = (id: string) => {
    if (expandedSemester === id) {
      setExpandedSemester(null);
    } else {
      setExpandedSemester(id);
    }
  };

  return (
    <div className="min-h-screen pt-20">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-blue-900 to-indigo-900 text-white">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{ backgroundImage: 'url(/images/pattern.svg)', backgroundSize: '20px' }}></div>
        </div>
        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center max-w-3xl mx-auto"
          >
            <h1 className="text-4xl md:text-5xl font-bold mb-6">Academic Calendar</h1>
            <p className="text-xl text-blue-100">
              {calendarData.settings?.academicYear || academicYear} School Year
            </p>
          </motion.div>
        </div>
      </section>

      {/* Calendar Actions */}
      <section className="py-8 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-4 md:mb-0">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">{calendarData.settings?.academicYear || academicYear} Academic Year</h2>
              <p className="text-gray-600 dark:text-gray-300">Plan your year with our comprehensive academic calendar</p>
            </div>
            <div className="flex space-x-4">
              <Button variant="outline" className="flex items-center gap-2">
                <FaDownload className="h-4 w-4" />
                <span>Download PDF</span>
              </Button>
              <Button variant="outline" className="flex items-center gap-2">
                <FaPrint className="h-4 w-4" />
                <span>Print Calendar</span>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Calendar Tabs */}
      <section className="py-12 bg-gray-50 dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <Tabs defaultValue="full-calendar" className="w-full">
            <TabsList className="grid w-full max-w-2xl mx-auto grid-cols-2 md:grid-cols-4 mb-8">
              <TabsTrigger value="full-calendar" className="text-sm">Full Calendar</TabsTrigger>
              <TabsTrigger value="important-dates" className="text-sm">Important Dates</TabsTrigger>
              <TabsTrigger value="holidays" className="text-sm">Holidays</TabsTrigger>
              <TabsTrigger value="exams" className="text-sm">Exams & Conferences</TabsTrigger>
            </TabsList>

            {/* Full Calendar Tab */}
            <TabsContent value="full-calendar" className="space-y-8">
              <div className="bg-white dark:bg-gray-900 rounded-lg shadow-md overflow-hidden">
                <div className="p-6 border-b border-gray-200 dark:border-gray-800">
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Full Academic Calendar</h3>
                  <p className="text-gray-600 dark:text-gray-300 mt-2">Complete listing of all school events and important dates</p>
                </div>

                <div className="p-6">
                  <div className="space-y-6">
                    {/* Legend */}
                    <div className="flex flex-wrap gap-2 mb-6">
                      <span className="px-3 py-1 text-xs rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">Academic</span>
                      <span className="px-3 py-1 text-xs rounded-full bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300">Holiday</span>
                      <span className="px-3 py-1 text-xs rounded-full bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">Parent Events</span>
                      <span className="px-3 py-1 text-xs rounded-full bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300">Staff Only</span>
                      <span className="px-3 py-1 text-xs rounded-full bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300">Special Events</span>
                      <span className="px-3 py-1 text-xs rounded-full bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-300">Student Events</span>
                    </div>

                    {/* Loading state */}
                    {isLoading && (
                      <div className="text-center py-8">
                        <p className="text-gray-600 dark:text-gray-400">Loading calendar events...</p>
                      </div>
                    )}

                    {/* Semesters */}
                    {!isLoading && updatedSemesters.map((semester) => (
                      <div key={semester.id} className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                        <div
                          className="p-4 bg-gray-50 dark:bg-gray-800 flex justify-between items-center cursor-pointer"
                          onClick={() => toggleSemester(semester.id)}
                        >
                          <h4 className="text-lg font-semibold text-gray-900 dark:text-white">{semester.name}</h4>
                          <div className="text-blue-600 dark:text-blue-400">
                            {expandedSemester === semester.id ? <FaChevronUp /> : <FaChevronDown />}
                          </div>
                        </div>

                        {expandedSemester === semester.id && (
                          <div className="p-4">
                            <table className="w-full">
                              <thead className="bg-gray-50 dark:bg-gray-800">
                                <tr>
                                  <th className="px-4 py-2 text-left text-sm font-semibold text-gray-900 dark:text-white">Date</th>
                                  <th className="px-4 py-2 text-left text-sm font-semibold text-gray-900 dark:text-white">Event</th>
                                  <th className="px-4 py-2 text-left text-sm font-semibold text-gray-900 dark:text-white">Category</th>
                                </tr>
                              </thead>
                              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                                {semester.events.map((event, index) => (
                                  <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                                    <td className="px-4 py-3 text-sm text-gray-900 dark:text-white">{event.date}</td>
                                    <td className="px-4 py-3 text-sm text-gray-900 dark:text-white">{event.event}</td>
                                    <td className="px-4 py-3 text-sm">
                                      <span className={`px-2 py-1 text-xs rounded-full ${getCategoryColor(event.category)}`}>
                                        {event.category.charAt(0).toUpperCase() + event.category.slice(1)}
                                      </span>
                                    </td>
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Important Dates Tab */}
            <TabsContent value="important-dates">
              <div className="bg-white dark:bg-gray-900 rounded-lg shadow-md overflow-hidden">
                <div className="p-6 border-b border-gray-200 dark:border-gray-800">
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Important Academic Dates</h3>
                  <p className="text-gray-600 dark:text-gray-300 mt-2">Key dates for the academic year</p>
                </div>

                <div className="p-6">
                  <table className="w-full">
                    <thead className="bg-gray-50 dark:bg-gray-800">
                      <tr>
                        <th className="px-4 py-2 text-left text-sm font-semibold text-gray-900 dark:text-white">Date</th>
                        <th className="px-4 py-2 text-left text-sm font-semibold text-gray-900 dark:text-white">Event</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                      {calendarData.importantDates.length > 0
                        ? calendarData.importantDates.map((event, index) => (
                          <tr key={event.id || index} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                            <td className="px-4 py-3 text-sm text-gray-900 dark:text-white">{event.date}</td>
                            <td className="px-4 py-3 text-sm text-gray-900 dark:text-white">{event.event}</td>
                          </tr>
                        ))
                        : importantDates.map((event, index) => (
                          <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                            <td className="px-4 py-3 text-sm text-gray-900 dark:text-white">{event.date}</td>
                            <td className="px-4 py-3 text-sm text-gray-900 dark:text-white">{event.event}</td>
                          </tr>
                        ))
                      }
                    </tbody>
                  </table>
                </div>
              </div>
            </TabsContent>

            {/* Holidays Tab */}
            <TabsContent value="holidays">
              <div className="bg-white dark:bg-gray-900 rounded-lg shadow-md overflow-hidden">
                <div className="p-6 border-b border-gray-200 dark:border-gray-800">
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white">School Holidays</h3>
                  <p className="text-gray-600 dark:text-gray-300 mt-2">Days when school is not in session</p>
                </div>

                <div className="p-6">
                  <table className="w-full">
                    <thead className="bg-gray-50 dark:bg-gray-800">
                      <tr>
                        <th className="px-4 py-2 text-left text-sm font-semibold text-gray-900 dark:text-white">Date</th>
                        <th className="px-4 py-2 text-left text-sm font-semibold text-gray-900 dark:text-white">Holiday</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                      {calendarData.holidays.length > 0
                        ? calendarData.holidays.map((holiday, index) => (
                          <tr key={holiday.id || index} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                            <td className="px-4 py-3 text-sm text-gray-900 dark:text-white">{holiday.date}</td>
                            <td className="px-4 py-3 text-sm text-gray-900 dark:text-white">{holiday.event}</td>
                          </tr>
                        ))
                        : holidays.map((holiday, index) => (
                          <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                            <td className="px-4 py-3 text-sm text-gray-900 dark:text-white">{holiday.date}</td>
                            <td className="px-4 py-3 text-sm text-gray-900 dark:text-white">{holiday.event}</td>
                          </tr>
                        ))
                      }
                    </tbody>
                  </table>
                </div>
              </div>
            </TabsContent>

            {/* Exams & Conferences Tab */}
            <TabsContent value="exams">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-white dark:bg-gray-900 rounded-lg shadow-md overflow-hidden">
                  <div className="p-6 border-b border-gray-200 dark:border-gray-800">
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Exam Schedule</h3>
                    <p className="text-gray-600 dark:text-gray-300 mt-2">Final examination periods</p>
                  </div>

                  <div className="p-6">
                    <table className="w-full">
                      <thead className="bg-gray-50 dark:bg-gray-800">
                        <tr>
                          <th className="px-4 py-2 text-left text-sm font-semibold text-gray-900 dark:text-white">Date</th>
                          <th className="px-4 py-2 text-left text-sm font-semibold text-gray-900 dark:text-white">Exam Period</th>
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                        {calendarData.examSchedule.length > 0
                          ? calendarData.examSchedule.map((exam, index) => (
                            <tr key={exam.id || index} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                              <td className="px-4 py-3 text-sm text-gray-900 dark:text-white">{exam.date}</td>
                              <td className="px-4 py-3 text-sm text-gray-900 dark:text-white">{exam.event}</td>
                            </tr>
                          ))
                          : examSchedule.map((exam, index) => (
                            <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                              <td className="px-4 py-3 text-sm text-gray-900 dark:text-white">{exam.date}</td>
                              <td className="px-4 py-3 text-sm text-gray-900 dark:text-white">{exam.event}</td>
                            </tr>
                          ))
                        }
                      </tbody>
                    </table>
                  </div>
                </div>

                <div className="bg-white dark:bg-gray-900 rounded-lg shadow-md overflow-hidden">
                  <div className="p-6 border-b border-gray-200 dark:border-gray-800">
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Parent-Teacher Conferences</h3>
                    <p className="text-gray-600 dark:text-gray-300 mt-2">Scheduled conference days</p>
                  </div>

                  <div className="p-6">
                    <table className="w-full">
                      <thead className="bg-gray-50 dark:bg-gray-800">
                        <tr>
                          <th className="px-4 py-2 text-left text-sm font-semibold text-gray-900 dark:text-white">Date</th>
                          <th className="px-4 py-2 text-left text-sm font-semibold text-gray-900 dark:text-white">Conference</th>
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                        {calendarData.parentTeacherConferences.length > 0
                          ? calendarData.parentTeacherConferences.map((conference, index) => (
                            <tr key={conference.id || index} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                              <td className="px-4 py-3 text-sm text-gray-900 dark:text-white">{conference.date}</td>
                              <td className="px-4 py-3 text-sm text-gray-900 dark:text-white">{conference.event}</td>
                            </tr>
                          ))
                          : parentTeacherConferences.map((conference, index) => (
                            <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                              <td className="px-4 py-3 text-sm text-gray-900 dark:text-white">{conference.date}</td>
                              <td className="px-4 py-3 text-sm text-gray-900 dark:text-white">{conference.event}</td>
                            </tr>
                          ))
                        }
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-12 bg-blue-600">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="text-white mb-6 md:mb-0">
              <h2 className="text-2xl font-bold mb-2">Have questions about our calendar?</h2>
              <p className="text-blue-100">Contact our administrative office for more information.</p>
            </div>
            <div className="flex flex-col sm:flex-row gap-4">
              <Link href="/website/contact">
                <Button className="bg-white text-blue-600 hover:bg-blue-50">
                  Contact Us
                </Button>
              </Link>
              <Link href="/website/academics">
                <Button variant="outline" className="border-white text-white hover:bg-blue-700">
                  Back to Academics
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

'use client'

import { useState } from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "./ui/dialog"
import { Button } from "./ui/button"
import { AlertTriangle } from "lucide-react"

interface PermissionDeniedDialogProps {
  isOpen: boolean
  onClose: () => void
  title: string
  message: string
}

export function PermissionDeniedDialog({ 
  isOpen, 
  onClose, 
  title, 
  message 
}: PermissionDeniedDialogProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <div className="flex items-center gap-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-full bg-red-100">
              <AlertTriangle className="h-5 w-5 text-red-600" />
            </div>
            <div>
              <DialogTitle className="text-lg font-semibold text-gray-900">
                Access Denied
              </DialogTitle>
              <DialogDescription className="text-sm text-gray-600 mt-1">
                {title}
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>
        
        <div className="py-4">
          <p className="text-sm text-gray-700 leading-relaxed">
            {message}
          </p>
        </div>

        <DialogFooter>
          <Button 
            onClick={onClose}
            className="w-full sm:w-auto"
            variant="default"
          >
            Understood
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

// Hook to use permission denied dialog
export function usePermissionDeniedDialog() {
  const [isOpen, setIsOpen] = useState(false)
  const [title, setTitle] = useState('')
  const [message, setMessage] = useState('')

  const showDialog = (dialogTitle: string, dialogMessage: string) => {
    setTitle(dialogTitle)
    setMessage(dialogMessage)
    setIsOpen(true)
  }

  const hideDialog = () => {
    setIsOpen(false)
    setTitle('')
    setMessage('')
  }

  const PermissionDialog = () => (
    <PermissionDeniedDialog
      isOpen={isOpen}
      onClose={hideDialog}
      title={title}
      message={message}
    />
  )

  return {
    showDialog,
    hideDialog,
    PermissionDialog
  }
}

import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET all teacher permissions for users with TEACHER role
export async function GET() {
  try {
    console.log('Fetching all teacher permissions for users with TEACHER role')

    // Since we don't have a dedicated model for user permissions yet,
    // we'll use the existing TeacherPermission model but join with User table
    const permissions = await prisma.teacherPermission.findMany({
      include: {
        // We can't include the User model directly since there's no relation
        // So we'll just return the permissions as they are
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    console.log(`Found ${permissions.length} teacher permissions`)
    return NextResponse.json({ permissions })
  } catch (error) {
    console.error('Error fetching teacher permissions:', error)
    return NextResponse.json(
      { error: 'Failed to fetch teacher permissions' },
      { status: 500 }
    )
  }
}

// POST to create or update teacher permissions for a user with TEACHER role
export async function POST(request: Request) {
  try {
    console.log('Creating/updating teacher permissions for user with TEACHER role')

    // Get the request body
    const data = await request.json()
    console.log('Received data:', JSON.stringify(data, null, 2))

    // Log the structure of the TeacherPermission model
    console.log('TeacherPermission model structure:', {
      fields: [
        'id', 'teacherId', 'classId',
        'canViewAttendance', 'canTakeAttendance',
        'canAddMarks', 'canEditMarks',
        'createdAt', 'updatedAt'
      ],
      uniqueConstraint: 'teacherId_classId'
    })

    // Validate required fields
    if (!data.teacherId || !data.classId || !data.permissions) {
      return NextResponse.json(
        { error: 'Teacher ID, Class ID, and permissions are required' },
        { status: 400 }
      )
    }

    // Check if the user with TEACHER role exists
    const teacher = await prisma.user.findFirst({
      where: {
        id: data.teacherId,
        role: 'TEACHER'
      }
    })

    if (!teacher) {
      return NextResponse.json(
        { error: 'Teacher not found or user is not a teacher' },
        { status: 404 }
      )
    }

    // Check if the class exists
    const classObj = await prisma.class.findUnique({
      where: { id: data.classId }
    })

    if (!classObj) {
      return NextResponse.json(
        { error: 'Class not found' },
        { status: 404 }
      )
    }

    // For now, we'll use the existing TeacherPermission model
    // In a real implementation, you would create a new model for user permissions

    // Check if permissions already exist for this teacher and class
    // We'll use a different approach since we can't use the unique constraint
    console.log(`Checking for existing permissions for teacherId: ${data.teacherId}, classId: ${data.classId}`)
    try {
      const existingPermissions = await prisma.teacherPermission.findFirst({
        where: {
          teacherId: data.teacherId,
          classId: data.classId
        }
      })
      console.log('Existing permissions found:', existingPermissions ? 'Yes' : 'No')
      if (existingPermissions) {
        console.log('Existing permissions details:', JSON.stringify(existingPermissions, null, 2))
      }

      let result;

      if (existingPermissions) {
        // Update existing permissions
        console.log(`Updating permissions with ID: ${existingPermissions.id}`)
        try {
          result = await prisma.teacherPermission.update({
            where: {
              id: existingPermissions.id
            },
            data: {
              canViewAttendance: data.permissions.canViewAttendance,
              canTakeAttendance: data.permissions.canTakeAttendance,
              canAddMarks: data.permissions.canAddMarks,
              canEditMarks: data.permissions.canEditMarks
            }
          })
          console.log(`Updated permissions for teacher ${teacher.name} in class ${classObj.name}`)
        } catch (updateError) {
          console.error('Error updating permissions:', updateError)
          if (updateError instanceof Error) {
            console.error('Update error message:', updateError.message)
            console.error('Update error stack:', updateError.stack)
          }
          throw updateError
        }
      } else {
        // Create new permissions
        console.log('Creating new permissions with data:', {
          teacherId: data.teacherId,
          classId: data.classId,
          permissions: data.permissions
        })
        try {
          result = await prisma.teacherPermission.create({
            data: {
              teacherId: data.teacherId,
              classId: data.classId,
              canViewAttendance: data.permissions.canViewAttendance,
              canTakeAttendance: data.permissions.canTakeAttendance,
              canAddMarks: data.permissions.canAddMarks,
              canEditMarks: data.permissions.canEditMarks
            }
          })
          console.log(`Created permissions for teacher ${teacher.name} in class ${classObj.name}`)
        } catch (createError) {
          console.error('Error creating permissions:', createError)
          if (createError instanceof Error) {
            console.error('Create error message:', createError.message)
            console.error('Create error stack:', createError.stack)
          }
          throw createError
        }
      }

      return NextResponse.json({
        message: existingPermissions ? 'Permissions updated successfully' : 'Permissions created successfully',
        permissions: result
      })
    } catch (findError) {
      console.error('Error in findFirst operation:', findError)
      if (findError instanceof Error) {
        console.error('Find error message:', findError.message)
        console.error('Find error stack:', findError.stack)
      }
      throw findError
    }
  } catch (error) {
    console.error('Error creating/updating teacher permissions:', error)
    // Log more detailed error information
    if (error instanceof Error) {
      console.error('Error message:', error.message)
      console.error('Error stack:', error.stack)
    }
    return NextResponse.json(
      { error: 'Failed to create/update teacher permissions', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    )
  }
}

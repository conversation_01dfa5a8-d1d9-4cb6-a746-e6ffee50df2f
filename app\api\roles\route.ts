import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET all roles
export async function GET() {
  try {
    console.log('Fetching all roles')

    // Check if we have any roles in the database
    const roleCount = await prisma.role.count()

    // If no roles exist, create the default system roles
    if (roleCount === 0) {
      console.log('No roles found, creating default system roles')
      await createDefaultRoles()
    }

    // Fetch all roles with their permissions
    const roles = await prisma.role.findMany({
      include: {
        permissions: {
          include: {
            permission: true
          }
        }
      },
      orderBy: {
        name: 'asc'
      }
    })

    // Transform the data to match the expected format
    const formattedRoles = roles.map(role => ({
      id: role.id,
      name: role.name,
      description: role.description,
      permissions: role.permissions.map(rp => rp.permission.name),
      systemDefined: role.systemDefined,
      createdAt: role.createdAt,
      updatedAt: role.updatedAt
    }))

    console.log(`Returning ${formattedRoles.length} roles`)
    return NextResponse.json(formattedRoles)
  } catch (error) {
    console.error('Error fetching roles:', error)
    return NextResponse.json(
      { error: 'Failed to fetch roles' },
      { status: 500 }
    )
  }
}

// Helper function to create default system roles
async function createDefaultRoles() {
  try {
    // First, check if we have permissions in the database
    const permissionCount = await prisma.permission.count()

    // If no permissions exist, create the default permissions
    if (permissionCount === 0) {
      console.log('No permissions found, creating default permissions')
      await createDefaultPermissions()
    }

    // Fetch all permissions
    const permissions = await prisma.permission.findMany()

    // Create the Administrator role with all permissions
    const adminRole = await prisma.role.create({
      data: {
        name: 'Administrator',
        description: 'Full system access with all permissions',
        systemDefined: true
      }
    })

    // Assign all permissions to the admin role
    await Promise.all(
      permissions.map(permission =>
        prisma.rolePermission.create({
          data: {
            roleId: adminRole.id,
            permissionId: permission.id
          }
        })
      )
    )

    // Create the HR Teacher role
    const hrTeacherRole = await prisma.role.create({
      data: {
        name: 'HR Teacher',
        description: 'Manage class, students, marks and attendance',
        systemDefined: true
      }
    })

    // Assign specific permissions to the HR teacher role
    const hrTeacherPermissions = permissions.filter(p =>
      ['View Students', 'Edit Students',
       'View Classes', 'Edit Classes',
       'View Marks', 'Add Marks', 'Edit Marks',
       'View Attendance', 'Add Attendance', 'Edit Attendance'].includes(p.name)
    )

    await Promise.all(
      hrTeacherPermissions.map(permission =>
        prisma.rolePermission.create({
          data: {
            roleId: hrTeacherRole.id,
            permissionId: permission.id
          }
        })
      )
    )

    // Create the Subject Teacher role
    const subjectTeacherRole = await prisma.role.create({
      data: {
        name: 'Subject Teacher',
        description: 'Manage marks and attendance for assigned subjects',
        systemDefined: true
      }
    })

    // Assign specific permissions to the subject teacher role
    const subjectTeacherPermissions = permissions.filter(p =>
      ['View Students', 'View Classes',
       'View Marks', 'Add Marks', 'Edit Marks',
       'View Attendance', 'Add Attendance', 'Edit Attendance'].includes(p.name)
    )

    await Promise.all(
      subjectTeacherPermissions.map(permission =>
        prisma.rolePermission.create({
          data: {
            roleId: subjectTeacherRole.id,
            permissionId: permission.id
          }
        })
      )
    )

    console.log('Default roles created successfully')
  } catch (error) {
    console.error('Error creating default roles:', error)
    throw error
  }
}

// Helper function to create default permissions
async function createDefaultPermissions() {
  try {
    const permissionCategories = {
      students: [
        { name: 'View Students', description: 'View student information' },
        { name: 'Add Students', description: 'Add new students' },
        { name: 'Edit Students', description: 'Edit student information' },
        { name: 'Delete Students', description: 'Delete students' },
        { name: 'Import Students', description: 'Import students from CSV' }
      ],
      classes: [
        { name: 'View Classes', description: 'View class information' },
        { name: 'Add Classes', description: 'Add new classes' },
        { name: 'Edit Classes', description: 'Edit class information' },
        { name: 'Delete Classes', description: 'Delete classes' }
      ],
      teachers: [
        { name: 'View Teachers', description: 'View teacher information' },
        { name: 'Add Teachers', description: 'Add new teachers' },
        { name: 'Edit Teachers', description: 'Edit teacher information' },
        { name: 'Delete Teachers', description: 'Delete teachers' },
        { name: 'Assign Teachers', description: 'Assign teachers to classes and subjects' }
      ],
      marks: [
        { name: 'View Marks', description: 'View student marks' },
        { name: 'Add Marks', description: 'Add new marks' },
        { name: 'Edit Marks', description: 'Edit marks' },
        { name: 'Delete Marks', description: 'Delete marks' },
        { name: 'Import Marks', description: 'Import marks from CSV' }
      ],
      attendance: [
        { name: 'View Attendance', description: 'View attendance records' },
        { name: 'Add Attendance', description: 'Add attendance records' },
        { name: 'Edit Attendance', description: 'Edit attendance records' }
      ],
      reports: [
        { name: 'Generate Reports', description: 'Generate reports' },
        { name: 'View Reports', description: 'View generated reports' },
        { name: 'Export Reports', description: 'Export reports to PDF/Excel' }
      ],
      system: [
        { name: 'System Settings', description: 'Manage system settings' },
        { name: 'Manage Users', description: 'Manage user accounts' },
        { name: 'Manage Roles', description: 'Manage roles and permissions' }
      ]
    }

    // Create all permissions
    for (const category in permissionCategories) {
      await Promise.all(
        permissionCategories[category].map(permission =>
          prisma.permission.create({
            data: {
              name: permission.name,
              description: permission.description,
              category: category
            }
          })
        )
      )
    }

    console.log('Default permissions created successfully')
  } catch (error) {
    console.error('Error creating default permissions:', error)
    throw error
  }
}

// POST to create a new role
export async function POST(request: Request) {
  try {
    console.log('Creating new role')

    // Get the request body
    const data = await request.json()

    // Validate the role data
    if (!data.name || !data.description) {
      console.error('Invalid role data:', data)
      return NextResponse.json(
        { error: 'Role name and description are required' },
        { status: 400 }
      )
    }

    if (!Array.isArray(data.permissions) || data.permissions.length === 0) {
      console.error('Invalid permissions:', data.permissions)
      return NextResponse.json(
        { error: 'At least one permission is required' },
        { status: 400 }
      )
    }

    // Check if a role with the same name already exists
    const existingRole = await prisma.role.findUnique({
      where: { name: data.name }
    })

    if (existingRole) {
      console.error(`Role with name ${data.name} already exists`)
      return NextResponse.json(
        { error: 'A role with this name already exists' },
        { status: 400 }
      )
    }

    // Get the permission IDs for the given permission names
    const permissions = await prisma.permission.findMany({
      where: {
        name: {
          in: data.permissions
        }
      }
    })

    // Check if all requested permissions exist
    if (permissions.length !== data.permissions.length) {
      const foundPermissionNames = permissions.map(p => p.name)
      const missingPermissions = data.permissions.filter(p => !foundPermissionNames.includes(p))

      console.error(`Some permissions were not found: ${missingPermissions.join(', ')}`)
      return NextResponse.json(
        { error: `Some permissions were not found: ${missingPermissions.join(', ')}` },
        { status: 400 }
      )
    }

    // Create the new role
    const newRole = await prisma.role.create({
      data: {
        name: data.name,
        description: data.description,
        systemDefined: false
      }
    })

    // Assign permissions to the role
    await Promise.all(
      permissions.map(permission =>
        prisma.rolePermission.create({
          data: {
            roleId: newRole.id,
            permissionId: permission.id
          }
        })
      )
    )

    // Fetch the created role with its permissions
    const createdRole = await prisma.role.findUnique({
      where: { id: newRole.id },
      include: {
        permissions: {
          include: {
            permission: true
          }
        }
      }
    })

    // Format the response
    const formattedRole = {
      id: createdRole.id,
      name: createdRole.name,
      description: createdRole.description,
      permissions: createdRole.permissions.map(rp => rp.permission.name),
      systemDefined: createdRole.systemDefined,
      createdAt: createdRole.createdAt,
      updatedAt: createdRole.updatedAt
    }

    console.log('New role created:', formattedRole)
    return NextResponse.json({
      message: 'Role created successfully',
      role: formattedRole
    })
  } catch (error) {
    console.error('Error creating role:', error)
    return NextResponse.json(
      { error: 'Failed to create role' },
      { status: 500 }
    )
  }
}

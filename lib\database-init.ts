import { prisma } from './prisma'

/**
 * Ensures that the user_teacher_permissions table exists in the database
 * This table is used for fine-grained teacher permissions per class
 */
export async function ensureUserTeacherPermissionsTable(): Promise<boolean> {
  try {
    console.log('Checking if user_teacher_permissions table exists...')
    
    // Check if the table exists
    const tableExists = await prisma.$queryRaw`
      SELECT COUNT(*) as count 
      FROM information_schema.tables 
      WHERE table_schema = DATABASE() 
      AND table_name = 'user_teacher_permissions'
    `
    
    // If the table doesn't exist, create it
    if (Array.isArray(tableExists) && tableExists[0].count === 0) {
      console.log('Creating user_teacher_permissions table...')
      
      await prisma.$executeRaw`
        CREATE TABLE user_teacher_permissions (
          id VARCHAR(191) NOT NULL,
          userId VARCHAR(191) NOT NULL,
          classId VARCHAR(191) NOT NULL,
          canViewAttendance BOOLEAN NOT NULL DEFAULT false,
          canTakeAttendance BOOLEAN NOT NULL DEFAULT false,
          canAddMarks BOOLEAN NOT NULL DEFAULT false,
          canEditMarks BOOLEAN NOT NULL DEFAULT false,
          createdAt DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
          updatedAt DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
          
          PRIMARY KEY (id),
          UNIQUE INDEX user_teacher_permissions_userId_classId_key(userId, classId),
          INDEX user_teacher_permissions_userId_idx(userId),
          INDEX user_teacher_permissions_classId_idx(classId)
        )
      `
      
      console.log('user_teacher_permissions table created successfully')
    } else {
      console.log('user_teacher_permissions table already exists')
    }
    
    return true
  } catch (error) {
    console.error('Error ensuring user_teacher_permissions table:', error)
    return false
  }
}

/**
 * Initializes the database by ensuring all required tables exist
 * and performing any necessary data migrations
 */
export async function initializeDatabase(): Promise<boolean> {
  try {
    console.log('Initializing database...')
    
    // Ensure user_teacher_permissions table exists
    const permissionsTableReady = await ensureUserTeacherPermissionsTable()
    if (!permissionsTableReady) {
      console.error('Failed to ensure user_teacher_permissions table')
      return false
    }
    
    // Test database connection
    await prisma.$queryRaw`SELECT 1`
    console.log('Database connection test successful')
    
    console.log('Database initialization completed successfully')
    return true
  } catch (error) {
    console.error('Database initialization failed:', error)
    return false
  }
}

/**
 * Checks if the database is properly initialized and all required tables exist
 */
export async function checkDatabaseHealth(): Promise<{
  isHealthy: boolean
  missingTables: string[]
  errors: string[]
}> {
  const result = {
    isHealthy: true,
    missingTables: [] as string[],
    errors: [] as string[]
  }
  
  try {
    // Check core tables
    const requiredTables = [
      'user',
      'class',
      'student',
      'teacher',
      'subject',
      'mark',
      'attendance',
      'user_teacher_permissions'
    ]
    
    for (const tableName of requiredTables) {
      try {
        const tableExists = await prisma.$queryRaw`
          SELECT COUNT(*) as count 
          FROM information_schema.tables 
          WHERE table_schema = DATABASE() 
          AND table_name = ${tableName}
        `
        
        if (Array.isArray(tableExists) && tableExists[0].count === 0) {
          result.missingTables.push(tableName)
          result.isHealthy = false
        }
      } catch (error) {
        result.errors.push(`Error checking table ${tableName}: ${error}`)
        result.isHealthy = false
      }
    }
    
    // Test basic database operations
    try {
      await prisma.$queryRaw`SELECT 1`
    } catch (error) {
      result.errors.push(`Database connection test failed: ${error}`)
      result.isHealthy = false
    }
    
  } catch (error) {
    result.errors.push(`Database health check failed: ${error}`)
    result.isHealthy = false
  }
  
  return result
}

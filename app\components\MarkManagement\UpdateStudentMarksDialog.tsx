'use client'

import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { Button } from '@/app/components/ui/button'
import { Label } from '@/app/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/app/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/app/components/ui/dialog'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/app/components/ui/table'
import { Input } from '@/app/components/ui/input'
import { RefreshCw, Edit, GraduationCap, Users, BookOpen } from 'lucide-react'
import { useToast } from '@/app/components/ui/use-toast'

// Types for Update Student Marks
interface MarkClass {
  id: string
  name: string
  totalStudents: number
  totalSubjects: number
}

interface MarkSubject {
  id: string
  name: string
  classId: string
}

interface StudentMark {
  id: string
  studentId: string
  studentName: string
  studentSid: string
  marks: number
  totalMarks: number
  originalMarks: number
  isEdited: boolean
}

interface UpdateMarksRequest {
  className: string
  subject: string
  term: string
  academicYear: string
}

interface UpdateStudentMarksDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: () => void
}

// Academic years and terms
const academicYears = [
  '2020-2021', '2021-2022', '2022-2023', '2023-2024',
  '2024-2025', '2025-2026', '2026-2027', '2027-2028'
]

const terms = [
  'First Semester',
  'Second Semester',
  'Third Semester',
  'Final Exam'
]

export function UpdateStudentMarksDialog({ 
  open, 
  onOpenChange, 
  onSuccess 
}: UpdateStudentMarksDialogProps) {
  const [selectedClassName, setSelectedClassName] = useState<string>('')
  const [selectedSubject, setSelectedSubject] = useState<string>('')
  const [selectedTerm, setSelectedTerm] = useState<string>('First Semester')
  const [selectedAcademicYear, setSelectedAcademicYear] = useState<string>('2024-2025')
  const [studentMarks, setStudentMarks] = useState<StudentMark[]>([])
  const [marksLoaded, setMarksLoaded] = useState(false)
  
  const queryClient = useQueryClient()
  const { toast } = useToast()

  // Fetch available classes
  const {
    data: classes = [],
    isLoading: isLoadingClasses,
    error: classesError
  } = useQuery<MarkClass[]>({
    queryKey: ['marks-available-classes'],
    queryFn: async () => {
      const res = await fetch('/api/marks/available-classes')
      if (!res.ok) throw new Error('Failed to fetch classes')
      return res.json()
    },
    enabled: open
  })

  // Fetch subjects for selected class
  const {
    data: subjects = [],
    isLoading: isLoadingSubjects,
    error: subjectsError
  } = useQuery<MarkSubject[]>({
    queryKey: ['marks-available-subjects', selectedClassName],
    queryFn: async () => {
      if (!selectedClassName) return []
      const res = await fetch(`/api/marks/available-subjects?className=${selectedClassName}`)
      if (!res.ok) throw new Error('Failed to fetch subjects')
      return res.json()
    },
    enabled: open && !!selectedClassName
  })

  // Load marks mutation
  const loadMarksMutation = useMutation({
    mutationFn: async (criteria: UpdateMarksRequest) => {
      const params = new URLSearchParams({
        className: criteria.className,
        subject: criteria.subject,
        term: criteria.term,
        academicYear: criteria.academicYear
      })
      
      const res = await fetch(`/api/marks/load-for-update?${params}`)
      if (!res.ok) {
        const error = await res.json()
        throw new Error(error.error || 'Failed to load marks')
      }
      return res.json()
    },
    onSuccess: (data) => {
      const formattedMarks = data.map((mark: any) => ({
        id: mark.id,
        studentId: mark.studentId,
        studentName: mark.studentName,
        studentSid: mark.studentSid || '',
        marks: mark.marks,
        totalMarks: mark.totalMarks || 100,
        originalMarks: mark.marks,
        isEdited: false
      }))
      setStudentMarks(formattedMarks)
      setMarksLoaded(true)
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: `Failed to load marks: ${error.message}`,
        variant: "destructive"
      })
    }
  })

  // Update marks mutation
  const updateMarksMutation = useMutation({
    mutationFn: async (marks: StudentMark[]) => {
      const editedMarks = marks.filter(mark => mark.isEdited)
      
      if (editedMarks.length === 0) {
        throw new Error('No changes to save')
      }

      const res = await fetch('/api/marks/update-batch', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          marks: editedMarks.map(mark => ({
            id: mark.id,
            marks: mark.marks,
            totalMarks: mark.totalMarks
          }))
        })
      })
      
      if (!res.ok) {
        const error = await res.json()
        throw new Error(error.error || 'Failed to update marks')
      }
      return res.json()
    },
    onSuccess: (data) => {
      // Invalidate and refetch related queries
      queryClient.invalidateQueries({ queryKey: ['marks'] })
      queryClient.invalidateQueries({ queryKey: ['marks-available-classes'] })

      // Reset form
      handleClose()
      
      // Call success callback
      onSuccess?.()
      
      toast({
        title: "Success",
        description: `Successfully updated ${data.updatedCount} mark${data.updatedCount !== 1 ? 's' : ''}`,
        variant: "default"
      })
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: `Failed to update marks: ${error.message}`,
        variant: "destructive"
      })
    }
  })

  const handleLoadMarks = () => {
    if (!selectedClassName || !selectedSubject || !selectedTerm || !selectedAcademicYear) {
      toast({
        title: "Missing Information",
        description: "Please select class, subject, term, and academic year",
        variant: "destructive"
      })
      return
    }

    loadMarksMutation.mutate({
      className: selectedClassName,
      subject: selectedSubject,
      term: selectedTerm,
      academicYear: selectedAcademicYear
    })
  }

  const handleMarkChange = (markId: string, newMarks: number) => {
    setStudentMarks(prev => 
      prev.map(mark => 
        mark.id === markId 
          ? { 
              ...mark, 
              marks: newMarks,
              isEdited: newMarks !== mark.originalMarks
            }
          : mark
      )
    )
  }

  const handleUpdateMarks = () => {
    const editedMarks = studentMarks.filter(mark => mark.isEdited)
    
    if (editedMarks.length === 0) {
      toast({
        title: "No Changes",
        description: "No changes to save",
        variant: "destructive"
      })
      return
    }

    updateMarksMutation.mutate(studentMarks)
  }

  const handleClose = () => {
    setSelectedClassName('')
    setSelectedSubject('')
    setSelectedTerm('First Semester')
    setSelectedAcademicYear('2024-2025')
    setStudentMarks([])
    setMarksLoaded(false)
    onOpenChange(false)
  }

  const editedCount = studentMarks.filter(mark => mark.isEdited).length

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[900px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Edit className="h-5 w-5" />
            Update Student Marks
          </DialogTitle>
          <DialogDescription>
            Select class, subject, and term to load and update student marks
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Selection Criteria */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Class Selection */}
            <div className="space-y-2">
              <Label htmlFor="class">Class *</Label>
              <Select
                value={selectedClassName}
                onValueChange={setSelectedClassName}
                disabled={isLoadingClasses}
              >
                <SelectTrigger>
                  <Users className="mr-2 h-4 w-4 text-gray-400" />
                  <SelectValue placeholder="Select class" />
                </SelectTrigger>
                <SelectContent>
                  {isLoadingClasses ? (
                    <div className="p-2 text-center text-sm text-gray-500">
                      Loading classes...
                    </div>
                  ) : classesError ? (
                    <div className="p-2 text-center text-sm text-red-500">
                      Error loading classes
                    </div>
                  ) : classes.length > 0 ? (
                    classes.map((cls) => (
                      <SelectItem key={cls.id} value={cls.name}>
                        <div className="flex items-center justify-between w-full">
                          <span>{cls.name}</span>
                          <span className="text-xs text-gray-500 ml-2">
                            {cls.totalSubjects} subjects
                          </span>
                        </div>
                      </SelectItem>
                    ))
                  ) : (
                    <div className="p-2 text-center text-sm text-gray-500">
                      No classes found
                    </div>
                  )}
                </SelectContent>
              </Select>
            </div>

            {/* Subject Selection */}
            <div className="space-y-2">
              <Label htmlFor="subject">Subject *</Label>
              <Select
                value={selectedSubject}
                onValueChange={setSelectedSubject}
                disabled={!selectedClassName || isLoadingSubjects}
              >
                <SelectTrigger>
                  <BookOpen className="mr-2 h-4 w-4 text-gray-400" />
                  <SelectValue placeholder="Select subject" />
                </SelectTrigger>
                <SelectContent>
                  {isLoadingSubjects ? (
                    <div className="p-2 text-center text-sm text-gray-500">
                      Loading subjects...
                    </div>
                  ) : subjectsError ? (
                    <div className="p-2 text-center text-sm text-red-500">
                      Error loading subjects
                    </div>
                  ) : subjects.length > 0 ? (
                    subjects.map((subject) => (
                      <SelectItem key={subject.id} value={subject.name}>
                        {subject.name}
                      </SelectItem>
                    ))
                  ) : (
                    <div className="p-2 text-center text-sm text-gray-500">
                      {selectedClassName 
                        ? 'No subjects found for this class' 
                        : 'Select a class first'
                      }
                    </div>
                  )}
                </SelectContent>
              </Select>
            </div>

            {/* Term Selection */}
            <div className="space-y-2">
              <Label htmlFor="term">Term *</Label>
              <Select
                value={selectedTerm}
                onValueChange={setSelectedTerm}
              >
                <SelectTrigger>
                  <GraduationCap className="mr-2 h-4 w-4 text-gray-400" />
                  <SelectValue placeholder="Select term" />
                </SelectTrigger>
                <SelectContent>
                  {terms.map(term => (
                    <SelectItem key={term} value={term}>
                      {term}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Academic Year Selection */}
            <div className="space-y-2">
              <Label htmlFor="academicYear">Academic Year *</Label>
              <Select
                value={selectedAcademicYear}
                onValueChange={setSelectedAcademicYear}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select academic year" />
                </SelectTrigger>
                <SelectContent>
                  {academicYears.map(year => (
                    <SelectItem key={year} value={year}>
                      {year}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Load Marks Button */}
          <div className="flex justify-center">
            <Button
              onClick={handleLoadMarks}
              disabled={!selectedClassName || !selectedSubject || !selectedTerm || !selectedAcademicYear || loadMarksMutation.isPending}
              className="w-full md:w-auto"
            >
              {loadMarksMutation.isPending ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Loading marks...
                </>
              ) : (
                <>
                  <Edit className="h-4 w-4 mr-2" />
                  Load Student Marks
                </>
              )}
            </Button>
          </div>

          {/* Student Marks Table */}
          {marksLoaded && studentMarks.length > 0 && (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Student Marks</h3>
                {editedCount > 0 && (
                  <span className="text-sm text-blue-600 bg-blue-50 px-2 py-1 rounded">
                    {editedCount} mark{editedCount !== 1 ? 's' : ''} edited
                  </span>
                )}
              </div>
              
              <div className="border rounded-md overflow-hidden">
                <div className="max-h-[300px] overflow-y-auto">
                  <Table>
                    <TableHeader className="sticky top-0 bg-white z-10">
                      <TableRow>
                        <TableHead className="w-12">#</TableHead>
                        <TableHead className="w-24">SID</TableHead>
                        <TableHead>Student Name</TableHead>
                        <TableHead>Current Mark</TableHead>
                        <TableHead>New Mark</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {studentMarks.map((student, index) => (
                        <TableRow key={student.id} className={student.isEdited ? "bg-blue-50" : ""}>
                          <TableCell className="text-center">{index + 1}</TableCell>
                          <TableCell>
                            <span className="font-mono text-xs font-medium bg-gray-100 px-2 py-1 rounded">
                              {student.studentSid}
                            </span>
                          </TableCell>
                          <TableCell>{student.studentName}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <span>{student.originalMarks}/{student.totalMarks}</span>
                              <div className="w-16 h-2 bg-gray-100 rounded-full">
                                <div
                                  className={`h-full rounded-full ${
                                    student.originalMarks >= 80 ? 'bg-green-500' :
                                    student.originalMarks >= 60 ? 'bg-yellow-500' :
                                    'bg-red-500'
                                  }`}
                                  style={{ width: `${(student.originalMarks / student.totalMarks) * 100}%` }}
                                ></div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Input
                              type="number"
                              min="0"
                              max={student.totalMarks}
                              value={student.marks}
                              onChange={(e) => handleMarkChange(student.id, parseInt(e.target.value) || 0)}
                              className={`w-20 text-right ${student.isEdited ? "border-blue-500 ring-1 ring-blue-500" : ""}`}
                            />
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </div>
            </div>
          )}

          {marksLoaded && studentMarks.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              No marks found for the selected criteria
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          {marksLoaded && studentMarks.length > 0 && (
            <Button
              onClick={handleUpdateMarks}
              disabled={editedCount === 0 || updateMarksMutation.isPending}
            >
              {updateMarksMutation.isPending ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Updating...
                </>
              ) : (
                `Update ${editedCount} Mark${editedCount !== 1 ? 's' : ''}`
              )}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

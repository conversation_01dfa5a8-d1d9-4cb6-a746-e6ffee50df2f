import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const className = searchParams.get('className')
    const subject = searchParams.get('subject')
    const term = searchParams.get('term')
    const academicYear = searchParams.get('academicYear')

    // Validate required parameters
    if (!className || !subject || !term || !academicYear) {
      return NextResponse.json(
        { error: 'Class name, subject, term, and academic year are required' },
        { status: 400 }
      )
    }

    console.log(`Loading students for bulk entry: ${className} - ${subject} - ${term} - ${academicYear}`)

    // Get all students in the class
    const students = await prisma.student.findMany({
      where: {
        className: className,
      },
      select: {
        id: true,
        name: true,
        sid: true,
        className: true,
        academicYear: true,
      },
      orderBy: [
        {
          sid: 'asc',
        },
        {
          name: 'asc',
        },
      ],
    })

    if (students.length === 0) {
      return NextResponse.json(
        { error: `No students found in class '${className}'` },
        { status: 404 }
      )
    }

    // Get existing marks for these students in the specified subject/term
    const existingMarks = await prisma.mark.findMany({
      where: {
        className: className,
        subject: subject,
        term: term,
        academicYear: academicYear,
        studentId: {
          in: students.map(s => s.id)
        }
      },
      select: {
        id: true,
        studentId: true,
        marks: true,
        totalMarks: true,
      },
    })

    // Create a map of existing marks by student ID
    const existingMarksMap = new Map(
      existingMarks.map(mark => [mark.studentId, mark])
    )

    // Combine students with their existing marks (if any)
    const studentsWithMarks = students.map(student => ({
      id: student.id,
      name: student.name,
      sid: student.sid,
      className: student.className,
      academicYear: student.academicYear,
      existingMark: existingMarksMap.get(student.id) || null,
    }))

    console.log(`Found ${studentsWithMarks.length} students, ${existingMarks.length} with existing marks`)

    return NextResponse.json(studentsWithMarks)
  } catch (error) {
    console.error('Error loading students for bulk entry:', error)
    return NextResponse.json(
      { error: 'Failed to load students for bulk entry' },
      { status: 500 }
    )
  }
}

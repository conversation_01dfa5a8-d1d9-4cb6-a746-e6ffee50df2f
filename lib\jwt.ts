import jwt from 'jsonwebtoken'

// Define the JWT payload type
export interface JWTPayload {
  id: string;
  email: string;
  role: string;
  name?: string;
  iat?: number;
  exp?: number;
}

export async function verifyJWT(token: string): Promise<JWTPayload> {
  try {
    const secret = process.env.JWT_SECRET;

    if (!secret) {
      console.error('JWT_SECRET is not defined in environment variables');
      throw new Error('Server configuration error: JWT_SECRET is missing');
    }

    console.log('Verifying JWT with secret length:', secret.length);

    const decoded = jwt.verify(token, secret) as JWTPayload;
    console.log('JWT verified successfully, payload:', {
      id: decoded.id,
      email: decoded.email,
      role: decoded.role,
      iat: decoded.iat,
      exp: decoded.exp
    });

    return decoded;
  } catch (error) {
    console.error('JWT verification error:', error);
    if (error instanceof jwt.JsonWebTokenError) {
      throw new Error('Invalid token');
    } else if (error instanceof jwt.TokenExpiredError) {
      throw new Error('Token expired');
    } else {
      throw error;
    }
  }
}
import { NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'
import { formatStudentName } from '@/lib/utils'
import { cookies } from 'next/headers'
import { verifyJWT } from '@/lib/jwt'

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  let prisma: PrismaClient | null = null;

  try {
    console.log(`Fetching student with ID: ${params.id}`);

    // Create a new PrismaClient instance specifically for this request
    prisma = new PrismaClient({
      log: ['error', 'warn'],
      datasources: {
        db: {
          url: process.env.DATABASE_URL
        }
      }
    });

    // Connect to the database
    await prisma.$connect();
    console.log('Database connection successful');

    const student = await prisma.student.findUnique({
      where: {
        id: params.id,
      },
      include: {
        class: true,
      },
    })

    if (!student) {
      console.log(`Student with ID ${params.id} not found`);
      return NextResponse.json(
        { error: 'Student not found' },
        { status: 404 }
      )
    }

    console.log(`Found student: ${student.name}`);
    return NextResponse.json(student)
  } catch (error) {
    console.error('Error fetching student:', error);
    return NextResponse.json(
      {
        error: 'Failed to fetch student',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  } finally {
    // Clean up the connection
    if (prisma) {
      await prisma.$disconnect().catch(e => console.error('Error disconnecting from database:', e));
    }
  }
}

export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  let prisma: PrismaClient | null = null;

  try {
    // Verify authentication and authorization
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    }

    const decoded = await verifyJWT(token)

    // Only allow SUPER_ADMIN, ADMIN, SUPERVISOR, and DATA_ENCODER to edit students
    if (!['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'DATA_ENCODER'].includes(decoded.role)) {
      return NextResponse.json(
        { error: 'Forbidden - You do not have permission to edit students' },
        { status: 403 }
      )
    }

    console.log(`Updating student with ID: ${params.id}`);
    const data = await request.json()
    console.log('Update data:', data);

    // Create a new PrismaClient instance specifically for this request
    prisma = new PrismaClient({
      log: ['error', 'warn'],
      datasources: {
        db: {
          url: process.env.DATABASE_URL
        }
      }
    });

    // Connect to the database
    await prisma.$connect();
    console.log('Database connection successful');

    // Start a transaction to ensure all operations succeed or fail together
    const result = await prisma.$transaction(async (tx) => {
      // 1. Find the current student to get the current class name
      const currentStudent = await tx.student.findUnique({
        where: { id: params.id },
      })

      if (!currentStudent) {
        throw new Error('Student not found')
      }

      console.log('Current student:', currentStudent.name, 'SID:', currentStudent.sid, 'Class:', currentStudent.className)
      console.log('New class:', data.class)

      // 2. Handle class changes and SID regeneration if the class has changed
      const oldClassName = currentStudent.className
      const newClassName = data.class
      let newSid = currentStudent.sid // Keep existing SID by default

      if (oldClassName !== newClassName) {
        console.log(`Class changed from ${oldClassName} to ${newClassName}, regenerating SID...`)

        // Generate new SID for the new class
        const newClassPrefix = newClassName.match(/^\d+[A-Z]+/)?.[0]
        if (!newClassPrefix) {
          throw new Error('Invalid new class name format')
        }

        // Get the highest student number for this class prefix across all students
        const existingStudents = await tx.student.findMany({
          where: {
            sid: {
              startsWith: newClassPrefix
            }
          },
          select: {
            sid: true
          },
          orderBy: {
            sid: 'desc'
          }
        })

        console.log('Existing students with prefix', newClassPrefix, ':', existingStudents.map(s => s.sid))

        let nextNumber = 1
        if (existingStudents.length > 0) {
          // Extract numbers from all SIDs and find the highest
          const numbers = existingStudents
            .map(student => {
              const numberPart = student.sid.replace(newClassPrefix, '')
              return parseInt(numberPart)
            })
            .filter(num => !isNaN(num))
            .sort((a, b) => b - a) // Sort descending

          if (numbers.length > 0) {
            nextNumber = numbers[0] + 1
          }
        }

        // Generate new student ID (e.g., "8B1", "8B2", etc.)
        newSid = `${newClassPrefix}${nextNumber}`
        console.log('Generated new SID:', newSid)

        // Double-check that this SID doesn't already exist
        const existingSid = await tx.student.findUnique({
          where: { sid: newSid }
        })

        if (existingSid) {
          console.error('Generated SID already exists:', newSid)
          throw new Error('Failed to generate unique student ID. Please try again.')
        }
      }

      // 3. Update the student with new data and potentially new SID
      const updatedStudent = await tx.student.update({
        where: {
          id: params.id,
        },
        data: {
          name: formatStudentName(data.name),
          fatherName: formatStudentName(data.fatherName),
          gfName: formatStudentName(data.gfName),
          className: data.class,
          sid: newSid, // Use the new SID if class changed, or keep existing SID
          age: parseInt(data.age),
          gender: data.gender,
          academicYear: data.academicYear || '2023-2024',
        },
      })

      console.log('Updated student:', updatedStudent.name, 'New SID:', updatedStudent.sid, 'New Class:', updatedStudent.className)

      // 4. Handle class student count updates if the class has changed
      if (oldClassName !== newClassName) {
        console.log('Updating class student counts...')

        // Update the old class totalStudents if it exists
        if (oldClassName) {
          const oldClassRecord = await tx.class.findFirst({
            where: { name: oldClassName },
          })

          if (oldClassRecord) {
            // Count students in the old class
            const oldClassStudentCount = await tx.student.count({
              where: {
                className: oldClassName,
              },
            })

            console.log(`Updating old class ${oldClassName} student count to ${oldClassStudentCount}`)
            // Update with the actual count
            await tx.class.update({
              where: { id: oldClassRecord.id },
              data: { totalStudents: oldClassStudentCount },
            })
          }
        }

        // Update the new class totalStudents if it exists
        if (newClassName) {
          const newClassRecord = await tx.class.findFirst({
            where: { name: newClassName },
          })

          if (newClassRecord) {
            // Count students in the new class
            const newClassStudentCount = await tx.student.count({
              where: {
                className: newClassName,
              },
            })

            console.log(`Updating new class ${newClassName} student count to ${newClassStudentCount}`)
            // Update with the actual count
            await tx.class.update({
              where: { id: newClassRecord.id },
              data: { totalStudents: newClassStudentCount },
            })
          }
        }
      }

      return updatedStudent
    })

    console.log('Student update transaction completed successfully');
    return NextResponse.json(result)
  } catch (error) {
    console.error('Error updating student:', error);
    return NextResponse.json(
      {
        error: 'Failed to update student',
        details: error instanceof Error ? error.message : 'Unknown error',
        suggestion: 'Please check the database connection and ensure the student exists.'
      },
      { status: 500 }
    )
  } finally {
    // Clean up the connection
    if (prisma) {
      await prisma.$disconnect().catch(e => console.error('Error disconnecting from database:', e));
    }
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  let prisma: PrismaClient | null = null;

  try {
    // Verify authentication and authorization
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    }

    const decoded = await verifyJWT(token)

    // Only allow SUPER_ADMIN, ADMIN, SUPERVISOR, and DATA_ENCODER to delete students
    if (!['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'DATA_ENCODER'].includes(decoded.role)) {
      return NextResponse.json(
        { error: 'Forbidden - You do not have permission to delete students' },
        { status: 403 }
      )
    }

    console.log(`Deleting student with ID: ${params.id}`);

    // Create a new PrismaClient instance specifically for this request
    prisma = new PrismaClient({
      log: ['error', 'warn'],
      datasources: {
        db: {
          url: process.env.DATABASE_URL
        }
      }
    });

    // Connect to the database
    await prisma.$connect();
    console.log('Database connection successful');

    // First check if the student exists before starting a transaction
    const existingStudent = await prisma.student.findUnique({
      where: { id: params.id },
    });

    if (!existingStudent) {
      console.log(`Student with ID ${params.id} not found`);
      return NextResponse.json(
        { error: 'Student not found', details: `No student exists with ID: ${params.id}` },
        { status: 404 }
      );
    }

    console.log(`Found student: ${existingStudent.name} in class ${existingStudent.className}`);

    // Start a transaction to ensure all operations succeed or fail together
    const result = await prisma.$transaction(async (tx) => {
      // We already verified the student exists, but we'll get it again within the transaction
      const student = await tx.student.findUnique({
        where: { id: params.id },
      })

      if (!student) {
        console.log(`Student with ID ${params.id} not found in transaction`);
        throw new Error('Student not found in transaction')
      }
      const className = student.className

      // 1. First delete all related attendance records
      console.log(`Deleting attendance records for student ID: ${params.id}`);
      const deletedAttendance = await tx.attendance.deleteMany({
        where: { studentId: params.id },
      })
      console.log(`Deleted ${deletedAttendance.count} attendance records`);

      // 2. Delete all related mark records
      console.log(`Deleting mark records for student ID: ${params.id}`);
      const deletedMarks = await tx.mark.deleteMany({
        where: { studentId: params.id },
      })
      console.log(`Deleted ${deletedMarks.count} mark records`);

      // 3. Now delete the student
      const deletedStudent = await tx.student.delete({
        where: { id: params.id },
      })
      console.log(`Deleted student with ID: ${params.id}`);

      // 3. Find the class by name and update totalStudents
      if (className) {
        // Find the class by name
        const classRecord = await tx.class.findFirst({
          where: { name: className },
        })

        if (classRecord) {
          // Count remaining students in this class
          const studentCount = await tx.student.count({
            where: {
              className: className,
            },
          })

          console.log(`Updating class ${className} student count to ${studentCount}`);
          // Update with the actual count
          await tx.class.update({
            where: { id: classRecord.id },
            data: { totalStudents: studentCount },
          })
        }
      }

      return { message: 'Student deleted successfully', student: deletedStudent }
    })

    console.log('Student deletion transaction completed successfully');
    return NextResponse.json(result)
  } catch (error) {
    console.error('Error deleting student:', error);
    return NextResponse.json(
      {
        error: 'Failed to delete student',
        details: error instanceof Error ? error.message : 'Unknown error',
        suggestion: 'Please check the database connection and ensure the student exists.'
      },
      { status: 500 }
    )
  } finally {
    // Clean up the connection
    if (prisma) {
      await prisma.$disconnect().catch(e => console.error('Error disconnecting from database:', e));
    }
  }
}

"use client";

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { FaMoneyBillWave, FaCalendarAlt, FaInfoCircle, FaQuestionCircle, FaHandHoldingUsd, FaUniversity, FaCheckCircle } from 'react-icons/fa';
import { Button } from '@/app/components/ui/button';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/app/components/ui/accordion';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.5 },
  },
};

// Tuition data
const tuitionRates = [
  {
    level: "Kindergarten",
    annualTuition: "$8,500",
    registrationFee: "$300",
    booksFee: "$250",
    activityFee: "$200",
    technologyFee: "$150",
  },
  {
    level: "Elementary School (Grades 1-5)",
    annualTuition: "$9,200",
    registrationFee: "$300",
    booksFee: "$350",
    activityFee: "$250",
    technologyFee: "$200",
  },
  {
    level: "Middle School (Grades 6-8)",
    annualTuition: "$9,800",
    registrationFee: "$300",
    booksFee: "$450",
    activityFee: "$300",
    technologyFee: "$250",
  },
  {
    level: "High School (Grades 9-12)",
    annualTuition: "$10,500",
    registrationFee: "$300",
    booksFee: "$550",
    activityFee: "$350",
    technologyFee: "$300",
  },
];

// Payment plans
const paymentPlans = [
  {
    id: 1,
    name: "Annual Payment Plan",
    description: "Pay the full tuition amount at the beginning of the school year.",
    discount: "3% discount on tuition",
    dueDate: "August 1, 2023",
    icon: <FaMoneyBillWave className="text-4xl text-green-600" />,
  },
  {
    id: 2,
    name: "Semester Payment Plan",
    description: "Pay tuition in two equal installments at the beginning of each semester.",
    discount: "1.5% discount on tuition",
    dueDate: "August 1, 2023 and January 5, 2024",
    icon: <FaCalendarAlt className="text-4xl text-blue-600" />,
  },
  {
    id: 3,
    name: "Monthly Payment Plan",
    description: "Pay tuition in 10 equal monthly installments from August to May.",
    discount: "No discount",
    dueDate: "1st of each month, August through May",
    icon: <FaCalendarAlt className="text-4xl text-purple-600" />,
  },
];

// Additional fees
const additionalFees = [
  {
    name: "Application Fee",
    amount: "$75",
    description: "Non-refundable fee due at the time of application submission.",
  },
  {
    name: "New Student Enrollment Fee",
    amount: "$500",
    description: "One-time fee for new students, due upon acceptance.",
  },
  {
    name: "Re-enrollment Fee",
    amount: "$250",
    description: "Annual fee for returning students, due at re-enrollment.",
  },
  {
    name: "Late Payment Fee",
    amount: "$50",
    description: "Applied to payments received after the 5th of the month.",
  },
  {
    name: "Returned Check Fee",
    amount: "$35",
    description: "Applied for each returned check or failed automatic payment.",
  },
  {
    name: "Graduation Fee (12th Grade)",
    amount: "$200",
    description: "Covers cap, gown, diploma, and ceremony expenses.",
  },
];

// FAQ data
const faqs = [
  {
    question: "Are there any sibling discounts available?",
    answer: "Yes, we offer sibling discounts to make education more affordable for families with multiple children. The oldest child pays full tuition, the second child receives a 10% discount, the third child receives a 15% discount, and the fourth child and beyond receive a 20% discount on tuition. Discounts apply to tuition only, not to fees."
  },
  {
    question: "What is included in the tuition?",
    answer: "Tuition covers the core educational program, including instruction in all required subjects, standard classroom materials, and basic student services. It does not include books, technology fees, activity fees, uniforms, lunch, transportation, or optional programs such as after-school activities or special field trips."
  },
  {
    question: "Is financial aid available?",
    answer: "Yes, we offer need-based financial aid to qualifying families. Financial aid applications are reviewed separately from admission applications and do not affect admission decisions. To apply for financial aid, families must complete a financial aid application and provide supporting documentation of financial need. The deadline for financial aid applications is March 15 for priority consideration."
  },
  {
    question: "What happens if we withdraw mid-year?",
    answer: "If a student withdraws during the school year, tuition is prorated on a monthly basis. The family is responsible for tuition through the end of the month in which the student withdraws. A 30-day written notice is required for withdrawal. Registration, book, activity, and technology fees are non-refundable regardless of when a student withdraws."
  },
  {
    question: "Are there any additional costs not listed here?",
    answer: "Potential additional costs include uniforms (approximately $200-300 per year), lunch (if not bringing from home), optional after-school programs, special field trips, sports team participation, and certain elective courses that may require special materials or equipment. We strive to keep these additional costs reasonable and communicate them clearly in advance."
  },
  {
    question: "Can I change my payment plan during the school year?",
    answer: "Payment plan changes during the school year are considered on a case-by-case basis. Requests must be submitted in writing to the Business Office. Changes from monthly to annual or semester plans are generally approved, while changes from annual or semester to monthly plans are approved only in cases of demonstrated financial hardship."
  },
];

export default function TuitionFeesPage() {
  return (
    <div className="min-h-screen pt-20">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-blue-900 to-indigo-900 text-white">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{ backgroundImage: 'url(/images/pattern.svg)', backgroundSize: '20px' }}></div>
        </div>
        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center max-w-3xl mx-auto"
          >
            <h1 className="text-4xl md:text-5xl font-bold mb-6">Tuition & Fees</h1>
            <p className="text-xl text-blue-100">
              Investment in your child's education for the 2023-2024 academic year
            </p>
          </motion.div>
        </div>
      </section>

      {/* Introduction Section */}
      <section className="py-16 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.7 }}
              viewport={{ once: true }}
              className="space-y-6"
            >
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Investing in Your Child's Future</h2>
              <p className="text-gray-600 dark:text-gray-300">
                At Alfalah Islamic School, we understand that choosing an independent school education represents a significant investment in your child's future. We strive to provide exceptional value through our comprehensive academic program, Islamic education, and supportive community.
              </p>
              <p className="text-gray-600 dark:text-gray-300">
                Our tuition and fee structure is designed to support the school's operational needs while remaining accessible to as many families as possible. We offer various payment plans and financial aid options to accommodate different financial situations.
              </p>
              <p className="text-gray-600 dark:text-gray-300">
                The information below outlines our tuition rates, fees, and payment options for the 2023-2024 academic year. If you have any questions, please don't hesitate to contact our Business Office.
              </p>
              <div className="pt-4 flex flex-wrap gap-4">
                <Link href="/website/admissions/how-to-apply">
                  <Button variant="outline">
                    How to Apply
                  </Button>
                </Link>
                <Link href="/website/admissions/requirements">
                  <Button variant="outline">
                    Admission Requirements
                  </Button>
                </Link>
                <Link href="/website/contact">
                  <Button variant="outline">
                    Contact Business Office
                  </Button>
                </Link>
                <Link href="#payment-receipt">
                  <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                    Submit Payment Receipt
                  </Button>
                </Link>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.7 }}
              viewport={{ once: true }}
            >
              <div className="relative h-[400px] rounded-lg overflow-hidden shadow-xl">
                <Image
                  src="/images/portrait.jpg" // Replace with actual image
                  alt="Students at Alfalah Islamic School"
                  fill
                  className="object-cover"
                />
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Tuition Rates */}
      <section id="tuition-rates" className="py-16 bg-gray-50 dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Tuition Rates & Fees</h2>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              2023-2024 Academic Year
            </p>
          </motion.div>

          <div className="max-w-5xl mx-auto">
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="bg-blue-600 text-white">
                    <th className="px-6 py-3 text-left text-sm font-semibold border border-blue-500">Grade Level</th>
                    <th className="px-6 py-3 text-left text-sm font-semibold border border-blue-500">Annual Tuition</th>
                    <th className="px-6 py-3 text-left text-sm font-semibold border border-blue-500">Registration Fee</th>
                    <th className="px-6 py-3 text-left text-sm font-semibold border border-blue-500">Books Fee</th>
                    <th className="px-6 py-3 text-left text-sm font-semibold border border-blue-500">Activity Fee</th>
                    <th className="px-6 py-3 text-left text-sm font-semibold border border-blue-500">Technology Fee</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                  {tuitionRates.map((rate, index) => (
                    <tr key={index} className={index % 2 === 0 ? 'bg-white dark:bg-gray-900' : 'bg-gray-50 dark:bg-gray-800'}>
                      <td className="px-6 py-4 text-sm text-gray-900 dark:text-white border border-gray-200 dark:border-gray-700 font-medium">{rate.level}</td>
                      <td className="px-6 py-4 text-sm text-gray-900 dark:text-white border border-gray-200 dark:border-gray-700">{rate.annualTuition}</td>
                      <td className="px-6 py-4 text-sm text-gray-900 dark:text-white border border-gray-200 dark:border-gray-700">{rate.registrationFee}</td>
                      <td className="px-6 py-4 text-sm text-gray-900 dark:text-white border border-gray-200 dark:border-gray-700">{rate.booksFee}</td>
                      <td className="px-6 py-4 text-sm text-gray-900 dark:text-white border border-gray-200 dark:border-gray-700">{rate.activityFee}</td>
                      <td className="px-6 py-4 text-sm text-gray-900 dark:text-white border border-gray-200 dark:border-gray-700">{rate.technologyFee}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div className="mt-6 bg-white dark:bg-gray-900 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
              <div className="flex items-start">
                <FaInfoCircle className="text-blue-600 mt-1 mr-2 flex-shrink-0" />
                <div className="text-sm text-gray-600 dark:text-gray-300">
                  <p><strong>Note:</strong> Registration, books, activity, and technology fees are non-refundable and due at the time of enrollment or re-enrollment. Tuition may be paid according to one of the payment plans described below.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Payment Plans */}
      <section id="payment-plans" className="py-16 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Payment Plans</h2>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              We offer flexible payment options to accommodate different family budgets
            </p>
          </motion.div>

          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-5xl mx-auto"
          >
            {paymentPlans.map((plan) => (
              <motion.div
                key={plan.id}
                variants={itemVariants}
                className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg shadow-md flex flex-col h-full"
              >
                <div className="mb-4">{plan.icon}</div>
                <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">{plan.name}</h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">{plan.description}</p>
                <div className="mt-auto space-y-2">
                  <div className="flex items-center">
                    <span className="text-sm font-medium text-gray-900 dark:text-white mr-2">Discount:</span>
                    <span className="text-sm text-gray-600 dark:text-gray-300">{plan.discount}</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-sm font-medium text-gray-900 dark:text-white mr-2">Due Date:</span>
                    <span className="text-sm text-gray-600 dark:text-gray-300">{plan.dueDate}</span>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>

          <div className="mt-8 max-w-5xl mx-auto bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <div className="flex items-start">
              <FaInfoCircle className="text-blue-600 mt-1 mr-2 flex-shrink-0" />
              <div className="text-sm text-gray-600 dark:text-gray-300">
                <p><strong>Payment Methods:</strong> We accept payments by check, ACH bank transfer, or credit card (a 2.5% processing fee applies to credit card payments). Monthly payments require enrollment in our automatic payment system.</p>
              </div>
            </div>
          </div>

          <div className="mt-6 text-center">
            <p className="text-gray-600 dark:text-gray-300 mb-4">Already made a payment? Submit your receipt for verification:</p>
            <Link href="#payment-receipt">
              <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                Submit Payment Receipt
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Additional Fees */}
      <section id="additional-fees" className="py-16 bg-gray-50 dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Additional Fees</h2>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Other fees that may apply during the academic year
            </p>
          </motion.div>

          <div className="max-w-3xl mx-auto">
            <div className="bg-white dark:bg-gray-900 rounded-lg shadow-md overflow-hidden">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="bg-gray-100 dark:bg-gray-800">
                    <th className="px-6 py-3 text-left text-sm font-semibold text-gray-900 dark:text-white">Fee</th>
                    <th className="px-6 py-3 text-left text-sm font-semibold text-gray-900 dark:text-white">Amount</th>
                    <th className="px-6 py-3 text-left text-sm font-semibold text-gray-900 dark:text-white">Description</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                  {additionalFees.map((fee, index) => (
                    <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                      <td className="px-6 py-4 text-sm text-gray-900 dark:text-white font-medium">{fee.name}</td>
                      <td className="px-6 py-4 text-sm text-gray-900 dark:text-white">{fee.amount}</td>
                      <td className="px-6 py-4 text-sm text-gray-600 dark:text-gray-300">{fee.description}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </section>

      {/* Financial Aid */}
      <section id="financial-aid" className="py-16 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center max-w-5xl mx-auto">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.7 }}
              viewport={{ once: true }}
              className="space-y-6"
            >
              <div className="flex items-center mb-2">
                <FaHandHoldingUsd className="text-4xl text-blue-600 mr-4" />
                <h2 className="text-3xl font-bold text-gray-900 dark:text-white">Financial Aid</h2>
              </div>
              <p className="text-gray-600 dark:text-gray-300">
                Alfalah Islamic School is committed to making quality education accessible to families from diverse economic backgrounds. Our need-based financial aid program supports families who demonstrate financial need.
              </p>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <FaCheckCircle className="text-green-600 mt-1 mr-2 flex-shrink-0" />
                  <span className="text-gray-600 dark:text-gray-300">Financial aid applications are reviewed separately from admission applications</span>
                </li>
                <li className="flex items-start">
                  <FaCheckCircle className="text-green-600 mt-1 mr-2 flex-shrink-0" />
                  <span className="text-gray-600 dark:text-gray-300">Aid is awarded based on demonstrated financial need and availability of funds</span>
                </li>
                <li className="flex items-start">
                  <FaCheckCircle className="text-green-600 mt-1 mr-2 flex-shrink-0" />
                  <span className="text-gray-600 dark:text-gray-300">Applications must be submitted by March 15 for priority consideration</span>
                </li>
                <li className="flex items-start">
                  <FaCheckCircle className="text-green-600 mt-1 mr-2 flex-shrink-0" />
                  <span className="text-gray-600 dark:text-gray-300">Financial aid must be applied for annually</span>
                </li>
              </ul>
              <div className="pt-4">
                <Link href="/website/admissions/forms">
                  <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                    Financial Aid Application
                  </Button>
                </Link>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.7 }}
              viewport={{ once: true }}
              className="space-y-6"
            >
              <div className="flex items-center mb-2">
                <FaUniversity className="text-4xl text-blue-600 mr-4" />
                <h2 className="text-3xl font-bold text-gray-900 dark:text-white">Scholarships</h2>
              </div>
              <p className="text-gray-600 dark:text-gray-300">
                We offer a limited number of merit-based scholarships to recognize exceptional academic achievement, leadership, and Islamic character. These scholarships are separate from our need-based financial aid program.
              </p>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <FaCheckCircle className="text-green-600 mt-1 mr-2 flex-shrink-0" />
                  <span className="text-gray-600 dark:text-gray-300">Academic Excellence Scholarships (grades 6-12)</span>
                </li>
                <li className="flex items-start">
                  <FaCheckCircle className="text-green-600 mt-1 mr-2 flex-shrink-0" />
                  <span className="text-gray-600 dark:text-gray-300">Quran Memorization Scholarships</span>
                </li>
                <li className="flex items-start">
                  <FaCheckCircle className="text-green-600 mt-1 mr-2 flex-shrink-0" />
                  <span className="text-gray-600 dark:text-gray-300">Leadership & Community Service Scholarships</span>
                </li>
                <li className="flex items-start">
                  <FaCheckCircle className="text-green-600 mt-1 mr-2 flex-shrink-0" />
                  <span className="text-gray-600 dark:text-gray-300">Scholarship applications due by April 1</span>
                </li>
              </ul>
              <div className="pt-4">
                <Link href="/website/admissions/forms">
                  <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                    Scholarship Application
                  </Button>
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Payment Receipt Upload */}
      <section id="payment-receipt" className="py-16 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Submit Payment Receipt</h2>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Upload your payment receipt or screenshot for verification
            </p>
          </motion.div>

          <div className="max-w-3xl mx-auto bg-gray-50 dark:bg-gray-800 rounded-lg shadow-md p-6">
            <form className="space-y-6" onSubmit={async (e) => {
              e.preventDefault();

              const formData = new FormData(e.target as HTMLFormElement);
              const fileInput = document.getElementById('file-upload') as HTMLInputElement;
              const file = fileInput.files?.[0];

              if (!file) {
                alert("Please upload a receipt image or PDF.");
                return;
              }

              try {
                // First, upload the file
                const uploadFormData = new FormData();
                uploadFormData.append('file', file);
                uploadFormData.append('type', 'receipt');

                const uploadResponse = await fetch('/api/website-management/upload-image', {
                  method: 'POST',
                  body: uploadFormData,
                });

                if (!uploadResponse.ok) {
                  throw new Error('Failed to upload receipt image');
                }

                const uploadResult = await uploadResponse.json();

                // Then, submit the payment receipt data
                const receiptData = {
                  studentName: formData.get('studentName'),
                  className: formData.get('class'),
                  month: formData.get('asOfMonth') + ' ' + new Date().getFullYear(),
                  bankName: formData.get('bankName'),
                  receiptImageUrl: uploadResult.url,
                };

                const receiptResponse = await fetch('/api/website-management/payment-receipts', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify(receiptData),
                });

                if (!receiptResponse.ok) {
                  throw new Error('Failed to submit payment receipt');
                }

                alert("Payment receipt submitted successfully! Our finance team will review and confirm your payment.");
                // Reset form
                (e.target as HTMLFormElement).reset();
              } catch (error) {
                console.error('Error submitting payment receipt:', error);
                alert("There was an error submitting your payment receipt. Please try again.");
              }
            }}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="studentName" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Student Name *
                  </label>
                  <input
                    type="text"
                    id="studentName"
                    name="studentName"
                    required
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    placeholder="Enter student's full name"
                  />
                </div>

                <div>
                  <label htmlFor="class" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Class *
                  </label>
                  <select
                    id="class"
                    name="class"
                    required
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  >
                    <option value="">Select Class</option>
                    <option value="KG">Kindergarten</option>
                    <option value="1A">1A</option>
                    <option value="1B">1B</option>
                    <option value="1C">1C</option>
                    <option value="1D">1D</option>
                    <option value="2A">2A</option>
                    <option value="2B">2B</option>
                    <option value="2C">2C</option>
                    <option value="2D">2D</option>
                    <option value="3A">3A</option>
                    <option value="3B">3B</option>
                    <option value="3C">3C</option>
                    <option value="3D">3D</option>
                    <option value="4A">4A</option>
                    <option value="4B">4B</option>
                    <option value="4C">4C</option>
                    <option value="4D">4D</option>
                    <option value="5A">5A</option>
                    <option value="5B">5B</option>
                    <option value="5C">5C</option>
                    <option value="5D">5D</option>
                    <option value="6A">6A</option>
                    <option value="6B">6B</option>
                    <option value="6C">6C</option>
                    <option value="6D">6D</option>
                    <option value="7A">7A</option>
                    <option value="7B">7B</option>
                    <option value="7C">7C</option>
                    <option value="7D">7D</option>
                    <option value="8A">8A</option>
                    <option value="8B">8B</option>
                    <option value="8C">8C</option>
                    <option value="8D">8D</option>
                    <option value="8E">8E</option>
                    <option value="9A">9A</option>
                    <option value="9B">9B</option>
                    <option value="9C">9C</option>
                    <option value="9D">9D</option>
                    <option value="10A">10A</option>
                    <option value="10B">10B</option>
                    <option value="10C">10C</option>
                    <option value="10D">10D</option>
                    <option value="11A">11A</option>
                    <option value="11B">11B</option>
                    <option value="11C">11C</option>
                    <option value="11D">11D</option>
                    <option value="12A">12A</option>
                    <option value="12B">12B</option>
                    <option value="12C">12C</option>
                    <option value="12D">12D</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="asOfMonth" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    As Of Month *
                  </label>
                  <select
                    id="asOfMonth"
                    name="asOfMonth"
                    required
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  >
                    <option value="">Select Month</option>
                    <option value="January">January</option>
                    <option value="February">February</option>
                    <option value="March">March</option>
                    <option value="April">April</option>
                    <option value="May">May</option>
                    <option value="June">June</option>
                    <option value="July">July</option>
                    <option value="August">August</option>
                    <option value="September">September</option>
                    <option value="October">October</option>
                    <option value="November">November</option>
                    <option value="December">December</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="bankName" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Bank Name *
                  </label>
                  <input
                    type="text"
                    id="bankName"
                    name="bankName"
                    required
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    placeholder="Enter bank name"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="paymentType" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Payment Type *
                </label>
                <select
                  id="paymentType"
                  name="paymentType"
                  required
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="">Select Payment Type</option>
                  <option value="Tuition Fee">Tuition Fee</option>
                  <option value="Registration Fee">Registration Fee</option>
                  <option value="Books Fee">Books Fee</option>
                  <option value="Activity Fee">Activity Fee</option>
                  <option value="Technology Fee">Technology Fee</option>
                  <option value="Application Fee">Application Fee</option>
                  <option value="Enrollment Fee">Enrollment Fee</option>
                  <option value="Re-enrollment Fee">Re-enrollment Fee</option>
                  <option value="Graduation Fee">Graduation Fee</option>
                  <option value="Other">Other</option>
                </select>
              </div>

              <div>
                <label htmlFor="amount" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Amount Paid *
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <span className="text-gray-500 dark:text-gray-400">$</span>
                  </div>
                  <input
                    type="number"
                    id="amount"
                    name="amount"
                    required
                    min="0"
                    step="0.01"
                    className="w-full pl-8 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    placeholder="0.00"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="receiptUpload" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Upload Receipt/Screenshot *
                </label>
                <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 dark:border-gray-600 border-dashed rounded-md">
                  <div className="space-y-1 text-center">
                    <svg
                      className="mx-auto h-12 w-12 text-gray-400"
                      stroke="currentColor"
                      fill="none"
                      viewBox="0 0 48 48"
                      aria-hidden="true"
                    >
                      <path
                        d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                        strokeWidth={2}
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                    <div className="flex text-sm text-gray-600 dark:text-gray-400">
                      <label
                        htmlFor="file-upload"
                        className="relative cursor-pointer bg-white dark:bg-gray-700 rounded-md font-medium text-blue-600 dark:text-blue-400 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500"
                      >
                        <span>Upload a file</span>
                        <input id="file-upload" name="file-upload" type="file" className="sr-only" required accept="image/*,.pdf" />
                      </label>
                      <p className="pl-1">or drag and drop</p>
                    </div>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      PNG, JPG, GIF or PDF up to 10MB
                    </p>
                  </div>
                </div>
              </div>

              <div>
                <label htmlFor="notes" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Additional Notes
                </label>
                <textarea
                  id="notes"
                  name="notes"
                  rows={3}
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  placeholder="Any additional information about your payment"
                ></textarea>
              </div>

              <div className="flex items-center">
                <input
                  id="terms"
                  name="terms"
                  type="checkbox"
                  required
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="terms" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                  I confirm that the information provided is accurate and the uploaded receipt is valid
                </label>
              </div>

              <div className="flex justify-center">
                <Button
                  type="submit"
                  className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-2 rounded-md shadow-sm"
                >
                  Submit Payment Receipt
                </Button>
              </div>
            </form>
          </div>
        </div>
      </section>

      {/* Frequently Asked Questions */}
      <section id="faqs" className="py-16 bg-gray-50 dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Frequently Asked Questions</h2>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Answers to common questions about tuition, fees, and financial matters
            </p>
          </motion.div>

          <div className="max-w-3xl mx-auto">
            <Accordion type="single" collapsible className="space-y-4">
              {faqs.map((faq, index) => (
                <AccordionItem
                  key={index}
                  value={`item-${index}`}
                  className="bg-white dark:bg-gray-900 rounded-lg shadow-md overflow-hidden"
                >
                  <AccordionTrigger className="px-6 py-4 text-left font-semibold text-gray-900 dark:text-white hover:no-underline">
                    {faq.question}
                  </AccordionTrigger>
                  <AccordionContent className="px-6 pb-4 text-gray-600 dark:text-gray-300">
                    {faq.answer}
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-12 bg-blue-600">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="text-white mb-6 md:mb-0">
              <h2 className="text-2xl font-bold mb-2">Questions About Tuition or Financial Aid?</h2>
              <p className="text-blue-100">Our Business Office is here to help you navigate the financial aspects of enrollment.</p>
            </div>
            <div className="flex flex-col sm:flex-row gap-4">
              <Link href="/website/contact">
                <Button className="bg-white text-blue-600 hover:bg-blue-50">
                  Contact Business Office
                </Button>
              </Link>
              <Link href="/website/admissions/how-to-apply">
                <Button variant="outline" className="border-white text-white hover:bg-blue-700">
                  Apply Now
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

"use client"

import React, { useState } from 'react'
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "./ui/table"
import { Button } from "./ui/button"
import { Input } from "./ui/input"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu"
import {
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  Download,
  Filter,
  Search,
  SlidersHorizontal,
  Edit,
  Trash2,
  MoreHorizontal
} from "lucide-react"
import { Mark } from './MarkList'
import { Badge } from "./ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select"
import { useAuth } from '../contexts/auth-context'
import { usePermissionDeniedDialog } from './PermissionDeniedDialog'
import { MobileSearch, MobileFilterGrid, MobileTableContainer, MobileEmptyState, MobileLoading } from './ui/mobile-container'
import { useTableScroll } from '../hooks/useHorizontalScroll'
import { MultiSelectSubjects } from './ui/multi-select-subjects'

interface MarksDataTableProps {
  data: Mark[]
  isLoading: boolean
  onEdit?: (mark: Mark) => void
  onDelete?: (id: string) => void
}

export function MarksDataTable({ data, isLoading = false, onEdit, onDelete }: MarksDataTableProps) {
  const { user } = useAuth()
  const { showDialog, PermissionDialog } = usePermissionDeniedDialog()
  const { scrollRef } = useTableScroll()
  const [sorting, setSorting] = useState<SortingState>([])
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({})
  const [rowSelection, setRowSelection] = useState({})
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedClass, setSelectedClass] = useState<string>("all")
  const [selectedSubjects, setSelectedSubjects] = useState<string[]>([])

  // Define the columns for the marks data table
  const columns: ColumnDef<Mark>[] = [
    {
      accessorKey: "studentName",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="p-0 hover:bg-transparent font-semibold"
          >
            Student Name
            <ChevronDown className="ml-2 h-4 w-4" />
          </Button>
        )
      },
      cell: ({ row }) => (
        <div className="font-medium text-gray-900 min-w-[150px] max-w-[200px] truncate" title={row.getValue("studentName")}>
          {row.getValue("studentName")}
        </div>
      ),
    },
    {
      accessorKey: "class",
      header: "Class",
      cell: ({ row }) => (
        <div className="min-w-[80px] font-medium text-center">
          {row.getValue("class")}
        </div>
      ),
    },
    {
      accessorKey: "subject",
      header: "Subject",
      cell: ({ row }) => (
        <div className="min-w-[120px] max-w-[150px] truncate" title={row.getValue("subject")}>
          {row.getValue("subject")}
        </div>
      ),
    },
    {
      accessorKey: "term",
      header: "Semester",
      cell: ({ row }) => (
        <div className="min-w-[100px] text-center">
          <Badge variant="outline" className="text-xs">
            {row.getValue("term")}
          </Badge>
        </div>
      ),
    },
    {
      accessorKey: "academicYear",
      header: "Academic Year",
      cell: ({ row }) => (
        <div className="min-w-[100px] text-center font-mono text-sm">
          {row.getValue("academicYear")}
        </div>
      ),
    },
    {
      accessorKey: "obtainedMarks",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="p-0 hover:bg-transparent font-semibold"
          >
            Marks
            <ChevronDown className="ml-2 h-4 w-4" />
          </Button>
        )
      },
      cell: ({ row }) => {
        const mark = row.original;
        const percentage = (mark.obtainedMarks / mark.totalMarks) * 100;
        return (
          <div className="min-w-[140px] space-y-1">
            <div className="flex items-center justify-between">
              <span className="font-medium text-sm">
                {mark.obtainedMarks}/{mark.totalMarks}
              </span>
              <span className="text-xs text-gray-500">
                {percentage.toFixed(0)}%
              </span>
            </div>
            <div className="w-full h-2 bg-gray-100 rounded-full overflow-hidden">
              <div
                className={`h-full rounded-full transition-all duration-300 ${
                  percentage >= 80 ? 'bg-green-500' :
                  percentage >= 60 ? 'bg-yellow-500' :
                  percentage >= 40 ? 'bg-orange-500' :
                  'bg-red-500'
                }`}
                style={{ width: `${percentage}%` }}
              ></div>
            </div>
          </div>
        )
      },
    },
    {
      id: "actions",
      header: () => (
        <div className="text-center font-semibold">Actions</div>
      ),
      cell: ({ row }) => {
        const mark = row.original;

        return (
          <div className="flex items-center justify-center gap-1 min-w-[100px]">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                if (!user || !['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'TEACHER', 'DATA_ENCODER'].includes(user.role)) {
                  showDialog(
                    'You do not have permission to edit marks',
                    'Only Super Admin, Admin, Supervisor, Teacher, and Data Encoder roles can edit marks.'
                  );
                  return;
                }
                onEdit && onEdit(mark);
              }}
              className="h-9 w-9 p-0 text-blue-600 hover:text-blue-900 hover:bg-blue-100 rounded-md transition-colors"
              title="Edit mark"
            >
              <Edit className="h-4 w-4" />
              <span className="sr-only">Edit mark</span>
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                if (!user || !['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'TEACHER', 'DATA_ENCODER'].includes(user.role)) {
                  showDialog(
                    'You do not have permission to delete marks',
                    'Only Super Admin, Admin, Supervisor, Teacher, and Data Encoder roles can delete marks.'
                  );
                  return;
                }
                onDelete && onDelete(mark.id);
              }}
              className="h-9 w-9 p-0 text-red-600 hover:text-red-900 hover:bg-red-100 rounded-md transition-colors"
              title="Delete mark"
            >
              <Trash2 className="h-4 w-4" />
              <span className="sr-only">Delete mark</span>
            </Button>
          </div>
        );
      },
      meta: {
        className: "sticky right-0 bg-white border-l border-gray-200"
      }
    },
  ]

  // Get unique classes and subjects for filters
  const classes = ["all", ...Array.from(new Set(data.map(mark => mark.class)))].sort()
  const subjects = ["all", ...Array.from(new Set(data.map(mark => mark.subject)))].sort()

  // Filter data based on search term, class, and subjects
  const filteredData = React.useMemo(() => {
    let filtered = [...data]

    if (searchTerm) {
      filtered = filtered.filter(mark =>
        mark.studentName.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (selectedClass && selectedClass !== "all") {
      filtered = filtered.filter(mark => mark.class === selectedClass)
    }

    if (selectedSubjects.length > 0) {
      filtered = filtered.filter(mark => selectedSubjects.includes(mark.subject))
    }

    return filtered
  }, [data, searchTerm, selectedClass, selectedSubjects])

  const table = useReactTable({
    data: filteredData,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  })

  // Export to CSV function
  const exportToCSV = () => {
    const headers = columns.map(col => (typeof col.header === 'string' ? col.header : col.accessorKey))

    const csvData = filteredData.map(row => {
      return columns.map(col => {
        const key = col.accessorKey as string
        return row[key]
      }).join(',')
    })

    const csv = [headers.join(','), ...csvData].join('\n')
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.setAttribute('href', url)
    link.setAttribute('download', `marks_export_${new Date().toISOString().split('T')[0]}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  return (
    <div className="space-y-4">
      {/* Filters and controls */}
      <div className="space-y-4">
        {/* Search bar - full width on mobile */}
        <MobileSearch
          placeholder="Search students..."
          value={searchTerm}
          onChange={setSearchTerm}
          icon={<Search className="h-4 w-4" />}
        />

        {/* Filters row */}
        <MobileFilterGrid columns={4}>
          {/* Class filter */}
          <Select value={selectedClass} onValueChange={setSelectedClass}>
            <SelectTrigger className="h-12">
              <SelectValue placeholder="All Classes" />
            </SelectTrigger>
            <SelectContent>
              {classes.map((cls) => (
                <SelectItem key={cls} value={cls}>
                  {cls === "all" ? "All Classes" : cls}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Subject filter - Multi-select */}
          <MultiSelectSubjects
            subjects={subjects}
            selectedSubjects={selectedSubjects}
            onSelectionChange={setSelectedSubjects}
            placeholder="All Subjects"
            className="h-12"
          />

          {/* Column visibility */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="h-12 justify-start">
                <SlidersHorizontal className="mr-2 h-4 w-4" />
                <span className="hidden sm:inline">View Options</span>
                <span className="sm:hidden">View</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => {
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) =>
                        column.toggleVisibility(!!value)
                      }
                    >
                      {column.id}
                    </DropdownMenuCheckboxItem>
                  )
                })}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Export button */}
          <Button variant="outline" onClick={exportToCSV} className="h-12 justify-start">
            <Download className="mr-2 h-4 w-4" />
            <span className="hidden sm:inline">Export CSV</span>
            <span className="sm:hidden">Export</span>
          </Button>
        </MobileFilterGrid>
      </div>

      {/* Data table with horizontal scroll */}
      <div className="rounded-lg border border-gray-200 bg-white shadow-sm">
        {/* Mobile scroll hint */}
        <div className="block lg:hidden bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-200 px-4 py-3">
          <div className="flex items-center justify-between">
            <p className="text-xs text-blue-700 flex items-center font-medium">
              <svg className="w-4 h-4 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16l-4-4m0 0l4-4m-4 4h18" />
              </svg>
              Scroll horizontally to view all {columns.length} columns
            </p>
            <div className="hidden sm:flex items-center text-xs text-blue-600 space-x-2">
              <span className="bg-blue-100 px-2 py-1 rounded text-blue-700 font-mono">←→</span>
              <span>or swipe</span>
            </div>
          </div>
        </div>

        {/* Horizontal scroll container */}
        <div
          ref={scrollRef}
          className="overflow-x-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 scroll-container"
        >
          <Table className="min-w-full mobile-table-enhanced">
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id} className="bg-gray-50 border-b border-gray-200">
                  {headerGroup.headers.map((header) => {
                    const isActionsColumn = header.id === 'actions';
                    return (
                      <TableHead
                        key={header.id}
                        className={`font-semibold text-gray-900 whitespace-nowrap px-4 py-3 text-left min-w-[120px] first:min-w-[150px] ${
                          isActionsColumn
                            ? 'sticky right-0 bg-gray-50 border-l border-gray-200 min-w-[100px] w-[100px] z-10'
                            : ''
                        }`}
                      >
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                      </TableHead>
                    )
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="h-32 text-center"
                  >
                    <MobileLoading message="Loading marks..." />
                  </TableCell>
                </TableRow>
              ) : table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && "selected"}
                    className="hover:bg-gray-50 transition-colors border-b border-gray-100 last:border-b-0"
                  >
                    {row.getVisibleCells().map((cell) => {
                      const isActionsColumn = cell.column.id === 'actions';
                      return (
                        <TableCell
                          key={cell.id}
                          className={`whitespace-nowrap px-4 py-3 text-sm min-w-[120px] first:min-w-[150px] ${
                            isActionsColumn
                              ? 'sticky right-0 bg-white border-l border-gray-200 min-w-[100px] w-[100px] z-10'
                              : ''
                          }`}
                        >
                          {flexRender(cell.column.columnDef.cell, cell.getContext())}
                        </TableCell>
                      )
                    })}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="h-32 text-center"
                  >
                    <MobileEmptyState
                      icon={<Search className="h-8 w-8 text-gray-400" />}
                      title="No marks found"
                      description="Add marks using the 'Add Mark' button above."
                    />
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Pagination */}
      <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
        <div className="text-sm text-muted-foreground">
          {table.getFilteredRowModel().rows.length} mark(s) found
        </div>

        <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-6">
          {/* Rows per page */}
          <div className="flex items-center space-x-2">
            <p className="text-sm font-medium whitespace-nowrap">Rows per page</p>
            <Select
              value={`${table.getState().pagination.pageSize}`}
              onValueChange={(value) => {
                table.setPageSize(Number(value))
              }}
            >
              <SelectTrigger className="h-10 w-[80px]">
                <SelectValue placeholder={table.getState().pagination.pageSize} />
              </SelectTrigger>
              <SelectContent side="top">
                {[10, 20, 30, 40, 50].map((pageSize) => (
                  <SelectItem key={pageSize} value={`${pageSize}`}>
                    {pageSize}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Page info and navigation */}
          <div className="flex items-center justify-between sm:justify-center space-x-4">
            <div className="text-sm font-medium whitespace-nowrap">
              Page {table.getState().pagination.pageIndex + 1} of {table.getPageCount()}
            </div>

            <div className="flex items-center space-x-1">
              <Button
                variant="outline"
                className="hidden h-10 w-10 p-0 sm:flex"
                onClick={() => table.setPageIndex(0)}
                disabled={!table.getCanPreviousPage()}
              >
                <span className="sr-only">Go to first page</span>
                <ChevronsLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="h-10 w-10 p-0"
                onClick={() => table.previousPage()}
                disabled={!table.getCanPreviousPage()}
              >
                <span className="sr-only">Go to previous page</span>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="h-10 w-10 p-0"
                onClick={() => table.nextPage()}
                disabled={!table.getCanNextPage()}
              >
                <span className="sr-only">Go to next page</span>
                <ChevronRight className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="hidden h-10 w-10 p-0 sm:flex"
                onClick={() => table.setPageIndex(table.getPageCount() - 1)}
                disabled={!table.getCanNextPage()}
              >
                <span className="sr-only">Go to last page</span>
                <ChevronsRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Permission Denied Dialog */}
      <PermissionDialog />
    </div>
  )
}

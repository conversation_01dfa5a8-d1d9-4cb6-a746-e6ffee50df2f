import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// Mock data for payments (used as fallback)
const mockPayments = [
  {
    id: '1',
    invoiceNumber: 'INV-2023-001',
    studentId: 'STD001',
    student: {
      id: 'student1',
      sid: 'STD001',
      name: '<PERSON>',
      className: '8E',
    },
    feeTypeId: 'fee1',
    feeType: {
      id: 'fee1',
      name: '<PERSON><PERSON> (Monthly)',
      description: 'Monthly tuition fee',
      amount: 1500,
      frequency: 'monthly',
      isActive: true
    },
    amount: 1500,
    paymentDate: new Date('2023-09-05'),
    paymentMethod: 'cash',
    status: 'paid',
    createdAt: new Date('2023-09-05T10:30:00Z'),
    updatedAt: new Date('2023-09-05T10:30:00Z')
  },
  {
    id: '2',
    invoiceNumber: 'INV-2023-002',
    studentId: 'STD002',
    student: {
      id: 'student2',
      sid: 'STD002',
      name: '<PERSON><PERSON>',
      className: '7A',
    },
    feeTypeId: 'fee2',
    feeType: {
      id: 'fee2',
      name: 'Registration Fee',
      description: 'One-time registration fee',
      amount: 2000,
      frequency: 'one-time',
      isActive: true
    },
    amount: 2000,
    paymentDate: new Date('2023-09-10'),
    paymentMethod: 'transfer',
    transferId: 'TRF12345',
    status: 'paid',
    createdAt: new Date('2023-09-10T11:15:00Z'),
    updatedAt: new Date('2023-09-10T11:15:00Z')
  }
];

// Helper function to check if a model exists in Prisma
function isModelAvailable(modelName: string): boolean {
  return Object.prototype.hasOwnProperty.call(prisma, modelName);
}

// GET all payments with filtering
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const studentId = searchParams.get('studentId')
    const className = searchParams.get('className')
    const feeTypeId = searchParams.get('feeTypeId')
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')
    const status = searchParams.get('status')

    // Check if Payment model is available
    if (!isModelAvailable('payment')) {
      console.warn('Payment model is not available in Prisma client. Using mock data.');

      // Filter the mock data based on the parameters
      let filteredData = [...mockPayments];

      if (studentId) {
        filteredData = filteredData.filter(payment => payment.studentId === studentId);
      }

      if (className) {
        filteredData = filteredData.filter(payment => payment.student.className === className);
      }

      if (feeTypeId) {
        filteredData = filteredData.filter(payment => payment.feeTypeId === feeTypeId);
      }

      if (status) {
        filteredData = filteredData.filter(payment => payment.status === status);
      }

      if (startDate) {
        const startDateObj = new Date(startDate);
        filteredData = filteredData.filter(payment => payment.paymentDate >= startDateObj);
      }

      if (endDate) {
        const endDateObj = new Date(endDate);
        endDateObj.setHours(23, 59, 59, 999);
        filteredData = filteredData.filter(payment => payment.paymentDate <= endDateObj);
      }

      // Sort by date (newest first)
      filteredData.sort((a, b) => b.paymentDate.getTime() - a.paymentDate.getTime());

      return NextResponse.json(filteredData);
    }

    // Build the where clause based on the provided filters
    const where: any = {}

    if (studentId) {
      where.studentId = studentId
    }

    if (className) {
      where.student = {
        className
      }
    }

    if (feeTypeId) {
      where.feeTypeId = feeTypeId
    }

    if (status) {
      where.status = status
    }

    // Date range filtering
    if (startDate || endDate) {
      where.paymentDate = {}

      if (startDate) {
        where.paymentDate.gte = new Date(startDate)
      }

      if (endDate) {
        // Set the end date to the end of the day
        const endDateTime = new Date(endDate)
        endDateTime.setHours(23, 59, 59, 999)
        where.paymentDate.lte = endDateTime
      }
    }

    try {
      // Fetch payments with related data
      const payments = await prisma.payment.findMany({
        where,
        include: {
          student: {
            select: {
              id: true,
              sid: true,
              name: true,
              className: true,
            },
          },
          feeType: true,
        },
        orderBy: {
          paymentDate: 'desc',
        },
      })

      return NextResponse.json(payments)
    } catch (dbError) {
      console.error('Database error fetching payments:', dbError);

      // If there's a database error, fall back to mock data
      console.warn('Falling back to mock payment data');
      return NextResponse.json(mockPayments);
    }
  } catch (error) {
    console.error('Error fetching payments:', error)

    // Return mock data as a fallback
    console.warn('Error in payments API, returning mock data');
    return NextResponse.json(mockPayments);
  }
}

// CREATE a new payment
export async function POST(request: Request) {
  try {
    const data = await request.json()

    // Validate required fields
    if (!data.studentId || !data.feeTypeId || !data.amount || !data.paymentMethod) {
      return NextResponse.json(
        { error: 'Student ID, fee type ID, amount, and payment method are required' },
        { status: 400 }
      )
    }

    // Check if Payment model is available
    if (!isModelAvailable('payment')) {
      console.warn('Payment model is not available in Prisma client. Using mock data.');

      // Generate a mock payment response
      const mockPayment = {
        id: `mock-${Date.now()}`,
        invoiceNumber: `INV-${new Date().getFullYear().toString().slice(-2)}-${(new Date().getMonth() + 1).toString().padStart(2, '0')}-${Math.floor(1000 + Math.random() * 9000)}`,
        studentId: data.studentId,
        student: {
          id: data.studentId,
          sid: `STD${Math.floor(1000 + Math.random() * 9000)}`,
          name: 'Mock Student',
          className: 'Mock Class',
        },
        feeTypeId: data.feeTypeId,
        feeType: {
          id: data.feeTypeId,
          name: 'Mock Fee Type',
          description: 'Mock fee type description',
          amount: parseFloat(data.amount),
          frequency: 'one-time',
          isActive: true
        },
        amount: parseFloat(data.amount),
        paymentDate: data.paymentDate ? new Date(data.paymentDate) : new Date(),
        paymentMethod: data.paymentMethod,
        transferId: data.transferId || null,
        status: data.status || 'paid',
        notes: data.notes || null,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      return NextResponse.json(mockPayment, { status: 201 });
    }

    try {
      // Check if student exists
      const student = await prisma.student.findUnique({
        where: { id: data.studentId },
      })

      if (!student) {
        return NextResponse.json(
          { error: 'Student not found' },
          { status: 404 }
        )
      }

      // Check if fee type exists
      const feeType = await prisma.feeType.findUnique({
        where: { id: data.feeTypeId },
      })

      if (!feeType) {
        return NextResponse.json(
          { error: 'Fee type not found' },
          { status: 404 }
        )
      }

      // Generate invoice number
      const invoiceNumber = await generateInvoiceNumber()

      // Create the payment
      const payment = await prisma.payment.create({
        data: {
          invoiceNumber,
          studentId: data.studentId,
          feeTypeId: data.feeTypeId,
          amount: parseFloat(data.amount),
          paymentDate: data.paymentDate ? new Date(data.paymentDate) : new Date(),
          forMonth: data.forMonth || null, // Store the month this payment is for
          paymentMethod: data.paymentMethod,
          transferId: data.transferId || null,
          status: data.status || 'paid',
          notes: data.notes || null,
        },
        include: {
          student: {
            select: {
              id: true,
              sid: true,
              name: true,
              className: true,
            },
          },
          feeType: true,
        },
      })

      return NextResponse.json(payment, { status: 201 })
    } catch (dbError) {
      console.error('Database error creating payment:', dbError);

      // Generate a mock payment response as fallback
      const mockPayment = {
        id: `mock-${Date.now()}`,
        invoiceNumber: `INV-${new Date().getFullYear().toString().slice(-2)}-${(new Date().getMonth() + 1).toString().padStart(2, '0')}-${Math.floor(1000 + Math.random() * 9000)}`,
        studentId: data.studentId,
        student: {
          id: data.studentId,
          sid: `STD${Math.floor(1000 + Math.random() * 9000)}`,
          name: 'Mock Student',
          className: 'Mock Class',
        },
        feeTypeId: data.feeTypeId,
        feeType: {
          id: data.feeTypeId,
          name: 'Mock Fee Type',
          description: 'Mock fee type description',
          amount: parseFloat(data.amount),
          frequency: 'one-time',
          isActive: true
        },
        amount: parseFloat(data.amount),
        paymentDate: data.paymentDate ? new Date(data.paymentDate) : new Date(),
        paymentMethod: data.paymentMethod,
        transferId: data.transferId || null,
        status: data.status || 'paid',
        notes: data.notes || null,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      return NextResponse.json(mockPayment, { status: 201 });
    }
  } catch (error) {
    console.error('Error creating payment:', error)
    return NextResponse.json(
      { error: 'Failed to create payment' },
      { status: 500 }
    )
  }
}

// Helper function to generate a unique invoice number
async function generateInvoiceNumber() {
  const date = new Date()
  const year = date.getFullYear().toString().slice(-2)
  const month = (date.getMonth() + 1).toString().padStart(2, '0')

  // Check if Payment model is available
  if (!isModelAvailable('payment')) {
    // Generate a random sequence number if the model is not available
    const randomSequence = Math.floor(1000 + Math.random() * 9000)
    return `INV-${year}-${month}-${randomSequence.toString().padStart(4, '0')}`
  }

  try {
    // Get the count of payments for this month to use as a sequence
    const count = await prisma.payment.count({
      where: {
        createdAt: {
          gte: new Date(date.getFullYear(), date.getMonth(), 1),
          lt: new Date(date.getFullYear(), date.getMonth() + 1, 1),
        },
      },
    })

    // Format: INV-YY-MM-SEQUENCE
    return `INV-${year}-${month}-${(count + 1).toString().padStart(4, '0')}`
  } catch (error) {
    // If there's an error, generate a random sequence number
    console.error('Error generating invoice number:', error)
    const randomSequence = Math.floor(1000 + Math.random() * 9000)
    return `INV-${year}-${month}-${randomSequence.toString().padStart(4, '0')}`
  }
}

'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';

// Theme types
export type ThemeName = 'light' | 'dark' | 'solarized' | 'high-contrast';

// Theme interface
export interface Theme {
  name: ThemeName;
  colors: {
    primary: string;
    onPrimary: string;
    secondary: string;
    onSecondary: string;
    bg: string;
    surface: string;
    text: string;
    border: string;
    error: string;
    success: string;
    warning: string;
    info: string;
  };
  typography: {
    fontFamily: string;
    fontWeights: {
      regular: number;
      medium: number;
      semibold: number;
      bold: number;
    };
    baseFontSize: string;
    lineHeight: number;
  };
  spacing: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
    xxl: string;
  };
  borderRadius: {
    sm: string;
    md: string;
    lg: string;
    pill: string;
  };
  shadows: {
    sm: string;
    md: string;
    lg: string;
  };
}

// Define the themes
export const themes: Record<ThemeName, Theme> = {
  light: {
    name: 'light',
    colors: {
      primary: '#0070f3',
      onPrimary: '#ffffff',
      secondary: '#6c757d',
      onSecondary: '#ffffff',
      bg: '#ffffff',
      surface: '#f8f9fa',
      text: '#212529',
      border: '#dee2e6',
      error: '#dc3545',
      success: '#28a745',
      warning: '#ffc107',
      info: '#17a2b8'
    },
    typography: {
      fontFamily: "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",
      fontWeights: {
        regular: 400,
        medium: 500,
        semibold: 600,
        bold: 700
      },
      baseFontSize: '16px',
      lineHeight: 1.5
    },
    spacing: {
      xs: '4px',
      sm: '8px',
      md: '16px',
      lg: '24px',
      xl: '32px',
      xxl: '48px'
    },
    borderRadius: {
      sm: '4px',
      md: '8px',
      lg: '16px',
      pill: '9999px'
    },
    shadows: {
      sm: '0 1px 2px rgba(0, 0, 0, 0.05)',
      md: '0 4px 6px rgba(0, 0, 0, 0.1)',
      lg: '0 10px 15px rgba(0, 0, 0, 0.1)'
    }
  },
  dark: {
    name: 'dark',
    colors: {
      primary: '#3b82f6',
      onPrimary: '#ffffff',
      secondary: '#6b7280',
      onSecondary: '#ffffff',
      bg: '#121212',
      surface: '#1e1e1e',
      text: '#e5e7eb',
      border: '#374151',
      error: '#ef4444',
      success: '#10b981',
      warning: '#f59e0b',
      info: '#3b82f6'
    },
    typography: {
      fontFamily: "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",
      fontWeights: {
        regular: 400,
        medium: 500,
        semibold: 600,
        bold: 700
      },
      baseFontSize: '16px',
      lineHeight: 1.5
    },
    spacing: {
      xs: '4px',
      sm: '8px',
      md: '16px',
      lg: '24px',
      xl: '32px',
      xxl: '48px'
    },
    borderRadius: {
      sm: '4px',
      md: '8px',
      lg: '16px',
      pill: '9999px'
    },
    shadows: {
      sm: '0 1px 2px rgba(0, 0, 0, 0.3)',
      md: '0 4px 6px rgba(0, 0, 0, 0.4)',
      lg: '0 10px 15px rgba(0, 0, 0, 0.5)'
    }
  },
  solarized: {
    name: 'solarized',
    colors: {
      primary: '#268bd2',
      onPrimary: '#fdf6e3',
      secondary: '#2aa198',
      onSecondary: '#fdf6e3',
      bg: '#fdf6e3',
      surface: '#eee8d5',
      text: '#657b83',
      border: '#93a1a1',
      error: '#dc322f',
      success: '#859900',
      warning: '#b58900',
      info: '#268bd2'
    },
    typography: {
      fontFamily: "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",
      fontWeights: {
        regular: 400,
        medium: 500,
        semibold: 600,
        bold: 700
      },
      baseFontSize: '16px',
      lineHeight: 1.5
    },
    spacing: {
      xs: '4px',
      sm: '8px',
      md: '16px',
      lg: '24px',
      xl: '32px',
      xxl: '48px'
    },
    borderRadius: {
      sm: '4px',
      md: '8px',
      lg: '16px',
      pill: '9999px'
    },
    shadows: {
      sm: '0 1px 2px rgba(0, 43, 54, 0.1)',
      md: '0 4px 6px rgba(0, 43, 54, 0.15)',
      lg: '0 10px 15px rgba(0, 43, 54, 0.2)'
    }
  },
  'high-contrast': {
    name: 'high-contrast',
    colors: {
      primary: '#0000ff',
      onPrimary: '#ffffff',
      secondary: '#000000',
      onSecondary: '#ffffff',
      bg: '#ffffff',
      surface: '#f0f0f0',
      text: '#000000',
      border: '#000000',
      error: '#ff0000',
      success: '#008000',
      warning: '#ff8000',
      info: '#0000ff'
    },
    typography: {
      fontFamily: "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",
      fontWeights: {
        regular: 400,
        medium: 500,
        semibold: 600,
        bold: 700
      },
      baseFontSize: '18px', // Larger for better readability
      lineHeight: 1.6 // Increased for better readability
    },
    spacing: {
      xs: '4px',
      sm: '8px',
      md: '16px',
      lg: '24px',
      xl: '32px',
      xxl: '48px'
    },
    borderRadius: {
      sm: '2px', // Reduced for clearer boundaries
      md: '4px',
      lg: '8px',
      pill: '9999px'
    },
    shadows: {
      sm: '0 0 0 2px #000000',
      md: '0 0 0 3px #000000',
      lg: '0 0 0 4px #000000'
    }
  }
};

// Reduced motion preference
export interface ReducedMotionPreference {
  enabled: boolean;
}

// Theme context interface
interface EnhancedThemeContextType {
  currentTheme: Theme;
  setTheme: (theme: ThemeName) => void;
  reducedMotion: boolean;
  toggleReducedMotion: () => void;
}

// Create the context
const EnhancedThemeContext = createContext<EnhancedThemeContextType | undefined>(undefined);

// Theme storage keys
const THEME_STORAGE_KEY = 'user-theme-preference';
const REDUCED_MOTION_KEY = 'user-reduced-motion';

// Theme provider component
export const EnhancedThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentThemeName, setCurrentThemeName] = useState<ThemeName>('light');
  const [reducedMotion, setReducedMotion] = useState<boolean>(false);
  const [themeLoaded, setThemeLoaded] = useState<boolean>(false);

  // Get the current theme object
  const currentTheme = themes[currentThemeName];

  // Initialize theme on mount - safely handling server-side rendering
  useEffect(() => {
    const initializeTheme = () => {
      try {
        // Check for saved preference - safely access localStorage
        const savedTheme = typeof localStorage !== 'undefined'
          ? localStorage.getItem(THEME_STORAGE_KEY) as ThemeName | null
          : null;

        if (savedTheme && themes[savedTheme]) {
          setCurrentThemeName(savedTheme);
        } else {
          // Fall back to system preference - safely check media query
          const prefersDark = typeof window !== 'undefined' && window.matchMedia
            ? window.matchMedia('(prefers-color-scheme: dark)').matches
            : false;
          setCurrentThemeName(prefersDark ? 'dark' : 'light');
        }

        // Check for reduced motion preference - safely access localStorage
        const savedReducedMotion = typeof localStorage !== 'undefined'
          ? localStorage.getItem(REDUCED_MOTION_KEY)
          : null;
        setReducedMotion(savedReducedMotion === 'true');
      } catch (error) {
        // Fallback to default theme if there's any error
        console.error('Error initializing theme:', error);
        setCurrentThemeName('light');
      } finally {
        setThemeLoaded(true);
      }
    };

    initializeTheme();
  }, []);

  // Listen for system preference changes - safely handling server-side rendering
  useEffect(() => {
    // Skip on server-side
    if (typeof window === 'undefined') return;

    try {
      const darkModeMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

      const handleChange = (e: MediaQueryListEvent) => {
        // Only update if user hasn't explicitly set a preference
        if (typeof localStorage !== 'undefined' && !localStorage.getItem(THEME_STORAGE_KEY)) {
          setCurrentThemeName(e.matches ? 'dark' : 'light');
        }
      };

      // Modern browsers
      if (darkModeMediaQuery.addEventListener) {
        darkModeMediaQuery.addEventListener('change', handleChange);
      }
      // Older browsers
      else if ('addListener' in darkModeMediaQuery) {
        // @ts-ignore - For older browsers
        darkModeMediaQuery.addListener(handleChange);
      }

      return () => {
        if (darkModeMediaQuery.removeEventListener) {
          darkModeMediaQuery.removeEventListener('change', handleChange);
        } else if ('removeListener' in darkModeMediaQuery) {
          // @ts-ignore - For older browsers
          darkModeMediaQuery.removeListener(handleChange);
        }
      };
    } catch (error) {
      console.error('Error setting up media query listener:', error);
      return () => {}; // Empty cleanup function
    }
  }, []);

  // Apply theme to document - safely handling server-side rendering
  useEffect(() => {
    // Skip if not loaded or if we're on the server
    if (!themeLoaded || typeof document === 'undefined') return;

    try {
      document.documentElement.setAttribute('data-theme', currentThemeName);
      document.documentElement.setAttribute('data-reduced-motion', reducedMotion ? 'true' : 'false');

      // Apply theme CSS variables
      Object.entries(currentTheme.colors).forEach(([key, value]) => {
        document.documentElement.style.setProperty(`--color-${key}`, value);
      });

      // Apply typography
      document.documentElement.style.setProperty('--font-family', currentTheme.typography.fontFamily);
      document.documentElement.style.setProperty('--font-size-base', currentTheme.typography.baseFontSize);
      document.documentElement.style.setProperty('--line-height', currentTheme.typography.lineHeight.toString());

      // Apply border radius
      Object.entries(currentTheme.borderRadius).forEach(([key, value]) => {
        document.documentElement.style.setProperty(`--radius-${key}`, value);
      });

      // Apply shadows
      Object.entries(currentTheme.shadows).forEach(([key, value]) => {
        document.documentElement.style.setProperty(`--shadow-${key}`, value);
      });

      // Apply spacing
      Object.entries(currentTheme.spacing).forEach(([key, value]) => {
        document.documentElement.style.setProperty(`--spacing-${key}`, value);
      });

      // Set the class for Tailwind dark mode
      if (currentThemeName === 'dark') {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }
    } catch (error) {
      console.error('Error applying theme:', error);
    }
  }, [currentTheme, currentThemeName, reducedMotion, themeLoaded]);

  // Set theme function - safely handling localStorage
  const setTheme = (themeName: ThemeName) => {
    if (themes[themeName]) {
      setCurrentThemeName(themeName);
      try {
        if (typeof localStorage !== 'undefined') {
          localStorage.setItem(THEME_STORAGE_KEY, themeName);
        }
      } catch (error) {
        console.error('Error saving theme preference:', error);
      }
    }
  };

  // Toggle reduced motion - safely handling localStorage
  const toggleReducedMotion = () => {
    const newValue = !reducedMotion;
    setReducedMotion(newValue);
    try {
      if (typeof localStorage !== 'undefined') {
        localStorage.setItem(REDUCED_MOTION_KEY, newValue ? 'true' : 'false');
      }
    } catch (error) {
      console.error('Error saving reduced motion preference:', error);
    }
  };

  // Context value
  const value = {
    currentTheme,
    setTheme,
    reducedMotion,
    toggleReducedMotion
  };

  return (
    <EnhancedThemeContext.Provider value={value}>
      {children}
    </EnhancedThemeContext.Provider>
  );
};

// Hook to use the theme context
export const useEnhancedTheme = () => {
  const context = useContext(EnhancedThemeContext);
  if (context === undefined) {
    throw new Error('useEnhancedTheme must be used within an EnhancedThemeProvider');
  }
  return context;
};

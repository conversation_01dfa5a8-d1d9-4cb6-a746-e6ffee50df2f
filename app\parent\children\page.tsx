'use client'

import React, { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/app/components/ui/card'
import { Badge } from '@/app/components/ui/badge'
import { Button } from '@/app/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/app/components/ui/avatar'
import { Progress } from '@/app/components/ui/progress'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/app/components/ui/tabs'

import { 
  Users, 
  GraduationCap, 
  Calendar, 
  TrendingUp, 
  BookOpen, 
  Clock,
  User,
  Phone,
  Mail,
  MapPin,
  RefreshCw
} from 'lucide-react'
import Link from 'next/link'

interface Student {
  id: string
  sid: string
  name: string
  className: string
  fatherName: string
  gfName: string
  age: number
  gender: string
  academicYear: string
  photoUrl?: string
  class: {
    name: string
    totalStudents: number
  }
  stats: {
    attendancePercentage: number
    totalAttendanceDays: number
    presentDays: number
    recentMarksCount: number
  }
}

interface ChildrenData {
  children: Student[]
  totalChildren: number
  message: string
}

export default function ParentChildrenPage() {
  const [selectedChild, setSelectedChild] = useState<string | null>(null)

  // Fetch children data
  const {
    data: childrenData,
    isLoading,
    error,
    refetch
  } = useQuery<ChildrenData>({
    queryKey: ['parent-children'],
    queryFn: async () => {
      const res = await fetch('/api/parent/children')
      if (!res.ok) {
        const errorData = await res.json()
        throw new Error(errorData.error || 'Failed to fetch children data')
      }
      return res.json()
    }
  })

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-gray-400" />
          <p className="text-gray-500">Loading your children's information...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="text-red-500 mb-4">
            <Users className="h-12 w-12 mx-auto mb-2" />
            <p className="text-lg font-semibold">Error Loading Data</p>
            <p className="text-sm">{error instanceof Error ? error.message : 'Something went wrong'}</p>
          </div>
          <Button onClick={() => refetch()} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  const children = childrenData?.children || []

  if (children.length === 0) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Users className="h-16 w-16 mx-auto mb-4 text-gray-300" />
          <h2 className="text-xl font-semibold text-gray-700 mb-2">No Children Found</h2>
          <p className="text-gray-500 mb-4">
            No students are currently linked to your parent account.
          </p>
          <p className="text-sm text-gray-400">
            Please contact the school administration to link your children to your account.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-800">My Children</h1>
          <p className="text-gray-500">
            Overview of your {children.length} {children.length === 1 ? 'child' : 'children'}
          </p>
        </div>
        <Button onClick={() => refetch()} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Children Cards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {children.map((child) => (
          <Card key={child.id} className="hover:shadow-lg transition-shadow duration-200">
            <CardHeader className="pb-3">
              <div className="flex items-center space-x-3">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={child.photoUrl} alt={child.name} />
                  <AvatarFallback className="bg-blue-100 text-blue-600">
                    {child.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <CardTitle className="text-lg">{child.name}</CardTitle>
                  <CardDescription className="flex items-center gap-2">
                    <Badge variant="outline" className="text-xs">
                      {child.sid}
                    </Badge>
                    <span className="text-xs">Class {child.className}</span>
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Basic Info */}
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="flex items-center gap-1">
                  <User className="h-3 w-3 text-gray-400" />
                  <span className="text-gray-600">Age: {child.age}</span>
                </div>
                <div className="flex items-center gap-1">
                  <GraduationCap className="h-3 w-3 text-gray-400" />
                  <span className="text-gray-600">{child.academicYear}</span>
                </div>
              </div>

              {/* Attendance Progress */}
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-gray-700">Attendance</span>
                  <span className="text-sm text-gray-500">
                    {child.stats.attendancePercentage}%
                  </span>
                </div>
                <Progress 
                  value={child.stats.attendancePercentage} 
                  className="h-2"
                />
                <div className="text-xs text-gray-500">
                  {child.stats.presentDays} of {child.stats.totalAttendanceDays} days present
                </div>
              </div>

              {/* Recent Activity */}
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center gap-1">
                  <BookOpen className="h-3 w-3 text-gray-400" />
                  <span className="text-gray-600">Recent marks: {child.stats.recentMarksCount}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Users className="h-3 w-3 text-gray-400" />
                  <span className="text-gray-600">{child.class.totalStudents} students</span>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2 pt-2">
                <Link href={`/parent/attendance?studentId=${child.id}`} className="flex-1">
                  <Button variant="outline" size="sm" className="w-full">
                    <Calendar className="h-3 w-3 mr-1" />
                    Attendance
                  </Button>
                </Link>
                <Link href={`/parent/marks?studentId=${child.id}`} className="flex-1">
                  <Button variant="outline" size="sm" className="w-full">
                    <TrendingUp className="h-3 w-3 mr-1" />
                    Marks
                  </Button>
                </Link>
              </div>
              
              <Link href={`/parent/reports?studentId=${child.id}`} className="block">
                <Button variant="default" size="sm" className="w-full">
                  <BookOpen className="h-3 w-3 mr-1" />
                  Report Card
                </Button>
              </Link>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Quick Stats Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <TrendingUp className="h-5 w-5 mr-2" />
            Overall Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                {children.length}
              </div>
              <div className="text-sm text-blue-600">
                {children.length === 1 ? 'Child' : 'Children'} Enrolled
              </div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {Math.round(
                  children.reduce((sum, child) => sum + child.stats.attendancePercentage, 0) / children.length
                )}%
              </div>
              <div className="text-sm text-green-600">Average Attendance</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">
                {children.reduce((sum, child) => sum + child.stats.recentMarksCount, 0)}
              </div>
              <div className="text-sm text-purple-600">Recent Assessments</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

"use client"

import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from './ui/button'
import { Input } from './ui/input'
import { Label } from "./ui/label"
import { Textarea } from "./ui/textarea"
import { Users, BookOpen, Loader2 } from 'lucide-react'
import { CustomSelect } from './ui/custom-select'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select"
import { DialogFooter } from "./ui/dialog"
import { Mark } from './MarkList'
import { useQuery } from '@tanstack/react-query'

// Academic terms and exam types (same thing in this case)
const terms = ['First Semester', 'Second Semester']

// Fixed academic years from 2020-2021 to 2027-2028
const academicYears = [
  '2020-2021',
  '2021-2022',
  '2022-2023',
  '2023-2024',
  '2024-2025',
  '2025-2026',
  '2026-2027',
  '2027-2028'
]



interface MarkFormProps {
  isEditMode: boolean
  initialData: Mark | null
  onSubmit: (mark: Omit<Mark, 'id' | 'dateRecorded' | 'grade'>) => void
  onCancel: () => void
  calculateGrade: (mark: number) => string
  currentClass?: string
  currentSubject?: string
}

export function MarkForm({
  isEditMode,
  initialData,
  onSubmit,
  onCancel,
  calculateGrade,
  currentClass,
  currentSubject
}: MarkFormProps) {
  const [formData, setFormData] = useState({
    id: '',
    studentId: '',
    studentName: '',
    class: currentClass || '',
    subject: currentSubject || '',
    term: 'First Semester',
    academicYear: '2023-2024',

    totalMarks: 100,
    obtainedMarks: 0,
    recordedBy: 'Current Teacher'
  })

  // Fetch classes from the database
  const { data: classes, isLoading: isLoadingClasses } = useQuery({
    queryKey: ['classes'],
    queryFn: async () => {
      const res = await fetch('/api/classes')
      if (!res.ok) throw new Error('Failed to fetch classes')
      return res.json()
    }
  })

  // Fetch subjects based on selected class
  const { data: subjects, isLoading: isLoadingSubjects } = useQuery({
    queryKey: ['subjects', formData.class],
    queryFn: async () => {
      if (!formData.class) return []
      const res = await fetch(`/api/subjects?className=${formData.class}`)
      if (!res.ok) throw new Error('Failed to fetch subjects')
      return res.json()
    },
    enabled: !!formData.class
  })

  // Fetch students based on selected class
  const { data: students, isLoading: isLoadingStudents } = useQuery({
    queryKey: ['students', formData.class],
    queryFn: async () => {
      if (!formData.class) return []
      const res = await fetch(`/api/students?class=${formData.class}`)
      if (!res.ok) throw new Error('Failed to fetch students')
      return res.json()
    },
    enabled: !!formData.class
  })

  // Initialize form data when editing
  useEffect(() => {
    if (isEditMode && initialData) {
      setFormData({
        id: initialData.id,
        studentId: initialData.studentId,
        studentName: initialData.studentName,
        class: initialData.class,
        subject: initialData.subject,
        term: initialData.term,
        academicYear: initialData.academicYear,
        examType: initialData.examType,
        totalMarks: initialData.totalMarks,
        obtainedMarks: initialData.obtainedMarks,
        recordedBy: initialData.recordedBy
      });
    }
  }, [isEditMode, initialData]);

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    setFormData({
      ...formData,
      [name]: name === 'obtainedMarks' || name === 'totalMarks' ? parseInt(value) || 0 : value
    });
  };

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    // Handle class change - reset subject and student
    if (name === 'class' && value !== formData.class) {
      setFormData({
        ...formData,
        class: value,
        subject: '',
        studentId: '',
        studentName: ''
      });
      return;
    }

    // Update student info when student changes
    if (name === 'studentId' && students) {
      const selectedStudent = students.find((student: any) => student.id === value);
      if (selectedStudent) {
        setFormData(prev => ({
          ...prev,
          studentId: value,
          studentName: selectedStudent.name,
          // Don't change class when selecting student - class is already selected
        }));
        return;
      }
    }

    // Default behavior for other fields
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="grid gap-6 py-4">
      {/* Student Selection */}
      <div className="grid grid-cols-2 gap-4">
        <div className="grid gap-2">
          <Label htmlFor="studentId" className="text-sm font-medium">
            Student
          </Label>
          <Select
            value={formData.studentId}
            onValueChange={(value) => handleSelectChange('studentId', value)}
            disabled={isEditMode || !formData.class || isLoadingStudents} // Cannot change student when editing or when class not selected
            required
          >
            <SelectTrigger>
              {isLoadingStudents ? (
                <div className="flex items-center">
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Loading students...
                </div>
              ) : (
                <SelectValue placeholder="Select student" />
              )}
            </SelectTrigger>
            <SelectContent>
              {students && students.length > 0 ? (
                students.map(student => (
                  <SelectItem key={student.id} value={student.id}>
                    {student.sid} - {student.name}
                  </SelectItem>
                ))
              ) : (
                <div className="p-2 text-center text-sm text-gray-500">
                  {formData.class ? 'No students found in this class' : 'Select a class first'}
                </div>
              )}
            </SelectContent>
          </Select>
        </div>

        <div className="grid gap-2">
          <Label htmlFor="class" className="text-sm font-medium">
            Class
          </Label>
          <Select
            value={formData.class}
            onValueChange={(value) => handleSelectChange('class', value)}
            disabled={isEditMode || formData.studentId !== ''}
            required
          >
            <SelectTrigger>
              {isLoadingClasses ? (
                <div className="flex items-center">
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Loading classes...
                </div>
              ) : (
                <SelectValue placeholder="Select class" />
              )}
            </SelectTrigger>
            <SelectContent>
              {classes && classes.length > 0 ? (
                classes.map((cls: { id: string, name: string }) => (
                  <SelectItem key={cls.id} value={cls.name}>
                    {cls.name}
                  </SelectItem>
                ))
              ) : (
                <div className="p-2 text-center text-sm text-gray-500">
                  No classes found
                </div>
              )}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Subject and Term */}
      <div className="grid grid-cols-2 gap-4">
        <div className="grid gap-2">
          <Label htmlFor="subject" className="text-sm font-medium">
            Subject
          </Label>
          <Select
            value={formData.subject}
            onValueChange={(value) => handleSelectChange('subject', value)}
            disabled={!formData.class || isLoadingSubjects}
            required
          >
            <SelectTrigger>
              {isLoadingSubjects ? (
                <div className="flex items-center">
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Loading subjects...
                </div>
              ) : (
                <SelectValue placeholder="Select subject" />
              )}
            </SelectTrigger>
            <SelectContent>
              {subjects && subjects.length > 0 ? (
                subjects.map((subject: { id: string, name: string }) => (
                  <SelectItem key={subject.id} value={subject.name}>
                    {subject.name}
                  </SelectItem>
                ))
              ) : (
                <div className="p-2 text-center text-sm text-gray-500">
                  {formData.class ? 'No subjects found for this class' : 'Select a class first'}
                </div>
              )}
            </SelectContent>
          </Select>
        </div>

        <div className="grid gap-2">
          <Label htmlFor="term" className="text-sm font-medium">
            Term Semester
          </Label>
          <Select
            value={formData.term}
            onValueChange={(value) => handleSelectChange('term', value)}
            required
          >
            <SelectTrigger>
              <SelectValue placeholder="Select term" />
            </SelectTrigger>
            <SelectContent>
              {terms.map(term => (
                <SelectItem key={term} value={term}>{term}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Academic Year and Exam Type */}
      <div className="grid grid-cols-2 gap-4">
        <div className="grid gap-2">
          <Label htmlFor="academicYear" className="text-sm font-medium">
            Academic Year
          </Label>
          <Select
            value={formData.academicYear}
            onValueChange={(value) => handleSelectChange('academicYear', value)}
            required
          >
            <SelectTrigger>
              <SelectValue placeholder="Select academic year" />
            </SelectTrigger>
            <SelectContent>
              {academicYears.map(year => (
                <SelectItem key={year} value={year}>{year}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>


      </div>

      {/* Marks */}
      <div className="grid grid-cols-2 gap-4">
        <div className="grid gap-2">
          <Label htmlFor="totalMarks" className="text-sm font-medium">
            Total Marks
          </Label>
          <Input
            id="totalMarks"
            name="totalMarks"
            type="number"
            min="1"
            value={formData.totalMarks}
            onChange={handleInputChange}
            required
          />
        </div>

        <div className="grid gap-2">
          <Label htmlFor="obtainedMarks" className="text-sm font-medium">
            Obtained Marks
          </Label>
          <Input
            id="obtainedMarks"
            name="obtainedMarks"
            type="number"
            min="0"
            max={formData.totalMarks}
            value={formData.obtainedMarks}
            onChange={handleInputChange}
            required
          />
        </div>
      </div>

      <DialogFooter>
        <Button variant="outline" type="button" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">
          {isEditMode ? 'Update' : 'Add'} Mark
        </Button>
      </DialogFooter>
    </form>
  );
}

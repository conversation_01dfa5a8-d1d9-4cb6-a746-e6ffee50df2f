"use client"

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'

interface AppSettingsData {
  id?: string
  schoolName: string
  schoolLogo?: string
  semesterCount: number
  defaultAcademicYear: string
  sidebarBgColor: string
  sidebarTextColor: string
  createdAt?: string
  updatedAt?: string
}

interface AppSettingsContextType {
  settings: AppSettingsData
  isLoading: boolean
  updateSettings: (newSettings: Partial<AppSettingsData>) => Promise<void>
  refreshSettings: () => Promise<void>
  applySidebarStyles: () => void
}

const defaultSettings: AppSettingsData = {
  schoolName: 'School Management System',
  schoolLogo: '',
  semesterCount: 2,
  defaultAcademicYear: '2024-2025',
  sidebarBgColor: '#1f2937',
  sidebarTextColor: '#ffffff'
}

const AppSettingsContext = createContext<AppSettingsContextType | undefined>(undefined)

export function AppSettingsProvider({ children }: { children: ReactNode }) {
  const [settings, setSettings] = useState<AppSettingsData>(defaultSettings)
  const [isLoading, setIsLoading] = useState(true)

  // Fetch settings from API
  const fetchSettings = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/app-settings')
      if (response.ok) {
        const data = await response.json()
        setSettings(data)
        applySidebarStyles(data)
      } else {
        // If no settings exist, create default ones
        await createDefaultSettings()
      }
    } catch (error) {
      console.error('Error fetching app settings:', error)
      setSettings(defaultSettings)
      applySidebarStyles(defaultSettings)
    } finally {
      setIsLoading(false)
    }
  }

  // Create default settings if none exist
  const createDefaultSettings = async () => {
    try {
      const response = await fetch('/api/app-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(defaultSettings),
      })
      if (response.ok) {
        const data = await response.json()
        setSettings(data)
        applySidebarStyles(data)
      }
    } catch (error) {
      console.error('Error creating default settings:', error)
    }
  }

  // Update settings
  const updateSettings = async (newSettings: Partial<AppSettingsData>) => {
    try {
      const updatedSettings = { ...settings, ...newSettings }
      
      const response = await fetch('/api/app-settings', {
        method: settings.id ? 'PUT' : 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedSettings),
      })

      if (response.ok) {
        const data = await response.json()
        setSettings(data)
        applySidebarStyles(data)
        
        // Trigger a custom event to notify other components
        window.dispatchEvent(new CustomEvent('appSettingsUpdated', { 
          detail: data 
        }))
      } else {
        throw new Error('Failed to update settings')
      }
    } catch (error) {
      console.error('Error updating settings:', error)
      throw error
    }
  }

  // Apply sidebar styles to CSS variables
  const applySidebarStyles = (settingsData?: AppSettingsData) => {
    const currentSettings = settingsData || settings
    if (typeof document !== 'undefined') {
      const root = document.documentElement
      root.style.setProperty('--sidebar-bg-color', currentSettings.sidebarBgColor)
      root.style.setProperty('--sidebar-text-color', currentSettings.sidebarTextColor)
      
      // Also update the sidebar class styles dynamically
      const existingStyle = document.getElementById('dynamic-sidebar-styles')
      if (existingStyle) {
        existingStyle.remove()
      }
      
      const style = document.createElement('style')
      style.id = 'dynamic-sidebar-styles'
      style.textContent = `
        .sidebar-bg {
          background-color: ${currentSettings.sidebarBgColor} !important;
        }
        .sidebar-text {
          color: ${currentSettings.sidebarTextColor} !important;
        }
        .sidebar-hover:hover {
          background-color: ${adjustColorOpacity(currentSettings.sidebarBgColor, 0.1)} !important;
        }
      `
      document.head.appendChild(style)
    }
  }

  // Helper function to adjust color opacity
  const adjustColorOpacity = (color: string, opacity: number) => {
    // Convert hex to rgba
    const hex = color.replace('#', '')
    const r = parseInt(hex.substr(0, 2), 16)
    const g = parseInt(hex.substr(2, 2), 16)
    const b = parseInt(hex.substr(4, 2), 16)
    return `rgba(${r}, ${g}, ${b}, ${opacity})`
  }

  // Refresh settings
  const refreshSettings = async () => {
    await fetchSettings()
  }

  // Initialize settings on mount
  useEffect(() => {
    fetchSettings()
  }, [])

  // Apply styles whenever settings change
  useEffect(() => {
    applySidebarStyles()
  }, [settings])

  const value: AppSettingsContextType = {
    settings,
    isLoading,
    updateSettings,
    refreshSettings,
    applySidebarStyles
  }

  return (
    <AppSettingsContext.Provider value={value}>
      {children}
    </AppSettingsContext.Provider>
  )
}

export function useAppSettings() {
  const context = useContext(AppSettingsContext)
  if (context === undefined) {
    throw new Error('useAppSettings must be used within an AppSettingsProvider')
  }
  return context
}

const mysql = require('mysql2/promise');
const net = require('net');

console.log('🔍 Starting MySQL connection diagnostics...');

// Test network connectivity first
async function testNetworkConnectivity(host, port) {
  return new Promise((resolve) => {
    console.log(`🌐 Testing network connectivity to ${host}:${port}...`);

    const socket = new net.Socket();
    const timeout = 5000;

    socket.setTimeout(timeout);

    socket.on('connect', () => {
      console.log(`✅ Network connection to ${host}:${port} successful`);
      socket.destroy();
      resolve(true);
    });

    socket.on('timeout', () => {
      console.log(`❌ Network connection to ${host}:${port} timed out`);
      socket.destroy();
      resolve(false);
    });

    socket.on('error', (err) => {
      console.log(`❌ Network connection to ${host}:${port} failed:`, err.message);
      socket.destroy();
      resolve(false);
    });

    socket.connect(port, host);
  });
}

async function testMySQLConnection() {
  console.log('🔌 Testing MySQL connection...');

  const config = {
    host: '**************',
    port: 3306,
    user: 'alfalasw_school',
    password: 'M1+fOZ8?Q],G',
    database: 'alfalasw_school',
    connectTimeout: 10000,
    acquireTimeout: 10000,
    timeout: 10000,
  };

  try {
    console.log('📋 Connection config:', {
      ...config,
      password: '****' // Hide password in logs
    });

    const connection = await mysql.createConnection(config);
    console.log('✅ Successfully connected to MySQL database!');

    // Test a simple query
    const [rows] = await connection.execute('SELECT 1 as test');
    console.log('✅ Test query successful:', rows);

    await connection.end();
    console.log('✅ Connection closed successfully');
    return true;

  } catch (error) {
    console.error('❌ MySQL connection failed:', error.message);
    console.error('📊 Error details:', {
      code: error.code,
      errno: error.errno,
      sqlState: error.sqlState
    });

    if (error.code === 'ECONNREFUSED') {
      console.log('💡 Connection refused - possible causes:');
      console.log('   - MySQL server is not running');
      console.log('   - Wrong port number');
      console.log('   - Firewall blocking the connection');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('💡 Access denied - possible causes:');
      console.log('   - Wrong username or password');
      console.log('   - User does not have permission to connect from your IP');
      console.log('   - Database does not exist');
    } else if (error.code === 'ENOTFOUND') {
      console.log('💡 Host not found - possible causes:');
      console.log('   - Wrong IP address');
      console.log('   - DNS resolution issues');
      console.log('   - Network connectivity problems');
    } else if (error.code === 'ETIMEDOUT') {
      console.log('💡 Connection timeout - possible causes:');
      console.log('   - Server is too slow to respond');
      console.log('   - Network latency issues');
      console.log('   - Firewall dropping packets');
    }
    return false;
  }
}

async function main() {
  // Test different common MySQL ports
  const commonPorts = [3306, 3307, 3308, 33060];

  for (const port of commonPorts) {
    const isReachable = await testNetworkConnectivity('**************', port);
    if (isReachable) {
      console.log(`🎯 Found open port: ${port}`);
      break;
    }
  }

  console.log(''); // Empty line for readability
  await testMySQLConnection();
}

main().catch(console.error);

"use client"

import React, { useState, useRef, useEffect } from 'react'
import { Button } from './ui/button'
import {
  FileText,
  Calendar,
  Printer,
  Download,
  CheckCircle,
  Search,
  User
} from 'lucide-react'
import { useQuery } from '@tanstack/react-query'
import { calculatePromotionStatus, getPromotionPolicyDescription, getPromotionStatusText, PromotionPolicyData, DEFAULT_PROMOTION_POLICY } from '../lib/promotion-policy'
import { useAppSettings } from '../contexts/app-settings-context'

export function ReportCard() {
  const { settings } = useAppSettings()
  const [step, setStep] = useState<'form' | 'preview'>('form')
  const [isGenerating, setIsGenerating] = useState(false)
  const [isGenerated, setIsGenerated] = useState(false)
  const printRef = useRef<HTMLDivElement>(null)

  // Get current academic year (e.g., 2023-2024)
  const getCurrentAcademicYear = () => {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth();

    // If we're in the second half of the year (July-December), academic year is current-next
    // Otherwise it's previous-current
    if (month >= 6) { // July or later
      return `${year}-${year + 1}`;
    } else {
      return `${year - 1}-${year}`;
    }
  };

  // Form state
  const [formData, setFormData] = useState({
    studentId: '',
    studentName: '',
    class: '',
    academicYear: getCurrentAcademicYear(),
    semester: 'First Semester'
  })

  // State for report mode (student or class)
  const [reportMode, setReportMode] = useState<'student' | 'class'>('student');

  // State for student report
  const [studentSid, setStudentSid] = useState<string>('');
  const [selectedStudent, setSelectedStudent] = useState<any>(null);
  const [reportData, setReportData] = useState<any>(null);

  // State for class report
  const [classReportData, setClassReportData] = useState<any>(null);

  // State for promotion policy
  const [promotionPolicy, setPromotionPolicy] = useState<PromotionPolicyData>(DEFAULT_PROMOTION_POLICY);

  // Fetch promotion policy
  const { data: fetchedPromotionPolicy } = useQuery({
    queryKey: ['promotionPolicy'],
    queryFn: async () => {
      try {
        const res = await fetch('/api/promotion-policy')
        if (!res.ok) {
          console.warn('Failed to fetch promotion policy, using default')
          return DEFAULT_PROMOTION_POLICY
        }
        const policy = await res.json()
        return policy || DEFAULT_PROMOTION_POLICY
      } catch (error) {
        console.error('Error fetching promotion policy:', error)
        return DEFAULT_PROMOTION_POLICY
      }
    }
  })

  // Update promotion policy when fetched
  useEffect(() => {
    if (fetchedPromotionPolicy) {
      setPromotionPolicy(fetchedPromotionPolicy)
    }
  }, [fetchedPromotionPolicy])

  // Fetch classes from the database
  const { data: classes, isLoading: isLoadingClasses } = useQuery({
    queryKey: ['classes'],
    queryFn: async () => {
      // Fetch classes without requiring a year parameter
      const res = await fetch('/api/classes')
      if (!res.ok) throw new Error('Failed to fetch classes')
      const data = await res.json()
      return Array.isArray(data) ? data : []
    }
  })

  // Fetch students from the database
  const { data: students, isLoading: isLoadingStudents } = useQuery({
    queryKey: ['students', formData.class],
    queryFn: async () => {
      // If class is selected, fetch students for that class
      const url = formData.class
        ? `/api/students?class=${formData.class}`
        : '/api/students'

      const res = await fetch(url)
      if (!res.ok) throw new Error('Failed to fetch students')
      return res.json()
    },
    enabled: !!classes // Only fetch students when classes are loaded
  });

  // Fetch report card data
  const {
    data: fetchedReportData,
    isLoading: isLoadingReport,
    refetch: refetchReport,
    isError: isReportError,
    error: reportError
  } = useQuery({
    queryKey: ['reportCard', reportMode, studentSid, formData.class, formData.academicYear, formData.semester],
    queryFn: async () => {
      // Build query parameters
      const params = new URLSearchParams();
      params.append('reportType', reportMode);
      params.append('academicYear', formData.academicYear);
      params.append('semester', formData.semester);

      if (reportMode === 'student' && studentSid) {
        params.append('studentId', studentSid);
        console.log('Sending studentId:', studentSid);
      } else if (reportMode === 'class' && formData.class) {
        params.append('className', formData.class);
        console.log('Sending className:', formData.class);
      } else {
        throw new Error('Missing required parameters');
      }

      const url = `/api/reportcard?${params.toString()}`;
      console.log('Fetching report card from URL:', url);

      const res = await fetch(url);

      if (!res.ok) {
        const errorData = await res.json().catch(() => ({}));
        console.error('Report card API error:', errorData);

        // Create a custom error object with the response data
        const error = new Error(errorData.error || 'Failed to fetch report card data');
        (error as any).response = { data: errorData };
        throw error;
      }

      return res.json();
    },
    enabled: false // Don't fetch automatically, we'll trigger it manually
  });

  // Update report data when fetched
  useEffect(() => {
    if (fetchedReportData) {
      if (reportMode === 'student') {
        setReportData(fetchedReportData);
      } else {
        setClassReportData(fetchedReportData);
      }
      setIsGenerated(true);
      setStep('preview');
    }
  }, [fetchedReportData, reportMode]);

  // Extract class names from the fetched classes
  const classesList = Array.isArray(classes)
    ? classes.map((cls: any) => cls.name || cls)
    : []

  // Update selected student when studentSid changes
  useEffect(() => {
    if (studentSid && students) {
      const student = students.find((s: any) => s.id === studentSid);
      if (student) {
        setSelectedStudent(student);
        setFormData(prev => ({
          ...prev,
          studentName: student.name,
          class: student.className
        }));
      }
    }
  }, [studentSid, students]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData({
      ...formData,
      [name]: value
    })
  }

  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const handleGenerateReportCard = async () => {
    setIsGenerating(true)
    setErrorMessage(null)

    try {
      // Validate required fields based on report mode
      if (reportMode === 'student') {
        if (!studentSid || !formData.studentName) {
          throw new Error('Student information is required')
        }
      } else if (reportMode === 'class') {
        if (!formData.class) {
          throw new Error('Class selection is required')
        }
      }

      // Trigger the report data fetch
      await refetchReport()
    } catch (error) {
      console.error('Error generating report card:', error)

      // Extract error message from the API response if available
      let message = 'Failed to generate report card';

      if (error.response && error.response.data && error.response.data.error) {
        message = error.response.data.error;
      } else if (error.message) {
        message = error.message;
      }

      // Set error message to display in the UI
      setErrorMessage(message);
    } finally {
      setIsGenerating(false)
    }
  }

  // Function to generate HTML for a student report card
  const generateStudentReportCardHtml = (studentReport: any, academicYear: string, semester: string) => {
    // Calculate promotion status using the dynamic promotion policy
    const isPromoted = calculatePromotionStatus(studentReport.subjects, semester, promotionPolicy);

    // Get promotion status text and description
    const statusInfo = getPromotionStatusText(isPromoted);
    const policyDescription = getPromotionPolicyDescription(promotionPolicy);

    // Helper function to get grade from marks
    const getGrade = (marks: number) => {
      if (marks >= 90) return 'A+';
      if (marks >= 80) return 'A';
      if (marks >= 70) return 'B+';
      if (marks >= 60) return 'B';
      if (marks >= 50) return 'C';
      return 'F';
    };

    return `
      <div class="report-card">
        <!-- Header Section -->
        <div class="header">
          <div class="header-content">
            <img src="${settings.schoolLogo || '/images/logo.png'}" alt="School Logo" class="logo">
            <div class="school-info">
              <div class="school-name">${settings.schoolName.toUpperCase()}</div>
              <div class="school-address">Dire Dawa, Ethiopia</div>
              <div class="report-title">STUDENT REPORT CARD</div>
              <div class="academic-year">${semester} - ${academicYear}</div>
            </div>
            <div class="stamp-area">
              <div class="stamp-placeholder">
                <div class="stamp-text">SCHOOL<br>OFFICIAL<br>SEAL</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Student Information Section -->
        <div class="student-section">
          <div class="student-info">
            <div class="info-row">
              <div class="info-item">
                <span class="label">Student Name:</span>
                <span class="value">${studentReport.student.name}</span>
              </div>
              <div class="info-item">
                <span class="label">Student ID:</span>
                <span class="value">${studentReport.student.sid || '-'}</span>
              </div>
            </div>
            <div class="info-row">
              <div class="info-item">
                <span class="label">Class:</span>
                <span class="value">${studentReport.student.className}</span>
              </div>
              <div class="info-item">
                <span class="label">Gender:</span>
                <span class="value">${studentReport.student.gender || '-'}</span>
              </div>
            </div>
            <div class="info-row">
              <div class="info-item">
                <span class="label">Father's Name:</span>
                <span class="value">${studentReport.student.fatherName || '-'}</span>
              </div>
              <div class="info-item">
                <span class="label">Age:</span>
                <span class="value">${studentReport.student.age || '-'}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Academic Performance Section -->
        <div class="academic-section">
          <div class="section-title">ACADEMIC PERFORMANCE</div>
          <table class="performance-table">
            <thead>
              <tr>
                <th class="subject-col">Subject</th>
                ${semester === 'Annual' ? `
                  <th class="score-col">1st Sem</th>
                  <th class="score-col">2nd Sem</th>
                  <th class="score-col">Average</th>
                ` : `
                  <th class="score-col">Score</th>
                `}
              </tr>
            </thead>
            <tbody>
              ${studentReport.subjects.map((subject: any) => {
                return `
                  <tr>
                    <td class="subject-col">${subject.name}</td>
                    ${semester === 'Annual' ? `
                      <td class="score-col">${subject['First Semester'] || '-'}</td>
                      <td class="score-col">${subject['Second Semester'] || '-'}</td>
                      <td class="score-col">${subject.average || '-'}</td>
                    ` : `
                      <td class="score-col">${subject[semester] || '-'}</td>
                    `}
                  </tr>
                `;
              }).join('')}

              ${studentReport.summary ? `
                <tr class="summary-row">
                  <td class="subject-col"><strong>TOTAL</strong></td>
                  ${semester === 'Annual' ? `
                    <td class="score-col"><strong>${studentReport.subjects.reduce((sum: number, subject: any) => sum + (subject['First Semester'] || 0), 0)}</strong></td>
                    <td class="score-col"><strong>${studentReport.subjects.reduce((sum: number, subject: any) => sum + (subject['Second Semester'] || 0), 0)}</strong></td>
                    <td class="score-col"><strong>${studentReport.summary.totalMarks}</strong></td>
                  ` : `
                    <td class="score-col"><strong>${studentReport.summary.totalMarks}</strong></td>
                  `}
                </tr>
                <tr class="summary-row">
                  <td class="subject-col"><strong>AVERAGE</strong></td>
                  ${semester === 'Annual' ? `
                    <td class="score-col"><strong>${studentReport.subjects.length > 0 ?
                      Math.round(studentReport.subjects.reduce((sum: number, subject: any) => sum + (subject['First Semester'] || 0), 0) /
                      studentReport.subjects.filter((s: any) => s['First Semester']).length) : '-'}</strong></td>
                    <td class="score-col"><strong>${studentReport.subjects.length > 0 ?
                      Math.round(studentReport.subjects.reduce((sum: number, subject: any) => sum + (subject['Second Semester'] || 0), 0) /
                      studentReport.subjects.filter((s: any) => s['Second Semester']).length) : '-'}</strong></td>
                    <td class="score-col"><strong>${studentReport.summary.average}</strong></td>
                  ` : `
                    <td class="score-col"><strong>${studentReport.summary.average}</strong></td>
                  `}
                </tr>
                <tr class="summary-row">
                  <td class="subject-col"><strong>CLASS RANK</strong></td>
                  ${semester === 'Annual' ? `
                    <td class="score-col"><strong>${studentReport.summary.firstSemesterRank || '-'}</strong></td>
                    <td class="score-col"><strong>${studentReport.summary.secondSemesterRank || '-'}</strong></td>
                    <td class="score-col"><strong>${studentReport.summary.rank || 1}</strong></td>
                  ` : `
                    <td class="score-col"><strong>${studentReport.summary.rank || 1}</strong></td>
                  `}
                </tr>
              ` : ''}
            </tbody>
          </table>
        </div>

        <!-- Two Column Layout for Attendance, Grading Scale, and Status -->
        <div class="bottom-section">
          <div class="left-column">
            <!-- Attendance Section -->
            ${studentReport.attendance ? `
              <div class="attendance-section">
                <div class="section-title">ATTENDANCE</div>
                <div class="attendance-grid">
                  <div class="attendance-item">
                    <span class="att-label">Present:</span>
                    <span class="att-value">${studentReport.attendance.present} days</span>
                  </div>
                  <div class="attendance-item">
                    <span class="att-label">Absent:</span>
                    <span class="att-value">${studentReport.attendance.absent || 0} days</span>
                  </div>
                  <div class="attendance-item">
                    <span class="att-label">Total Days:</span>
                    <span class="att-value">${studentReport.attendance.totalDays} days</span>
                  </div>
                  <div class="attendance-item">
                    <span class="att-label">Percentage:</span>
                    <span class="att-value">${studentReport.attendance.percentage}%</span>
                  </div>
                </div>
              </div>
            ` : ''}

            <!-- Promotion Status -->
            <div class="status-section">
              <div class="section-title">PROMOTION STATUS</div>
              <div class="status-box ${isPromoted ? 'promoted' : 'detained'}">
                <div class="status-text">${statusInfo.status}</div>
                <div class="status-note">${statusInfo.description}</div>
              </div>
              <div class="policy-info" style="margin-top: 8px; font-size: 11px; color: #6b7280; text-align: center;">
                Policy: ${policyDescription}
              </div>
            </div>
          </div>

          <div class="right-column">
            <!-- Grading Scale -->
            <div class="grading-section">
              <div class="section-title">GRADING SCALE</div>
              <table class="grading-table">
                <thead>
                  <tr>
                    <th>Grade</th>
                    <th>Marks</th>
                    <th>Performance</th>
                  </tr>
                </thead>
                <tbody>
                  <tr><td>A+</td><td>90-100</td><td>Excellent</td></tr>
                  <tr><td>A</td><td>80-89</td><td>Very Good</td></tr>
                  <tr><td>B+</td><td>70-79</td><td>Good</td></tr>
                  <tr><td>B</td><td>60-69</td><td>Satisfactory</td></tr>
                  <tr><td>C</td><td>50-59</td><td>Fair</td></tr>
                  <tr><td>F</td><td>0-49</td><td>Fail</td></tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- Signatures Section -->
        <div class="signatures-section">
          <div class="signature-box">
            <div class="signature-title">Home Room Teacher</div>
            <div class="signature-line"></div>
            <div class="signature-label">Signature & Date</div>
          </div>

          <div class="signature-box">
            <div class="signature-title">Principal</div>
            <div class="signature-line"></div>
            <div class="signature-label">Signature & Date</div>
          </div>
        </div>

        <!-- Footer -->
        <div class="footer">
          <div class="footer-text">This report card is valid only with the official school seal and authorized signatures.</div>
        </div>
      </div>
    `;
  };

  return (
    <div className="space-y-8 p-6">
      {/* Enhanced Header - Only show if not generated */}
      {!isGenerated && (
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Report Card Generator</h2>
          <p className="text-gray-600">Select options below to generate comprehensive student performance reports</p>
        </div>
      )}

      {isGenerated && (
        <div className="flex items-center justify-between bg-gradient-to-r from-rose-50 to-pink-50 p-4 rounded-xl border border-rose-200">
          <div>
            <h2 className="text-xl font-bold text-gray-900">Report Generated Successfully</h2>
            <p className="text-gray-600 mt-1">Your report card has been generated and is ready for download</p>
          </div>
          <Button
            variant="outline"
            onClick={() => setStep('form')}
            className="flex items-center gap-2 hover:bg-white border-rose-300 text-rose-700 hover:text-rose-800"
          >
            <Search className="h-4 w-4" />
            Generate New
          </Button>
        </div>
      )}

      {step === 'form' ? (
        <div className="bg-white/90 backdrop-blur-sm rounded-2xl border-0 shadow-xl overflow-hidden">
          <div className="p-8 border-b border-rose-100 bg-gradient-to-r from-rose-50 to-pink-50 text-center">
            <div className="flex items-center justify-center mb-4">
              <div className="h-12 w-12 rounded-full bg-rose-500/20 flex items-center justify-center">
                <FileText className="h-6 w-6 text-rose-600" />
              </div>
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Generate Report Card</h2>
            <p className="text-gray-600">Fill in the details below to generate comprehensive student performance reports</p>
          </div>

          <div className="p-8">
            <div className="w-full">
              {/* Enhanced Mode Selector */}
              <div className="flex justify-center mb-8">
                <div className="bg-gradient-to-r from-gray-100 to-gray-200 p-1 rounded-xl shadow-inner">
                  <div
                    className={`flex items-center gap-3 px-8 py-3 cursor-pointer transition-all duration-300 ${
                      reportMode === 'student'
                        ? 'bg-white shadow-lg text-rose-600 font-semibold'
                        : 'bg-transparent text-gray-600 hover:text-gray-800'
                    } rounded-lg`}
                    onClick={() => setReportMode('student')}
                  >
                    <User className="h-5 w-5" />
                    Individual Student
                  </div>
                  <div
                    className={`flex items-center gap-3 px-8 py-3 cursor-pointer transition-all duration-300 ${
                      reportMode === 'class'
                        ? 'bg-white shadow-lg text-rose-600 font-semibold'
                        : 'bg-transparent text-gray-600 hover:text-gray-800'
                    } rounded-lg`}
                    onClick={() => setReportMode('class')}
                  >
                    <Calendar className="h-5 w-5" />
                    Entire Class
                  </div>
                </div>
              </div>

              {reportMode === 'student' ? (
                <div className="space-y-8">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-6">
                      <div className="bg-gradient-to-r from-rose-50 to-pink-50 p-6 rounded-xl border border-rose-100">
                        <label className="block text-sm font-semibold text-gray-800 mb-3" htmlFor="class">
                          <Calendar className="inline h-4 w-4 mr-2 text-rose-600" />
                          Select Class
                        </label>
                        <select
                          id="studentClass"
                          value={formData.class}
                          onChange={(e) => {
                            setFormData(prev => ({
                              ...prev,
                              class: e.target.value,
                              studentId: '',
                              studentName: ''
                            }));
                            setStudentSid('');
                          }}
                          className="h-12 w-full rounded-lg border-2 border-rose-200 text-sm focus:outline-none focus:ring-2 focus:ring-rose-500 focus:border-rose-500 bg-white shadow-sm transition-all duration-200"
                          required
                          disabled={isLoadingClasses}
                        >
                          {isLoadingClasses ? (
                            <option value="">Loading classes...</option>
                          ) : (
                            <>
                              <option value="">Select a class</option>
                              {classesList.map((cls: string) => (
                                <option key={cls} value={cls}>{cls}</option>
                              ))}
                            </>
                          )}
                        </select>
                      </div>

                      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-xl border border-blue-100">
                        <label className="block text-sm font-semibold text-gray-800 mb-3" htmlFor="studentId">
                          <User className="inline h-4 w-4 mr-2 text-blue-600" />
                          Select Student
                        </label>
                        <select
                          id="studentId"
                          value={studentSid}
                          onChange={(e) => {
                            const sid = e.target.value;
                            setStudentSid(sid);

                            // Find the selected student to get their name
                            const student = students?.find((s: any) => s.id === sid);
                            if (student) {
                              setFormData(prev => ({
                                ...prev,
                                studentId: student.sid || '',
                                studentName: student.name || ''
                              }));
                            }
                          }}
                          className="h-12 w-full rounded-lg border-2 border-blue-200 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white shadow-sm transition-all duration-200"
                          required
                          disabled={!formData.class || isLoadingStudents}
                        >
                          <option value="">Select a student</option>
                          {students && students.map((student: any) => (
                            <option key={student.id} value={student.id}>
                              {student.sid} - {student.name}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>
                    <div className="space-y-6">
                      <div className="bg-gradient-to-r from-purple-50 to-violet-50 p-6 rounded-xl border border-purple-100">
                        <label className="block text-sm font-semibold text-gray-800 mb-3" htmlFor="academicYear">
                          <Calendar className="inline h-4 w-4 mr-2 text-purple-600" />
                          Academic Year
                        </label>
                        <select
                          id="academicYear"
                          name="academicYear"
                          value={formData.academicYear}
                          onChange={handleInputChange}
                          className="h-12 w-full rounded-lg border-2 border-purple-200 text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 bg-white shadow-sm transition-all duration-200"
                          required
                        >
                          <option value="2025-2026">2025-2026</option>
                          <option value="2024-2025">2024-2025</option>
                          <option value="2023-2024">2023-2024</option>
                        </select>
                      </div>

                      <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-6 rounded-xl border border-green-100">
                        <label className="block text-sm font-semibold text-gray-800 mb-3" htmlFor="semester">
                          <FileText className="inline h-4 w-4 mr-2 text-green-600" />
                          Semester
                        </label>
                        <select
                          id="semester"
                          name="semester"
                          value={formData.semester}
                          onChange={handleInputChange}
                          className="h-12 w-full rounded-lg border-2 border-green-200 text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white shadow-sm transition-all duration-200"
                          required
                        >
                          <option value="First Semester">First Semester</option>
                          <option value="Second Semester">Second Semester</option>
                          <option value="Annual">Annual</option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1" htmlFor="classSelection">Class</label>
                      <select
                        id="classSelection"
                        value={formData.class}
                        onChange={(e) => {
                          setFormData(prev => ({
                            ...prev,
                            class: e.target.value
                          }));
                        }}
                        className="h-9 w-full rounded-md border border-gray-300 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                        required
                        disabled={isLoadingClasses}
                      >
                        {isLoadingClasses ? (
                          <option value="">Loading classes...</option>
                        ) : (
                          <>
                            <option value="">Select a class</option>
                            {classesList.map((cls: string) => (
                              <option key={cls} value={cls}>{cls}</option>
                            ))}
                          </>
                        )}
                      </select>
                    </div>

                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1" htmlFor="academicYear">Academic Year</label>
                      <select
                        id="academicYear"
                        name="academicYear"
                        value={formData.academicYear}
                        onChange={handleInputChange}
                        className="h-9 w-full rounded-md border border-gray-300 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                        required
                      >
                        <option value="2025-2026">2025-2026</option>
                        <option value="2024-2025">2024-2025</option>
                        <option value="2023-2024">2023-2024</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1" htmlFor="semester">Semester</label>
                      <select
                        id="semester"
                        name="semester"
                        value={formData.semester}
                        onChange={handleInputChange}
                        className="h-9 w-full rounded-md border border-gray-300 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                        required
                      >
                        <option value="First Semester">First Semester</option>
                        <option value="Annual">Annual</option>
                      </select>
                    </div>
                  </div>
                </div>
              )}

              {errorMessage && (
                <div className="mt-4 p-3 bg-red-50 border border-red-200 text-red-700 rounded-md flex items-center gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                  <span>{errorMessage}</span>
                </div>
              )}

              <div className="mt-10 flex justify-center">
                <Button
                  className="w-full md:w-2/3 bg-gradient-to-r from-rose-600 via-pink-600 to-fuchsia-600 hover:from-rose-700 hover:via-pink-700 hover:to-fuchsia-700 h-14 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 text-lg font-semibold"
                  onClick={handleGenerateReportCard}
                  disabled={
                    (reportMode === 'student' && (!formData.studentId || !formData.studentName)) ||
                    (reportMode === 'class' && !formData.class) ||
                    !formData.academicYear ||
                    isGenerating
                  }
                >
                  {isGenerating ? (
                    <div className="flex items-center justify-center">
                      <div className="animate-spin mr-2">
                        <Calendar className="h-5 w-5" />
                      </div>
                      <span>Generating...</span>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center">
                      {isGenerated ? (
                        <>
                          <CheckCircle className="h-5 w-5 mr-2" />
                          <span>View {reportMode === 'student' ? 'Report Card' : 'Class Reports'}</span>
                        </>
                      ) : (
                        <>
                          <FileText className="h-5 w-5 mr-2" />
                          <span>{reportMode === 'student' ? 'Generate Report Card' : 'Generate Class Reports'}</span>
                        </>
                      )}
                    </div>
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="bg-white rounded-xl border border-gray-200 shadow-lg overflow-hidden">
          <div className="p-6 border-b border-gray-100 bg-gradient-to-r from-gray-50 to-gray-100/50 flex justify-between items-center">
            <div>
              <h2 className="text-lg font-semibold text-gray-800">
                {reportMode === 'student' ? 'Student Report Card' : 'Class Report Cards'}
              </h2>
              <p className="text-sm text-gray-600 mt-1">{formData.academicYear} • {formData.semester}</p>
            </div>
            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={() => setStep('form')}
                className="flex items-center gap-2 hover:bg-gray-50"
              >
                <Search className="h-4 w-4" />
                New Search
              </Button>
            </div>
          </div>

          <div ref={printRef} className="p-8">
            {reportMode === 'student' && reportData ? (
              <>
                {/* Student Information */}
                <div className="grid grid-cols-2 gap-6 mb-8 p-6 bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl border border-gray-200">
                  <div>
                    <label className="text-sm text-gray-600">Name</label>
                    <p className="text-lg font-medium text-gray-900 mt-1">{reportData.student.name}</p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-600">Class</label>
                    <p className="text-lg font-medium text-gray-900 mt-1">{reportData.student.className}</p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-600">Father Name</label>
                    <p className="text-lg font-medium text-gray-900 mt-1">{reportData.student.fatherName || '-'}</p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-600">GrandFather Name</label>
                    <p className="text-lg font-medium text-gray-900 mt-1">{reportData.student.gfName || '-'}</p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-600">Gender</label>
                    <p className="text-lg font-medium text-gray-900 mt-1">{reportData.student.gender || '-'}</p>
                  </div>
                  {reportData.attendance && (
                    <div>
                      <label className="text-sm text-gray-600">Attendance</label>
                      <p className="text-lg font-medium text-gray-900 mt-1">
                        Present: {reportData.attendance.present} / {reportData.attendance.totalDays} days ({reportData.attendance.percentage}%)
                      </p>
                    </div>
                  )}
                </div>

                {/* Academic Performance */}
                <div className="mb-8">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Academic Performance</h3>
                  <div className="bg-white rounded-xl border border-gray-200 overflow-hidden">
                    <table className="w-full border-collapse">
                      <thead>
                        <tr className="bg-gradient-to-r from-gray-50 to-gray-100">
                          <th className="py-3 px-4 text-left font-semibold text-gray-900 border-b border-gray-200">Subject</th>
                          {formData.semester === 'Annual' ? (
                            <>
                              <th className="py-3 px-4 text-center font-semibold text-gray-900 border-b border-gray-200">First Semester</th>
                              <th className="py-3 px-4 text-center font-semibold text-gray-900 border-b border-gray-200">Second Semester</th>
                              <th className="py-3 px-4 text-center font-semibold text-gray-900 border-b border-gray-200">Average</th>
                            </>
                          ) : (
                            <th className="py-3 px-4 text-center font-semibold text-gray-900 border-b border-gray-200">{formData.semester}</th>
                          )}
                        </tr>
                      </thead>
                      <tbody>
                        {reportData.subjects && reportData.subjects.map((subject: any, index: number) => (
                          <tr key={index} className="hover:bg-gray-50">
                            <td className="py-3 px-4 border-b border-gray-200">{subject.name}</td>
                            {formData.semester === 'Annual' ? (
                              <>
                                <td className="py-3 px-4 text-center border-b border-gray-200">{subject['First Semester'] || '-'}</td>
                                <td className="py-3 px-4 text-center border-b border-gray-200">{subject['Second Semester'] || '-'}</td>
                                <td className="py-3 px-4 text-center border-b border-gray-200">{subject.average || '-'}</td>
                              </>
                            ) : (
                              <td className="py-3 px-4 text-center border-b border-gray-200">{subject[formData.semester] || '-'}</td>
                            )}
                          </tr>
                        ))}
                        {reportData.summary && (
                          <>
                            <tr className="bg-gray-50 font-medium">
                              <td className="py-3 px-4 border-b border-gray-200">Total</td>
                              {formData.semester === 'Annual' ? (
                                <>
                                  <td className="py-3 px-4 text-center border-b border-gray-200">
                                    {reportData.subjects.reduce((sum: number, subject: any) => sum + (subject['First Semester'] || 0), 0)}
                                  </td>
                                  <td className="py-3 px-4 text-center border-b border-gray-200">
                                    {reportData.subjects.reduce((sum: number, subject: any) => sum + (subject['Second Semester'] || 0), 0)}
                                  </td>
                                  <td className="py-3 px-4 text-center border-b border-gray-200">{reportData.summary.totalMarks}</td>
                                </>
                              ) : (
                                <td className="py-3 px-4 text-center border-b border-gray-200">{reportData.summary.totalMarks}</td>
                              )}
                            </tr>
                            <tr className="bg-gray-50 font-medium">
                              <td className="py-3 px-4 border-b border-gray-200">Average</td>
                              {formData.semester === 'Annual' ? (
                                <>
                                  <td className="py-3 px-4 text-center border-b border-gray-200">
                                    {reportData.subjects.length > 0 ?
                                      Math.round(reportData.subjects.reduce((sum: number, subject: any) => sum + (subject['First Semester'] || 0), 0) /
                                      reportData.subjects.filter((s: any) => s['First Semester']).length) : '-'}
                                  </td>
                                  <td className="py-3 px-4 text-center border-b border-gray-200">
                                    {reportData.subjects.length > 0 ?
                                      Math.round(reportData.subjects.reduce((sum: number, subject: any) => sum + (subject['Second Semester'] || 0), 0) /
                                      reportData.subjects.filter((s: any) => s['Second Semester']).length) : '-'}
                                  </td>
                                  <td className="py-3 px-4 text-center border-b border-gray-200">{reportData.summary.average}</td>
                                </>
                              ) : (
                                <td className="py-3 px-4 text-center border-b border-gray-200">{reportData.summary.average}</td>
                              )}
                            </tr>
                            <tr className="bg-gray-50 font-medium">
                              <td className="py-3 px-4 border-b border-gray-200">Rank</td>
                              {formData.semester === 'Annual' ? (
                                <>
                                  <td className="py-3 px-4 text-center border-b border-gray-200">{reportData.summary?.firstSemesterRank || '-'}</td>
                                  <td className="py-3 px-4 text-center border-b border-gray-200">{reportData.summary?.secondSemesterRank || '-'}</td>
                                  <td className="py-3 px-4 text-center border-b border-gray-200">{reportData.summary.rank || 1}</td>
                                </>
                              ) : (
                                <td className="py-3 px-4 text-center border-b border-gray-200">{reportData.summary.rank || 1}</td>
                              )}
                            </tr>
                          </>
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>
              </>
            ) : reportMode === 'class' && classReportData ? (
              <div>
                <div className="mb-8 p-6 bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl border border-gray-200">
                  <div className="flex justify-between items-center mb-4">
                    <div>
                      <label className="text-sm text-gray-600">Class</label>
                      <p className="text-lg font-medium text-gray-900 mt-1">{classReportData.className}</p>
                    </div>
                    <div>
                      <label className="text-sm text-gray-600">Academic Year</label>
                      <p className="text-lg font-medium text-gray-900 mt-1">{classReportData.academicYear}</p>
                    </div>
                    <div>
                      <label className="text-sm text-gray-600">Semester</label>
                      <p className="text-lg font-medium text-gray-900 mt-1">{classReportData.semester}</p>
                    </div>
                    <div>
                      <label className="text-sm text-gray-600">Total Students</label>
                      <p className="text-lg font-medium text-gray-900 mt-1">{classReportData.students?.length || 0}</p>
                    </div>
                  </div>

                  <div className="mt-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Class Performance Summary</h3>
                    <div className="bg-white rounded-xl border border-gray-200 overflow-hidden">
                      <table className="w-full border-collapse">
                        <thead>
                          <tr className="bg-gradient-to-r from-gray-50 to-gray-100">
                            <th className="py-3 px-4 text-left font-semibold text-gray-900 border-b border-gray-200">Rank</th>
                            <th className="py-3 px-4 text-left font-semibold text-gray-900 border-b border-gray-200">Student ID</th>
                            <th className="py-3 px-4 text-left font-semibold text-gray-900 border-b border-gray-200">Student Name</th>
                            <th className="py-3 px-4 text-center font-semibold text-gray-900 border-b border-gray-200">Average</th>
                            <th className="py-3 px-4 text-center font-semibold text-gray-900 border-b border-gray-200">Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          {classReportData.students && classReportData.students.map((student: any, index: number) => (
                            <tr key={index} className="hover:bg-gray-50">
                              <td className="py-3 px-4 border-b border-gray-200">{student.summary?.rank || index + 1}</td>
                              <td className="py-3 px-4 border-b border-gray-200">{student.student?.sid || '-'}</td>
                              <td className="py-3 px-4 border-b border-gray-200">{student.student?.name || '-'}</td>
                              <td className="py-3 px-4 text-center border-b border-gray-200">{student.summary?.average || '-'}</td>
                              <td className="py-3 px-4 text-center border-b border-gray-200">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="mr-2"
                                  onClick={() => {
                                    // Set up for individual student print
                                    const studentReport = {
                                      student: student.student,
                                      subjects: student.subjects,
                                      summary: student.summary,
                                      attendance: student.attendance,
                                      academicYear: classReportData.academicYear,
                                      semester: classReportData.semester
                                    };

                                    // Create a new window for printing
                                    const printWindow = window.open('', '_blank');
                                    if (printWindow) {
                                      const htmlContent = `
                                        <!DOCTYPE html>
                                        <html>
                                          <head>
                                            <title>Report Card - ${student.student.name}</title>
                                            <style>
                                              @page {
                                                size: A4 portrait;
                                                margin: 15mm;
                                              }
                                              body {
                                                font-family: 'Times New Roman', serif;
                                                margin: 0;
                                                padding: 0;
                                                font-size: 11px;
                                                line-height: 1.3;
                                                -webkit-print-color-adjust: exact !important;
                                                print-color-adjust: exact !important;
                                                color-adjust: exact !important;
                                              }
                                              .report-card {
                                                width: 100%;
                                                max-width: 210mm;
                                                margin: 0 auto;
                                                padding: 0;
                                                box-sizing: border-box;
                                                background: white;
                                              }

                                              /* Header Styles */
                                              .header {
                                                border: 2px solid #000;
                                                margin-bottom: 12px;
                                                padding: 12px;
                                              }
                                              .header-content {
                                                display: flex;
                                                align-items: center;
                                                justify-content: space-between;
                                              }
                                              .logo {
                                                width: 60px;
                                                height: 60px;
                                                flex-shrink: 0;
                                              }
                                              .school-info {
                                                flex: 1;
                                                text-align: center;
                                                margin: 0 15px;
                                              }
                                              .school-name {
                                                font-size: 16px;
                                                font-weight: bold;
                                                margin: 0 0 4px 0;
                                                text-transform: uppercase;
                                                line-height: 1.2;
                                              }
                                              .school-address {
                                                font-size: 12px;
                                                margin: 0 0 6px 0;
                                                color: #666;
                                              }
                                              .report-title {
                                                font-size: 15px;
                                                font-weight: bold;
                                                margin: 4px 0;
                                                text-decoration: underline;
                                              }
                                              .academic-year {
                                                font-size: 13px;
                                                margin: 4px 0 0 0;
                                                font-weight: bold;
                                              }
                                              .stamp-area {
                                                width: 60px;
                                                height: 60px;
                                                flex-shrink: 0;
                                              }
                                              .stamp-placeholder {
                                                width: 100%;
                                                height: 100%;
                                                border: 2px dashed #999;
                                                border-radius: 50%;
                                                display: flex;
                                                align-items: center;
                                                justify-content: center;
                                                text-align: center;
                                              }
                                              .stamp-text {
                                                font-size: 9px;
                                                color: #999;
                                                line-height: 1.1;
                                                font-weight: bold;
                                              }

                                              /* Student Info Styles */
                                              .student-section {
                                                margin-bottom: 12px;
                                              }
                                              .student-info {
                                                border: 1px solid #000;
                                                padding: 10px;
                                              }
                                              .info-row {
                                                display: flex;
                                                margin-bottom: 6px;
                                              }
                                              .info-row:last-child {
                                                margin-bottom: 0;
                                              }
                                              .info-item {
                                                flex: 1;
                                                display: flex;
                                              }
                                              .label {
                                                font-weight: bold;
                                                min-width: 90px;
                                                margin-right: 8px;
                                                font-size: 12px;
                                              }
                                              .value {
                                                border-bottom: 1px dotted #666;
                                                flex: 1;
                                                min-height: 16px;
                                                font-size: 12px;
                                              }

                                              /* Academic Performance Styles */
                                              .academic-section {
                                                margin-bottom: 12px;
                                              }
                                              .section-title {
                                                font-size: 14px;
                                                font-weight: bold;
                                                text-align: center;
                                                margin: 0 0 6px 0;
                                                text-decoration: underline;
                                              }
                                              .performance-table {
                                                width: 100%;
                                                border-collapse: collapse;
                                                border: 1px solid #000;
                                              }
                                              .performance-table th,
                                              .performance-table td {
                                                border: 1px solid #000;
                                                padding: 5px 6px;
                                                text-align: center;
                                                font-size: 11px;
                                              }
                                              .performance-table th {
                                                background-color: #f0f0f0;
                                                font-weight: bold;
                                              }
                                              .subject-col {
                                                text-align: left !important;
                                                width: 40%;
                                              }
                                              .score-col {
                                                width: 20%;
                                              }
                                              .summary-row {
                                                background-color: #f8f8f8;
                                              }
                                              .summary-row td {
                                                font-weight: bold;
                                              }

                                              /* Bottom Section Styles */
                                              .bottom-section {
                                                display: flex;
                                                gap: 12px;
                                                margin-bottom: 12px;
                                              }
                                              .left-column,
                                              .right-column {
                                                flex: 1;
                                              }

                                              /* Attendance Styles */
                                              .attendance-section {
                                                border: 1px solid #000;
                                                padding: 6px;
                                                margin-bottom: 8px;
                                              }
                                              .attendance-grid {
                                                display: grid;
                                                grid-template-columns: 1fr 1fr;
                                                gap: 4px;
                                              }
                                              .attendance-item {
                                                display: flex;
                                                justify-content: space-between;
                                                font-size: 10px;
                                              }
                                              .att-label {
                                                font-weight: bold;
                                              }

                                              /* Status Styles */
                                              .status-section {
                                                border: 1px solid #000;
                                                padding: 6px;
                                              }
                                              .status-box {
                                                text-align: center;
                                                padding: 6px;
                                                border-radius: 4px;
                                              }
                                              .status-box.promoted {
                                                background-color: #d4edda;
                                                border: 1px solid #c3e6cb;
                                                color: #155724;
                                              }
                                              .status-box.detained {
                                                background-color: #f8d7da;
                                                border: 1px solid #f5c6cb;
                                                color: #721c24;
                                              }
                                              .status-text {
                                                font-size: 13px;
                                                font-weight: bold;
                                              }
                                              .status-note {
                                                font-size: 9px;
                                                margin-top: 3px;
                                              }

                                              /* Grading Scale Styles */
                                              .grading-section {
                                                border: 1px solid #000;
                                                padding: 6px;
                                              }
                                              .grading-table {
                                                width: 100%;
                                                border-collapse: collapse;
                                                border: 1px solid #000;
                                              }
                                              .grading-table th,
                                              .grading-table td {
                                                border: 1px solid #000;
                                                padding: 3px 4px;
                                                text-align: center;
                                                font-size: 9px;
                                              }
                                              .grading-table th {
                                                background-color: #f0f0f0;
                                                font-weight: bold;
                                              }

                                              /* Signatures Styles */
                                              .signatures-section {
                                                display: flex;
                                                gap: 30px;
                                                margin-bottom: 12px;
                                              }
                                              .signature-box {
                                                flex: 1;
                                                text-align: center;
                                              }
                                              .signature-title {
                                                font-size: 11px;
                                                font-weight: bold;
                                                margin-bottom: 25px;
                                              }
                                              .signature-line {
                                                border-bottom: 1px solid #000;
                                                height: 1px;
                                                margin-bottom: 5px;
                                              }
                                              .signature-label {
                                                font-size: 9px;
                                                color: #666;
                                              }

                                              /* Footer Styles */
                                              .footer {
                                                text-align: center;
                                                border-top: 1px solid #000;
                                                padding-top: 6px;
                                              }
                                              .footer-text {
                                                font-size: 9px;
                                                color: #666;
                                                font-style: italic;
                                              }
                                            </style>
                                          </head>
                                          <body>
                                            ${generateStudentReportCardHtml(studentReport, classReportData.academicYear, classReportData.semester)}
                                            <script>
                                              window.onload = function() {
                                                setTimeout(function() {
                                                  window.print();
                                                  setTimeout(function() {
                                                    window.close();
                                                  }, 500);
                                                }, 1000);
                                              };
                                            </script>
                                          </body>
                                        </html>
                                      `;

                                      printWindow.document.open();
                                      printWindow.document.write(htmlContent);
                                      printWindow.document.close();
                                    }
                                  }}
                                >
                                  <Printer className="h-4 w-4 mr-1" />
                                  Print
                                </Button>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>

                <div className="mb-8">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Bulk Actions</h3>
                  <div className="flex gap-4">
                    <Button
                      variant="outline"
                      className="flex items-center gap-2"
                      onClick={() => {
                        // Print all student reports
                        const printWindow = window.open('', '_blank');
                        if (printWindow) {
                          // Create HTML content with all student reports
                          const allReportsHtml = classReportData.students.map((student: any) => {
                            const studentReport = {
                              student: student.student,
                              subjects: student.subjects,
                              summary: student.summary,
                              attendance: student.attendance,
                              academicYear: classReportData.academicYear,
                              semester: classReportData.semester
                            };

                            return generateStudentReportCardHtml(studentReport, classReportData.academicYear, classReportData.semester);
                          }).join('<div style="page-break-after: always;"></div>');

                          const htmlContent = `
                            <!DOCTYPE html>
                            <html>
                              <head>
                                <title>Class Report Cards - ${classReportData.className}</title>
                                <style>
                                  @page {
                                    size: A4 portrait;
                                    margin: 15mm;
                                  }
                                  body {
                                    font-family: 'Times New Roman', serif;
                                    margin: 0;
                                    padding: 0;
                                    font-size: 11px;
                                    line-height: 1.3;
                                    -webkit-print-color-adjust: exact !important;
                                    print-color-adjust: exact !important;
                                    color-adjust: exact !important;
                                  }
                                  .report-card {
                                    width: 100%;
                                    max-width: 210mm;
                                    margin: 0 auto;
                                    padding: 0;
                                    box-sizing: border-box;
                                    background: white;
                                    page-break-after: always;
                                  }
                                  /* Enhanced styles for professional report card */
                                  .header { border: 2px solid #000; margin-bottom: 12px; padding: 12px; }
                                  .header-content { display: flex; align-items: center; justify-content: space-between; }
                                  .logo { width: 60px; height: 60px; flex-shrink: 0; }
                                  .school-info { flex: 1; text-align: center; margin: 0 15px; }
                                  .school-name { font-size: 16px; font-weight: bold; margin: 0 0 4px 0; text-transform: uppercase; line-height: 1.2; }
                                  .school-address { font-size: 12px; margin: 0 0 6px 0; color: #666; }
                                  .report-title { font-size: 15px; font-weight: bold; margin: 4px 0; text-decoration: underline; }
                                  .academic-year { font-size: 13px; margin: 4px 0 0 0; font-weight: bold; }
                                  .stamp-area { width: 60px; height: 60px; flex-shrink: 0; }
                                  .stamp-placeholder { width: 100%; height: 100%; border: 2px dashed #999; border-radius: 50%; display: flex; align-items: center; justify-content: center; text-align: center; }
                                  .stamp-text { font-size: 9px; color: #999; line-height: 1.1; font-weight: bold; }
                                  .student-section { margin-bottom: 12px; }
                                  .student-info { border: 1px solid #000; padding: 10px; }
                                  .info-row { display: flex; margin-bottom: 6px; }
                                  .info-row:last-child { margin-bottom: 0; }
                                  .info-item { flex: 1; display: flex; }
                                  .label { font-weight: bold; min-width: 90px; margin-right: 8px; font-size: 12px; }
                                  .value { border-bottom: 1px dotted #666; flex: 1; min-height: 16px; font-size: 12px; }
                                  .academic-section { margin-bottom: 12px; }
                                  .section-title { font-size: 14px; font-weight: bold; text-align: center; margin: 0 0 6px 0; text-decoration: underline; }
                                  .performance-table { width: 100%; border-collapse: collapse; border: 1px solid #000; }
                                  .performance-table th, .performance-table td { border: 1px solid #000; padding: 5px 6px; text-align: center; font-size: 11px; }
                                  .performance-table th { background-color: #f0f0f0; font-weight: bold; }
                                  .subject-col { text-align: left !important; width: 40%; }
                                  .score-col { width: 20%; }
                                  .summary-row { background-color: #f8f8f8; }
                                  .summary-row td { font-weight: bold; }
                                  .bottom-section { display: flex; gap: 12px; margin-bottom: 12px; }
                                  .left-column, .right-column { flex: 1; }
                                  .attendance-section { border: 1px solid #000; padding: 6px; margin-bottom: 8px; }
                                  .attendance-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 4px; }
                                  .attendance-item { display: flex; justify-content: space-between; font-size: 10px; }
                                  .att-label { font-weight: bold; }
                                  .status-section { border: 1px solid #000; padding: 6px; }
                                  .status-box { text-align: center; padding: 6px; border-radius: 4px; }
                                  .status-box.promoted { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
                                  .status-box.detained { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
                                  .status-text { font-size: 13px; font-weight: bold; }
                                  .status-note { font-size: 9px; margin-top: 3px; }
                                  .grading-section { border: 1px solid #000; padding: 6px; }
                                  .grading-table { width: 100%; border-collapse: collapse; border: 1px solid #000; }
                                  .grading-table th, .grading-table td { border: 1px solid #000; padding: 3px 4px; text-align: center; font-size: 9px; }
                                  .grading-table th { background-color: #f0f0f0; font-weight: bold; }
                                  .signatures-section { display: flex; gap: 30px; margin-bottom: 12px; }
                                  .signature-box { flex: 1; text-align: center; }
                                  .signature-title { font-size: 11px; font-weight: bold; margin-bottom: 25px; }
                                  .signature-line { border-bottom: 1px solid #000; height: 1px; margin-bottom: 5px; }
                                  .signature-label { font-size: 9px; color: #666; }
                                  .footer { text-align: center; border-top: 1px solid #000; padding-top: 6px; }
                                  .footer-text { font-size: 9px; color: #666; font-style: italic; }
                                </style>
                              </head>
                              <body>
                                ${allReportsHtml}
                                <script>
                                  window.onload = function() {
                                    setTimeout(function() {
                                      window.print();
                                      setTimeout(function() {
                                        window.close();
                                      }, 500);
                                    }, 1000);
                                  };
                                </script>
                              </body>
                            </html>
                          `;

                          printWindow.document.open();
                          printWindow.document.write(htmlContent);
                          printWindow.document.close();
                        }
                      }}
                    >
                      <Printer className="h-4 w-4" />
                      Print All Reports
                    </Button>

                    <Button
                      variant="outline"
                      className="flex items-center gap-2"
                      onClick={() => {
                        // Download class summary as PDF
                        const printWindow = window.open('', '_blank');
                        if (printWindow) {
                          // Create HTML content with class summary
                          const htmlContent = `
                            <!DOCTYPE html>
                            <html>
                              <head>
                                <title>Class Summary - ${classReportData.className}</title>
                                <style>
                                  @page {
                                    size: A4 portrait;
                                    margin: 0;
                                  }
                                  body {
                                    font-family: Arial, sans-serif;
                                    margin: 0;
                                    padding: 20px;
                                    -webkit-print-color-adjust: exact !important;
                                    print-color-adjust: exact !important;
                                    color-adjust: exact !important;
                                  }
                                  .summary {
                                    width: 794px;
                                    margin: 0 auto;
                                    padding: 20px;
                                    box-sizing: border-box;
                                  }
                                  .header {
                                    text-align: center;
                                    margin-bottom: 20px;
                                    border-bottom: 1px solid #ccc;
                                    padding-bottom: 10px;
                                  }
                                  .logo {
                                    width: 50px;
                                    height: 50px;
                                    margin: 0 auto 5px;
                                    display: block;
                                  }
                                  .school-name {
                                    font-size: 16px;
                                    font-weight: bold;
                                    margin: 5px 0;
                                  }
                                  .report-title {
                                    font-size: 14px;
                                    font-weight: bold;
                                    margin: 5px 0;
                                  }
                                  .academic-year {
                                    font-size: 12px;
                                    color: #0369a1;
                                    margin: 5px 0 10px;
                                  }
                                  .class-info {
                                    display: flex;
                                    justify-content: space-between;
                                    margin-bottom: 20px;
                                    padding: 10px;
                                    background-color: #f5f5f5;
                                    border-radius: 5px;
                                  }
                                  .class-info-item {
                                    text-align: center;
                                  }
                                  .class-info-label {
                                    font-weight: bold;
                                    font-size: 12px;
                                  }
                                  .class-info-value {
                                    font-size: 14px;
                                    margin-top: 5px;
                                  }
                                  table {
                                    width: 100%;
                                    border-collapse: collapse;
                                    margin-bottom: 20px;
                                  }
                                  th, td {
                                    border: 1px solid #ccc;
                                    padding: 8px;
                                    text-align: center;
                                    font-size: 12px;
                                  }
                                  th {
                                    background-color: #f5f5f5;
                                    font-weight: bold;
                                  }
                                  .text-left {
                                    text-align: left;
                                  }
                                  .pdf-message {
                                    position: fixed;
                                    top: 10px;
                                    left: 50%;
                                    transform: translateX(-50%);
                                    background: #f0f9ff;
                                    border: 1px solid #bae6fd;
                                    color: #0369a1;
                                    padding: 8px 16px;
                                    border-radius: 4px;
                                    font-size: 14px;
                                    z-index: 9999;
                                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                                  }
                                  @media print {
                                    .pdf-message {
                                      display: none;
                                    }
                                  }
                                </style>
                              </head>
                              <body>
                                <div class="pdf-message">To save as PDF, select "Save as PDF" in the print dialog</div>
                                <div class="summary">
                                  <div class="header">
                                    <img src="${settings.schoolLogo || '/images/logo.png'}" alt="School Logo" class="logo">
                                    <div class="school-name">${settings.schoolName}</div>
                                    <div class="report-title">Class Performance Summary</div>
                                    <div class="academic-year">${classReportData.semester} - ${classReportData.academicYear}</div>
                                  </div>

                                  <div class="class-info">
                                    <div class="class-info-item">
                                      <div class="class-info-label">Class</div>
                                      <div class="class-info-value">${classReportData.className}</div>
                                    </div>
                                    <div class="class-info-item">
                                      <div class="class-info-label">Total Students</div>
                                      <div class="class-info-value">${classReportData.students.length}</div>
                                    </div>
                                    <div class="class-info-item">
                                      <div class="class-info-label">Academic Year</div>
                                      <div class="class-info-value">${classReportData.academicYear}</div>
                                    </div>
                                    <div class="class-info-item">
                                      <div class="class-info-label">Semester</div>
                                      <div class="class-info-value">${classReportData.semester}</div>
                                    </div>
                                  </div>

                                  <table>
                                    <thead>
                                      <tr>
                                        <th>Rank</th>
                                        <th>Student ID</th>
                                        <th class="text-left">Student Name</th>
                                        <th>Average</th>
                                        <th>Status</th>
                                      </tr>
                                    </thead>
                                    <tbody>
                                      ${classReportData.students.map((student: any) => {
                                        // Calculate promotion status using the dynamic promotion policy
                                        const isPromoted = calculatePromotionStatus(student.subjects, classReportData.semester, promotionPolicy);
                                        const statusInfo = getPromotionStatusText(isPromoted);

                                        return `
                                          <tr>
                                            <td>${student.summary.rank}</td>
                                            <td>${student.student.sid}</td>
                                            <td class="text-left">${student.student.name}</td>
                                            <td>${student.summary.average}</td>
                                            <td style="color: ${isPromoted ? '#16a34a' : '#dc2626'}; font-weight: bold;">
                                              ${statusInfo.status}
                                            </td>
                                          </tr>
                                        `;
                                      }).join('')}
                                    </tbody>
                                  </table>

                                  <div style="margin-top: 40px; display: flex; justify-content: space-between;">
                                    <div style="width: 45%;">
                                      <div style="font-weight: bold; margin-bottom: 5px;">Home Room Teacher:</div>
                                      <div style="margin-top: 50px; border-top: 1px solid #ccc; padding-top: 5px;">Signature</div>
                                    </div>
                                    <div style="width: 45%;">
                                      <div style="font-weight: bold; margin-bottom: 5px;">Director:</div>
                                      <div style="margin-top: 50px; border-top: 1px solid #ccc; padding-top: 5px;">Signature</div>
                                    </div>
                                  </div>
                                </div>

                                <script>
                                  window.onload = function() {
                                    setTimeout(function() {
                                      window.print();
                                      setTimeout(function() {
                                        window.close();
                                      }, 500);
                                    }, 1000);
                                  };
                                </script>
                              </body>
                            </html>
                          `;

                          printWindow.document.open();
                          printWindow.document.write(htmlContent);
                          printWindow.document.close();
                        }
                      }}
                    >
                      <Download className="h-4 w-4" />
                      Download Class Summary
                    </Button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg border border-gray-200">
                <div className="text-center">
                  <div className="animate-spin mb-4">
                    <Calendar className="h-8 w-8 text-gray-400 mx-auto" />
                  </div>
                  <p className="text-gray-500">Loading report data...</p>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

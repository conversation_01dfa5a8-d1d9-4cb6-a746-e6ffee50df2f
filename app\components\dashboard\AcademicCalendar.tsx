'use client'

import React from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON><PERSON>, CardTitle } from '../ui/card'
import { format, parseISO, addDays, startOfWeek, isSameDay, isSameMonth, isToday } from 'date-fns'
import { cn } from '@/app/lib/utils'
import { Button } from '../ui/button'
import {
  CalendarIcon,
  ChevronRight,
  ChevronLeft,
  ChevronDown,
  Clock,
  MapPin
} from 'lucide-react'
import Link from 'next/link'

interface CalendarEvent {
  id: string
  title: string
  date: string
  category: 'academic' | 'holiday' | 'exam' | 'staff' | 'student' | 'parent' | 'special'
}

interface AcademicCalendarProps {
  events: CalendarEvent[]
}

export function AcademicCalendar({ events }: AcademicCalendarProps) {
  const [currentDate, setCurrentDate] = React.useState<Date>(new Date())
  const [selectedDate, setSelectedDate] = React.useState<Date>(new Date())
  const [viewMode, setViewMode] = React.useState<'month' | 'week'>('week')

  // Function to get category color
  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'academic':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-300 border-blue-300'
      case 'holiday':
        return 'bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-300 border-red-300'
      case 'exam':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/50 dark:text-purple-300 border-purple-300'
      case 'staff':
        return 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300 border-green-300'
      case 'student':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900/50 dark:text-amber-300 border-amber-300'
      case 'parent':
        return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/50 dark:text-indigo-300 border-indigo-300'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300 border-gray-300'
    }
  }

  // Function to get category icon
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'academic':
        return '📚'
      case 'holiday':
        return '🎉'
      case 'exam':
        return '📝'
      case 'staff':
        return '👨‍🏫'
      case 'student':
        return '👨‍🎓'
      case 'parent':
        return '👪'
      default:
        return '📅'
    }
  }

  // Get events for the selected date
  const selectedDateEvents = events.filter(event =>
    format(parseISO(event.date), 'yyyy-MM-dd') === format(selectedDate, 'yyyy-MM-dd')
  );

  // Get upcoming events (next 5 events)
  const upcomingEvents = [...events]
    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
    .filter(event => new Date(event.date) >= new Date())
    .slice(0, 5);

  // Generate calendar days (4 weeks)
  const generateCalendarDays = () => {
    const startDate = startOfWeek(currentDate, { weekStartsOn: 1 }); // Start from Monday
    return Array.from({ length: 28 }).map((_, index) => addDays(startDate, index));
  };

  const calendarDays = generateCalendarDays();

  // Navigate to previous/next month
  const navigatePrevious = () => {
    setCurrentDate(prevDate => addDays(prevDate, -28));
  };

  const navigateNext = () => {
    setCurrentDate(prevDate => addDays(prevDate, 28));
  };

  // Navigate to today
  const navigateToday = () => {
    setCurrentDate(new Date());
    setSelectedDate(new Date());
  };

  // Check if a date has events
  const hasEvents = (date: Date) => {
    return events.some(event =>
      format(parseISO(event.date), 'yyyy-MM-dd') === format(date, 'yyyy-MM-dd')
    );
  };

  // Get events for a specific date
  const getEventsForDate = (date: Date) => {
    return events.filter(event =>
      format(parseISO(event.date), 'yyyy-MM-dd') === format(date, 'yyyy-MM-dd')
    );
  };

  return (
    <div className="grid grid-cols-1 gap-6">
      <Card className="border-0 shadow-lg dark:bg-gray-800">
        <CardHeader className="pb-2 flex flex-row items-center justify-between">
          <CardTitle className="text-lg font-medium">Academic Calendar</CardTitle>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={navigatePrevious}
              className="h-8 w-8 p-0"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={navigateToday}
              className="h-8 px-2 text-xs"
            >
              Today
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={navigateNext}
              className="h-8 w-8 p-0"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
            <div className="text-sm font-medium">
              {format(currentDate, 'MMMM yyyy')}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Monthly view (4 weeks) */}
          <div className="mb-6">
            <div className="grid grid-cols-7 gap-1 mb-1">
              {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map((day) => (
                <div key={day} className="text-center text-xs font-medium text-gray-500 dark:text-gray-400">
                  {day}
                </div>
              ))}
            </div>
            <div className="grid grid-cols-7 gap-1">
              {calendarDays.map((date) => {
                const dateEvents = getEventsForDate(date);
                const isSelected = isSameDay(date, selectedDate);
                const isCurrentMonth = isSameMonth(date, currentDate);
                const isCurrentDay = isToday(date);

                return (
                  <div
                    key={date.toString()}
                    className={cn(
                      "h-20 p-1 border rounded-md cursor-pointer transition-colors",
                      isSelected
                        ? "border-indigo-500 bg-indigo-50 dark:bg-indigo-900/20"
                        : "border-gray-200 dark:border-gray-700",
                      !isCurrentMonth && "opacity-40",
                      isCurrentDay && !isSelected && "border-indigo-300 dark:border-indigo-700"
                    )}
                    onClick={() => setSelectedDate(date)}
                  >
                    <div className="flex justify-between items-start">
                      <span
                        className={cn(
                          "inline-flex items-center justify-center h-6 w-6 rounded-full text-xs font-medium",
                          isCurrentDay
                            ? "bg-indigo-500 text-white"
                            : "text-gray-700 dark:text-gray-300"
                        )}
                      >
                        {format(date, 'd')}
                      </span>
                      {hasEvents(date) && (
                        <span className="h-2 w-2 rounded-full bg-indigo-500"></span>
                      )}
                    </div>
                    <div className="mt-1 overflow-y-auto max-h-[calc(100%-1.5rem)]">
                      {dateEvents.slice(0, 1).map((event, idx) => (
                        <div
                          key={event.id}
                          className={cn(
                            "text-xs p-1 mb-1 rounded truncate border-l-2",
                            getCategoryColor(event.category)
                          )}
                        >
                          {event.title}
                        </div>
                      ))}
                      {dateEvents.length > 1 && (
                        <div className="text-xs text-gray-500 dark:text-gray-400 pl-1">
                          +{dateEvents.length - 1} more
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
            {/* Selected date events */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                  Events for {format(selectedDate, 'MMMM d, yyyy')}
                </h3>
              </div>

              {selectedDateEvents.length > 0 ? (
                <div className="space-y-3">
                  {selectedDateEvents.map((event) => (
                    <div
                      key={event.id}
                      className={cn(
                        "p-3 rounded-lg border-l-4",
                        getCategoryColor(event.category).replace('bg-', 'border-')
                      )}
                    >
                      <div className="flex items-start">
                        <span className="text-2xl mr-3">{getCategoryIcon(event.category)}</span>
                        <div>
                          <h4 className="font-medium text-gray-900 dark:text-white">{event.title}</h4>
                          <div className="flex items-center mt-1 text-xs text-gray-500 dark:text-gray-400">
                            <Clock className="h-3 w-3 mr-1" />
                            <span>{format(parseISO(event.date), 'h:mm a')}</span>
                            <span className={cn(
                              "ml-2 px-2 py-0.5 rounded-full text-xs",
                              getCategoryColor(event.category)
                            )}>
                              {event.category.charAt(0).toUpperCase() + event.category.slice(1)}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-6 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                  <CalendarIcon className="mx-auto h-10 w-10 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No events</h3>
                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    There are no events scheduled for this day.
                  </p>
                </div>
              )}
            </div>

            {/* Upcoming Academic Events until June 30 */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                  Upcoming Academic Events
                </h3>
                <Link href="/website/academics/calendar" passHref>
                  <Button variant="ghost" size="sm" className="text-indigo-600 dark:text-indigo-400 hover:text-indigo-700 dark:hover:text-indigo-300">
                    View All
                    <ChevronRight className="ml-1 h-4 w-4" />
                  </Button>
                </Link>
              </div>

              {events.length > 0 ? (
                <div className="space-y-3 max-h-[350px] overflow-y-auto pr-2">
                  {events
                    .filter(event => {
                      // Filter events until June 30
                      const eventDate = parseISO(event.date);
                      const today = new Date();
                      const june30 = new Date(today.getFullYear(), 5, 30);

                      // If today is after June 30, use next year's June 30
                      if (today > june30) {
                        june30.setFullYear(today.getFullYear() + 1);
                      }

                      return eventDate >= today && eventDate <= june30;
                    })
                    .map((event) => (
                      <div
                        key={event.id}
                        className={cn(
                          "p-3 rounded-lg border-l-4",
                          getCategoryColor(event.category).replace('bg-', 'border-')
                        )}
                      >
                        <div className="flex items-start">
                          <div className="flex-shrink-0 mr-3">
                            <div className="w-10 h-10 rounded-lg bg-indigo-50 dark:bg-indigo-900/30 flex flex-col items-center justify-center text-xs">
                              <span className="font-bold text-indigo-600 dark:text-indigo-400">
                                {format(parseISO(event.date), 'MMM')}
                              </span>
                              <span className="text-sm font-bold text-indigo-800 dark:text-indigo-300">
                                {format(parseISO(event.date), 'd')}
                              </span>
                            </div>
                          </div>
                          <div>
                            <h4 className="font-medium text-gray-900 dark:text-white">{event.title}</h4>
                            <div className="flex items-center mt-1 text-xs text-gray-500 dark:text-gray-400">
                              <span>{format(parseISO(event.date), 'EEEE, MMMM d')}</span>
                              <span className={cn(
                                "ml-2 px-2 py-0.5 rounded-full text-xs",
                                getCategoryColor(event.category)
                              )}>
                                {event.category.charAt(0).toUpperCase() + event.category.slice(1)}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))
                  }
                </div>
              ) : (
                <div className="text-center py-6 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                  <CalendarIcon className="mx-auto h-10 w-10 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No upcoming events</h3>
                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    There are no academic events scheduled until June 30.
                  </p>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

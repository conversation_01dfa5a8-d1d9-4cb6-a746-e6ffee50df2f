// Mark Management related types

export interface MarkClass {
  id: string
  name: string
  totalStudents: number
  totalSubjects: number
}

export interface MarkSubject {
  id: string
  name: string
  classId: string
}

export interface StudentMark {
  id: string
  studentId: string
  studentName: string
  studentSid: string
  marks: number
  totalMarks: number
  className: string
  subject: string
  term: string
  academicYear: string
  remarks?: string
  createdAt: string
  updatedAt: string
}

export interface StudentMarkForUpdate extends StudentMark {
  originalMarks: number
  isEdited: boolean
}

export interface UpdateMarksRequest {
  className: string
  subject: string
  term: string
  academicYear: string
}

export interface BatchUpdateRequest {
  marks: {
    id: string
    marks: number
    totalMarks: number
  }[]
}

export interface BatchUpdateResponse {
  message: string
  updatedCount: number
  updatedMarks: {
    id: string
    studentName: string
    studentSid: string
    marks: number
    totalMarks: number
    subject: string
    className: string
    term: string
    academicYear: string
  }[]
}

export interface MarkCriteria {
  className?: string
  subject?: string
  term?: string
  academicYear?: string
  studentId?: string
}

export interface MarkStatistics {
  totalMarks: number
  averageMarks: number
  highestMark: number
  lowestMark: number
  passCount: number
  failCount: number
  passPercentage: number
}

// Academic year and term constants
export const ACADEMIC_YEARS = [
  '2020-2021',
  '2021-2022', 
  '2022-2023',
  '2023-2024',
  '2024-2025',
  '2025-2026',
  '2026-2027',
  '2027-2028'
] as const

export const TERMS = [
  'First Semester',
  'Second Semester'
] as const

export type AcademicYear = typeof ACADEMIC_YEARS[number]
export type Term = typeof TERMS[number]

import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    console.log('Fetching available classes for HR assignment...')

    // Get all classes with their current HR teacher info
    const classes = await prisma.class.findMany({
      select: {
        id: true,
        name: true,
        hrTeacherId: true,
        hrTeacher: {
          select: {
            id: true,
            name: true,
            email: true,
            subject: true,
            mobile: true,
          },
        },
        totalStudents: true,
        totalSubjects: true,
      },
      orderBy: {
        name: 'asc',
      },
    })

    // For each class, also check if there's an HR teacher assigned through TeacherAssignment
    const classesWithHRInfo = await Promise.all(
      classes.map(async (cls) => {
        let hrTeacher = cls.hrTeacher

        // If no HR teacher from the traditional relation, check TeacherAssignment
        if (!hrTeacher) {
          const teacherAssignment = await prisma.teacherAssignment.findFirst({
            where: {
              classId: cls.id,
              isHRTeacher: true
            },
            include: {
              teacher: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  phone: true
                }
              }
            }
          })

          if (teacherAssignment) {
            hrTeacher = {
              id: teacherAssignment.teacher.id,
              name: teacherAssignment.teacher.name,
              email: teacherAssignment.teacher.email,
              subject: null, // Users might not have a subject
              mobile: teacherAssignment.teacher.phone
            }
          }
        }

        return {
          ...cls,
          hrTeacher
        }
      })
    )

    console.log(`Found ${classesWithHRInfo.length} classes for HR assignment`)

    return NextResponse.json(classesWithHRInfo)
  } catch (error) {
    console.error('Error fetching available classes:', error)
    return NextResponse.json(
      { error: 'Failed to fetch available classes' },
      { status: 500 }
    )
  }
}

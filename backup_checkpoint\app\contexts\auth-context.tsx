'use client'

import React, { createContext, useContext, useState, useEffect } from 'react'

// Define a simple user type
type User = {
  id: string
  name: string
  email: string
  role: string
}

// Define the auth context type
interface AuthContextType {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
  checkAuth: () => Promise<void>
  logout: () => void
  updateProfile: (name: string, email: string) => Promise<void>
  updatePassword: (currentPassword: string, newPassword: string) => Promise<void>
}

// Create the context with default values
const AuthContext = createContext<AuthContextType>({
  user: null,
  isLoading: false,
  isAuthenticated: false,
  checkAuth: async () => {},
  logout: () => {},
  updateProfile: async () => {},
  updatePassword: async () => {}
})

// Hook to use the auth context
export function useAuth() {
  return useContext(AuthContext)
}

// Auth provider component
export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  // Function to check authentication with the server
  const checkAuth = async () => {
    setIsLoading(true)
    try {
      // Call the debug API to check authentication status
      const response = await fetch('/api/auth/debug')
      const data = await response.json()

      if (data.authenticated && data.user) {
        // User is authenticated, set user data
        setUser({
          id: data.user.id || '1',
          name: data.user.name || 'User',
          email: data.user.email || '<EMAIL>',
          role: data.user.role || 'user'
        })
      } else {
        // User is not authenticated
        setUser(null)
      }
    } catch (error) {
      console.error('Auth check error:', error)
      setUser(null)
    } finally {
      setIsLoading(false)
    }
  }

  // Logout function
  const logout = async () => {
    // Clear user data
    setUser(null)
    setIsLoading(true)

    try {
      // Call the logout API to clear the token cookie
      const response = await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include',
      })

      if (response.ok) {
        console.log('Logged out successfully')
      } else {
        console.error('Logout failed')
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      setIsLoading(false)
      // Redirect to login page
      window.location.href = '/login'
    }
  }

  // Check auth on mount
  useEffect(() => {
    checkAuth()
  }, [])

  // Update profile function
  const updateProfile = async (name: string, email: string) => {
    try {
      setIsLoading(true)
      const res = await fetch('/api/user/profile', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name, email })
      })

      const data = await res.json()

      if (!res.ok) {
        throw new Error(data.error || 'Failed to update profile')
      }

      // Update the user state
      setUser(data.user)

      return data
    } catch (error) {
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  // Update password function
  const updatePassword = async (currentPassword: string, newPassword: string) => {
    try {
      setIsLoading(true)
      const res = await fetch('/api/user/password', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ currentPassword, newPassword })
      })

      const data = await res.json()

      if (!res.ok) {
        throw new Error(data.error || 'Failed to update password')
      }

      return data
    } catch (error) {
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  // Context value
  const value = {
    user,
    isLoading,
    isAuthenticated: !!user,
    checkAuth,
    logout,
    updateProfile,
    updatePassword
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

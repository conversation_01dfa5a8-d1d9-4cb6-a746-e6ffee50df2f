// Mock implementation of PrismaClient for development
class MockPrismaClient {
  // Mock data storage
  private mockData = {
    classes: [
      { id: 'class-1', name: '1A', totalStudents: 25, totalSubjects: 6, hrTeacherId: 'teacher-1', createdAt: new Date(), updatedAt: new Date() },
      { id: 'class-2', name: '1B', totalStudents: 22, totalSubjects: 6, hrTeacherId: 'teacher-2', createdAt: new Date(), updatedAt: new Date() },
      { id: 'class-3', name: '2A', totalStudents: 24, totalSubjects: 7, hrTeacherId: 'teacher-3', createdAt: new Date(), updatedAt: new Date() },
      { id: 'class-4', name: '2B', totalStudents: 23, totalSubjects: 7, hrTeacherId: null, createdAt: new Date(), updatedAt: new Date() },
    ],
    teachers: [
      { id: 'teacher-1', name: '<PERSON>', father<PERSON>ame: '<PERSON>', gender: 'Male', email: '<EMAIL>', subject: 'Mathematics', mobile: '1234567890', createdAt: new Date(), updatedAt: new Date() },
      { id: 'teacher-2', name: '<PERSON>', fatherName: '<PERSON>', gender: 'Female', email: '<EMAIL>', subject: 'English', mobile: '2345678901', createdAt: new Date(), updatedAt: new Date() },
      { id: 'teacher-3', name: 'Michael Brown', fatherName: '<PERSON>', gender: 'Male', email: '<EMAIL>', subject: 'Science', mobile: '3456789012', createdAt: new Date(), updatedAt: new Date() },
    ],
    students: [
      { id: 'student-1', sid: '1A001', name: 'Alice Cooper', className: '1A', fatherName: 'John Cooper', gfName: 'William Cooper', age: 6, gender: 'Female', createdAt: new Date(), updatedAt: new Date() },
      { id: 'student-2', sid: '1A002', name: 'Bob Wilson', className: '1A', fatherName: 'Thomas Wilson', gfName: 'George Wilson', age: 6, gender: 'Male', createdAt: new Date(), updatedAt: new Date() },
      { id: 'student-3', sid: '1B001', name: 'Charlie Davis', className: '1B', fatherName: 'Richard Davis', gfName: 'Edward Davis', age: 6, gender: 'Male', createdAt: new Date(), updatedAt: new Date() },
      { id: 'student-4', sid: '2A001', name: 'Diana Evans', className: '2A', fatherName: 'Michael Evans', gfName: 'Joseph Evans', age: 7, gender: 'Female', createdAt: new Date(), updatedAt: new Date() },
    ],
    attendance: [],
    subjects: [
      { id: 'subject-1', name: 'Mathematics', classId: 'class-1', createdAt: new Date(), updatedAt: new Date() },
      { id: 'subject-2', name: 'English', classId: 'class-1', createdAt: new Date(), updatedAt: new Date() },
      { id: 'subject-3', name: 'Science', classId: 'class-1', createdAt: new Date(), updatedAt: new Date() },
    ],
    marks: [],
    users: [
      { id: 'user-1', email: '<EMAIL>', password: 'password', name: 'Admin User', role: 'ADMIN', createdAt: new Date(), updatedAt: new Date(), lastLogin: null, status: 'active' },
    ],
    classTeacher: [],
    subjectTeacher: [],
  };

  constructor() {
    console.warn('Using MockPrismaClient - using mock data for development');
  }

  // Mock connection methods
  async $connect() { return Promise.resolve(); }
  async $disconnect() { return Promise.resolve(); }

  // Mock data access methods
  user = this.createMockModel('users');
  class = this.createMockModel('classes');
  teacher = this.createMockModel('teachers');
  student = this.createMockModel('students');
  subject = this.createMockModel('subjects');
  mark = this.createMockModel('marks');
  classTeacher = this.createMockModel('classTeacher');
  subjectTeacher = this.createMockModel('subjectTeacher');
  attendance = this.createMockModel('attendance');

  // Helper to create mock model methods
  createMockModel(modelName: string) {
    return {
      findMany: async (params?: any) => {
        console.log(`Mock findMany for ${modelName}`, params);
        let data = [...this.mockData[modelName]];

        // Handle where clause (very basic implementation)
        if (params?.where) {
          Object.entries(params.where).forEach(([key, value]) => {
            if (value !== undefined) {
              data = data.filter(item => item[key] === value);
            }
          });
        }

        // Handle include (very basic implementation)
        if (params?.include) {
          data = data.map(item => {
            const result = { ...item };

            Object.entries(params.include).forEach(([relationName, include]) => {
              if (include) {
                if (relationName === 'hrTeacher' && item.hrTeacherId) {
                  result.hrTeacher = this.mockData.teachers.find(t => t.id === item.hrTeacherId);
                } else if (relationName === 'students' && item.name) { // For classes
                  result.students = this.mockData.students.filter(s => s.className === item.name);
                } else if (relationName === 'class' && item.className) { // For students
                  result.class = this.mockData.classes.find(c => c.name === item.className);
                } else if (relationName === 'student' && item.studentId) { // For attendance
                  result.student = this.mockData.students.find(s => s.id === item.studentId);
                }
              }
            });

            return result;
          });
        }

        // Handle orderBy (very basic implementation)
        if (params?.orderBy) {
          const [field, direction] = Object.entries(params.orderBy)[0];
          data.sort((a, b) => {
            if (a[field] < b[field]) return direction === 'asc' ? -1 : 1;
            if (a[field] > b[field]) return direction === 'asc' ? 1 : -1;
            return 0;
          });
        }

        return data;
      },
      findUnique: async (params?: any) => {
        console.log(`Mock findUnique for ${modelName}`, params);
        if (params?.where?.id) {
          return this.mockData[modelName].find(item => item.id === params.where.id) || null;
        }
        return null;
      },
      findFirst: async (params?: any) => {
        console.log(`Mock findFirst for ${modelName}`, params);
        let data = this.mockData[modelName];

        // Handle where clause (very basic implementation)
        if (params?.where) {
          Object.entries(params.where).forEach(([key, value]) => {
            if (value !== undefined) {
              data = data.filter(item => item[key] === value);
            }
          });
        }

        return data[0] || null;
      },
      create: async (params: any) => {
        console.log(`Mock create for ${modelName}`, params);
        const newItem = {
          id: `mock-${Date.now()}`,
          ...params.data,
          createdAt: new Date(),
          updatedAt: new Date()
        };
        this.mockData[modelName].push(newItem);
        return newItem;
      },
      createMany: async (params: any) => {
        console.log(`Mock createMany for ${modelName}`, params);
        const count = params.data.length;
        params.data.forEach((item: any) => {
          this.mockData[modelName].push({
            id: `mock-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
            ...item,
            createdAt: new Date(),
            updatedAt: new Date()
          });
        });
        return { count };
      },
      update: async (params: any) => {
        console.log(`Mock update for ${modelName}`, params);
        const index = this.mockData[modelName].findIndex(item => item.id === params.where.id);
        if (index !== -1) {
          this.mockData[modelName][index] = {
            ...this.mockData[modelName][index],
            ...params.data,
            updatedAt: new Date()
          };
          return this.mockData[modelName][index];
        }
        return { id: 'mock-id', ...params.data };
      },
      updateMany: async (params: any) => {
        console.log(`Mock updateMany for ${modelName}`, params);
        let count = 0;
        this.mockData[modelName].forEach((item, index) => {
          let match = true;
          if (params.where) {
            Object.entries(params.where).forEach(([key, value]) => {
              if (item[key] !== value) match = false;
            });
          }
          if (match) {
            this.mockData[modelName][index] = {
              ...item,
              ...params.data,
              updatedAt: new Date()
            };
            count++;
          }
        });
        return { count };
      },
      delete: async (params: any) => {
        console.log(`Mock delete for ${modelName}`, params);
        const index = this.mockData[modelName].findIndex(item => item.id === params.where.id);
        if (index !== -1) {
          const deleted = this.mockData[modelName][index];
          this.mockData[modelName].splice(index, 1);
          return deleted;
        }
        return {};
      },
      deleteMany: async (params: any) => {
        console.log(`Mock deleteMany for ${modelName}`, params);
        let count = 0;
        const newData = this.mockData[modelName].filter(item => {
          let match = true;
          if (params?.where) {
            Object.entries(params.where).forEach(([key, value]) => {
              if (item[key] !== value) match = false;
            });
          }
          if (match) count++;
          return !match;
        });
        this.mockData[modelName] = newData;
        return { count };
      },
      count: async (params?: any) => {
        console.log(`Mock count for ${modelName}`, params);
        if (params?.where) {
          let count = 0;
          this.mockData[modelName].forEach(item => {
            let match = true;
            Object.entries(params.where).forEach(([key, value]) => {
              if (item[key] !== value) match = false;
            });
            if (match) count++;
          });
          return count;
        }
        return this.mockData[modelName].length;
      },
      aggregate: async () => {
        console.log(`Mock aggregate for ${modelName}`);
        return {};
      },
    };
  }
}

// Import PrismaClient
import { PrismaClient } from '@prisma/client';

// Define global type
const globalForPrisma = global as unknown as { prisma: PrismaClient };

// Initialize prisma client with singleton pattern
let prisma: PrismaClient;

// Get connection pool settings from environment variables or use defaults
const poolLimit = parseInt(process.env.POOL_CONNECTIONS_LIMIT || '5', 10);
const poolTimeout = parseInt(process.env.POOL_CONNECTIONS_TIMEOUT || '30000', 10);

// Always try to use the real database first
try {
  // Check if we already have a connection to prevent too many connections
  if (process.env.NODE_ENV === 'production') {
    prisma = new PrismaClient({
      log: ['error', 'warn'],
      datasources: {
        db: {
          url: process.env.DATABASE_URL
        }
      },
      // Configure connection pooling for better performance and reliability
      connection: {
        pool: {
          max: poolLimit,
          timeout: poolTimeout
        }
      }
    });
    console.log('Production environment: Using new PrismaClient instance with connection pooling');
  } else {
    // In development, reuse the same connection
    if (!globalForPrisma.prisma) {
      globalForPrisma.prisma = new PrismaClient({
        log: ['query', 'error', 'warn'],
        datasources: {
          db: {
            url: process.env.DATABASE_URL
          }
        },
        // Configure connection pooling for better performance and reliability
        connection: {
          pool: {
            max: poolLimit,
            timeout: poolTimeout
          }
        }
      });
      console.log(`Development environment: Created new PrismaClient instance with connection pooling (max: ${poolLimit}, timeout: ${poolTimeout}ms)`);
    } else {
      console.log('Development environment: Reusing existing PrismaClient instance');
    }
    prisma = globalForPrisma.prisma;
  }

  // Test the connection
  prisma.$connect()
    .then(() => {
      console.log('Successfully connected to the database');
    })
    .catch((error) => {
      console.error('Failed to connect to the database:', error);
      throw error; // Rethrow to be caught by the outer try/catch
    });

} catch (error) {
  console.error('Failed to initialize Prisma Client:', error);
  // If we can't connect to the database, use the mock client as a fallback
  console.warn('Falling back to MockPrismaClient');
  prisma = new MockPrismaClient() as unknown as PrismaClient;
}

export { prisma };
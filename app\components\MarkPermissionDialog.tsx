import React from 'react'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from './ui/alert-dialog'
import { ShieldAlert } from 'lucide-react'

interface MarkPermissionDialogProps {
  isOpen: boolean
  onClose: () => void
  className?: string
}

export function MarkPermissionDialog({
  isOpen,
  onClose,
  className
}: MarkPermissionDialogProps) {
  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <div className="flex items-center gap-2 text-red-500">
            <ShieldAlert className="h-5 w-5" />
            <AlertDialogTitle>Permission Denied</AlertDialogTitle>
          </div>
          <AlertDialogDescription>
            You do not have permission to view marks for {className ? `class "${className}"` : "this class"}.
            <br /><br />
            Please contact your administrator to request access to this class's marks.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogAction onClick={onClose}>Understood</AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}

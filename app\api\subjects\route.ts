import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const classId = searchParams.get('classId')
    const className = searchParams.get('className')

    let whereClause = {}

    // Filter by classId if provided
    if (classId) {
      whereClause = { classId }
    }
    // Filter by className if provided
    else if (className) {
      // First find the class by name
      const classRecord = await prisma.class.findUnique({
        where: { name: className },
        select: { id: true }
      })

      if (classRecord) {
        whereClause = { classId: classRecord.id }
      } else {
        // Return empty array if class not found
        console.log(`Class ${className} not found in database`)
        return NextResponse.json([], { status: 200 })
      }
    }

    const subjects = await prisma.subject.findMany({
      where: whereClause,
      include: {
        class: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        name: 'asc'
      }
    })

    return NextResponse.json(subjects)
  } catch (error) {
    console.error('Error fetching subjects:', error)
    return NextResponse.json(
      { error: 'Failed to fetch subjects' },
      { status: 500 }
    )
  }
}

export async function POST(request: Request) {
  try {
    console.log('POST /api/subjects - Creating subject')
    const data = await request.json()

    // Validate required fields
    if (!data.name || !data.classId) {
      return NextResponse.json(
        { error: 'Subject name and class ID are required' },
        { status: 400 }
      )
    }

    // Check if class exists
    const classExists = await prisma.class.findUnique({
      where: { id: data.classId }
    })

    if (!classExists) {
      return NextResponse.json(
        { error: 'Class not found' },
        { status: 404 }
      )
    }

    // Check if subject already exists for this class
    const existingSubject = await prisma.subject.findFirst({
      where: {
        name: data.name,
        classId: data.classId
      },
      include: {
        class: {
          select: {
            name: true
          }
        }
      }
    })

    if (existingSubject) {
      // Subject already exists for this class, return the existing one
      console.log(`Subject ${data.name} already exists for class ${existingSubject.class.name}`)
      return NextResponse.json(existingSubject)
    }

    // Try to create the subject
    let subject
    try {
      subject = await prisma.subject.create({
        data: {
          name: data.name,
          classId: data.classId
        },
        include: {
          class: {
            select: {
              name: true
            }
          }
        }
      })

      // Update the class's total subjects count
      await prisma.class.update({
        where: { id: data.classId },
        data: {
          totalSubjects: {
            increment: 1
          }
        }
      })

      return NextResponse.json(subject)
    } catch (createError: any) {
      // Handle unique constraint violation specifically for composite constraint (name + classId)
      if (createError.code === 'P2002' && createError.meta?.target?.includes('name_classId')) {
        // Subject already exists for this specific class
        console.log(`Subject ${data.name} already exists for this class`)
        return NextResponse.json(
          {
            error: `Subject "${data.name}" already exists for this class.`
          },
          { status: 409 }
        )
      }

      throw createError
    }
  } catch (error) {
    console.error('Error creating subject:', error)

    // Provide more specific error messages
    if (error instanceof Error) {
      if (error.message.includes('Unique constraint failed')) {
        return NextResponse.json(
          { error: 'Subject with this name already exists' },
          { status: 409 }
        )
      }
    }

    return NextResponse.json(
      { error: 'Failed to create subject' },
      { status: 500 }
    )
  }
}

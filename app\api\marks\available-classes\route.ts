import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    console.log('Fetching available classes for mark management...')

    // Get all classes with their student and subject counts
    const classes = await prisma.class.findMany({
      select: {
        id: true,
        name: true,
        totalStudents: true,
        totalSubjects: true,
        _count: {
          select: {
            students: true,
            subjects: true,
          },
        },
      },
      orderBy: {
        name: 'asc',
      },
    })

    // Format the response to include actual counts
    const formattedClasses = classes.map(cls => ({
      id: cls.id,
      name: cls.name,
      totalStudents: cls._count.students,
      totalSubjects: cls._count.subjects,
    }))

    console.log(`Found ${formattedClasses.length} classes for mark management`)

    return NextResponse.json(formattedClasses)
  } catch (error) {
    console.error('Error fetching available classes:', error)
    return NextResponse.json(
      { error: 'Failed to fetch available classes' },
      { status: 500 }
    )
  }
}

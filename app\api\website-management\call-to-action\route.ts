import { NextResponse } from 'next/server';
import { prisma, isUsingMockPrisma } from '@/lib/prisma';

// Default mock data for call to action
const defaultCallToAction = {
  id: 'mock-cta-1',
  title: 'Apply for Admission',
  description: 'Join our school community and experience quality education with Islamic values. Applications for the new academic year are now open.',
  primaryBtnText: 'Apply Now',
  primaryBtnLink: '/dashboard',
  secondaryBtnText: 'Contact Us',
  secondaryBtnLink: '/website/contact',
  isActive: true,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString()
};

// GET all call to action items
export async function GET() {
  try {
    // Check if we're using the mock client
    const usingMock = isUsingMockPrisma();

    // Try to fetch call to actions from the database
    const callToActions = await prisma.callToAction.findMany({
      orderBy: {
        createdAt: 'desc'
      }
    });

    // If we got data, return it
    if (callToActions && callToActions.length > 0) {
      return NextResponse.json(callToActions);
    }

    // If we're using the mock client or no data was found, return mock data
    if (usingMock || callToActions.length === 0) {
      console.log('No call to action items found or using mock client, returning default data');
      return NextResponse.json([defaultCallToAction]);
    }

    // Otherwise return an empty array
    return NextResponse.json([]);
  } catch (error) {
    console.error('Error fetching call to action items:', error);

    // Return mock data for development with error information
    // Return as an array to match the expected format in the component
    return NextResponse.json([defaultCallToAction]);
  }
}

// POST a new call to action
export async function POST(request: Request) {
  try {
    const data = await request.json();

    // Check if we're using the mock client
    const usingMock = isUsingMockPrisma();

    // Validate required fields
    if (!data.title || !data.description || !data.primaryBtnText || !data.primaryBtnLink) {
      return NextResponse.json({
        error: 'Title, description, primary button text, and primary button link are required'
      }, { status: 400 });
    }

    // Prepare the data for creation
    const callToActionData = {
      title: data.title,
      description: data.description,
      primaryBtnText: data.primaryBtnText,
      primaryBtnLink: data.primaryBtnLink,
      secondaryBtnText: data.secondaryBtnText || null,
      secondaryBtnLink: data.secondaryBtnLink || null,
      isActive: data.isActive !== undefined ? data.isActive : true
    };

    try {
      // Create the new call to action
      const callToAction = await prisma.callToAction.create({
        data: callToActionData
      });

      return NextResponse.json({
        ...callToAction,
        usingMockClient: usingMock
      });
    } catch (dbError) {
      console.error('Database error creating call to action:', dbError);

      // If using mock client or there was a database error, return a mock response
      if (usingMock) {
        const mockResponse = {
          id: `mock-${Date.now()}`,
          ...callToActionData,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          usingMockClient: true
        };

        return NextResponse.json(mockResponse);
      }

      // Otherwise, throw the error to be caught by the outer catch
      throw dbError;
    }
  } catch (error) {
    console.error('Error creating call to action:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Failed to create call to action',
      suggestion: 'Please check your database connection and ensure the call_to_action table exists.'
    }, { status: 500 });
  }
}

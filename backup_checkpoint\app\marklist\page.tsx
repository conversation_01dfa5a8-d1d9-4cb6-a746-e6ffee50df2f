"use client"

import React, { useState } from 'react'
import { MarkList } from "../components/MarkList"
import { Sidebar } from "../components/Sidebar"
import { Header } from "../components/Header"

export default function MarkListPage() {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false)
  
  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }
  
  const toggleMobileSidebar = () => {
    setMobileSidebarOpen(!mobileSidebarOpen)
  }

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar 
        isCollapsed={sidebarCollapsed} 
        toggleSidebar={toggleSidebar}
        isMobileOpen={mobileSidebarOpen}
      />
      
      <main className={`main-content ${sidebarCollapsed ? 'sidebar-collapsed' : ''} w-full h-screen overflow-y-auto`}>
        <Header toggleMobileSidebar={toggleMobileSidebar} />
        
        <div className="p-6">
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold">Academic Records</h1>
                <p className="text-gray-500">Manage and track student marks and grades</p>
              </div>
            </div>
            
            <div className="bg-white rounded-lg shadow">
              <MarkList />
            </div>
          </div>
        </div>
      </main>
      
      {/* Overlay for mobile menu */}
      {mobileSidebarOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 md:hidden z-40"
          onClick={toggleMobileSidebar}
        ></div>
      )}
    </div>
  )
} 

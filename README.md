# Responsive Shadcn UI Website

A beautiful modern responsive website built with Next.js and Shadcn UI, designed to work perfectly on mobile, tablet, laptop, desktop, and large screens.

## Features

- Fully responsive design that adapts to all screen sizes
- Modern UI components from Shadcn UI
- Tailwind CSS for styling
- Next.js for React framework
- Mobile-first approach
- Clean and maintainable code structure
- Performance optimized

## Device Support

This website is designed to look great on:
- Mobile phones (small screens)
- Tablets (medium screens)
- Laptops (large screens)
- Desktops (extra large screens)
- Large monitors (2XL screens)

## Getting Started

### Prerequisites

- Node.js 14.x or later

### Installation

1. Clone the repository
```bash
git clone https://github.com/yourusername/responsive-shadcn-website.git
cd responsive-shadcn-website
```

2. Install dependencies
```bash
npm install
```

3. Run the development server
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser to see the result.

## Project Structure

- `/app` - Next.js application
- `/app/components` - Reusable UI components
- `/app/pages` - Application pages
- `/public` - Static files

## Technologies Used

- Next.js
- React
- Tailwind CSS
- Shadcn UI
- Lucide Icons
- TypeScript

## License

MIT 
import React from 'react'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from './ui/alert-dialog'
import { ShieldAlert } from 'lucide-react'

interface PermissionDialogProps {
  isOpen: boolean
  onClose: () => void
  title?: string
  message?: string
  resource?: string
  action?: string
  actionText?: string
}

/**
 * A reusable dialog component for displaying permission denied messages
 * 
 * @param isOpen Whether the dialog is open
 * @param onClose Function to call when the dialog is closed
 * @param title Dialog title
 * @param message Custom message to display
 * @param resource The resource being accessed (e.g., "marks", "attendance")
 * @param action The action being performed (e.g., "view", "edit")
 * @param actionText Text for the action button
 */
export function PermissionDialog({
  isOpen,
  onClose,
  title = 'Permission Denied',
  message,
  resource = 'feature',
  action = 'access',
  actionText = 'Understood'
}: PermissionDialogProps) {
  // Generate a default message if none is provided
  const defaultMessage = `You do not have permission to ${action} ${resource}. Please contact your administrator if you believe this is an error.`;
  const displayMessage = message || defaultMessage;

  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <div className="flex items-center gap-2 text-red-500">
            <ShieldAlert className="h-5 w-5" />
            <AlertDialogTitle>{title}</AlertDialogTitle>
          </div>
          <AlertDialogDescription className="whitespace-pre-line">
            {displayMessage}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogAction onClick={onClose}>{actionText}</AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}

export default PermissionDialog

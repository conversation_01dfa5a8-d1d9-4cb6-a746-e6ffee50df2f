import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

export async function GET() {
  let prisma: PrismaClient | null = null;

  try {
    console.log('Initializing database with sample data...');
    console.log('Database URL:', process.env.DATABASE_URL);

    // Create a new PrismaClient instance specifically for this operation
    prisma = new PrismaClient({
      log: ['query', 'info', 'warn', 'error'],
      datasources: {
        db: {
          url: process.env.DATABASE_URL
        }
      }
    });

    // Try to connect to the database
    console.log('Connecting to database...');
    await prisma.$connect();
    console.log('Database connection successful');

    // First, check if the database tables exist by trying to count records
    try {
      // Check if we already have classes
      const existingClasses = await prisma.class.count();

      // Create sample classes if none exist
      if (existingClasses === 0) {
        console.log('No classes found in the database, creating sample classes...');

        // Create sample classes
        const classes = [
          { name: '1A', totalStudents: 0, totalSubjects: 6, hrTeacherId: null },
          { name: '1B', totalStudents: 0, totalSubjects: 6, hrTeacherId: null },
          { name: '2A', totalStudents: 0, totalSubjects: 7, hrTeacherId: null },
          { name: '2B', totalStudents: 0, totalSubjects: 7, hrTeacherId: null },
        ];

        for (const classData of classes) {
          await prisma.class.create({
            data: classData
          });
        }

        console.log(`Created ${classes.length} sample classes`);
      } else {
        console.log(`Database already has ${existingClasses} classes`);
      }

      // Check if we already have students
      const existingStudents = await prisma.student.count();

      if (existingStudents > 0) {
        console.log(`Database already has ${existingStudents} students`);

        // Even if we don't create new students, update class student counts
        // This is important if we created classes but already had students
        if (existingClasses > 0) {
          console.log('Updating class student counts for existing students...');

          const classes = await prisma.class.findMany();

          for (const classItem of classes) {
            const studentCount = await prisma.student.count({
              where: { className: classItem.name }
            });

            await prisma.class.update({
              where: { id: classItem.id },
              data: { totalStudents: studentCount }
            });

            console.log(`Updated class ${classItem.name} with ${studentCount} students`);
          }
        }
      } else {
        // Create sample students
        const students = [
          {
            sid: '1A1',
            name: 'Alice Cooper',
            className: '1A',
            fatherName: 'John Cooper',
            gfName: 'William Cooper',
            age: 6,
            gender: 'Female'
          },
          {
            sid: '1A2',
            name: 'Bob Wilson',
            className: '1A',
            fatherName: 'Thomas Wilson',
            gfName: 'George Wilson',
            age: 6,
            gender: 'Male'
          },
          {
            sid: '1B1',
            name: 'Charlie Davis',
            className: '1B',
            fatherName: 'Richard Davis',
            gfName: 'Edward Davis',
            age: 6,
            gender: 'Male'
          },
          {
            sid: '2A1',
            name: 'Diana Evans',
            className: '2A',
            fatherName: 'Michael Evans',
            gfName: 'Joseph Evans',
            age: 7,
            gender: 'Female'
          },
        ];

        for (const studentData of students) {
          await prisma.student.create({
            data: studentData
          });
        }

        console.log(`Created ${students.length} sample students`);

        // Update class student counts
        const classes = await prisma.class.findMany();

        for (const classItem of classes) {
          const studentCount = await prisma.student.count({
            where: { className: classItem.name }
          });

          await prisma.class.update({
            where: { id: classItem.id },
            data: { totalStudents: studentCount }
          });

          console.log(`Updated class ${classItem.name} with ${studentCount} students`);
        }
      }

      // Return success response
      return NextResponse.json({
        status: 'success',
        message: 'Database initialized with sample data',
        data: {
          classes: await prisma.class.count(),
          students: await prisma.student.count()
        }
      });
    } catch (tableError) {
      console.error('Error accessing database tables:', tableError);

      // This might be because the database tables don't exist yet
      return NextResponse.json({
        status: 'error',
        message: 'Database tables not found',
        error: tableError instanceof Error ? tableError.message : 'Unknown error',
        suggestion: 'You may need to run prisma migrate or prisma db push to create the database tables first.'
      }, { status: 500 });
    }
  } catch (error) {
    console.error('Error initializing database:', error);

    // Return detailed error information
    return NextResponse.json({
      status: 'error',
      message: 'Failed to initialize database',
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      databaseUrl: process.env.DATABASE_URL ? '***' + process.env.DATABASE_URL.substring(process.env.DATABASE_URL.indexOf('@')) : 'Not set',
      suggestion: 'Please check your database connection string and ensure the database server is running.'
    }, { status: 500 });
  } finally {
    // Clean up the connection
    if (prisma) {
      await prisma.$disconnect().catch(e => console.error('Error disconnecting from database:', e));
    }
  }
}

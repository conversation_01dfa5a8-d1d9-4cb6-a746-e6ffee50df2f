/*
  Warnings:

  - A unique constraint covering the columns `[sid]` on the table `Student` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `sid` to the `Student` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE `student` ADD COLUMN `sid` VARCHAR(191) NULL;

-- Update existing records with generated SIDs
UPDATE `student` s
JOIN (
  SELECT id, className,
         CONCAT(className, ROW_NUMBER() OVER (PARTITION BY className ORDER BY createdAt)) as generated_sid
  FROM `student`
) t ON s.id = t.id
SET s.sid = t.generated_sid;

-- Now make the column NOT NULL
ALTER TABLE `student` MODIFY COLUMN `sid` VARCHAR(191) NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX `Student_sid_key` ON `Student`(`sid`);

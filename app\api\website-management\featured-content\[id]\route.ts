import { NextResponse } from 'next/server'
import { prisma, isModelAvailable } from '@/lib/prisma'

// Define the Feature interface
interface Feature {
  title: string;
  description: string;
}

// Helper function to parse features
function parseFeatures(features: any): Feature[] {
  if (!features) return [];

  try {
    // If features is a string, parse it
    if (typeof features === 'string') {
      return JSON.parse(features);
    }
    // If features is already an array, return it
    else if (Array.isArray(features)) {
      return features;
    }
    // If features is an object, wrap it in an array
    else if (typeof features === 'object') {
      return [features];
    }
  } catch (e) {
    console.error('Error parsing features:', e);
  }

  return [];
}

// GET a specific featured content
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Check if the model is available
    if (!isModelAvailable('featuredContent')) {
      console.warn('FeaturedContent model is not available in Prisma client');
      return NextResponse.json({
        error: 'FeaturedContent model is not available'
      }, { status: 500 });
    }

    const id = params.id;

    const featuredContent = await (prisma as any).featuredContent.findUnique({
      where: { id }
    });

    if (!featuredContent) {
      return NextResponse.json({
        error: 'Featured content not found'
      }, { status: 404 });
    }

    // Parse features if it's a string
    if (featuredContent.features && typeof featuredContent.features === 'string') {
      try {
        featuredContent.features = parseFeatures(featuredContent.features);
      } catch (e) {
        console.error('Error parsing features:', e);
        featuredContent.features = [];
      }
    }

    return NextResponse.json(featuredContent);
  } catch (error) {
    console.error('Error fetching featured content:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Failed to fetch featured content'
    }, { status: 500 });
  }
}

// PUT (update) a featured content
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Check if the model is available
    if (!isModelAvailable('featuredContent')) {
      console.warn('FeaturedContent model is not available in Prisma client');
      return NextResponse.json({
        error: 'FeaturedContent model is not available'
      }, { status: 500 });
    }

    const id = params.id;
    const data = await request.json();

    // Check if the featured content exists
    const existingFeaturedContent = await (prisma as any).featuredContent.findUnique({
      where: { id }
    });

    if (!existingFeaturedContent) {
      return NextResponse.json({
        error: 'Featured content not found'
      }, { status: 404 });
    }

    // Validate features format if provided
    if (data.features) {
      if (!Array.isArray(data.features)) {
        return NextResponse.json({
          error: 'Features must be an array'
        }, { status: 400 });
      }

      // Validate each feature has title and description
      for (const feature of data.features) {
        if (!feature.title || !feature.description) {
          return NextResponse.json({
            error: 'Each feature must have a title and description'
          }, { status: 400 });
        }
      }
    }

    // Convert features to JSON string if it's an array or object
    let featuresData = data.features;
    if (featuresData !== undefined && typeof featuresData === 'object') {
      try {
        featuresData = JSON.stringify(featuresData);
      } catch (e) {
        console.error('Error stringifying features:', e);
        // If stringification fails, keep the existing features
        featuresData = existingFeaturedContent.features;
      }
    }

    // Update the featured content
    const updatedFeaturedContent = await (prisma as any).featuredContent.update({
      where: { id },
      data: {
        title: data.title !== undefined ? data.title : existingFeaturedContent.title,
        description: data.description !== undefined ? data.description : existingFeaturedContent.description,
        imageUrl: data.imageUrl !== undefined ? data.imageUrl : existingFeaturedContent.imageUrl,
        features: featuresData !== undefined ? featuresData : existingFeaturedContent.features,
        isActive: data.isActive !== undefined ? data.isActive : existingFeaturedContent.isActive
      }
    });

    // Parse features for the response
    if (updatedFeaturedContent.features && typeof updatedFeaturedContent.features === 'string') {
      try {
        updatedFeaturedContent.features = parseFeatures(updatedFeaturedContent.features);
      } catch (e) {
        console.error('Error parsing features for response:', e);
        updatedFeaturedContent.features = [];
      }
    }

    return NextResponse.json(updatedFeaturedContent);
  } catch (error) {
    console.error('Error updating featured content:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Failed to update featured content'
    }, { status: 500 });
  }
}

// DELETE a featured content
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Check if the model is available
    if (!isModelAvailable('featuredContent')) {
      console.warn('FeaturedContent model is not available in Prisma client');
      return NextResponse.json({
        error: 'FeaturedContent model is not available'
      }, { status: 500 });
    }

    const id = params.id;

    // Check if the featured content exists
    const existingFeaturedContent = await (prisma as any).featuredContent.findUnique({
      where: { id }
    });

    if (!existingFeaturedContent) {
      return NextResponse.json({
        error: 'Featured content not found'
      }, { status: 404 });
    }

    // Delete the featured content
    await (prisma as any).featuredContent.delete({
      where: { id }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting featured content:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Failed to delete featured content'
    }, { status: 500 });
  }
}

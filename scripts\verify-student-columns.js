// Script to verify the student table columns
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    // Execute a raw query to get the table schema
    const result = await prisma.$queryRaw`DESCRIBE student`;
    console.log('Student table schema:');
    result.forEach(column => {
      console.log(`${column.Field}: ${column.Type} ${column.Null === 'YES' ? '(nullable)' : '(required)'}`);
    });
    
    // Check if academicYear column exists
    const academicYearColumn = result.find(column => column.Field === 'academicYear');
    if (academicYearColumn) {
      console.log('\nacademicYear column exists in the student table!');
    } else {
      console.log('\nacademicYear column does NOT exist in the student table!');
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();

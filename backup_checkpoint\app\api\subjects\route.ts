import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const classId = searchParams.get('classId')
    const className = searchParams.get('className')

    let whereClause = {}

    // Filter by classId if provided
    if (classId) {
      whereClause = { classId }
    } 
    // Filter by className if provided
    else if (className) {
      // First find the class by name
      const classRecord = await prisma.class.findUnique({
        where: { name: className },
        select: { id: true }
      })

      if (classRecord) {
        whereClause = { classId: classRecord.id }
      } else {
        return NextResponse.json([], { status: 200 }) // Return empty array if class not found
      }
    }

    const subjects = await prisma.subject.findMany({
      where: whereClause,
      include: {
        class: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        name: 'asc'
      }
    })

    return NextResponse.json(subjects)
  } catch (error) {
    console.error('Error fetching subjects:', error)
    return NextResponse.json(
      { error: 'Failed to fetch subjects' },
      { status: 500 }
    )
  }
}

export async function POST(request: Request) {
  try {
    const data = await request.json()
    
    // Validate required fields
    if (!data.name || !data.classId) {
      return NextResponse.json(
        { error: 'Subject name and class ID are required' },
        { status: 400 }
      )
    }

    // Check if class exists
    const classExists = await prisma.class.findUnique({
      where: { id: data.classId }
    })

    if (!classExists) {
      return NextResponse.json(
        { error: 'Class not found' },
        { status: 404 }
      )
    }

    // Create the subject
    const subject = await prisma.subject.create({
      data: {
        name: data.name,
        classId: data.classId
      },
      include: {
        class: {
          select: {
            name: true
          }
        }
      }
    })

    // Update the class's total subjects count
    await prisma.class.update({
      where: { id: data.classId },
      data: {
        totalSubjects: {
          increment: 1
        }
      }
    })

    return NextResponse.json(subject)
  } catch (error) {
    console.error('Error creating subject:', error)
    return NextResponse.json(
      { error: 'Failed to create subject' },
      { status: 500 }
    )
  }
}

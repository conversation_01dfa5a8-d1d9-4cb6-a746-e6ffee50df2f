const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function createAdminUser() {
  try {
    console.log('Connecting to database...')
    
    // Check if admin user already exists
    const existingAdmin = await prisma.user.findUnique({
      where: {
        email: '<EMAIL>'
      }
    })
    
    if (existingAdmin) {
      console.log('Admin user already exists:')
      console.log(`- ${existingAdmin.name} (${existingAdmin.email}) - ${existingAdmin.role}`)
      return
    }
    
    console.log('Creating admin user...')
    const hashedPassword = await bcrypt.hash('admin@123', 10)
    
    const adminUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Israel Admin',
        role: 'SUPER_ADMIN',
        status: 'active'
      }
    })
    
    console.log('Admin user created successfully:')
    console.log(`- ${adminUser.name} (${adminUser.email}) - ${adminUser.role}`)
    
  } catch (error) {
    console.error('Error creating admin user:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createAdminUser()

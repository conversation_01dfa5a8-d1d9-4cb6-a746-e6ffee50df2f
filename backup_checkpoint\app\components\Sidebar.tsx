"use client"

import React, { useState } from 'react'
import Link from 'next/link'
import {
  Home as HomeIcon,
  User as UserIcon,
  UserPlus as UserPlusIcon,
  ClipboardList,
  CalendarCheck,
  ListChecks,
  FileText,
  FileDigit,
  ShieldCheck,
  KeyRound,
  Settings,
  Menu as MenuIcon
} from 'lucide-react'

interface SidebarProps {
  isCollapsed: boolean
  toggleSidebar: () => void
  isMobileOpen: boolean
}

// Custom icons that aren't in Lucide
const BootstrapIcon: React.FC = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className="sidebar-icon"
  >
    <path d="M2 12a5 5 0 0 0 5 5 8 8 0 0 1 5 2 8 8 0 0 1 5-2 5 5 0 0 0 5-5V7H2Z"></path>
    <path d="M6 11V9h12v2"></path>
  </svg>
)

const AppsIcon: React.FC = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className="sidebar-icon"
  >
    <rect x="3" y="3" width="6" height="6" rx="1"></rect>
    <rect x="15" y="3" width="6" height="6" rx="1"></rect>
    <rect x="3" y="15" width="6" height="6" rx="1"></rect>
    <rect x="15" y="15" width="6" height="6" rx="1"></rect>
  </svg>
)

export const Sidebar: React.FC<SidebarProps> = ({
  isCollapsed,
  toggleSidebar,
  isMobileOpen
}) => {
  const [activeItem, setActiveItem] = useState('dashboard')

  const sidebarClass = `sidebar ${isCollapsed ? 'collapsed' : ''} ${isMobileOpen ? 'mobile-open' : ''}`

  return (
    <aside className={sidebarClass}>
      <div className="py-4 px-4 flex items-center justify-between border-b border-white/10">
        <div className="flex items-center">
          <div className="w-8 h-8 rounded-md bg-white text-akademi-red flex items-center justify-center font-bold text-xl mr-3">
            A
          </div>
          {!isCollapsed && <h1 className="text-xl font-bold sidebar-text">Alfalah</h1>}
        </div>
        <button
          onClick={toggleSidebar}
          className="text-white p-1 rounded-md hover:bg-white/10"
        >
          <MenuIcon size={20} />
        </button>
      </div>

      <nav className="py-4">
        <Link href="/" className="block">
        <div
          className={`sidebar-item ${activeItem === 'dashboard' ? 'active' : ''}`}
          onClick={() => setActiveItem('dashboard')}
        >
          <HomeIcon size={20} className="sidebar-icon" />
          {!isCollapsed && <span className="sidebar-text">Dashboard</span>}
        </div>
        </Link>

        <Link href="/students" className="block">
        <div
          className={`sidebar-item ${activeItem === 'students' ? 'active' : ''}`}
          onClick={() => setActiveItem('students')}
        >
          <UserIcon size={20} className="sidebar-icon" />
          {!isCollapsed && <span className="sidebar-text">Students</span>}
        </div>
        </Link>

        <Link href="/teachers" className="block">
        <div
          className={`sidebar-item ${activeItem === 'teachers' ? 'active' : ''}`}
          onClick={() => setActiveItem('teachers')}
        >
          <UserPlusIcon size={20} className="sidebar-icon" />
          {!isCollapsed && <span className="sidebar-text">Teachers</span>}
        </div>
        </Link>



        <Link href="/classes" className="block">
        <div
          className={`sidebar-item ${activeItem === 'class' ? 'active' : ''}`}
          onClick={() => setActiveItem('class')}
        >
          <ClipboardList size={20} className="sidebar-icon" />
          {!isCollapsed && <span className="sidebar-text">Class</span>}
        </div>
        </Link>

        <Link href="/attendance" className="block">
        <div
          className={`sidebar-item ${activeItem === 'attendance' ? 'active' : ''}`}
          onClick={() => setActiveItem('attendance')}
        >
          <CalendarCheck size={20} className="sidebar-icon" />
          {!isCollapsed && <span className="sidebar-text">Attendance</span>}
        </div>
        </Link>

        <Link href="/marklist" className="block">
        <div
          className={`sidebar-item ${activeItem === 'marklist' ? 'active' : ''}`}
          onClick={() => setActiveItem('marklist')}
        >
          <ListChecks size={20} className="sidebar-icon" />
          {!isCollapsed && <span className="sidebar-text">Mark List</span>}
        </div>
        </Link>

        <Link href="/reportcard" className="block">
        <div
          className={`sidebar-item ${activeItem === 'reportcard' ? 'active' : ''}`}
          onClick={() => setActiveItem('reportcard')}
        >
          <FileText size={20} className="sidebar-icon" />
          {!isCollapsed && <span className="sidebar-text">Report Card</span>}
        </div>
        </Link>



        <Link href="/roles" className="block">
        <div
          className={`sidebar-item ${activeItem === 'role' ? 'active' : ''}`}
          onClick={() => setActiveItem('role')}
        >
          <ShieldCheck size={20} className="sidebar-icon" />
          {!isCollapsed && <span className="sidebar-text">Role</span>}
        </div>
        </Link>

        <Link href="/account-reset" className="block">
        <div
          className={`sidebar-item ${activeItem === 'accountreset' ? 'active' : ''}`}
          onClick={() => setActiveItem('accountreset')}
        >
          <KeyRound size={20} className="sidebar-icon" />
          {!isCollapsed && <span className="sidebar-text">Account Reset</span>}
        </div>
        </Link>

        <Link href="/settings" className="block">
        <div
          className={`sidebar-item ${activeItem === 'setting' ? 'active' : ''}`}
          onClick={() => setActiveItem('setting')}
        >
          <Settings size={20} className="sidebar-icon" />
          {!isCollapsed && <span className="sidebar-text">Setting</span>}
        </div>
        </Link>
      </nav>
    </aside>
  )
}
import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET endpoint to fetch a teacher's assignments
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const teacherId = params.id

    // Check if teacher exists
    const teacher = await prisma.teacher.findUnique({
      where: { id: teacherId },
    })

    if (!teacher) {
      return NextResponse.json(
        { error: 'Teacher not found' },
        { status: 404 }
      )
    }

    // Get HR classes (classes where this teacher is the homeroom teacher)
    const hrClasses = await prisma.class.findMany({
      where: { hrTeacherId: teacherId },
      select: { name: true },
    })

    // Get assigned classes (classes where this teacher teaches)
    const classTeacherRecords = await prisma.classTeacher.findMany({
      where: { teacherId },
      include: {
        class: {
          select: { name: true },
        },
      },
    })

    // Extract class names
    const hrClassNames = hrClasses.map(cls => cls.name)
    const assignedClassNames = classTeacherRecords.map(record => record.class.name)
      // Filter out HR classes from assigned classes to avoid duplication
      .filter(name => !hrClassNames.includes(name))

    return NextResponse.json({
      teacherId,
      hrClasses: hrClassNames,
      assignedClasses: assignedClassNames,
    })
  } catch (error) {
    console.error('Error fetching teacher assignments:', error)
    return NextResponse.json(
      { error: 'Failed to fetch teacher assignments' },
      { status: 500 }
    )
  }
}

// PUT endpoint to update a teacher's assignments
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const teacherId = params.id
    const { hrClasses, assignedClasses } = await request.json()

    // Validate input
    if (!Array.isArray(hrClasses) || !Array.isArray(assignedClasses)) {
      return NextResponse.json(
        { error: 'Invalid input format' },
        { status: 400 }
      )
    }

    // Check if teacher exists
    const teacher = await prisma.teacher.findUnique({
      where: { id: teacherId },
    })

    if (!teacher) {
      return NextResponse.json(
        { error: 'Teacher not found' },
        { status: 404 }
      )
    }

    // Start a transaction to ensure all operations succeed or fail together
    const result = await prisma.$transaction(async (tx) => {
      // 1. Update HR classes
      // First, get all classes where this teacher is currently the HR teacher
      const currentHrClasses = await tx.class.findMany({
        where: { hrTeacherId: teacherId },
        select: { id: true, name: true },
      })

      // Remove HR teacher from classes that are no longer assigned
      for (const cls of currentHrClasses) {
        if (!hrClasses.includes(cls.name)) {
          await tx.class.update({
            where: { id: cls.id },
            data: { hrTeacherId: null },
          })
        }
      }

      // Assign HR teacher to new classes
      for (const className of hrClasses) {
        if (!currentHrClasses.some(cls => cls.name === className)) {
          await tx.class.updateMany({
            where: { name: className },
            data: { hrTeacherId: teacherId },
          })
        }
      }

      // 2. Update assigned classes
      // First, get all current class-teacher relationships
      const currentAssignments = await tx.classTeacher.findMany({
        where: { teacherId },
        include: {
          class: {
            select: { name: true },
          },
        },
      })

      // Get all classes for lookup
      const allClasses = await tx.class.findMany({
        select: { id: true, name: true },
      })

      // Create a map of class names to IDs for easier lookup
      const classNameToId = new Map(allClasses.map(cls => [cls.name, cls.id]))

      // Remove teacher from classes that are no longer assigned
      const currentAssignedClassNames = currentAssignments.map(a => a.class.name)
      const classesToRemove = currentAssignedClassNames.filter(
        name => !assignedClasses.includes(name) && !hrClasses.includes(name)
      )

      for (const className of classesToRemove) {
        const classId = classNameToId.get(className)
        if (classId) {
          await tx.classTeacher.deleteMany({
            where: {
              teacherId,
              classId,
            },
          })
        }
      }

      // Assign teacher to new classes
      const classesToAdd = assignedClasses.filter(
        name => !currentAssignedClassNames.includes(name)
      )

      for (const className of classesToAdd) {
        const classId = classNameToId.get(className)
        if (classId) {
          await tx.classTeacher.create({
            data: {
              teacherId,
              classId,
            },
          })
        }
      }

      // Return the updated assignments
      return {
        teacherId,
        hrClasses,
        assignedClasses,
      }
    })

    return NextResponse.json(result)
  } catch (error) {
    console.error('Error updating teacher assignments:', error)
    return NextResponse.json(
      { error: 'Failed to update teacher assignments' },
      { status: 500 }
    )
  }
}

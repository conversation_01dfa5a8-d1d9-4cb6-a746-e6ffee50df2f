"use client"

import React, { useState } from 'react'

const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']

// Generate random data for the chart
const generateRandomData = (min: number, max: number, count: number) => {
  return Array.from({ length: count }).map(() => 
    Math.floor(Math.random() * (max - min + 1)) + min
  )
}

const numberProjectsData = generateRandomData(40, 100, 12)
const revenueData = generateRandomData(30, 70, 12)
const activeProjectsData = generateRandomData(20, 50, 12)

// Chart scale
const chartHeight = 200
const maxValue = 100

export const SchoolOverview: React.FC = () => {
  const [period, setPeriod] = useState('week')
  
  // Tooltip state
  const [tooltip, setTooltip] = useState({
    visible: false,
    month: '',
    data: { projects: 0, revenue: 0, active: 0 },
    position: { x: 0, y: 0 }
  })
  
  const handleBarMouseEnter = (month: string, index: number, x: number, y: number) => {
    setTooltip({
      visible: true,
      month,
      data: {
        projects: numberProjectsData[index],
        revenue: revenueData[index],
        active: activeProjectsData[index]
      },
      position: { x, y }
    })
  }
  
  const handleBarMouseLeave = () => {
    setTooltip({
      ...tooltip,
      visible: false
    })
  }
  
  return (
    <div className="chart-container">
      <div className="flex items-center justify-between mb-6">
        <h2 className="chart-title">School Overview</h2>
        <div className="flex items-center space-x-2">
          <button
            className={`px-3 py-1 text-sm rounded-md ${period === 'week' ? 'bg-red-100 text-red-600' : 'bg-gray-100 text-gray-600'}`}
            onClick={() => setPeriod('week')}
          >
            Week
          </button>
          <button
            className={`px-3 py-1 text-sm rounded-md ${period === 'month' ? 'bg-red-100 text-red-600' : 'bg-gray-100 text-gray-600'}`}
            onClick={() => setPeriod('month')}
          >
            Month
          </button>
          <button
            className={`px-3 py-1 text-sm rounded-md ${period === 'year' ? 'bg-red-100 text-red-600' : 'bg-gray-100 text-gray-600'}`}
            onClick={() => setPeriod('year')}
          >
            Year
          </button>
          <button
            className={`px-3 py-1 text-sm rounded-md ${period === 'all' ? 'bg-red-100 text-red-600' : 'bg-gray-100 text-gray-600'}`}
            onClick={() => setPeriod('all')}
          >
            All
          </button>
        </div>
      </div>
      
      <div className="relative h-64">
        {/* Y-axis labels */}
        <div className="absolute left-0 top-0 h-full flex flex-col justify-between">
          <span className="text-xs text-gray-500">100</span>
          <span className="text-xs text-gray-500">75</span>
          <span className="text-xs text-gray-500">50</span>
          <span className="text-xs text-gray-500">25</span>
          <span className="text-xs text-gray-500">0</span>
        </div>
        
        {/* Chart SVG */}
        <div className="ml-10 h-full">
          <svg width="100%" height="100%" viewBox="0 0 800 220" preserveAspectRatio="none">
            {/* Horizontal grid lines */}
            <line x1="0" y1="0" x2="800" y2="0" stroke="#eee" strokeWidth="1" />
            <line x1="0" y1="50" x2="800" y2="50" stroke="#eee" strokeWidth="1" />
            <line x1="0" y1="100" x2="800" y2="100" stroke="#eee" strokeWidth="1" />
            <line x1="0" y1="150" x2="800" y2="150" stroke="#eee" strokeWidth="1" />
            <line x1="0" y1="200" x2="800" y2="200" stroke="#eee" strokeWidth="1" />
            
            {/* Revenue line */}
            <path
              d={`M${10},${chartHeight - (revenueData[0] / maxValue) * chartHeight} ${revenueData.map((value, i) => 
                `L${i * 65 + 10},${chartHeight - (value / maxValue) * chartHeight}`).join(' ')}`}
              fill="none"
              stroke="#4ade80"
              strokeWidth="2"
            />
            <path
              d={`M${10},${chartHeight - (revenueData[0] / maxValue) * chartHeight} ${revenueData.map((value, i) => 
                `L${i * 65 + 10},${chartHeight - (value / maxValue) * chartHeight}`).join(' ')} L${10 + (revenueData.length - 1) * 65},${chartHeight} L10,${chartHeight} Z`}
              fill="url(#revenueGradient)"
              fillOpacity="0.2"
            />
            
            {/* Active projects line (dashed) */}
            <path
              d={`M${10},${chartHeight - (activeProjectsData[0] / maxValue) * chartHeight} ${activeProjectsData.map((value, i) => 
                `L${i * 65 + 10},${chartHeight - (value / maxValue) * chartHeight}`).join(' ')}`}
              fill="none"
              stroke="#ef4444"
              strokeWidth="2"
              strokeDasharray="5,5"
            />
            
            {/* Bar charts for projects */}
            <defs>
              <linearGradient id="revenueGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" stopColor="#4ade80" />
                <stop offset="100%" stopColor="#4ade8000" />
              </linearGradient>
            </defs>
            
            {numberProjectsData.map((value, i) => (
              <rect
                key={i}
                x={i * 65 + 3}
                y={chartHeight - (value / maxValue) * chartHeight}
                width={14}
                height={(value / maxValue) * chartHeight}
                fill="#ef4444"
                onMouseEnter={(e) => {
                  const rect = e.currentTarget.getBoundingClientRect()
                  handleBarMouseEnter(months[i], i, rect.x, rect.y)
                }}
                onMouseLeave={handleBarMouseLeave}
              />
            ))}
          </svg>
          
          {/* Tooltip */}
          {tooltip.visible && (
            <div 
              className="absolute bg-white p-2 rounded-md shadow-lg text-xs border border-gray-200 z-10"
              style={{ 
                top: tooltip.position.y - 100, 
                left: tooltip.position.x - 20
              }}
            >
              <div className="font-semibold mb-1">{tooltip.month}</div>
              <div className="flex items-center mb-1">
                <span className="w-2 h-2 bg-red-500 rounded-full mr-1"></span>
                <span>Number of Projects: {tooltip.data.projects} points</span>
              </div>
              <div className="flex items-center mb-1">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-1"></span>
                <span>Revenue: {tooltip.data.revenue} points</span>
              </div>
              <div className="flex items-center">
                <span className="w-2 h-2 bg-red-400 rounded-full mr-1"></span>
                <span>Active Projects: {tooltip.data.active} points</span>
              </div>
            </div>
          )}
        </div>
        
        {/* X-axis labels */}
        <div className="ml-10 mt-2 flex justify-between pr-5">
          {months.map(month => (
            <span key={month} className="text-xs text-gray-500">{month}</span>
          ))}
        </div>
        
        {/* Legend */}
        <div className="mt-4 flex items-center justify-center space-x-6">
          <div className="flex items-center">
            <span className="w-2 h-2 bg-red-500 rounded-full mr-2"></span>
            <span className="text-xs">Number of Projects</span>
          </div>
          <div className="flex items-center">
            <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
            <span className="text-xs">Revenue</span>
          </div>
          <div className="flex items-center">
            <span className="w-2 h-2 bg-red-400 rounded-full mr-2"></span>
            <span className="text-xs">Active Projects</span>
          </div>
        </div>
      </div>
    </div>
  )
} 
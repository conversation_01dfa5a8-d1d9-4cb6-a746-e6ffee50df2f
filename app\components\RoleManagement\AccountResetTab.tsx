'use client'

import React, { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import {
  Table, TableBody, TableCell, TableHead,
  TableHeader, TableRow
} from '@/app/components/ui/table'
import { Button } from '@/app/components/ui/button'
import { Input } from '@/app/components/ui/input'
import { Badge } from '@/app/components/ui/badge'
import {
  RecursionSafeDialog,
  RecursionSafeDialogContent,
  RecursionSafeDialogHeader,
  RecursionSafeDialogTitle,
  RecursionSafeDialogDescription,
  RecursionSafeDialogFooter
} from '@/app/components/RecursionSafeDialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/app/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/app/components/ui/card'
import { 
  Search, RefreshCw, KeyRound, Mail, AlertTriangle, 
  CheckCircle, Clock, User, Shield, Loader2
} from 'lucide-react'
import { useToast } from '@/app/components/ui/use-toast'

interface User {
  id: string
  name: string
  email: string
  role: string
  status: string
  lastLogin?: string
  createdAt: string
  updatedAt: string
}

interface PasswordResetRequest {
  id: string
  userId: string
  user: User
  status: 'pending' | 'completed' | 'expired'
  requestedAt: string
  completedAt?: string
  requestedBy: string
  reason: string
  method: 'email' | 'admin_reset' | 'temporary_password'
}

export function AccountResetTab() {
  const { toast } = useToast()
  const queryClient = useQueryClient()
  
  // State management
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [resetDialogOpen, setResetDialogOpen] = useState(false)
  const [resetMethod, setResetMethod] = useState<'email' | 'temporary_password'>('email')
  const [resetReason, setResetReason] = useState('')
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false)
  const [pendingReset, setPendingReset] = useState<{
    userId: string
    userName: string
    method: string
    reason: string
  } | null>(null)

  // Fetch users
  const { data: users = [], isLoading: isLoadingUsers, refetch: refetchUsers } = useQuery({
    queryKey: ['users'],
    queryFn: async () => {
      const response = await fetch('/api/users')
      if (!response.ok) {
        throw new Error('Failed to fetch users')
      }
      return response.json()
    },
    staleTime: 30000, // 30 seconds
  })

  // Fetch password reset requests
  const { data: resetRequests = [], isLoading: isLoadingResets, refetch: refetchResets } = useQuery({
    queryKey: ['password-reset-requests'],
    queryFn: async () => {
      const response = await fetch('/api/admin/password-reset-requests')
      if (!response.ok) {
        // If endpoint doesn't exist, return empty array
        if (response.status === 404) return []
        throw new Error('Failed to fetch reset requests')
      }
      return response.json()
    },
    staleTime: 30000,
  })

  // Password reset mutation
  const resetPasswordMutation = useMutation({
    mutationFn: async ({ userId, method, reason }: { 
      userId: string
      method: 'email' | 'temporary_password'
      reason: string 
    }) => {
      const response = await fetch(`/api/users/${userId}/reset-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ method, reason })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to reset password')
      }

      return response.json()
    },
    onSuccess: (_, variables) => {
      // Invalidate and refetch relevant queries
      queryClient.invalidateQueries({ queryKey: ['users'] })
      queryClient.invalidateQueries({ queryKey: ['password-reset-requests'] })
      
      // Show success message
      toast({
        title: "Password Reset Initiated",
        description: variables.method === 'email' 
          ? `Password reset email sent to ${selectedUser?.email}`
          : `Temporary password generated for ${selectedUser?.name}`,
        variant: "default",
      })

      // Close dialogs and reset state
      setResetDialogOpen(false)
      setConfirmDialogOpen(false)
      setSelectedUser(null)
      setPendingReset(null)
      setResetReason('')
    },
    onError: (error: Error) => {
      toast({
        title: "Reset Failed",
        description: error.message,
        variant: "destructive",
      })
    }
  })

  // Filter users based on search term and status
  const filteredUsers = users.filter((user: User) => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase())
    
    if (statusFilter === 'all') return matchesSearch
    return matchesSearch && user.status === statusFilter
  })

  // Filter reset requests based on search term
  const filteredResetRequests = resetRequests.filter((request: PasswordResetRequest) => {
    return request.user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
           request.user.email.toLowerCase().includes(searchTerm.toLowerCase())
  })

  // Handle password reset initiation
  const handlePasswordReset = (user: User) => {
    setSelectedUser(user)
    setResetDialogOpen(true)
  }

  // Handle reset confirmation
  const handleResetConfirm = () => {
    if (!selectedUser || !resetReason.trim()) {
      toast({
        title: "Missing Information",
        description: "Please provide a reason for the password reset.",
        variant: "destructive",
      })
      return
    }

    setPendingReset({
      userId: selectedUser.id,
      userName: selectedUser.name,
      method: resetMethod,
      reason: resetReason.trim()
    })
    setResetDialogOpen(false)
    setConfirmDialogOpen(true)
  }

  // Execute the password reset
  const executePasswordReset = () => {
    if (pendingReset) {
      resetPasswordMutation.mutate({
        userId: pendingReset.userId,
        method: resetMethod,
        reason: pendingReset.reason
      })
    }
  }

  // Render status badge
  const renderStatusBadge = (status: string) => {
    const statusConfig = {
      active: { color: 'bg-green-100 text-green-800', label: 'Active' },
      inactive: { color: 'bg-gray-100 text-gray-800', label: 'Inactive' },
      suspended: { color: 'bg-red-100 text-red-800', label: 'Suspended' },
      pending: { color: 'bg-yellow-100 text-yellow-800', label: 'Pending' }
    }
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.inactive
    
    return (
      <Badge className={`${config.color} border-0`}>
        {config.label}
      </Badge>
    )
  }

  // Render role badge
  const renderRoleBadge = (role: string) => {
    const roleConfig = {
      SUPER_ADMIN: { color: 'bg-red-100 text-red-800', icon: Shield },
      ADMIN: { color: 'bg-purple-100 text-purple-800', icon: Shield },
      SUPERVISOR: { color: 'bg-blue-100 text-blue-800', icon: Shield },
      ACCOUNTANT: { color: 'bg-yellow-100 text-yellow-800', icon: Shield },
      TEACHER: { color: 'bg-green-100 text-green-800', icon: User },
      PARENT: { color: 'bg-pink-100 text-pink-800', icon: User }
    }
    
    const config = roleConfig[role as keyof typeof roleConfig] || { 
      color: 'bg-gray-100 text-gray-800', 
      icon: User 
    }
    
    return (
      <Badge className={`${config.color} border-0 flex items-center gap-1`}>
        <config.icon className="h-3 w-3" />
        {role}
      </Badge>
    )
  }

  // Render reset request status badge
  const renderResetStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: 'bg-yellow-100 text-yellow-800', icon: Clock, label: 'Pending' },
      completed: { color: 'bg-green-100 text-green-800', icon: CheckCircle, label: 'Completed' },
      expired: { color: 'bg-red-100 text-red-800', icon: AlertTriangle, label: 'Expired' }
    }
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending
    
    return (
      <Badge className={`${config.color} border-0 flex items-center gap-1`}>
        <config.icon className="h-3 w-3" />
        {config.label}
      </Badge>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header with search and filters */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex flex-col sm:flex-row gap-4 flex-1">
          <div className="relative w-full sm:w-80">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search users by name or email..."
              className="pl-10"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-full sm:w-48">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
              <SelectItem value="suspended">Suspended</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <Button
          variant="outline"
          onClick={() => {
            refetchUsers()
            refetchResets()
          }}
          disabled={isLoadingUsers || isLoadingResets}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${(isLoadingUsers || isLoadingResets) ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Users Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <KeyRound className="h-5 w-5" />
            User Accounts
          </CardTitle>
          <CardDescription>
            Select a user to initiate password reset. All reset actions are logged for security audit.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>User</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Last Login</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoadingUsers ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8">
                      <div className="flex items-center justify-center gap-2">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Loading users...
                      </div>
                    </TableCell>
                  </TableRow>
                ) : filteredUsers.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8 text-gray-500">
                      No users found matching your criteria
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredUsers.map((user: User) => (
                    <TableRow key={user.id}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center text-sm font-medium">
                            {user.name.charAt(0).toUpperCase()}
                          </div>
                          <div>
                            <div className="font-medium">{user.name}</div>
                            <div className="text-sm text-gray-500">ID: {user.id.slice(0, 8)}...</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{user.email}</TableCell>
                      <TableCell>{renderRoleBadge(user.role)}</TableCell>
                      <TableCell>{renderStatusBadge(user.status)}</TableCell>
                      <TableCell>
                        {user.lastLogin ? (
                          <div className="text-sm">
                            {new Date(user.lastLogin).toLocaleDateString()}
                          </div>
                        ) : (
                          <span className="text-gray-400 text-sm">Never</span>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePasswordReset(user)}
                          disabled={resetPasswordMutation.isPending}
                          className="text-orange-600 border-orange-200 hover:bg-orange-50"
                        >
                          <KeyRound className="h-4 w-4 mr-2" />
                          Reset Password
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Recent Reset Requests */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Recent Reset Requests
          </CardTitle>
          <CardDescription>
            Track password reset requests and their status for audit purposes.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>User</TableHead>
                  <TableHead>Method</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Requested</TableHead>
                  <TableHead>Reason</TableHead>
                  <TableHead>Requested By</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoadingResets ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8">
                      <div className="flex items-center justify-center gap-2">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Loading reset requests...
                      </div>
                    </TableCell>
                  </TableRow>
                ) : filteredResetRequests.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8 text-gray-500">
                      No recent reset requests found
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredResetRequests.slice(0, 10).map((request: PasswordResetRequest) => (
                    <TableRow key={request.id}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <div className="h-6 w-6 rounded-full bg-gray-200 flex items-center justify-center text-xs font-medium">
                            {request.user.name.charAt(0).toUpperCase()}
                          </div>
                          <div>
                            <div className="font-medium text-sm">{request.user.name}</div>
                            <div className="text-xs text-gray-500">{request.user.email}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className="flex items-center gap-1 w-fit">
                          {request.method === 'email' ? (
                            <Mail className="h-3 w-3" />
                          ) : (
                            <KeyRound className="h-3 w-3" />
                          )}
                          {request.method === 'email' ? 'Email Reset' : 'Temp Password'}
                        </Badge>
                      </TableCell>
                      <TableCell>{renderResetStatusBadge(request.status)}</TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {new Date(request.requestedAt).toLocaleDateString()}
                        </div>
                        <div className="text-xs text-gray-500">
                          {new Date(request.requestedAt).toLocaleTimeString()}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm max-w-48 truncate" title={request.reason}>
                          {request.reason}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm font-medium">{request.requestedBy}</div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Password Reset Dialog */}
      <RecursionSafeDialog
        open={resetDialogOpen}
        onOpenChange={setResetDialogOpen}
        maxWidth="max-w-md"
      >
        <RecursionSafeDialogHeader>
          <RecursionSafeDialogTitle className="flex items-center gap-2">
            <KeyRound className="h-5 w-5 text-orange-600" />
            Reset Password
          </RecursionSafeDialogTitle>
          <RecursionSafeDialogDescription>
            Reset password for <strong>{selectedUser?.name}</strong> ({selectedUser?.email})
          </RecursionSafeDialogDescription>
        </RecursionSafeDialogHeader>

          <div className="space-y-4 py-4">
            <div>
              <label className="text-sm font-medium mb-2 block">Reset Method</label>
              <Select value={resetMethod} onValueChange={(value: 'email' | 'temporary_password') => setResetMethod(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="email">
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4" />
                      Send Reset Email
                    </div>
                  </SelectItem>
                  <SelectItem value="temporary_password">
                    <div className="flex items-center gap-2">
                      <KeyRound className="h-4 w-4" />
                      Generate Temporary Password
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
              <p className="text-xs text-gray-500 mt-1">
                {resetMethod === 'email'
                  ? 'User will receive an email with reset instructions'
                  : 'A temporary password will be generated and displayed'
                }
              </p>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">Reason for Reset</label>
              <Input
                placeholder="e.g., User forgot password, security concern..."
                value={resetReason}
                onChange={(e) => setResetReason(e.target.value)}
                className="w-full"
              />
              <p className="text-xs text-gray-500 mt-1">
                This will be logged for audit purposes
              </p>
            </div>
          </div>

        <RecursionSafeDialogFooter>
          <Button variant="outline" onClick={() => setResetDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleResetConfirm}
            disabled={!resetReason.trim()}
            className="bg-orange-600 hover:bg-orange-700"
          >
            Continue
          </Button>
        </RecursionSafeDialogFooter>
      </RecursionSafeDialog>

      {/* Confirmation Dialog */}
      <RecursionSafeDialog
        open={confirmDialogOpen}
        onOpenChange={setConfirmDialogOpen}
        maxWidth="max-w-lg"
      >
        <RecursionSafeDialogHeader>
          <RecursionSafeDialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-orange-600" />
            Confirm Password Reset
          </RecursionSafeDialogTitle>
          <RecursionSafeDialogDescription>
            Are you sure you want to reset the password for <strong>{pendingReset?.userName}</strong>?
            <br /><br />
            <strong>Method:</strong> {resetMethod === 'email' ? 'Email Reset Link' : 'Temporary Password'}
            <br />
            <strong>Reason:</strong> {pendingReset?.reason}
            <br /><br />
            This action will be logged and cannot be undone.
          </RecursionSafeDialogDescription>
        </RecursionSafeDialogHeader>
        <RecursionSafeDialogFooter>
          <Button variant="outline" onClick={() => setConfirmDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={executePasswordReset}
            disabled={resetPasswordMutation.isPending}
            className="bg-red-600 hover:bg-red-700"
          >
            {resetPasswordMutation.isPending ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Resetting...
              </>
            ) : (
              'Reset Password'
            )}
          </Button>
        </RecursionSafeDialogFooter>
      </RecursionSafeDialog>
    </div>
  )
}

"use client"

import React from 'react'
import DashboardLayout from '../components/DashboardLayout'
import RoleGuard from '../components/RoleGuard'

// Access denied component
const AccessDenied = () => (
  <div className="flex items-center justify-center h-full">
    <div className="text-center">
      <h1 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h1>
      <p className="text-gray-600">You don't have permission to access this page.</p>
    </div>
  </div>
)

// Page content component
interface PageContentProps {
  title: string
  description: string
  children: React.ReactNode
}

const PageContent = ({ title, description, children }: PageContentProps) => (
  <>
    <div className="mb-6">
      <h1 className="text-2xl font-bold text-gray-800 dark:text-white">{title}</h1>
      <p className="text-sm text-gray-500 dark:text-gray-400">{description}</p>
    </div>
    {children}
  </>
)

// Protected page component
interface ProtectedPageProps {
  // The sidebar item ID that corresponds to this page
  sidebarItem: string
  // Optional array of roles that can access this page
  roles?: string[]
  // Whether to redirect to dashboard if access is denied
  redirectOnAccessDenied?: boolean
  // Page title
  title: string
  // Page description
  description: string
  // Page content
  children: React.ReactNode
}

export default function ProtectedPage({
  sidebarItem,
  roles,
  redirectOnAccessDenied = true,
  title,
  description,
  children
}: ProtectedPageProps) {
  return (
    <DashboardLayout>
      <RoleGuard
        sidebarItems={sidebarItem}
        roles={roles}
        redirectOnAccessDenied={redirectOnAccessDenied}
        fallback={<AccessDenied />}
      >
        <PageContent title={title} description={description}>
          {children}
        </PageContent>
      </RoleGuard>
    </DashboardLayout>
  )
}

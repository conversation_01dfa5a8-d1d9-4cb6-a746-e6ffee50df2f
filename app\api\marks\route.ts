import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { cookies } from 'next/headers'
import { verifyJWT } from '@/lib/jwt'
import { canTeacherViewAttendance, canTeacherAddMarks } from '@/app/lib/teacher-permissions'
import { createPermissionDeniedResponse, PermissionErrors } from '@/app/lib/permission-errors'

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const studentId = searchParams.get('studentId')
    const className = searchParams.get('className')
    const subject = searchParams.get('subject')
    const term = searchParams.get('term')
    const academicYear = searchParams.get('academicYear')
    const forVisualizer = searchParams.get('visualizer') === 'true'

    // Verify authentication
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    }

    try {
      // Verify the token
      const decoded = await verifyJWT(token)

      // If the user is a teacher, check if they have permission to view marks for this class
      if (decoded.role === 'TEACHER' && className && className !== 'all') {
        // Get the teacher ID from the email
        const teacher = await prisma.teacher.findUnique({
          where: { email: decoded.email },
          select: { id: true }
        })

        if (!teacher) {
          return NextResponse.json(
            { error: 'Teacher not found' },
            { status: 404 }
          )
        }

        // Check if the teacher has permission to view marks for this class
        const classObj = await prisma.class.findUnique({
          where: { name: className },
          select: { id: true }
        })

        if (!classObj) {
          return NextResponse.json(
            { error: 'Class not found' },
            { status: 404 }
          )
        }

        const hasPermission = await canTeacherViewAttendance(teacher.id, classObj.id)

        if (!hasPermission) {
          return createPermissionDeniedResponse('view', 'marks', `class "${className}"`)
        }
      }

      // Build where clause based on provided filters
      const whereClause: any = {}

      if (studentId) whereClause.studentId = studentId
      if (className && className !== 'all') whereClause.className = className
      if (subject && subject !== 'all') whereClause.subject = subject
      if (term) whereClause.term = term

      // Note: academicYear filtering is not implemented in the database schema
      // The Mark model doesn't have an academicYear field
      // Academic year filtering would need to be done based on createdAt date range

      const marks = await prisma.mark.findMany({
        where: whereClause,
        include: {
          student: {
            select: {
              name: true,
              sid: true,
              gender: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      })

      // Format the response to include student name and match UI expectations
      const formattedMarks = marks.map(mark => {
        // Use the actual academicYear from database, fallback to calculated if not present
        const academicYear = mark.academicYear || (new Date(mark.createdAt).getFullYear() + '-' + (new Date(mark.createdAt).getFullYear() + 1))

        if (forVisualizer) {
          return {
            id: mark.id,
            studentId: mark.studentId,
            studentName: mark.student.name,
            gender: mark.student?.gender || 'unknown',
            className: mark.className,
            subject: mark.subject,
            academicYear: academicYear,
            examType: mark.term,
            mark: mark.marks
          }
        }

        return {
          id: mark.id,
          studentId: mark.studentId,
          studentName: mark.student.name,
          studentSid: mark.student.sid,
          subject: mark.subject,
          obtainedMarks: mark.marks, // Map marks to obtainedMarks for UI
          totalMarks: mark.totalMarks || 100, // Use actual totalMarks from database
          class: mark.className, // Map className to class for UI
          term: mark.term,
          academicYear: academicYear, // Use actual academicYear from database

          dateRecorded: mark.createdAt,
          recordedBy: 'Teacher', // Default recorded by
          createdAt: mark.createdAt,
          updatedAt: mark.updatedAt
        }
      })

      // Return with cache-busting headers to ensure fresh data
      const response = NextResponse.json(formattedMarks)
      response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate')
      response.headers.set('Pragma', 'no-cache')
      response.headers.set('Expires', '0')
      return response
    } catch (error) {
      console.error('Error fetching marks:', error)
      return NextResponse.json(
        { error: 'Failed to fetch marks' },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Error in GET request:', error)
    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    )
  }
}

export async function POST(request: Request) {
  try {
    const data = await request.json()

    // Validate required fields
    if (!data.studentId || !data.subject || !data.class || !data.term || (data.marks === undefined && data.obtainedMarks === undefined)) {
      return NextResponse.json(
        { error: 'Missing required fields', details: JSON.stringify(data) },
        { status: 400 }
      )
    }

    // Map fields to expected names
    const className = data.className || data.class;
    const marks = data.marks !== undefined ? data.marks : data.obtainedMarks;

    // Verify authentication
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    }

    try {
      // Verify the token
      const decoded = await verifyJWT(token)

      // Only allow SUPER_ADMIN, ADMIN, SUPERVISOR, TEACHER, and DATA_ENCODER to add marks
      if (!['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'TEACHER', 'DATA_ENCODER'].includes(decoded.role)) {
        return NextResponse.json(
          { error: 'Forbidden - You do not have permission to add marks' },
          { status: 403 }
        )
      }

      // If the user is a teacher, check if they have permission to add marks for this class
      if (decoded.role === 'TEACHER') {
        // Get the teacher ID from the email
        const teacher = await prisma.teacher.findUnique({
          where: { email: decoded.email },
          select: { id: true }
        })

        if (!teacher) {
          return NextResponse.json(
            { error: 'Teacher not found' },
            { status: 404 }
          )
        }

        // Check if the teacher has permission to add marks for this class
        const classObj = await prisma.class.findUnique({
          where: { name: className },
          select: { id: true }
        })

        if (!classObj) {
          return NextResponse.json(
            { error: 'Class not found' },
            { status: 404 }
          )
        }

        const hasPermission = await canTeacherAddMarks(teacher.id, classObj.id)

        if (!hasPermission) {
          return createPermissionDeniedResponse('add', 'marks', `class "${className}"`)
        }
      }

      // Check if student exists
      const student = await prisma.student.findUnique({
        where: { id: data.studentId },
        select: { name: true }
      })

      if (!student) {
        return NextResponse.json(
          { error: 'Student not found' },
          { status: 404 }
        )
      }
    } catch (jwtError) {
      console.error('JWT verification error:', jwtError)
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Create the mark
    const mark = await prisma.mark.create({
      data: {
        studentId: data.studentId,
        subject: data.subject,
        marks: marks,
        className: className,
        term: data.term,
        createdBy: decoded.id // Track who created the mark
      },
      include: {
        student: {
          select: {
            name: true,
            sid: true
          }
        }
      }
    })

    // Format the response to include student name and match UI expectations
    const formattedMark = {
      id: mark.id,
      studentId: mark.studentId,
      studentName: mark.student.name,
      studentSid: mark.student.sid,
      subject: mark.subject,
      obtainedMarks: mark.marks, // Map marks to obtainedMarks for UI
      totalMarks: mark.totalMarks || 100, // Use actual totalMarks from database
      class: mark.className, // Map className to class for UI
      term: mark.term,
      academicYear: mark.academicYear || (new Date(mark.createdAt).getFullYear() + '-' + (new Date(mark.createdAt).getFullYear() + 1)), // Use actual academicYear from database

      dateRecorded: mark.createdAt,
      recordedBy: 'Teacher', // Default recorded by
      createdAt: mark.createdAt,
      updatedAt: mark.updatedAt
    }

    return NextResponse.json(formattedMark)
  } catch (error) {
    console.error('Error creating mark:', error)
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json(
      { error: 'Failed to create mark', details: errorMessage },
      { status: 500 }
    )
  }
}

"use client"

import React, { useState, useEffect, useCallback } from 'react'
import { But<PERSON> } from './ui/button'
import { RefreshCw, Plus, FileText, Edit } from 'lucide-react'

import { ConfirmDialog } from './ConfirmDialog'
import { AddNewMarkDialog } from './MarkManagement/AddNewMarkDialog'
import { BulkMarkEntryDialog } from './MarkManagement/BulkMarkEntryDialog'
import { UpdateStudentMarksDialog } from './MarkManagement/UpdateStudentMarksDialog'
import { toast } from './ui/use-toast'
import { MarksDataTable } from './MarksDataTable'
import PermissionDialog from './PermissionDialog'
import { useAuth } from '../contexts/auth-context'
import { usePermissionDeniedDialog } from './PermissionDeniedDialog'
import { MobileContainer, MobileHeader, MobileButtonGroup, MobileCard } from './ui/mobile-container'

// Mark interface definition
export interface Mark {
  id: string;
  studentId: string;
  studentName: string;
  class: string;
  subject: string;
  term: string;                // "First Semester" | "Second Semester" | "Annual"
  academicYear: string;        // e.g., "2023-2024"

  totalMarks: number;          // Usually 100
  obtainedMarks: number;       // Points scored

  remarks?: string;            // Teacher's comments (optional)
  dateRecorded: string;        // ISO date string
  recordedBy: string;          // Teacher who recorded the mark
}

// We'll fetch classes and subjects from the database instead of hardcoding them

export function MarkList() {
  // State variables
  const { user } = useAuth()
  const { showDialog, PermissionDialog } = usePermissionDeniedDialog()
  const [marks, setMarks] = useState<Mark[]>([]);
  const [filteredMarks, setFilteredMarks] = useState<Mark[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedClass, setSelectedClass] = useState('all');
  const [selectedSubject, setSelectedSubject] = useState('all');
  const [selectedTerm, setSelectedTerm] = useState('all');
  const [selectedYear, setSelectedYear] = useState('2023-2024');

  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [markToDelete, setMarkToDelete] = useState<string | null>(null);

  // Dialog states for mark forms
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isBulkEntryOpen, setIsBulkEntryOpen] = useState(false);
  const [isUpdateMarkOpen, setIsUpdateMarkOpen] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [currentMark, setCurrentMark] = useState<Mark | null>(null);

  // Add new state for loading and error handling
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Add state for classes and subjects
  const [classes, setClasses] = useState<{id: string, name: string}[]>([])
  const [subjects, setSubjects] = useState<{id: string, name: string}[]>([])
  const [isLoadingClasses, setIsLoadingClasses] = useState(false)
  const [isLoadingSubjects, setIsLoadingSubjects] = useState(false)

  // Add state for permission dialog
  const [showPermissionDialog, setShowPermissionDialog] = useState(false)
  const [permissionMessage, setPermissionMessage] = useState('')

  // Add function to handle data loading
  const fetchMarks = useCallback(async () => {
    setIsLoading(true)
    setError(null)
    try {
      // TODO: Replace with actual API call
      const response = await fetch('/api/marks');

      if (!response.ok) {
        // Check if it's a permission error
        if (response.status === 403) {
          const errorData = await response.json();
          setPermissionMessage(errorData.error || 'You do not have permission to view marks');
          setShowPermissionDialog(true);
          throw new Error(errorData.error || 'Permission denied');
        }
        throw new Error('Failed to fetch marks');
      }

      const data = await response.json();
      setMarks(data)
      setFilteredMarks(data)
    } catch (err) {
      // Only set general error if it's not a permission error
      if (!(err instanceof Error && err.message.includes('permission'))) {
        setError('Failed to load marks')
        toast({
          title: "Error",
          description: "Failed to load marks. Please try again.",
          className: "bg-destructive text-destructive-foreground",
        })
      }
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Alias for fetchMarks for backward compatibility
  const loadMarks = fetchMarks;

  // Function to fetch classes from the database
  const fetchClasses = useCallback(async () => {
    setIsLoadingClasses(true)
    try {
      const response = await fetch('/api/classes')
      if (!response.ok) throw new Error('Failed to fetch classes')
      const data = await response.json()
      setClasses(data)
    } catch (err) {
      console.error('Error fetching classes:', err)
      toast({
        title: "Error",
        description: "Failed to load classes. Please try again.",
        className: "bg-destructive text-destructive-foreground",
      })
    } finally {
      setIsLoadingClasses(false)
    }
  }, [])

  // Function to fetch subjects from the database
  const fetchSubjects = useCallback(async () => {
    setIsLoadingSubjects(true)
    try {
      const response = await fetch('/api/subjects')
      if (!response.ok) throw new Error('Failed to fetch subjects')
      const data = await response.json()
      setSubjects(data)
    } catch (err) {
      console.error('Error fetching subjects:', err)
      toast({
        title: "Error",
        description: "Failed to load subjects. Please try again.",
        className: "bg-destructive text-destructive-foreground",
      })
    } finally {
      setIsLoadingSubjects(false)
    }
  }, [])

  // Load marks, classes, and subjects on component mount
  useEffect(() => {
    fetchMarks()
    fetchClasses()
    fetchSubjects()
  }, [])

  // Add refresh function
  const handleRefresh = useCallback(() => {
    fetchMarks()
    fetchClasses()
    fetchSubjects()
  }, [fetchMarks, fetchClasses, fetchSubjects])

  // Start editing a mark
  const startEdit = useCallback((mark: Mark) => {
    setCurrentMark(mark);
    setIsEditMode(true);
    setIsAddDialogOpen(true);
  }, []);

  // Filter marks based on selections
  useEffect(() => {
    let result = [...marks];

    if (selectedClass && selectedClass !== 'all') {
      result = result.filter(mark => mark.class === selectedClass);
    }

    if (selectedSubject && selectedSubject !== 'all') {
      result = result.filter(mark => mark.subject === selectedSubject);
    }

    if (selectedTerm && selectedTerm !== 'all') {
      result = result.filter(mark => mark.term === selectedTerm);
    }

    if (selectedYear) {
      result = result.filter(mark => mark.academicYear === selectedYear);
    }

    if (searchTerm) {
      result = result.filter(mark =>
        mark.studentName.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    setFilteredMarks(result);
  }, [marks, selectedClass, selectedSubject, selectedTerm, selectedYear, searchTerm]);

  // Calculate statistics
  const calculateStats = () => {
    if (filteredMarks.length === 0) return null;

    const scores = filteredMarks.map(mark => mark.obtainedMarks);
    const average = scores.reduce((a, b) => a + b, 0) / scores.length;
    const highest = Math.max(...scores);
    const lowest = Math.min(...scores);
    const highestStudent = filteredMarks.find(mark => mark.obtainedMarks === highest)?.studentName;
    const lowestStudent = filteredMarks.find(mark => mark.obtainedMarks === lowest)?.studentName;

    const gradeDistribution = {
      'A+': 0, 'A': 0, 'B+': 0, 'B': 0, 'C+': 0, 'C': 0, 'D': 0, 'F': 0
    };

    filteredMarks.forEach(mark => {
      if (gradeDistribution.hasOwnProperty(mark.grade)) {
        gradeDistribution[mark.grade as keyof typeof gradeDistribution]++;
      }
    });

    return { average, highest, lowest, highestStudent, lowestStudent, gradeDistribution };
  };







  // Initiate delete mark process
  const handleDeleteMark = (id: string) => {
    setMarkToDelete(id);
    setIsDeleteDialogOpen(true);
  };

  // Confirm and execute mark deletion
  const confirmDeleteMark = async () => {
    if (!markToDelete) return;

    try {
      // TODO: Replace with actual API call
      const response = await fetch(`/api/marks/${markToDelete}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        // Check if it's a permission error
        if (response.status === 403) {
          const errorData = await response.json();
          setPermissionMessage(errorData.error || 'You do not have permission to delete marks');
          setShowPermissionDialog(true);
          throw new Error(errorData.error || 'Permission denied');
        }
        throw new Error('Failed to delete mark');
      }

      setMarks(marks.filter(mark => mark.id !== markToDelete));
      toast({
        title: "Success",
        description: "Mark deleted successfully.",
      });
    } catch (error) {
      // Only show toast if it's not a permission error
      if (!(error instanceof Error && error.message.includes('permission'))) {
        toast({
          title: "Error",
          description: "Failed to delete mark. Please try again.",
          className: "bg-destructive text-destructive-foreground",
        });
      }
    } finally {
      setIsDeleteDialogOpen(false);
      setMarkToDelete(null);
    }
  };





  return (
    <MobileContainer className="space-y-6">
      {/* Permission Denied Dialog */}
      <PermissionDialog
        isOpen={showPermissionDialog}
        onClose={() => setShowPermissionDialog(false)}
        message={permissionMessage}
        resource="marks"
        action="view"
      />

      {/* Header */}
      <MobileHeader
        title="Mark List"
        subtitle="Manage and view student marks"
      >
        {/* Mobile-first button layout */}
        <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2 lg:flex-row lg:space-x-2">
          {/* Primary actions row */}
          <MobileButtonGroup orientation="horizontal">
            <Button
              onClick={() => {
                if (!user || !['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'TEACHER', 'DATA_ENCODER'].includes(user.role)) {
                  showDialog(
                    'You do not have permission to add marks',
                    'Only Super Admin, Admin, Supervisor, Teacher, and Data Encoder roles can add marks.'
                  );
                  return;
                }
                setIsEditMode(false);
                setCurrentMark(null);
                setIsAddDialogOpen(true);
              }}
              className="bg-indigo-600 hover:bg-indigo-700 flex items-center justify-center min-h-[44px] px-4"
            >
              <Plus className="mr-2 h-4 w-4" />
              <span className="hidden sm:inline">Add Mark</span>
              <span className="sm:hidden">Add</span>
            </Button>
            <Button
              variant="outline"
              onClick={() => {
                if (!user || !['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'TEACHER', 'DATA_ENCODER'].includes(user.role)) {
                  showDialog(
                    'You do not have permission to perform bulk entry',
                    'Only Super Admin, Admin, Supervisor, Teacher, and Data Encoder roles can perform bulk entry.'
                  );
                  return;
                }
                setIsBulkEntryOpen(true);
              }}
              className="flex items-center justify-center min-h-[44px] px-4"
            >
              <FileText className="mr-2 h-4 w-4" />
              <span className="hidden sm:inline">Bulk Entry</span>
              <span className="sm:hidden">Bulk</span>
            </Button>
          </MobileButtonGroup>

          {/* Secondary actions row */}
          <MobileButtonGroup orientation="horizontal">
            <Button
              variant="outline"
              onClick={() => {
                if (!user || !['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'TEACHER', 'DATA_ENCODER'].includes(user.role)) {
                  showDialog(
                    'You do not have permission to update marks',
                    'Only Super Admin, Admin, Supervisor, Teacher, and Data Encoder roles can update marks.'
                  );
                  return;
                }
                setIsUpdateMarkOpen(true);
              }}
              className="flex items-center justify-center min-h-[44px] px-4"
            >
              <Edit className="mr-2 h-4 w-4" />
              <span className="hidden sm:inline">Update</span>
              <span className="sm:hidden">Edit</span>
            </Button>
            <Button
              variant="outline"
              onClick={handleRefresh}
              className="flex items-center justify-center min-h-[44px] px-4"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              <span className="hidden sm:inline">Refresh</span>
              <span className="sm:hidden">Sync</span>
            </Button>
          </MobileButtonGroup>
        </div>
      </MobileHeader>

      {/* Add loading and error states */}
      {isLoading && (
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
        </div>
      )}

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative">
          {error}
        </div>
      )}

      {/* Beautiful data table to display marks */}
      <MobileCard
        title="Student Marks"
        subtitle="View and manage student academic records"
      >
        <MarksDataTable
          data={marks}
          isLoading={isLoading}
          onEdit={startEdit}
          onDelete={handleDeleteMark}
        />
      </MobileCard>



      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        title="Delete Mark"
        description="Are you sure you want to delete this mark? This action cannot be undone."
        onConfirm={confirmDeleteMark}
        onCancel={() => {
          setIsDeleteDialogOpen(false);
          setMarkToDelete(null);
        }}
        confirmText="Delete"
        cancelText="Cancel"
        variant="destructive"
      />

      {/* Add/Edit Mark Dialog */}
      <AddNewMarkDialog
        open={isAddDialogOpen}
        onOpenChange={(open) => {
          if (!open) {
            setIsEditMode(false);
            setCurrentMark(null);
          }
          setIsAddDialogOpen(open);
        }}
        isEditMode={isEditMode}
        initialData={currentMark}
        onSuccess={() => {
          setIsAddDialogOpen(false);
          setIsEditMode(false);
          setCurrentMark(null);
          fetchMarks();
        }}
      />

      {/* Bulk Entry Dialog */}
      <BulkMarkEntryDialog
        open={isBulkEntryOpen}
        onOpenChange={setIsBulkEntryOpen}
        onSuccess={() => {
          setIsBulkEntryOpen(false);
          fetchMarks();
        }}
      />

      {/* Update Student Marks Dialog */}
      <UpdateStudentMarksDialog
        open={isUpdateMarkOpen}
        onOpenChange={setIsUpdateMarkOpen}
        onSuccess={() => {
          setIsUpdateMarkOpen(false);
          fetchMarks();
        }}
      />

      {/* Permission Denied Dialog */}
      <PermissionDialog />
    </MobileContainer>
  );
}

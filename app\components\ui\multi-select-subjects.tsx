"use client"

import React, { useState, useMemo } from 'react'
import { <PERSON><PERSON> } from "./button"
import { Input } from "./input"
import { Checkbox } from "./checkbox"
import { Label } from "./label"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "./popover"
import { Badge } from "./badge"
import {
  ChevronDown,
  Search,
  X,
  Check,
  Filter
} from "lucide-react"
import { cn } from "../../lib/utils"

interface MultiSelectSubjectsProps {
  subjects: string[]
  selectedSubjects: string[]
  onSelectionChange: (subjects: string[]) => void
  placeholder?: string
  className?: string
}

export function MultiSelectSubjects({
  subjects,
  selectedSubjects,
  onSelectionChange,
  placeholder = "Select subjects...",
  className
}: MultiSelectSubjectsProps) {
  const [open, setOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")

  // Filter subjects based on search term
  const filteredSubjects = useMemo(() => {
    if (!searchTerm) return subjects.filter(subject => subject !== "all")
    return subjects
      .filter(subject => subject !== "all")
      .filter(subject => 
        subject.toLowerCase().includes(searchTerm.toLowerCase())
      )
  }, [subjects, searchTerm])

  // Handle individual subject selection
  const handleSubjectToggle = (subject: string) => {
    const isSelected = selectedSubjects.includes(subject)
    if (isSelected) {
      onSelectionChange(selectedSubjects.filter(s => s !== subject))
    } else {
      onSelectionChange([...selectedSubjects, subject])
    }
  }

  // Handle select all
  const handleSelectAll = () => {
    const availableSubjects = subjects.filter(subject => subject !== "all")
    onSelectionChange(availableSubjects)
  }

  // Handle deselect all
  const handleDeselectAll = () => {
    onSelectionChange([])
  }

  // Remove individual subject
  const removeSubject = (subjectToRemove: string) => {
    onSelectionChange(selectedSubjects.filter(s => s !== subjectToRemove))
  }

  return (
    <div className={cn("w-full", className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="h-12 w-full justify-between text-left font-normal"
          >
            <div className="flex items-center gap-2 flex-1 min-w-0">
              <Filter className="h-4 w-4 text-gray-500 flex-shrink-0" />
              <div className="flex-1 min-w-0">
                {selectedSubjects.length === 0 ? (
                  <span className="text-gray-500">{placeholder}</span>
                ) : selectedSubjects.length === 1 ? (
                  <span className="truncate">{selectedSubjects[0]}</span>
                ) : (
                  <span className="text-sm">
                    {selectedSubjects.length} subjects selected
                  </span>
                )}
              </div>
            </div>
            <ChevronDown className="h-4 w-4 opacity-50 flex-shrink-0" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <div className="p-0">
            {/* Search input */}
            <div className="flex items-center border-b px-3">
              <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
              <Input
                placeholder="Search subjects..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="border-0 focus-visible:ring-0 focus-visible:ring-offset-0 h-11"
              />
            </div>

            {/* Action buttons */}
            <div className="p-3 border-b">
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSelectAll}
                  className="h-8 text-xs"
                >
                  Select All
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDeselectAll}
                  className="h-8 text-xs"
                >
                  Deselect All
                </Button>
              </div>
              {selectedSubjects.length > 0 && (
                <div className="text-xs text-gray-500 mt-2">
                  {selectedSubjects.length} of {subjects.filter(s => s !== "all").length} subjects selected
                </div>
              )}
            </div>

            {/* Subject list */}
            <div className="max-h-[200px] overflow-y-auto">
              {filteredSubjects.length === 0 ? (
                <div className="p-4 text-center text-sm text-gray-500">
                  No subjects found.
                </div>
              ) : (
                <div className="p-2">
                  {filteredSubjects.map((subject) => (
                    <div
                      key={subject}
                      onClick={() => handleSubjectToggle(subject)}
                      className="flex items-center space-x-2 cursor-pointer p-2 hover:bg-gray-50 rounded-md"
                    >
                      <Checkbox
                        checked={selectedSubjects.includes(subject)}
                        onCheckedChange={() => handleSubjectToggle(subject)}
                        className="h-4 w-4"
                      />
                      <label className="flex-1 text-sm cursor-pointer">
                        {subject}
                      </label>
                      {selectedSubjects.includes(subject) && (
                        <Check className="h-4 w-4 text-blue-600" />
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </PopoverContent>
      </Popover>

      {/* Selected subjects badges */}
      {selectedSubjects.length > 0 && (
        <div className="flex flex-wrap gap-1 mt-2">
          {selectedSubjects.slice(0, 3).map((subject) => (
            <Badge
              key={subject}
              variant="secondary"
              className="text-xs px-2 py-1 flex items-center gap-1 max-w-[120px]"
            >
              <span className="truncate">{subject}</span>
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  removeSubject(subject)
                }}
                className="ml-1 hover:bg-gray-300 rounded-full p-0.5"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          ))}
          {selectedSubjects.length > 3 && (
            <Badge variant="outline" className="text-xs px-2 py-1">
              +{selectedSubjects.length - 3} more
            </Badge>
          )}
        </div>
      )}
    </div>
  )
}

import { NextResponse } from 'next/server'
import { prisma, isModelAvailable } from '@/lib/prisma'

// Define the Feature interface
interface Feature {
  title: string;
  description: string;
}

// Define the FeaturedContent interface
interface FeaturedContent {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  features: Feature[] | string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// GET all featured content
export async function GET() {
  try {
    // Check if the model is available
    if (!isModelAvailable('featuredContent')) {
      console.warn('FeaturedContent model is not available in Prisma client');
      return NextResponse.json([]);
    }

    // Use any type to bypass TypeScript checking for dynamic property access
    const featuredContent = await (prisma as any).featuredContent.findMany({
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Process the features field to ensure it's properly parsed
    const processedContent = featuredContent.map((content: any) => {
      try {
        // If features is a string, parse it
        if (content.features && typeof content.features === 'string') {
          return {
            ...content,
            features: JSON.parse(content.features)
          };
        }
        // If features is not a string but also not an array, handle it
        else if (content.features && !Array.isArray(content.features) && typeof content.features === 'object') {
          // Convert to array if it's an object
          return {
            ...content,
            features: [content.features]
          };
        }
      } catch (e) {
        console.error(`Error parsing features for content ${content.id}:`, e);
        // Return with empty features array if parsing fails
        return {
          ...content,
          features: []
        };
      }

      // Return unchanged if no processing needed
      return content;
    });

    return NextResponse.json(processedContent);
  } catch (error) {
    console.error('Error fetching featured content:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Failed to fetch featured content'
    }, { status: 500 });
  }
}

// POST a new featured content
export async function POST(request: Request) {
  try {
    // Check if the model is available
    if (!isModelAvailable('featuredContent')) {
      console.warn('FeaturedContent model is not available in Prisma client');
      return NextResponse.json({
        error: 'FeaturedContent model is not available'
      }, { status: 500 });
    }

    const data = await request.json();

    // Validate required fields
    if (!data.title || !data.description || !data.imageUrl || !data.features) {
      return NextResponse.json({
        error: 'All fields are required'
      }, { status: 400 });
    }

    // Validate features format
    if (!Array.isArray(data.features)) {
      return NextResponse.json({
        error: 'Features must be an array'
      }, { status: 400 });
    }

    // Validate each feature has title and description
    for (const feature of data.features) {
      if (!feature.title || !feature.description) {
        return NextResponse.json({
          error: 'Each feature must have a title and description'
        }, { status: 400 });
      }
    }

    // Create the new featured content
    // Convert features to JSON string if it's an array or object
    let featuresData = data.features;
    if (typeof featuresData === 'object') {
      try {
        featuresData = JSON.stringify(featuresData);
      } catch (e) {
        console.error('Error stringifying features:', e);
        // If stringification fails, use an empty array
        featuresData = '[]';
      }
    }

    const featuredContent = await (prisma as any).featuredContent.create({
      data: {
        title: data.title,
        description: data.description,
        imageUrl: data.imageUrl,
        features: featuresData,
        isActive: data.isActive !== undefined ? data.isActive : true
      }
    });

    return NextResponse.json(featuredContent);
  } catch (error) {
    console.error('Error creating featured content:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Failed to create featured content'
    }, { status: 500 });
  }
}

'use client'

import { useState, useEffect, useRef } from 'react'
import { useQuery } from '@tanstack/react-query'
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/ui/card'
import { Button } from '@/app/components/ui/button'
import { Badge } from '@/app/components/ui/badge'
import { ScrollArea, ScrollBar } from '@/app/components/ui/scroll-area'
import { ChevronLeft, ChevronRight, Calendar, Users, CheckCircle, Clock, XCircle } from 'lucide-react'
import { format, subDays, addDays, startOfDay, isToday, isYesterday } from 'date-fns'

interface AttendanceData {
  date: string
  className: string
  totalStudents: number
  presentStudents: number
  absentStudents: number
  permissionStudents: number
  attendanceCount: number
  attendanceRate: number
  status: 'completed' | 'partial' | 'not_started'
  completedAt?: string
}

export default function AttendanceProgress() {
  const [selectedDate, setSelectedDate] = useState(new Date())
  const [dateRange, setDateRange] = useState({
    start: subDays(new Date(), 6), // Show last 7 days including today
    end: new Date()
  })
  const scrollRef = useRef<HTMLDivElement>(null)

  // Fetch attendance progress data
  const { data: attendanceData, isLoading } = useQuery({
    queryKey: ['attendance-progress', dateRange],
    queryFn: async () => {
      const params = new URLSearchParams({
        startDate: format(dateRange.start, 'yyyy-MM-dd'),
        endDate: format(dateRange.end, 'yyyy-MM-dd')
      })
      const res = await fetch(`/api/progress/attendance?${params}`)
      if (!res.ok) throw new Error('Failed to fetch attendance progress')
      return res.json()
    }
  })

  // Navigate date range
  const navigateRange = (direction: 'prev' | 'next') => {
    const days = 7
    if (direction === 'prev') {
      setDateRange({
        start: subDays(dateRange.start, days),
        end: subDays(dateRange.end, days)
      })
    } else {
      setDateRange({
        start: addDays(dateRange.start, days),
        end: addDays(dateRange.end, days)
      })
    }
  }

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-500'
      case 'partial': return 'bg-yellow-500'
      case 'not_started': return 'bg-red-500'
      default: return 'bg-gray-300'
    }
  }

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="w-4 h-4" />
      case 'partial': return <Clock className="w-4 h-4" />
      case 'not_started': return <XCircle className="w-4 h-4" />
      default: return null
    }
  }

  // Generate date range array
  const generateDateRange = () => {
    const dates = []
    let currentDate = new Date(dateRange.start)
    while (currentDate <= dateRange.end) {
      dates.push(new Date(currentDate))
      currentDate = addDays(currentDate, 1)
    }
    return dates
  }

  // Get date label
  const getDateLabel = (date: Date) => {
    if (isToday(date)) return 'Today'
    if (isYesterday(date)) return 'Yesterday'
    return format(date, 'MMM dd')
  }

  // Get classes for a specific date
  const getClassesForDate = (date: Date) => {
    if (!attendanceData?.data) return []
    const dateStr = format(date, 'yyyy-MM-dd')
    return attendanceData.data.filter((item: AttendanceData) => item.date === dateStr)
  }

  // Calculate completion percentage for a date
  const getDateCompletion = (date: Date) => {
    const classes = getClassesForDate(date)
    if (classes.length === 0) return 0
    const completed = classes.filter((c: AttendanceData) => c.status === 'completed').length
    return Math.round((completed / classes.length) * 100)
  }

  const dates = generateDateRange()

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-500">Loading attendance progress...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Date Navigation */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Calendar className="w-5 h-5 text-muted-foreground" />
          <h3 className="text-lg font-semibold">
            {format(dateRange.start, 'MMM dd')} - {format(dateRange.end, 'MMM dd, yyyy')}
          </h3>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={() => navigateRange('prev')}>
            <ChevronLeft className="w-4 h-4" />
            Previous Week
          </Button>
          <Button variant="outline" size="sm" onClick={() => navigateRange('next')}>
            Next Week
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Timeline View */}
      <ScrollArea className="w-full" ref={scrollRef}>
        <div className="flex gap-4 pb-4">
          {dates.map((date) => {
            const classes = getClassesForDate(date)
            const completion = getDateCompletion(date)
            const isSelected = format(date, 'yyyy-MM-dd') === format(selectedDate, 'yyyy-MM-dd')

            return (
              <Card
                key={format(date, 'yyyy-MM-dd')}
                className={`min-w-[300px] cursor-pointer transition-all duration-300 border-0 shadow-lg bg-white/80 backdrop-blur-sm rounded-xl overflow-hidden ${
                  isSelected ? 'ring-2 ring-blue-500 shadow-2xl scale-105' : 'hover:shadow-xl hover:scale-102'
                }`}
                onClick={() => setSelectedDate(date)}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-sm font-medium">
                        {getDateLabel(date)}
                      </CardTitle>
                      <p className="text-xs text-muted-foreground">
                        {format(date, 'EEEE')}
                      </p>
                    </div>
                    <Badge variant={completion === 100 ? 'default' : completion > 0 ? 'secondary' : 'destructive'}>
                      {completion}%
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm">
                      <Users className="w-4 h-4 text-muted-foreground" />
                      <span>{classes.length} classes</span>
                    </div>
                    
                    {/* Progress Bar */}
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${completion}%` }}
                      ></div>
                    </div>

                    {/* Quick Stats Summary */}
                    <div className="flex justify-between text-xs">
                      <div className="flex gap-2">
                        <span className="text-green-600">P: {classes.reduce((sum: number, c: AttendanceData) => sum + c.presentStudents, 0)}</span>
                        <span className="text-red-600">A: {classes.reduce((sum: number, c: AttendanceData) => sum + c.absentStudents, 0)}</span>
                        <span className="text-yellow-600">Pr: {classes.reduce((sum: number, c: AttendanceData) => sum + c.permissionStudents, 0)}</span>
                      </div>
                      <div className="flex gap-1">
                        {['completed', 'partial', 'not_started'].map((status) => {
                          const count = classes.filter((c: AttendanceData) => c.status === status).length
                          if (count === 0) return null
                          return (
                            <div key={status} className="flex items-center gap-1">
                              <div className={`w-2 h-2 rounded-full ${getStatusColor(status)}`}></div>
                              <span>{count}</span>
                            </div>
                          )
                        })}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>

      {/* Detailed View for Selected Date */}
      <Card className="border-0 shadow-xl bg-white/80 backdrop-blur-sm rounded-2xl overflow-hidden">
        <CardHeader className="bg-gradient-to-r from-blue-500/10 to-indigo-500/10 border-b border-blue-100">
          <CardTitle className="flex items-center gap-3 text-blue-700">
            <div className="h-10 w-10 rounded-full bg-blue-500/20 flex items-center justify-center">
              <Calendar className="w-5 h-5 text-blue-600" />
            </div>
            {getDateLabel(selectedDate)} - Class Details
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {getClassesForDate(selectedDate).map((classData: AttendanceData) => (
              <Card key={`${classData.date}-${classData.className}`} className="border-0 shadow-lg bg-gradient-to-br from-white to-blue-50/30 rounded-xl overflow-hidden hover:shadow-xl transition-all duration-300">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-sm font-medium">
                      Class {classData.className}
                    </CardTitle>
                    <div className="flex items-center gap-1">
                      {getStatusIcon(classData.status)}
                      <Badge 
                        variant={classData.status === 'completed' ? 'default' : 
                                classData.status === 'partial' ? 'secondary' : 'destructive'}
                        className="text-xs"
                      >
                        {classData.status.replace('_', ' ')}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="space-y-3">
                    {/* Attendance Breakdown */}
                    <div className="grid grid-cols-2 gap-3 text-sm">
                      <div className="flex justify-between">
                        <span className="text-green-600 font-medium">Present:</span>
                        <span className="font-bold text-green-600">{classData.presentStudents}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-red-600 font-medium">Absent:</span>
                        <span className="font-bold text-red-600">{classData.absentStudents}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-yellow-600 font-medium">Permission:</span>
                        <span className="font-bold text-yellow-600">{classData.permissionStudents}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-blue-600 font-medium">Total Recorded:</span>
                        <span className="font-bold text-blue-600">{classData.attendanceCount}/{classData.totalStudents}</span>
                      </div>
                    </div>

                    {/* Attendance Rate */}
                    <div className="border-t pt-2">
                      <div className="flex justify-between items-center text-sm">
                        <span className="font-medium">Attendance Rate:</span>
                        <span className={`font-bold text-lg ${
                          classData.attendanceRate >= 90 ? 'text-green-600' :
                          classData.attendanceRate >= 75 ? 'text-yellow-600' : 'text-red-600'
                        }`}>
                          {classData.attendanceRate}%
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                        <div
                          className={`h-2 rounded-full transition-all duration-300 ${
                            classData.attendanceRate >= 90 ? 'bg-green-500' :
                            classData.attendanceRate >= 75 ? 'bg-yellow-500' : 'bg-red-500'
                          }`}
                          style={{ width: `${classData.attendanceRate}%` }}
                        ></div>
                      </div>
                    </div>

                    {/* Completion Info */}
                    {classData.completedAt && (
                      <div className="text-xs text-muted-foreground border-t pt-2">
                        <div className="flex items-center gap-1">
                          <CheckCircle className="w-3 h-3" />
                          Completed at {format(new Date(classData.completedAt), 'HH:mm')}
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
          
          {getClassesForDate(selectedDate).length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <Calendar className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>No attendance data available for this date</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

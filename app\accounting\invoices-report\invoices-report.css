/* Print styles for Invoice Report */
@media print {
  /* Hide unnecessary elements */
  header, 
  nav, 
  aside, 
  .no-print,
  button,
  .sidebar {
    display: none !important;
  }

  /* Show print-only elements */
  .print-header,
  .print-footer {
    display: block !important;
  }

  /* Full width content */
  body, 
  .main-content, 
  .container {
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    overflow: visible !important;
  }

  /* Reset background colors for printing */
  body, 
  div, 
  table {
    background-color: white !important;
    color: black !important;
    box-shadow: none !important;
  }

  /* Table styling */
  table {
    width: 100% !important;
    border-collapse: collapse !important;
  }

  th, td {
    border: 1px solid #ddd !important;
    padding: 8px !important;
    text-align: left !important;
  }

  th {
    background-color: #f2f2f2 !important;
    font-weight: bold !important;
  }

  /* Status badges */
  .status-badge {
    padding: 2px 8px !important;
    border-radius: 12px !important;
    font-size: 12px !important;
  }

  .status-paid {
    background-color: #d1fae5 !important;
    color: #065f46 !important;
  }

  .status-unpaid {
    background-color: #fef3c7 !important;
    color: #92400e !important;
  }

  /* Summary cards */
  .summary-card {
    border: 1px solid #ddd !important;
    padding: 12px !important;
    margin-bottom: 16px !important;
  }

  /* Page breaks */
  .page-break {
    page-break-after: always !important;
  }

  /* Avoid breaking inside elements */
  tr, td, th, .summary-card {
    page-break-inside: avoid !important;
  }

  /* Headers and footers */
  .print-header {
    margin-bottom: 20px !important;
  }

  .print-footer {
    margin-top: 20px !important;
    font-size: 10px !important;
    text-align: center !important;
    color: #666 !important;
  }
}

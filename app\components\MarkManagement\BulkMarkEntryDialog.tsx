'use client'

import { useState, useMemo } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { Button } from '@/app/components/ui/button'
import { Label } from '@/app/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/app/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/app/components/ui/dialog'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/app/components/ui/table'
import { Input } from '@/app/components/ui/input'
import { RefreshCw, FileText, GraduationCap, Users, BookOpen, ArrowUp, ArrowDown } from 'lucide-react'
import { MarkClass, MarkSubject, ACADEMIC_YEARS, TERMS } from '@/app/types/mark-management'
import { useToast } from '@/app/components/ui/use-toast'

// Types for Bulk Mark Entry
interface StudentForBulkEntry {
  id: string
  name: string
  sid: string
  className: string
  obtainedMarks: number
  hasExistingMark: boolean
  markId?: string | null
}

interface BulkMarkEntryDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: () => void
}

export function BulkMarkEntryDialog({ 
  open, 
  onOpenChange, 
  onSuccess 
}: BulkMarkEntryDialogProps) {
  const [selectedClassName, setSelectedClassName] = useState<string>('')
  const [selectedSubject, setSelectedSubject] = useState<string>('')
  const [selectedTerm, setSelectedTerm] = useState<string>('First Semester')
  const [selectedAcademicYear, setSelectedAcademicYear] = useState<string>('2024-2025')
  const [totalMarks, setTotalMarks] = useState<number>(100)
  const [studentMarks, setStudentMarks] = useState<StudentForBulkEntry[]>([])
  const [studentsLoaded, setStudentsLoaded] = useState(false)
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc')
  
  const queryClient = useQueryClient()
  const { toast } = useToast()

  // Fetch available classes
  const {
    data: classes = [],
    isLoading: isLoadingClasses,
    error: classesError
  } = useQuery<MarkClass[]>({
    queryKey: ['marks-available-classes'],
    queryFn: async () => {
      const res = await fetch('/api/marks/available-classes')
      if (!res.ok) throw new Error('Failed to fetch classes')
      return res.json()
    },
    enabled: open
  })

  // Fetch subjects for selected class
  const {
    data: subjects = [],
    isLoading: isLoadingSubjects,
    error: subjectsError
  } = useQuery<MarkSubject[]>({
    queryKey: ['marks-available-subjects', selectedClassName],
    queryFn: async () => {
      if (!selectedClassName) return []
      const res = await fetch(`/api/marks/available-subjects?className=${selectedClassName}`)
      if (!res.ok) throw new Error('Failed to fetch subjects')
      return res.json()
    },
    enabled: open && !!selectedClassName
  })

  // Fetch subject submission status for color coding
  const {
    data: subjectStatus,
    isLoading: isLoadingSubjectStatus
  } = useQuery({
    queryKey: ['marks-submitted-subjects', selectedClassName, selectedTerm, selectedAcademicYear],
    queryFn: async () => {
      if (!selectedClassName || !selectedTerm || !selectedAcademicYear) return null
      const res = await fetch(`/api/marks/submitted-subjects?className=${selectedClassName}&term=${selectedTerm}&academicYear=${selectedAcademicYear}`)
      if (!res.ok) throw new Error('Failed to fetch subject status')
      return res.json()
    },
    enabled: open && !!selectedClassName && !!selectedTerm && !!selectedAcademicYear
  })

  // Load students mutation
  const loadStudentsMutation = useMutation({
    mutationFn: async (criteria: {
      className: string
      subject: string
      term: string
      academicYear: string
    }) => {
      const params = new URLSearchParams({
        className: criteria.className,
        subject: criteria.subject,
        term: criteria.term,
        academicYear: criteria.academicYear
      })
      
      const res = await fetch(`/api/marks/load-students-for-bulk?${params}`)
      if (!res.ok) {
        const error = await res.json()
        throw new Error(error.error || 'Failed to load students')
      }
      return res.json()
    },
    onSuccess: (data) => {
      const formattedStudents = data.map((student: any) => ({
        id: student.id,
        name: student.name,
        sid: student.sid || '',
        className: student.className,
        obtainedMarks: student.existingMark ? student.existingMark.marks : 0,
        hasExistingMark: !!student.existingMark,
        markId: student.existingMark?.id || null
      }))

      // Sort students by SID
      const sortedStudents = [...formattedStudents].sort((a, b) => {
        return sortDirection === 'asc' 
          ? a.sid.localeCompare(b.sid, undefined, { numeric: true })
          : b.sid.localeCompare(a.sid, undefined, { numeric: true })
      })

      setStudentMarks(sortedStudents)
      setStudentsLoaded(true)
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: `Failed to load students: ${error.message}`,
        variant: "destructive"
      })
    }
  })

  // Submit bulk marks mutation
  const submitBulkMarksMutation = useMutation({
    mutationFn: async (marks: any[]) => {
      const res = await fetch('/api/marks/submit-bulk', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ marks })
      })
      
      if (!res.ok) {
        const error = await res.json()
        throw new Error(error.error || 'Failed to submit bulk marks')
      }
      return res.json()
    },
    onSuccess: (data) => {
      // Invalidate and refetch related queries
      queryClient.invalidateQueries({ queryKey: ['marks'] })
      queryClient.invalidateQueries({ queryKey: ['marks-available-classes'] })

      // Reset form
      handleClose()

      // Call success callback
      onSuccess?.()

      toast({
        title: "Success",
        description: `Successfully processed ${data.processedCount} mark${data.processedCount !== 1 ? 's' : ''}`,
        variant: "default"
      })
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: `Failed to submit marks: ${error.message}`,
        variant: "destructive"
      })
    }
  })

  // Helper function to get subject color status
  const getSubjectColorStatus = (subjectName: string) => {
    if (!subjectStatus?.subjectStatus) return 'default'

    const status = subjectStatus.subjectStatus.find((s: any) =>
      s.subject.toLowerCase().trim() === subjectName.toLowerCase().trim()
    )

    return status?.isSubmitted ? 'submitted' : 'not-submitted'
  }

  const handleLoadStudents = () => {
    if (!selectedClassName || !selectedSubject || !selectedTerm || !selectedAcademicYear) {
      toast({
        title: "Missing Information",
        description: "Please select class, subject, term, and academic year",
        variant: "destructive"
      })
      return
    }

    loadStudentsMutation.mutate({
      className: selectedClassName,
      subject: selectedSubject,
      term: selectedTerm,
      academicYear: selectedAcademicYear
    })
  }

  const handleMarkChange = (studentId: string, marks: number) => {
    setStudentMarks(prev => 
      prev.map(student => 
        student.id === studentId 
          ? { ...student, obtainedMarks: marks }
          : student
      )
    )
  }

  const handleSubmitMarks = () => {
    const marksToSubmit = studentMarks
      .filter(student => student.obtainedMarks > 0) // Only include students with marks
      .map(student => ({
        studentId: student.id,
        studentName: student.name,
        className: selectedClassName,
        subject: selectedSubject,
        term: selectedTerm,
        academicYear: selectedAcademicYear,
        totalMarks: totalMarks,
        marks: student.obtainedMarks,
        remarks: '',
        markId: student.markId // Include existing mark ID for updates
      }))

    if (marksToSubmit.length === 0) {
      toast({
        title: "No Marks to Submit",
        description: "Please enter marks for at least one student",
        variant: "destructive"
      })
      return
    }

    submitBulkMarksMutation.mutate(marksToSubmit)
  }

  const toggleSortDirection = () => {
    setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc')
    
    // Re-sort existing students
    if (studentMarks.length > 0) {
      const sorted = [...studentMarks].sort((a, b) => {
        return sortDirection === 'desc' 
          ? a.sid.localeCompare(b.sid, undefined, { numeric: true })
          : b.sid.localeCompare(a.sid, undefined, { numeric: true })
      })
      setStudentMarks(sorted)
    }
  }

  const handleClose = () => {
    setSelectedClassName('')
    setSelectedSubject('')
    setSelectedTerm('First Semester')
    setSelectedAcademicYear('2024-2025')
    setTotalMarks(100)
    setStudentMarks([])
    setStudentsLoaded(false)
    onOpenChange(false)
  }

  // Calculate statistics
  const statistics = useMemo(() => {
    const totalStudents = studentMarks.length
    const studentsWithMarks = studentMarks.filter(s => s.obtainedMarks > 0).length
    const existingMarksCount = studentMarks.filter(s => s.hasExistingMark).length
    
    return {
      totalStudents,
      studentsWithMarks,
      existingMarksCount,
      newMarksCount: studentsWithMarks - existingMarksCount
    }
  }, [studentMarks])

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[1000px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Bulk Mark Entry
          </DialogTitle>
          <DialogDescription>
            Enter marks for multiple students at once for a specific class and subject
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Selection Criteria */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pb-4 border-b">
            {/* Class Selection */}
            <div className="space-y-2">
              <Label htmlFor="class">Class *</Label>
              <Select
                value={selectedClassName}
                onValueChange={setSelectedClassName}
                disabled={isLoadingClasses}
              >
                <SelectTrigger>
                  <Users className="mr-2 h-4 w-4 text-gray-400" />
                  <SelectValue placeholder="Select class" />
                </SelectTrigger>
                <SelectContent>
                  {isLoadingClasses ? (
                    <div className="p-2 text-center text-sm text-gray-500">
                      Loading classes...
                    </div>
                  ) : classesError ? (
                    <div className="p-2 text-center text-sm text-red-500">
                      Error loading classes
                    </div>
                  ) : classes.length > 0 ? (
                    classes.map((cls) => (
                      <SelectItem key={cls.id} value={cls.name}>
                        <div className="flex items-center justify-between w-full">
                          <span>{cls.name}</span>
                          <span className="text-xs text-gray-500 ml-2">
                            {cls.totalSubjects} subjects
                          </span>
                        </div>
                      </SelectItem>
                    ))
                  ) : (
                    <div className="p-2 text-center text-sm text-gray-500">
                      No classes found
                    </div>
                  )}
                </SelectContent>
              </Select>
            </div>

            {/* Subject Selection */}
            <div className="space-y-2">
              <Label htmlFor="subject">Subject *</Label>
              <Select
                value={selectedSubject}
                onValueChange={setSelectedSubject}
                disabled={!selectedClassName || isLoadingSubjects}
              >
                <SelectTrigger>
                  <BookOpen className="mr-2 h-4 w-4 text-gray-400" />
                  {selectedSubject ? (
                    <div className="flex items-center gap-2 flex-1">
                      <div
                        className={`w-3 h-3 rounded-full flex-shrink-0 ${
                          getSubjectColorStatus(selectedSubject) === 'submitted'
                            ? 'bg-green-500'
                            : getSubjectColorStatus(selectedSubject) === 'not-submitted'
                            ? 'bg-red-500'
                            : 'bg-gray-300'
                        }`}
                      />
                      <span className="flex-1 text-left">{selectedSubject}</span>
                    </div>
                  ) : (
                    <SelectValue placeholder="Select subject" />
                  )}
                </SelectTrigger>
                <SelectContent>
                  {isLoadingSubjects ? (
                    <div className="p-2 text-center text-sm text-gray-500">
                      Loading subjects...
                    </div>
                  ) : subjectsError ? (
                    <div className="p-2 text-center text-sm text-red-500">
                      Error loading subjects
                    </div>
                  ) : subjects.length > 0 ? (
                    subjects.map((subject) => {
                      const colorStatus = getSubjectColorStatus(subject.name)
                      return (
                        <SelectItem key={subject.id} value={subject.name}>
                          <div className="flex items-center gap-2 w-full">
                            <div
                              className={`w-3 h-3 rounded-full flex-shrink-0 ${
                                colorStatus === 'submitted'
                                  ? 'bg-green-500'
                                  : colorStatus === 'not-submitted'
                                  ? 'bg-red-500'
                                  : 'bg-gray-300'
                              }`}
                              title={
                                colorStatus === 'submitted'
                                  ? 'Marks submitted for this subject'
                                  : colorStatus === 'not-submitted'
                                  ? 'No marks submitted for this subject'
                                  : 'Loading status...'
                              }
                            />
                            <span className="flex-1">{subject.name}</span>
                          </div>
                        </SelectItem>
                      )
                    })
                  ) : (
                    <div className="p-2 text-center text-sm text-gray-500">
                      {selectedClassName 
                        ? 'No subjects found for this class' 
                        : 'Select a class first'
                      }
                    </div>
                  )}
                </SelectContent>
              </Select>

              {/* Color Legend */}
              {selectedClassName && selectedTerm && selectedAcademicYear && subjects.length > 0 && (
                <div className="mt-2 p-2 bg-gray-50 rounded-md">
                  <div className="text-xs text-gray-600 mb-1">Subject Status:</div>
                  <div className="flex items-center gap-4 text-xs">
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 rounded-full bg-green-500"></div>
                      <span className="text-gray-700">Marks Submitted</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 rounded-full bg-red-500"></div>
                      <span className="text-gray-700">No Marks Submitted</span>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Semester Selection */}
            <div className="space-y-2">
              <Label htmlFor="term">Semester *</Label>
              <Select
                value={selectedTerm}
                onValueChange={setSelectedTerm}
              >
                <SelectTrigger>
                  <GraduationCap className="mr-2 h-4 w-4 text-gray-400" />
                  <SelectValue placeholder="Select semester" />
                </SelectTrigger>
                <SelectContent>
                  {TERMS.map(term => (
                    <SelectItem key={term} value={term}>
                      {term}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Academic Year and Total Marks */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pb-4 border-b">
            <div className="space-y-2">
              <Label htmlFor="academicYear">Academic Year *</Label>
              <Select
                value={selectedAcademicYear}
                onValueChange={setSelectedAcademicYear}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select academic year" />
                </SelectTrigger>
                <SelectContent>
                  {ACADEMIC_YEARS.map(year => (
                    <SelectItem key={year} value={year}>
                      {year}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="totalMarks">Total Marks *</Label>
              <Input
                id="totalMarks"
                type="number"
                min="1"
                max="1000"
                value={totalMarks}
                onChange={(e) => setTotalMarks(parseInt(e.target.value) || 100)}
                required
              />
            </div>
          </div>

          {/* Load Students Button */}
          <div className="flex justify-center">
            <Button
              onClick={handleLoadStudents}
              disabled={!selectedClassName || !selectedSubject || !selectedTerm || !selectedAcademicYear || loadStudentsMutation.isPending}
              className="w-full md:w-auto"
            >
              {loadStudentsMutation.isPending ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Loading students...
                </>
              ) : (
                <>
                  <FileText className="h-4 w-4 mr-2" />
                  Load Students
                </>
              )}
            </Button>
          </div>

          {/* Statistics */}
          {studentsLoaded && studentMarks.length > 0 && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{statistics.totalStudents}</div>
                <div className="text-sm text-gray-600">Total Students</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{statistics.studentsWithMarks}</div>
                <div className="text-sm text-gray-600">With Marks</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">{statistics.existingMarksCount}</div>
                <div className="text-sm text-gray-600">Existing</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{statistics.newMarksCount}</div>
                <div className="text-sm text-gray-600">New</div>
              </div>
            </div>
          )}

          {/* Student List */}
          {studentsLoaded && studentMarks.length > 0 && (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Student List</h3>
                <span className="text-sm text-gray-600">
                  Enter marks out of {totalMarks}
                </span>
              </div>
              
              <div className="border rounded-md overflow-hidden">
                <div className="max-h-[400px] overflow-y-auto">
                  <Table>
                    <TableHeader className="sticky top-0 bg-white z-10">
                      <TableRow>
                        <TableHead className="w-12">#</TableHead>
                        <TableHead className="w-24">
                          <div className="flex items-center space-x-1">
                            <span>SID</span>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-5 w-5 ml-1 -mr-2"
                              onClick={toggleSortDirection}
                              title={`Sort by SID ${sortDirection === 'asc' ? 'Z-A' : 'A-Z'}`}
                            >
                              {sortDirection === 'asc' ?
                                <ArrowUp className="h-3 w-3" /> :
                                <ArrowDown className="h-3 w-3" />
                              }
                            </Button>
                          </div>
                        </TableHead>
                        <TableHead>Student Name</TableHead>
                        <TableHead>Marks (out of {totalMarks})</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {studentMarks.map((student, index) => (
                        <TableRow 
                          key={student.id}
                          className={student.hasExistingMark ? 'bg-blue-50 border-l-4 border-l-blue-400' : ''}
                        >
                          <TableCell className="text-center">{index + 1}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <span className="font-mono text-xs font-medium bg-gray-100 px-2 py-1 rounded">
                                {student.sid}
                              </span>
                              {student.hasExistingMark && (
                                <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full font-medium">
                                  Update
                                </span>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <span>{student.name}</span>
                              {student.hasExistingMark && (
                                <span className="text-blue-600" title="This student already has marks for this subject">
                                  📝
                                </span>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Input
                              type="number"
                              min="0"
                              max={totalMarks}
                              value={student.obtainedMarks || ''}
                              onChange={(e) => handleMarkChange(student.id, parseInt(e.target.value) || 0)}
                              className={`w-24 text-right ${student.hasExistingMark ? 'border-blue-300 bg-blue-50' : ''}`}
                              placeholder={student.hasExistingMark ? 'Update' : 'Enter'}
                            />
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </div>
            </div>
          )}

          {studentsLoaded && studentMarks.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              No students found in this class
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          {studentsLoaded && studentMarks.length > 0 && (
            <Button
              onClick={handleSubmitMarks}
              disabled={studentMarks.every(s => s.obtainedMarks === 0) || submitBulkMarksMutation.isPending}
            >
              {submitBulkMarksMutation.isPending ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                `Save ${statistics.studentsWithMarks} Mark${statistics.studentsWithMarks !== 1 ? 's' : ''}`
              )}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

"use client"

import React, { useState, useEffect } from 'react'
import { Button } from './ui/button'
import { Input } from './ui/input'
import { Label } from './ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Separator } from './ui/separator'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select'
import {
  Save,
  Upload,
  School,
  Palette,
  Settings,
  Image,
  Hash,
  Calendar
} from 'lucide-react'
import { useToast } from './ui/use-toast'
import { useAppSettings } from '../contexts/app-settings-context'
import { generateAcademicYearOptions, getCurrentAcademicYear } from '../utils/academic-year'

interface AppSettingsData {
  id?: string
  schoolName: string
  schoolLogo?: string
  semesterCount: number
  defaultAcademicYear: string
  sidebarBgColor: string
  sidebarTextColor: string
  createdAt?: string
  updatedAt?: string
}

export function BasicInformation() {
  const { toast } = useToast()
  const { settings: globalSettings, updateSettings, isLoading: globalLoading } = useAppSettings()
  const [settings, setSettings] = useState<AppSettingsData>(globalSettings)
  const [isSaving, setIsSaving] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)
  const [logoFile, setLogoFile] = useState<File | null>(null)
  const [logoPreview, setLogoPreview] = useState<string>('')

  // Generate academic year options
  const academicYearOptions = generateAcademicYearOptions(2020, 15) // 2020-2035

  // Sync with global settings
  useEffect(() => {
    setSettings(globalSettings)
    setLogoPreview(globalSettings.schoolLogo || '')
  }, [globalSettings])



  const handleInputChange = (field: keyof AppSettingsData, value: any) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }))
    setHasChanges(true)
  }

  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setLogoFile(file)
      const reader = new FileReader()
      reader.onload = (e) => {
        const result = e.target?.result as string
        setLogoPreview(result)
        setHasChanges(true)
      }
      reader.readAsDataURL(file)
    }
  }

  const uploadLogo = async (): Promise<string | null> => {
    if (!logoFile) return null

    const formData = new FormData()
    formData.append('logo', logoFile)

    try {
      const response = await fetch('/api/upload/logo', {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) throw new Error('Failed to upload logo')

      const data = await response.json()
      return data.url
    } catch (error) {
      console.error('Error uploading logo:', error)
      throw error
    }
  }

  const handleSave = async () => {
    try {
      setIsSaving(true)
      
      let logoUrl = settings.schoolLogo

      // Upload logo if a new file was selected
      if (logoFile) {
        logoUrl = await uploadLogo()
      }

      const settingsToSave = {
        ...settings,
        schoolLogo: logoUrl
      }

      // Use the global context to update settings
      await updateSettings(settingsToSave)

      setHasChanges(false)
      setLogoFile(null)

      toast({
        title: "Success",
        description: "App settings saved successfully. Changes applied throughout the application.",
        className: "bg-green-600 text-white",
      })
    } catch (error) {
      console.error('Error saving app settings:', error)
      toast({
        title: "Error",
        description: error.message || "Failed to save app settings.",
        className: "bg-destructive text-destructive-foreground",
      })
    } finally {
      setIsSaving(false)
    }
  }

  if (globalLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-2/3 mb-8"></div>
          <div className="space-y-4">
            <div className="h-32 bg-gray-200 rounded"></div>
            <div className="h-32 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold flex items-center gap-2">
          <School className="h-5 w-5 text-indigo-600" />
          Basic Information
        </h2>
        <p className="text-gray-500 text-sm mt-1">
          Configure basic information about your school and application
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* School Information */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <School className="h-5 w-5" />
              School Information
            </CardTitle>
            <CardDescription>
              Basic details about your educational institution
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* School Name */}
            <div className="space-y-2">
              <Label htmlFor="schoolName" className="text-sm font-medium">
                School Name
              </Label>
              <Input
                id="schoolName"
                type="text"
                value={settings.schoolName}
                onChange={(e) => handleInputChange('schoolName', e.target.value)}
                placeholder="Enter school name"
              />
              <p className="text-xs text-gray-500">
                This will appear in headers, reports, and official documents
              </p>
            </div>

            {/* School Logo */}
            <div className="space-y-2">
              <Label htmlFor="schoolLogo" className="text-sm font-medium">
                School Logo
              </Label>
              <div className="flex items-center space-x-4">
                {logoPreview && (
                  <div className="w-16 h-16 border rounded-lg overflow-hidden bg-gray-50">
                    <img 
                      src={logoPreview} 
                      alt="School Logo" 
                      className="w-full h-full object-contain"
                    />
                  </div>
                )}
                <div className="flex-1">
                  <Input
                    id="schoolLogo"
                    type="file"
                    accept="image/*"
                    onChange={handleLogoChange}
                    className="cursor-pointer"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Upload PNG, JPG, or SVG. Recommended size: 200x200px
                  </p>
                </div>
              </div>
            </div>

            {/* Semester Count */}
            <div className="space-y-2">
              <Label htmlFor="semesterCount" className="text-sm font-medium">
                Number of Semesters
              </Label>
              <div className="flex items-center space-x-3">
                <Input
                  id="semesterCount"
                  type="number"
                  min="1"
                  max="4"
                  value={settings.semesterCount}
                  onChange={(e) => handleInputChange('semesterCount', parseInt(e.target.value) || 1)}
                  className="w-24"
                />
                <span className="text-sm text-gray-500">per academic year</span>
              </div>
              <p className="text-xs text-gray-500">
                This affects report card generation and academic planning
              </p>
            </div>

            {/* Default Academic Year */}
            <div className="space-y-2">
              <Label htmlFor="defaultAcademicYear" className="text-sm font-medium">
                Default Academic Year
              </Label>
              <Select
                value={settings.defaultAcademicYear}
                onValueChange={(value) => handleInputChange('defaultAcademicYear', value)}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select academic year" />
                </SelectTrigger>
                <SelectContent>
                  {academicYearOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        {option.label}
                        {option.value === getCurrentAcademicYear() && (
                          <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                            Current
                          </span>
                        )}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-gray-500">
                This will be used as the default year throughout the dashboard and website
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Appearance Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <Palette className="h-5 w-5" />
              Sidebar Appearance
            </CardTitle>
            <CardDescription>
              Customize the look and feel of the sidebar
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Sidebar Background Color */}
            <div className="space-y-2">
              <Label htmlFor="sidebarBgColor" className="text-sm font-medium">
                Sidebar Background Color
              </Label>
              <div className="flex items-center space-x-3">
                <Input
                  id="sidebarBgColor"
                  type="color"
                  value={settings.sidebarBgColor}
                  onChange={(e) => handleInputChange('sidebarBgColor', e.target.value)}
                  className="w-16 h-10 p-1 border rounded cursor-pointer"
                />
                <Input
                  type="text"
                  value={settings.sidebarBgColor}
                  onChange={(e) => handleInputChange('sidebarBgColor', e.target.value)}
                  placeholder="#1f2937"
                  className="flex-1"
                />
              </div>
              <p className="text-xs text-gray-500">
                Choose the background color for the sidebar navigation
              </p>
            </div>

            {/* Sidebar Text Color */}
            <div className="space-y-2">
              <Label htmlFor="sidebarTextColor" className="text-sm font-medium">
                Sidebar Text Color
              </Label>
              <div className="flex items-center space-x-3">
                <Input
                  id="sidebarTextColor"
                  type="color"
                  value={settings.sidebarTextColor}
                  onChange={(e) => handleInputChange('sidebarTextColor', e.target.value)}
                  className="w-16 h-10 p-1 border rounded cursor-pointer"
                />
                <Input
                  type="text"
                  value={settings.sidebarTextColor}
                  onChange={(e) => handleInputChange('sidebarTextColor', e.target.value)}
                  placeholder="#ffffff"
                  className="flex-1"
                />
              </div>
              <p className="text-xs text-gray-500">
                Choose the text color for sidebar navigation items
              </p>
            </div>

            {/* Color Preview */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Preview</Label>
              <div 
                className="p-4 rounded-lg border"
                style={{ 
                  backgroundColor: settings.sidebarBgColor,
                  color: settings.sidebarTextColor 
                }}
              >
                <div className="flex items-center space-x-2 mb-2">
                  <Settings className="h-4 w-4" />
                  <span className="text-sm font-medium">Dashboard</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Hash className="h-4 w-4" />
                  <span className="text-sm">Students</span>
                </div>
              </div>
            </div>

            <Separator />

            {/* Save Button */}
            <Button 
              onClick={handleSave} 
              disabled={!hasChanges || isSaving}
              className="w-full bg-indigo-600 hover:bg-indigo-700"
            >
              <Save className="h-4 w-4 mr-2" />
              {isSaving ? 'Saving...' : 'Save Settings'}
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

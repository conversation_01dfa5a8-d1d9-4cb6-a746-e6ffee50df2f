import { NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'

export async function POST(request: Request) {
  let prisma: PrismaClient | null = null;

  try {
    console.log('Ensuring admin user exists...')
    
    // Create a new PrismaClient instance
    prisma = new PrismaClient();
    
    // Connect to the database
    try {
      await prisma.$connect()
      console.log('Database connection successful')
    } catch (connectionError) {
      console.error('Database connection failed:', connectionError)
      return NextResponse.json(
        {
          error: 'Database connection failed. Please check your database configuration.',
          details: connectionError instanceof Error ? connectionError.message : 'Unknown error'
        },
        { status: 500 }
      )
    }
    
    // Check if admin user already exists
    const adminEmail = '<EMAIL>'
    const existingAdmin = await prisma.user.findUnique({
      where: { email: adminEmail }
    })
    
    if (existingAdmin) {
      console.log('Admin user already exists')
      
      // Ensure the user has ADMIN role
      if (existingAdmin.role !== 'ADMIN') {
        console.log('Updating admin user role to ADMIN')
        await prisma.user.update({
          where: { id: existingAdmin.id },
          data: { role: 'ADMIN' }
        })
      }
      
      // Generate JWT token for the admin user
      const jwtSecret = process.env.JWT_SECRET || 'school_management_secure_jwt_secret_key_2023';
      const token = jwt.sign(
        {
          id: existingAdmin.id,
          email: existingAdmin.email,
          role: 'ADMIN'
        },
        jwtSecret,
        { expiresIn: '24h' }
      )
      
      // Create response with token
      const response = NextResponse.json({
        status: 'success',
        message: 'Admin user already exists',
        user: {
          id: existingAdmin.id,
          email: existingAdmin.email,
          name: existingAdmin.name,
          role: 'ADMIN'
        }
      })
      
      // Set the token cookie
      response.cookies.set('token', token, {
        httpOnly: true,
        secure: false, // Set to false for development
        sameSite: 'lax',
        maxAge: 86400, // 24 hours
        path: '/'
      })
      
      // Also set a non-httpOnly cookie for client-side access
      response.cookies.set('auth_status', 'logged_in', {
        httpOnly: false,
        secure: false,
        sameSite: 'lax',
        maxAge: 86400,
        path: '/'
      })
      
      return response
    }
    
    // Create admin user if it doesn't exist
    console.log('Creating new admin user')
    
    // Hash the password
    const hashedPassword = await bcrypt.hash('admin123', 10)
    
    // Create the user
    const newAdmin = await prisma.user.create({
      data: {
        email: adminEmail,
        name: 'Admin User',
        password: hashedPassword,
        role: 'ADMIN',
        status: 'active'
      }
    })
    
    console.log('Admin user created successfully')
    
    // Generate JWT token for the new admin user
    const jwtSecret = process.env.JWT_SECRET || 'school_management_secure_jwt_secret_key_2023';
    const token = jwt.sign(
      {
        id: newAdmin.id,
        email: newAdmin.email,
        role: 'ADMIN'
      },
      jwtSecret,
      { expiresIn: '24h' }
    )
    
    // Create response with token
    const response = NextResponse.json({
      status: 'success',
      message: 'Admin user created successfully',
      user: {
        id: newAdmin.id,
        email: newAdmin.email,
        name: newAdmin.name,
        role: 'ADMIN'
      }
    })
    
    // Set the token cookie
    response.cookies.set('token', token, {
      httpOnly: true,
      secure: false, // Set to false for development
      sameSite: 'lax',
      maxAge: 86400, // 24 hours
      path: '/'
    })
    
    // Also set a non-httpOnly cookie for client-side access
    response.cookies.set('auth_status', 'logged_in', {
      httpOnly: false,
      secure: false,
      sameSite: 'lax',
      maxAge: 86400,
      path: '/'
    })
    
    return response
  } catch (error) {
    console.error('Error ensuring admin user exists:', error)
    return NextResponse.json(
      { 
        error: 'Failed to ensure admin user exists',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  } finally {
    // Clean up the connection
    if (prisma) {
      await prisma.$disconnect().catch(e => console.error('Error disconnecting from database:', e))
    }
  }
}

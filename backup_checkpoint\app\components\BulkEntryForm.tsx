"use client"

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from './ui/button'
import { Input } from './ui/input'
import { Label } from "./ui/label"
import { Textarea } from "./ui/textarea"
import { Users, BookOpen, Loader2 } from 'lucide-react'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select"
import { DialogFooter } from "./ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/table'
import { Mark } from './MarkList'
import { useQuery } from '@tanstack/react-query'

// Academic terms and exam types (same thing in this case)
const terms = ['First Semester', 'Second Semester']

// Fixed academic years from 2020-2021 to 2027-2028
const academicYears = [
  '2020-2021',
  '2021-2022',
  '2022-2023',
  '2023-2024',
  '2024-2025',
  '2025-2026',
  '2026-2027',
  '2027-2028'
]





interface BulkEntryFormProps {
  onSubmit: (marks: Omit<Mark, 'id' | 'dateRecorded' | 'grade'>[]) => void
  onCancel: () => void
  calculateGrade: (mark: number) => string
  currentClass?: string
  currentSubject?: string
}

export function BulkEntryForm({
  onSubmit,
  onCancel,
  calculateGrade,
  currentClass,
  currentSubject
}: BulkEntryFormProps) {
  // Form settings
  const [formSettings, setFormSettings] = useState({
    class: currentClass || '',
    subject: currentSubject || '',
    term: 'First Semester',
    academicYear: '2023-2024',

    totalMarks: 100
  })

  // Fetch classes from the database
  const { data: classes, isLoading: isLoadingClasses } = useQuery({
    queryKey: ['classes'],
    queryFn: async () => {
      const res = await fetch('/api/classes')
      if (!res.ok) throw new Error('Failed to fetch classes')
      return res.json()
    }
  })

  // Fetch subjects based on selected class
  const { data: subjects, isLoading: isLoadingSubjects } = useQuery({
    queryKey: ['subjects', formSettings.class],
    queryFn: async () => {
      if (!formSettings.class) return []
      const res = await fetch(`/api/subjects?className=${formSettings.class}`)
      if (!res.ok) throw new Error('Failed to fetch subjects')
      return res.json()
    },
    enabled: !!formSettings.class
  })

  // Fetch students based on selected class
  const { data: students, isLoading: isLoadingStudents } = useQuery({
    queryKey: ['students', formSettings.class],
    queryFn: async () => {
      if (!formSettings.class) return []
      const res = await fetch(`/api/students?class=${formSettings.class}`)
      if (!res.ok) throw new Error('Failed to fetch students')
      return res.json()
    },
    enabled: !!formSettings.class
  })

  // Student mark data
  const [studentMarks, setStudentMarks] = useState<Array<{
    studentId: string;
    studentName: string;
    obtainedMarks: number;
  }>>([]);

  // Load students when class changes or students data is fetched
  useEffect(() => {
    if (formSettings.class && students && students.length > 0) {
      const studentMarksData = students.map((student: any) => ({
        studentId: student.id,
        studentName: student.name,
        obtainedMarks: 0,
      }));

      setStudentMarks(studentMarksData);
    } else {
      setStudentMarks([]);
    }
  }, [formSettings.class, students]);

  // Handle form setting changes
  const handleSettingChange = (name: string, value: string | number) => {
    setFormSettings({
      ...formSettings,
      [name]: value
    });
  };

  // Handle mark input for a student
  const handleMarkChange = (studentId: string, value: number) => {
    setStudentMarks(studentMarks.map(student => {
      if (student.studentId === studentId) {
        return {
          ...student,
          obtainedMarks: value,
        };
      }
      return student;
    }));
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Create mark objects for each student
    const marks = studentMarks.map(student => ({
      studentId: student.studentId,
      studentName: student.studentName,
      class: formSettings.class, // This will be mapped to className in the API
      subject: formSettings.subject,
      term: formSettings.term,
      academicYear: formSettings.academicYear,
      totalMarks: formSettings.totalMarks,
      obtainedMarks: student.obtainedMarks, // This will be mapped to marks in the API
      remarks: '', // Add empty remarks
      recordedBy: 'Current Teacher'
    })).filter(mark => mark.obtainedMarks > 0);

    onSubmit(marks);
  };

  const [showClassesList, setShowClassesList] = useState(false)

  return (
    <form onSubmit={handleSubmit} className="grid gap-6 py-4">
      {/* Form Settings */}
      <div className="grid grid-cols-3 gap-4 pb-4 border-b">
        {/* Class */}
        <div className="grid gap-2">
          <Label htmlFor="class" className="text-sm font-medium">
            Class
          </Label>
          <Select
            value={formSettings.class}
            onValueChange={(value) => handleSettingChange('class', value)}
            required
          >
            <SelectTrigger>
              {isLoadingClasses ? (
                <div className="flex items-center">
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Loading classes...
                </div>
              ) : (
                <>
                  <Users className="mr-2 h-4 w-4 text-gray-400" />
                  <SelectValue placeholder="Select Class" />
                </>
              )}
            </SelectTrigger>
            <SelectContent>
              {classes && classes.length > 0 ? (
                classes.map((cls: { id: string, name: string }) => (
                  <SelectItem key={cls.id} value={cls.name}>
                    {cls.name}
                  </SelectItem>
                ))
              ) : (
                <div className="p-2 text-center text-sm text-gray-500">
                  No classes found
                </div>
              )}
            </SelectContent>
          </Select>
        </div>

        {/* Subject */}
        <div className="grid gap-2">
          <Label htmlFor="subject" className="text-sm font-medium">
            Subject
          </Label>
          <Select
            value={formSettings.subject}
            onValueChange={(value) => handleSettingChange('subject', value)}
            disabled={!formSettings.class || isLoadingSubjects}
            required
          >
            <SelectTrigger>
              {isLoadingSubjects ? (
                <div className="flex items-center">
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Loading subjects...
                </div>
              ) : (
                <>
                  <BookOpen className="mr-2 h-4 w-4 text-gray-400" />
                  <SelectValue placeholder="Select Subject" />
                </>
              )}
            </SelectTrigger>
            <SelectContent>
              {subjects && subjects.length > 0 ? (
                subjects.map((subject: { id: string, name: string }) => (
                  <SelectItem key={subject.id} value={subject.name}>
                    {subject.name}
                  </SelectItem>
                ))
              ) : (
                <div className="p-2 text-center text-sm text-gray-500">
                  {formSettings.class ? 'No subjects found for this class' : 'Select a class first'}
                </div>
              )}
            </SelectContent>
          </Select>
        </div>

        {/* Semester */}
        <div className="grid gap-2">
          <Label htmlFor="term" className="text-sm font-medium">
            Semester
          </Label>
          <Select
            value={formSettings.term}
            onValueChange={(value) => handleSettingChange('term', value)}
            required
          >
            <SelectTrigger>
              <SelectValue placeholder="Select semester" />
            </SelectTrigger>
            <SelectContent>
              {terms.map(term => (
                <SelectItem key={term} value={term}>
                  {term}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Academic Year */}
        <div className="grid gap-2">
          <Label htmlFor="academicYear" className="text-sm font-medium">
            Academic Year
          </Label>
          <Select
            value={formSettings.academicYear}
            onValueChange={(value) => handleSettingChange('academicYear', value)}
            required
          >
            <SelectTrigger>
              <SelectValue placeholder="Select academic year" />
            </SelectTrigger>
            <SelectContent>
              {academicYears.map(year => (
                <SelectItem key={year} value={year}>
                  {year}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Total Marks */}
        <div className="grid gap-2">
          <Label htmlFor="totalMarks" className="text-sm font-medium">
            Total Marks
          </Label>
          <Input
            id="totalMarks"
            type="number"
            min="1"
            max="1000"
            value={formSettings.totalMarks}
            onChange={(e) => handleSettingChange('totalMarks', parseInt(e.target.value) || 100)}
            required
          />
        </div>
      </div>

      {/* Student List */}
      {isLoadingStudents ? (
        <div className="mt-4 p-8 text-center border rounded-md">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
          <p className="text-gray-500">Loading students...</p>
        </div>
      ) : formSettings.class && studentMarks.length > 0 ? (
        <div className="mt-4 border rounded-md overflow-hidden">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">#</TableHead>
                <TableHead className="w-24">SID</TableHead>
                <TableHead>Student</TableHead>
                <TableHead>Marks (out of {formSettings.totalMarks})</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {studentMarks.map((student, index) => {
                const studentData = students?.find((s: any) => s.id === student.studentId);
                return (
                  <TableRow key={student.studentId}>
                    <TableCell className="text-center">{index + 1}</TableCell>
                    <TableCell>
                      <span className="font-mono text-xs font-medium bg-gray-100 px-2 py-1 rounded">
                        {studentData?.sid || ''}
                      </span>
                    </TableCell>
                    <TableCell>{student.studentName}</TableCell>
                    <TableCell>
                      <Input
                        type="number"
                        min="0"
                        max={formSettings.totalMarks}
                        value={student.obtainedMarks || ''}
                        onChange={(e) => handleMarkChange(student.studentId, parseInt(e.target.value) || 0)}
                        className="w-24 text-right"
                      />
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </div>
      ) : formSettings.class ? (
        <div className="py-8 text-center text-gray-500">
          No students found in this class
        </div>
      ) : (
        <div className="py-8 text-center text-gray-500">
          Please select a class to load students
        </div>
      )}

      <DialogFooter>
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={!formSettings.class || !formSettings.subject || studentMarks.every(s => s.obtainedMarks === 0)}
        >
          Save Marks
        </Button>
      </DialogFooter>
    </form>
  );
}

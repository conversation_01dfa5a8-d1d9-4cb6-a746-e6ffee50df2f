import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    // Check if we're using the mock client
    const isMockClient = !!(prisma as any).constructor.name === 'MockPrismaClient'
    
    // Try to connect to the database
    let dbStatus = 'unknown'
    let error = null
    let mockStatus = isMockClient
    
    try {
      // Try to query the database
      const classCount = await prisma.class.count()
      const studentCount = await prisma.student.count()
      const teacherCount = await prisma.teacher.count()
      
      dbStatus = 'connected'
      
      return NextResponse.json({
        status: dbStatus,
        isMockClient: mockStatus,
        counts: {
          classes: classCount,
          students: studentCount,
          teachers: teacherCount
        }
      })
    } catch (err) {
      error = err.message || 'Unknown error'
      dbStatus = 'error'
      
      return NextResponse.json({
        status: dbStatus,
        isMockClient: mockStatus,
        error
      }, { status: 500 })
    }
  } catch (error) {
    return NextResponse.json({
      status: 'error',
      error: error.message || 'Unknown error'
    }, { status: 500 })
  }
}

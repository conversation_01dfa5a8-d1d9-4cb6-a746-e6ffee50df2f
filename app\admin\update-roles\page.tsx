'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/app/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/app/components/ui/card'
import { Alert, AlertDescription, AlertTitle } from '@/app/components/ui/alert'
import { CheckCircle, AlertCircle, RefreshCw } from 'lucide-react'

export default function UpdateRolesPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)

  const updateRoles = async () => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await fetch('/api/db/update-roles')
      const data = await response.json()
      
      if (response.ok) {
        setResult(data)
      } else {
        setError(data.error || 'Failed to update roles')
      }
    } catch (err) {
      setError('An error occurred while updating roles')
      console.error(err)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="container mx-auto py-10">
      <Card className="w-full max-w-3xl mx-auto">
        <CardHeader>
          <CardTitle>Update User Roles</CardTitle>
          <CardDescription>
            This utility will update all user roles to match the new role system:
            'SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'ACCOUNTANT', 'TEACHER'
          </CardDescription>
        </CardHeader>
        <CardContent>
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          {result && (
            <Alert variant="success" className="mb-4 bg-green-50 border-green-200">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertTitle className="text-green-600">Success</AlertTitle>
              <AlertDescription>
                <p className="mb-2">Successfully updated {result.usersUpdated} user roles.</p>
                {result.updatedUsers && result.updatedUsers.length > 0 && (
                  <div>
                    <h4 className="font-semibold mb-1">Updated Users:</h4>
                    <ul className="list-disc pl-5">
                      {result.updatedUsers.map((user: any, index: number) => (
                        <li key={index}>
                          {user.email}: {user.oldRole} → {user.newRole}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </AlertDescription>
            </Alert>
          )}
          
          <div className="flex items-center justify-center p-6">
            <Button 
              onClick={updateRoles} 
              disabled={isLoading}
              size="lg"
            >
              {isLoading ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Updating Roles...
                </>
              ) : (
                'Update User Roles'
              )}
            </Button>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <p className="text-sm text-gray-500">
            This action will map existing roles to the new role system.
          </p>
        </CardFooter>
      </Card>
    </div>
  )
}

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import bcrypt from 'bcryptjs';

export async function GET() {
  return POST();
}

export async function POST() {
  try {
    console.log('Creating Data Encoder user...');

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (existingUser) {
      console.log('Data Encoder user already exists, updating role...');
      
      // Update the existing user's role
      const hashedPassword = await bcrypt.hash('data@123', 10);
      const updatedUser = await prisma.user.update({
        where: { email: '<EMAIL>' },
        data: {
          password: hashedPassword,
          role: 'DATA_ENCODER',
          status: 'active'
        }
      });

      return NextResponse.json({
        status: 'success',
        message: 'Data Encoder user updated successfully',
        user: {
          id: updatedUser.id,
          email: updatedUser.email,
          name: updatedUser.name,
          role: updatedUser.role,
          password: 'data@123' // Only for testing
        }
      });
    } else {
      console.log('Creating new Data Encoder user...');
      
      // Create new Data Encoder user
      const hashedPassword = await bcrypt.hash('data@123', 10);
      const newUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: hashedPassword,
          name: 'Data Encoder',
          role: 'DATA_ENCODER',
          status: 'active'
        }
      });

      return NextResponse.json({
        status: 'success',
        message: 'Data Encoder user created successfully',
        user: {
          id: newUser.id,
          email: newUser.email,
          name: newUser.name,
          role: newUser.role,
          password: 'data@123' // Only for testing
        }
      });
    }
  } catch (error) {
    console.error('Error creating Data Encoder user:', error);
    
    return NextResponse.json({
      status: 'error',
      message: 'Failed to create Data Encoder user',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET - Fetch all HR teacher assignments
export async function GET() {
  try {
    console.log('Fetching HR teacher assignments')

    // Fetch all HR teacher records with teacher and class details
    const hrTeachers = await prisma.hRTeacher.findMany({
      include: {
        teacher: {
          select: {
            id: true,
            name: true,
            email: true,
            subject: true,
            mobile: true,
            fatherName: true,
            gender: true
          }
        },
        class: {
          select: {
            id: true,
            name: true,
            totalStudents: true,
            totalSubjects: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    console.log(`Found ${hrTeachers.length} HR teacher assignments`)

    return NextResponse.json(hrTeachers)
  } catch (error) {
    console.error('Error fetching HR teacher assignments:', error)
    return NextResponse.json(
      { error: 'Failed to fetch HR teacher assignments' },
      { status: 500 }
    )
  }
}

// POST - Assign a teacher as HR teacher to a class
export async function POST(request: Request) {
  try {
    console.log('Assigning HR teacher to class')
    
    const data = await request.json()
    
    // Validate required fields
    if (!data.teacherId || !data.classId) {
      return NextResponse.json(
        { error: 'Teacher ID and Class ID are required' },
        { status: 400 }
      )
    }
    
    // Check if the teacher exists in either Teacher table or User table with teacher role
    let teacher = await prisma.teacher.findUnique({
      where: { id: data.teacherId }
    })

    let teacherFromUser = null
    if (!teacher) {
      teacherFromUser = await prisma.user.findFirst({
        where: {
          id: data.teacherId,
          role: 'TEACHER',
          status: 'active'
        }
      })
    }

    if (!teacher && !teacherFromUser) {
      return NextResponse.json(
        { error: 'Teacher not found in either Teacher table or User table with teacher role' },
        { status: 404 }
      )
    }

    const teacherName = teacher?.name || teacherFromUser?.name || 'Unknown Teacher'
    
    // Check if the class exists
    const classObj = await prisma.class.findUnique({
      where: { id: data.classId }
    })
    
    if (!classObj) {
      return NextResponse.json(
        { error: 'Class not found' },
        { status: 404 }
      )
    }
    
    // Check if the class already has an HR teacher
    const existingHRTeacher = await prisma.hRTeacher.findUnique({
      where: { classId: data.classId },
      include: {
        teacher: {
          select: {
            name: true
          }
        }
      }
    })
    
    if (existingHRTeacher) {
      return NextResponse.json(
        { error: `Class ${classObj.name} already has an HR teacher (${existingHRTeacher.teacher.name})` },
        { status: 400 }
      )
    }
    
    // Create the HR teacher assignment
    const hrTeacher = await prisma.hRTeacher.create({
      data: {
        teacherId: data.teacherId,
        classId: data.classId
      },
      include: {
        teacher: {
          select: {
            id: true,
            name: true,
            email: true,
            subject: true
          }
        },
        class: {
          select: {
            id: true,
            name: true
          }
        }
      }
    })
    
    // Also update the class hrTeacherId field for backward compatibility
    await prisma.class.update({
      where: { id: data.classId },
      data: { hrTeacherId: data.teacherId }
    })
    
    console.log(`HR teacher assigned successfully: ${teacher.name} to ${classObj.name}`)
    
    return NextResponse.json({
      message: 'HR teacher assigned successfully',
      hrTeacher
    })
  } catch (error) {
    console.error('Error assigning HR teacher:', error)
    return NextResponse.json(
      { error: 'Failed to assign HR teacher' },
      { status: 500 }
    )
  }
}

// PUT - Update HR teacher assignment
export async function PUT(request: Request) {
  try {
    console.log('Updating HR teacher assignment')
    
    const data = await request.json()
    
    // Validate required fields
    if (!data.classId || !data.teacherId) {
      return NextResponse.json(
        { error: 'Class ID and Teacher ID are required' },
        { status: 400 }
      )
    }
    
    // Check if the HR teacher assignment exists
    const existingHRTeacher = await prisma.hRTeacher.findUnique({
      where: { classId: data.classId }
    })
    
    if (!existingHRTeacher) {
      return NextResponse.json(
        { error: 'HR teacher assignment not found for this class' },
        { status: 404 }
      )
    }
    
    // Check if the new teacher exists in either Teacher table or User table with teacher role
    let teacher = await prisma.teacher.findUnique({
      where: { id: data.teacherId }
    })

    let teacherFromUser = null
    if (!teacher) {
      teacherFromUser = await prisma.user.findFirst({
        where: {
          id: data.teacherId,
          role: 'TEACHER',
          status: 'active'
        }
      })
    }

    if (!teacher && !teacherFromUser) {
      return NextResponse.json(
        { error: 'Teacher not found in either Teacher table or User table with teacher role' },
        { status: 404 }
      )
    }
    
    // Update the HR teacher assignment
    const updatedHRTeacher = await prisma.hRTeacher.update({
      where: { classId: data.classId },
      data: { teacherId: data.teacherId },
      include: {
        teacher: {
          select: {
            id: true,
            name: true,
            email: true,
            subject: true
          }
        },
        class: {
          select: {
            id: true,
            name: true
          }
        }
      }
    })
    
    // Also update the class hrTeacherId field for backward compatibility
    await prisma.class.update({
      where: { id: data.classId },
      data: { hrTeacherId: data.teacherId }
    })
    
    console.log(`HR teacher updated successfully`)
    
    return NextResponse.json({
      message: 'HR teacher updated successfully',
      hrTeacher: updatedHRTeacher
    })
  } catch (error) {
    console.error('Error updating HR teacher:', error)
    return NextResponse.json(
      { error: 'Failed to update HR teacher' },
      { status: 500 }
    )
  }
}

// DELETE - Remove HR teacher assignment
export async function DELETE(request: Request) {
  try {
    console.log('Removing HR teacher assignment')
    
    const data = await request.json()
    
    // Validate required fields
    if (!data.classId) {
      return NextResponse.json(
        { error: 'Class ID is required' },
        { status: 400 }
      )
    }
    
    // Check if the HR teacher assignment exists
    const existingHRTeacher = await prisma.hRTeacher.findUnique({
      where: { classId: data.classId },
      include: {
        teacher: { select: { name: true } },
        class: { select: { name: true } }
      }
    })
    
    if (!existingHRTeacher) {
      return NextResponse.json(
        { error: 'HR teacher assignment not found for this class' },
        { status: 404 }
      )
    }
    
    // Remove the HR teacher assignment
    await prisma.hRTeacher.delete({
      where: { classId: data.classId }
    })
    
    // Also update the class hrTeacherId field to null
    await prisma.class.update({
      where: { id: data.classId },
      data: { hrTeacherId: null }
    })
    
    console.log(`HR teacher removed successfully: ${existingHRTeacher.teacher.name} from ${existingHRTeacher.class.name}`)
    
    return NextResponse.json({
      message: 'HR teacher removed successfully'
    })
  } catch (error) {
    console.error('Error removing HR teacher:', error)
    return NextResponse.json(
      { error: 'Failed to remove HR teacher' },
      { status: 500 }
    )
  }
}

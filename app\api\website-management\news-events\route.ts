import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET all news and events
export async function GET(request: Request) {
  try {
    // Get query parameters
    const url = new URL(request.url)
    const category = url.searchParams.get('category')

    // Check if prisma is available
    if (!prisma) {
      console.error('Prisma client is not available')
      // Return mock data for development
      const mockData = [
        {
          id: '1',
          title: 'New Science Lab Opening',
          content: 'We are excited to announce the opening of our new state-of-the-art science laboratory. This facility will provide our students with hands-on learning experiences in biology, chemistry, and physics. The lab is equipped with the latest technology and equipment to support our science curriculum.',
          excerpt: 'Alfalah Islamic School opens a new state-of-the-art science laboratory to enhance STEM education.',
          imageUrl: '/images/science-lab.jpg',
          date: new Date('2023-09-15').toISOString(),
          category: 'news',
          tags: 'science,laboratory,stem',
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: '2',
          title: 'Back to School Night',
          content: 'Join us for our Back to School Night on September 5th at 6:00 PM. This is an opportunity for parents to meet teachers, learn about the curriculum, and get important information about the upcoming school year. We look forward to seeing all parents there!',
          excerpt: 'Parents are invited to meet teachers and learn about the curriculum for the new school year.',
          imageUrl: '/images/back-to-school.jpg',
          date: new Date('2023-09-05').toISOString(),
          category: 'event',
          tags: 'parents,teachers,curriculum',
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ]

      if (category) {
        return NextResponse.json(mockData.filter(item => item.category === category))
      }

      return NextResponse.json(mockData)
    }

    // Build query
    const where = category ? { category } : {}

    const newsEvents = await prisma.newsEvent.findMany({
      where,
      orderBy: {
        date: 'desc'
      }
    })

    return NextResponse.json(newsEvents)
  } catch (error) {
    console.error('Error fetching news and events:', error)

    // Return mock data for development
    const mockData = [
      {
        id: '1',
        title: 'New Science Lab Opening',
        content: 'We are excited to announce the opening of our new state-of-the-art science laboratory. This facility will provide our students with hands-on learning experiences in biology, chemistry, and physics. The lab is equipped with the latest technology and equipment to support our science curriculum.',
        excerpt: 'Alfalah Islamic School opens a new state-of-the-art science laboratory to enhance STEM education.',
        imageUrl: '/images/science-lab.jpg',
        date: new Date('2023-09-15').toISOString(),
        category: 'news',
        tags: 'science,laboratory,stem',
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '2',
        title: 'Back to School Night',
        content: 'Join us for our Back to School Night on September 5th at 6:00 PM. This is an opportunity for parents to meet teachers, learn about the curriculum, and get important information about the upcoming school year. We look forward to seeing all parents there!',
        excerpt: 'Parents are invited to meet teachers and learn about the curriculum for the new school year.',
        imageUrl: '/images/back-to-school.jpg',
        date: new Date('2023-09-05').toISOString(),
        category: 'event',
        tags: 'parents,teachers,curriculum',
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ]

    if (category) {
      return NextResponse.json(mockData.filter(item => item.category === category))
    }

    return NextResponse.json(mockData)
  }
}

// POST a new news/event
export async function POST(request: Request) {
  try {
    const data = await request.json()

    // Validate required fields
    if (!data.title || !data.content || !data.excerpt || !data.imageUrl || !data.date || !data.category) {
      return NextResponse.json({
        error: 'All fields are required'
      }, { status: 400 })
    }

    // Validate category
    if (data.category !== 'news' && data.category !== 'event') {
      return NextResponse.json({
        error: 'Category must be either "news" or "event"'
      }, { status: 400 })
    }

    // Create the new news/event
    const newsEvent = await prisma.newsEvent.create({
      data: {
        title: data.title,
        content: data.content,
        excerpt: data.excerpt,
        imageUrl: data.imageUrl,
        date: new Date(data.date),
        category: data.category,
        tags: data.tags || null,
        isActive: data.isActive !== undefined ? data.isActive : true
      }
    })

    return NextResponse.json(newsEvent)
  } catch (error) {
    console.error('Error creating news/event:', error)
    return NextResponse.json({
      error: error.message || 'Failed to create news/event'
    }, { status: 500 })
  }
}

import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET all HR teacher assignments
export async function GET(request: Request) {
  try {
    console.log('Fetching all HR teacher assignments')
    
    // Get query parameters
    const { searchParams } = new URL(request.url)
    const classId = searchParams.get('classId')
    
    // Build the where clause
    let whereClause: any = {}
    
    if (classId) {
      whereClause.id = classId
    }
    
    // Fetch classes with their HR teachers
    const classes = await prisma.class.findMany({
      where: whereClause,
      include: {
        hrTeacher: {
          select: {
            id: true,
            name: true,
            email: true,
            subject: true
          }
        }
      },
      orderBy: {
        name: 'asc'
      }
    })
    
    // Transform the data to match the expected format
    const formattedClasses = classes.map(cls => ({
      id: cls.id,
      name: cls.name,
      hrTeacherId: cls.hrTeacherId,
      hrTeacherName: cls.hrTeacher ? cls.hrTeacher.name : null,
      hrTeacherEmail: cls.hrTeacher ? cls.hrTeacher.email : null,
      hrTeacherSubject: cls.hrTeacher ? cls.hrTeacher.subject : null,
      totalStudents: cls.totalStudents,
      totalSubjects: cls.totalSubjects
    }))
    
    console.log(`Returning ${formattedClasses.length} HR teacher assignments`)
    return NextResponse.json(formattedClasses)
  } catch (error) {
    console.error('Error fetching HR teacher assignments:', error)
    return NextResponse.json(
      { error: 'Failed to fetch HR teacher assignments' },
      { status: 500 }
    )
  }
}

// POST to assign an HR teacher to a class
export async function POST(request: Request) {
  try {
    console.log('Assigning HR teacher to class')
    
    // Get the request body
    const data = await request.json()
    
    // Validate required fields
    if (!data.teacherId || !data.classId) {
      return NextResponse.json(
        { error: 'Teacher ID and Class ID are required' },
        { status: 400 }
      )
    }
    
    // Check if the teacher exists
    const teacher = await prisma.teacher.findUnique({
      where: { id: data.teacherId }
    })
    
    if (!teacher) {
      return NextResponse.json(
        { error: 'Teacher not found' },
        { status: 404 }
      )
    }
    
    // Check if the class exists
    const classObj = await prisma.class.findUnique({
      where: { id: data.classId },
      include: {
        hrTeacher: true
      }
    })
    
    if (!classObj) {
      return NextResponse.json(
        { error: 'Class not found' },
        { status: 404 }
      )
    }
    
    // Check if this is an update operation
    const isUpdate = request.url.includes('update=true')
    
    // Check if the class already has an HR teacher
    if (classObj.hrTeacherId && classObj.hrTeacherId !== data.teacherId && !isUpdate) {
      return NextResponse.json(
        { error: `Class ${classObj.name} already has an HR teacher (${classObj.hrTeacher?.name})` },
        { status: 400 }
      )
    }
    
    // Update the class with the new HR teacher
    const updatedClass = await prisma.class.update({
      where: { id: data.classId },
      data: { hrTeacherId: data.teacherId },
      include: {
        hrTeacher: true
      }
    })
    
    // Create or update the teacher assignment record
    let assignment = await prisma.teacherAssignment.findFirst({
      where: {
        teacherId: data.teacherId,
        classId: data.classId,
        isHRTeacher: true
      }
    })
    
    if (assignment) {
      // Update existing assignment
      assignment = await prisma.teacherAssignment.update({
        where: { id: assignment.id },
        data: {
          updatedAt: new Date()
        }
      })
    } else {
      // Create new assignment
      assignment = await prisma.teacherAssignment.create({
        data: {
          teacherId: data.teacherId,
          classId: data.classId,
          isHRTeacher: true
        }
      })
    }
    
    // Format the response
    const response = {
      message: isUpdate ? 'HR teacher updated successfully' : 'HR teacher assigned successfully',
      class: {
        id: updatedClass.id,
        name: updatedClass.name,
        hrTeacherId: updatedClass.hrTeacherId,
        hrTeacherName: updatedClass.hrTeacher?.name || null
      },
      assignment: {
        id: assignment.id,
        teacherId: assignment.teacherId,
        classId: assignment.classId,
        isHRTeacher: assignment.isHRTeacher
      }
    }
    
    return NextResponse.json(response)
  } catch (error) {
    console.error('Error assigning HR teacher:', error)
    return NextResponse.json(
      { error: 'Failed to assign HR teacher' },
      { status: 500 }
    )
  }
}

// DELETE to remove an HR teacher from a class
export async function DELETE(request: Request) {
  try {
    console.log('Removing HR teacher from class')
    
    // Get the request body
    const data = await request.json()
    
    // Validate required fields
    if (!data.classId) {
      return NextResponse.json(
        { error: 'Class ID is required' },
        { status: 400 }
      )
    }
    
    // Check if the class exists
    const classObj = await prisma.class.findUnique({
      where: { id: data.classId }
    })
    
    if (!classObj) {
      return NextResponse.json(
        { error: 'Class not found' },
        { status: 404 }
      )
    }
    
    // Check if the class has an HR teacher
    if (!classObj.hrTeacherId) {
      return NextResponse.json(
        { error: `Class ${classObj.name} does not have an HR teacher` },
        { status: 400 }
      )
    }
    
    // Store the current HR teacher ID for deleting the assignment
    const currentHRTeacherId = classObj.hrTeacherId
    
    // Update the class to remove the HR teacher
    const updatedClass = await prisma.class.update({
      where: { id: data.classId },
      data: { hrTeacherId: null }
    })
    
    // Delete the teacher assignment record
    const assignment = await prisma.teacherAssignment.findFirst({
      where: {
        teacherId: currentHRTeacherId,
        classId: data.classId,
        isHRTeacher: true
      }
    })
    
    if (assignment) {
      await prisma.teacherAssignment.delete({
        where: { id: assignment.id }
      })
    }
    
    // Format the response
    const response = {
      message: 'HR teacher removed successfully',
      class: {
        id: updatedClass.id,
        name: updatedClass.name,
        hrTeacherId: null
      }
    }
    
    return NextResponse.json(response)
  } catch (error) {
    console.error('Error removing HR teacher:', error)
    return NextResponse.json(
      { error: 'Failed to remove HR teacher' },
      { status: 500 }
    )
  }
}

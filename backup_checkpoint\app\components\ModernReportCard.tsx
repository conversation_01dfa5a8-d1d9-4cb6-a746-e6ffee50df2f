"use client"

import React, { useState, useRef, useEffect } from 'react'
import { But<PERSON> } from './ui/button'
import { Input } from './ui/input'
import { 
  FileText, 
  Calendar, 
  Printer, 
  Download,
  CheckCircle,
  Star,
  Trophy,
  User,
  GraduationCap,
  Search,
  ChevronDown
} from 'lucide-react'
import { useReactToPrint } from 'react-to-print'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu"

interface SubjectGrade {
  name: string
  marks: number
  totalMarks: number
  grade: string
  remarks: string
}

interface AttendanceData {
  present: number
  absent: number
  totalDays: number
  percentage: number
}

// Sample data for students
const sampleStudents = [
  { id: '1A1', name: '<PERSON>', class: '1A' },
  { id: '1A2', name: '<PERSON>', class: '1A' },
  { id: '2B1', name: '<PERSON>', class: '2B' },
  { id: '2B2', name: '<PERSON>', class: '2B' },
  { id: '3C1', name: '<PERSON><PERSON><PERSON>', class: '3C' },
  { id: '7A1', name: '<PERSON>', class: '7A' },
  { id: '7A2', name: '<PERSON> <PERSON>', class: '7A' },
  { id: '9C1', name: 'Omar Hassan', class: '9C' },
]

export function ModernReportCard() {
  const [step, setStep] = useState<'form' | 'preview'>('form')
  const [isGenerating, setIsGenerating] = useState(false)
  const [isGenerated, setIsGenerated] = useState(false)
  const printRef = useRef<HTMLDivElement>(null)
  
  // Get current academic year (e.g., 2023-2024)
  const getCurrentAcademicYear = () => {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth();
    
    // If we're in the second half of the year (July-December), academic year is current-next
    // Otherwise it's previous-current
    if (month >= 6) { // July or later
      return `${year}-${year + 1}`;
    } else {
      return `${year - 1}-${year}`;
    }
  };
  
  // Form state
  const [formData, setFormData] = useState({
    studentId: '',
    studentName: '',
    class: '',
    academicYear: getCurrentAcademicYear(),
    semester: 'First Semester'
  })
  
  // Add state for dropdown
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  
  // Filter students based on search
  const filteredStudents = searchQuery.trim() === '' 
    ? sampleStudents 
    : sampleStudents.filter(student => 
        student.id.toLowerCase().includes(searchQuery.toLowerCase()) || 
        student.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
  
  // Effect to automatically populate student name and class based on ID
  useEffect(() => {
    if (formData.studentId) {
      const student = sampleStudents.find(s => s.id === formData.studentId);
      if (student) {
        setFormData(prev => ({
          ...prev,
          studentName: student.name,
          class: student.class
        }));
      } else {
        // Clear name and class if student ID doesn't match
        setFormData(prev => ({
          ...prev,
          studentName: '',
          class: ''
        }));
      }
    }
  }, [formData.studentId]);
  
  // Sample report card data - in a real app, this would be fetched from an API
  const [reportData, setReportData] = useState({
    subjects: [
      { name: "Mathematics", marks: 85, totalMarks: 100, grade: "A", remarks: "Excellent problem-solving skills" },
      { name: "Science", marks: 78, totalMarks: 100, grade: "B+", remarks: "Good understanding of concepts" },
      { name: "English", marks: 92, totalMarks: 100, grade: "A+", remarks: "Outstanding communication skills" },
      { name: "Social Studies", marks: 80, totalMarks: 100, grade: "A-", remarks: "Strong analytical thinking" },
      { name: "Computer Science", marks: 95, totalMarks: 100, grade: "A+", remarks: "Exceptional programming abilities" },
      { name: "Physical Education", marks: 88, totalMarks: 100, grade: "A", remarks: "Great team player" },
      { name: "Art", marks: 83, totalMarks: 100, grade: "A-", remarks: "Creative and innovative" }
    ],
    attendance: {
      present: 85,
      absent: 5,
      totalDays: 90,
      percentage: 94.4
    },
    teacherRemarks: "A diligent student who consistently shows dedication to learning. Participates actively in class discussions and demonstrates leadership qualities.",
    principalRemarks: "Commendable performance. Keep up the good work and continue to excel in all areas of academic and personal development.",
    classTeacher: "Ms. Sarah Johnson",
    principal: "Dr. Robert Williams",
    gradingSystem: [
      { grade: "A+", range: "95-100", description: "Outstanding" },
      { grade: "A", range: "85-94", description: "Excellent" },
      { grade: "B+", range: "80-84", description: "Very Good" },
      { grade: "B", range: "75-79", description: "Good" },
      { grade: "C+", range: "70-74", description: "Above Average" },
      { grade: "C", range: "65-69", description: "Average" },
      { grade: "D", range: "60-64", description: "Below Average" },
      { grade: "F", range: "Below 60", description: "Needs Improvement" }
    ],
    behavior: {
      discipline: "Excellent",
      cooperation: "Excellent",
      punctuality: "Very Good",
      neatness: "Good"
    }
  })
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData({
      ...formData,
      [name]: value
    })
  }
  
  const handleGenerateReportCard = () => {
    setIsGenerating(true)
    
    // Simulate API call delay
    setTimeout(() => {
      setIsGenerating(false)
      setIsGenerated(true)
      setStep('preview')
    }, 1500)
  }
  
  const handlePrint = () => {
    if (printRef.current) {
      // Use the print function from the hook
      const printFunction = useReactToPrint({
        documentTitle: `Report Card - ${formData.studentName}`,
        // @ts-ignore - The 'content' property is actually supported but not in types
        content: () => printRef.current,
      });
      
      // Execute the print function
      printFunction();
    }
  };
  
  const calculateTotalMarks = () => {
    let total = 0
    let obtained = 0
    
    reportData.subjects.forEach(subject => {
      total += subject.totalMarks
      obtained += subject.marks
    })
    
    return {
      total,
      obtained,
      percentage: ((obtained / total) * 100).toFixed(2)
    }
  }
  
  const getGradeColor = (grade: string) => {
    if (grade.startsWith('A')) return 'text-green-600'
    if (grade.startsWith('B')) return 'text-blue-600'
    if (grade.startsWith('C')) return 'text-yellow-600'
    if (grade.startsWith('D')) return 'text-orange-600'
    if (grade.startsWith('F')) return 'text-red-600'
    return 'text-gray-600'
  }
  
  const getOverallGrade = (percentage: number) => {
    if (percentage >= 95) return 'A+'
    if (percentage >= 85) return 'A'
    if (percentage >= 80) return 'B+'
    if (percentage >= 75) return 'B'
    if (percentage >= 70) return 'C+'
    if (percentage >= 65) return 'C'
    if (percentage >= 60) return 'D'
    return 'F'
  }
  
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Report Card</h1>
          <p className="text-gray-500 mt-1">Generate and view student performance reports</p>
        </div>
        {isGenerated && (
          <Button 
            variant="outline" 
            onClick={() => setStep('form')}
            className="flex items-center gap-2"
          >
            <Search className="h-4 w-4" />
            Generate New
          </Button>
        )}
      </div>
      
      {step === 'form' ? (
        <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
          <div className="p-5 border-b border-gray-100 bg-gray-50/50">
            <h2 className="text-xl font-semibold text-gray-800">Generate Report Card</h2>
            <p className="text-sm text-gray-500 mt-1">Fill in the details to generate a student report card</p>
          </div>

          <form className="p-5" onSubmit={(e) => { e.preventDefault(); handleGenerateReportCard(); }}>
            <div className="grid grid-cols-1 lg:grid-cols-12 gap-5">
              {/* Left column - Student Information and Academic Details */}
              <div className="lg:col-span-8 space-y-5">
                {/* Student Information section */}
                <div className="bg-blue-50 rounded-lg p-3 flex items-center space-x-3">
                  <div className="bg-blue-100 p-2 rounded-md text-blue-700">
                    <User className="h-4 w-4" />
                  </div>
                  <h3 className="text-base font-medium text-blue-800">Student Information</h3>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                  {/* Student ID field with dropdown */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="studentId">Student ID</label>
                    <div className="relative">
                      <Input
                        id="studentId"
                        name="studentId"
                        placeholder="e.g., 1A1, 2B2, etc."
                        value={formData.studentId}
                        onChange={(e) => {
                          handleInputChange(e);
                          setSearchQuery(e.target.value);
                        }}
                        className="h-10 pr-10 text-sm"
                        required
                      />
                      <DropdownMenu open={isSearchOpen} onOpenChange={setIsSearchOpen}>
                        <DropdownMenuTrigger asChild>
                          <button
                            type="button"
                            className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
                            onClick={() => {
                              setSearchQuery(formData.studentId);
                              setIsSearchOpen(true);
                            }}
                          >
                            <ChevronDown className="h-4 w-4" />
                          </button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-[260px] max-h-[240px] overflow-auto p-0">
                          <div className="p-2 border-b">
                            <Input
                              placeholder="Search by ID or name..."
                              value={searchQuery}
                              onChange={(e) => setSearchQuery(e.target.value)}
                              className="h-8"
                            />
                          </div>
                          {filteredStudents.length > 0 ? (
                            filteredStudents.map((student) => (
                              <DropdownMenuItem
                                key={student.id}
                                className="px-3 py-2 cursor-pointer hover:bg-blue-50"
                                onClick={() => {
                                  setFormData(prev => ({
                                    ...prev,
                                    studentId: student.id,
                                    studentName: student.name,
                                    class: student.class
                                  }));
                                  setIsSearchOpen(false);
                                }}
                              >
                                <div className="flex flex-col">
                                  <span className="font-medium text-blue-700">{student.id}</span>
                                  <span className="text-xs text-gray-600">{student.name} ({student.class})</span>
                                </div>
                              </DropdownMenuItem>
                            ))
                          ) : (
                            <div className="px-3 py-2 text-sm text-gray-500">No students found</div>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                  
                  {/* Student Name field (auto-populated) */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="studentName">Full Name</label>
                    <Input
                      id="studentName"
                      name="studentName"
                      placeholder="Auto-populated from Student ID"
                      value={formData.studentName}
                      className="h-10 bg-gray-50 text-sm"
                      readOnly
                    />
                  </div>
                </div>
                
                {/* Academic Details Section */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-5">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="class">Class</label>
                    <Input
                      id="class"
                      name="class"
                      placeholder="Auto-populated from Student ID"
                      value={formData.class}
                      className="h-10 bg-gray-50 text-sm"
                      readOnly
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="academicYear">Academic Year</label>
                    <select
                      id="academicYear"
                      name="academicYear"
                      value={formData.academicYear}
                      onChange={handleInputChange}
                      className="h-10 w-full rounded-md border border-gray-300 text-sm py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    >
                      <option value="2024-2025">2024-2025</option>
                      <option value="2023-2024">2023-2024</option>
                      <option value="2022-2023">2022-2023</option>
                      <option value="2021-2022">2021-2022</option>
                      <option value="2020-2021">2020-2021</option>
                      <option value="2019-2020">2019-2020</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="semester">Semester</label>
                    <div className="grid grid-cols-2 gap-2">
                      <label className="flex items-center p-2 border border-gray-200 rounded-md hover:bg-blue-50 cursor-pointer transition-colors">
                        <input
                          type="radio"
                          name="semester"
                          value="First Semester"
                          checked={formData.semester === 'First Semester'}
                          onChange={handleInputChange}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="ml-2 text-sm text-gray-700">First</span>
                      </label>
                      
                      <label className="flex items-center p-2 border border-gray-200 rounded-md hover:bg-blue-50 cursor-pointer transition-colors">
                        <input
                          type="radio"
                          name="semester"
                          value="Second Semester"
                          checked={formData.semester === 'Second Semester'}
                          onChange={handleInputChange}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="ml-2 text-sm text-gray-700">Second</span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Right column - Action Card */}
              <div className="lg:col-span-4">
                <div className="bg-blue-50 p-5 rounded-lg h-full flex flex-col justify-between">
                  <div className="text-center">
                    <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mx-auto mb-3">
                      <FileText className="h-6 w-6 text-blue-600" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-2">Generate Report Card</h3>
                    <p className="text-sm text-gray-600 mb-6">Click the button below to generate the report for this student</p>
                  </div>
                  
                  <Button
                    type="submit"
                    className="w-full bg-blue-600 hover:bg-blue-700 h-10 text-sm shadow"
                    disabled={
                      !formData.studentId ||
                      !formData.studentName ||
                      !formData.class ||
                      !formData.academicYear
                    }
                  >
                    {isGenerating ? (
                      <div className="flex items-center justify-center">
                        <div className="animate-spin mr-2">
                          <Calendar className="h-4 w-4" />
                        </div>
                        <span>Generating...</span>
                      </div>
                    ) : (
                      <div className="flex items-center justify-center">
                        {isGenerated ? (
                          <>
                            <CheckCircle className="h-4 w-4 mr-2" />
                            <span>View Report Card</span>
                          </>
                        ) : (
                          <>
                            <FileText className="h-4 w-4 mr-2" />
                            <span>Generate Report Card</span>
                          </>
                        )}
                      </div>
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </form>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="flex justify-between items-center bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center space-x-3">
              <Button 
                variant="outline" 
                onClick={() => setStep('form')}
              >
                Back to Form
              </Button>
              <span className="text-sm text-gray-500">
                Report Card for: <span className="font-medium text-gray-900">{formData.studentName}</span>
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                className="flex items-center space-x-2"
                onClick={handlePrint}
              >
                <Printer className="h-4 w-4" />
                <span>Print</span>
              </Button>
              <Button
                className="bg-indigo-600 hover:bg-indigo-700 flex items-center space-x-2"
                onClick={handlePrint}
              >
                <Download className="h-4 w-4" />
                <span>Download PDF</span>
              </Button>
            </div>
          </div>
          
          {/* A4 Size Report Card Preview */}
          <div className="bg-white border border-gray-200 rounded-lg mx-auto shadow-md overflow-hidden">
            <div 
              ref={printRef} 
              className="w-full p-8"
              style={{ maxWidth: '794px', margin: '0 auto' }} // A4 width in pixels at 96 DPI
            >
              {/* Header */}
              <div className="text-center mb-6">
                <h1 className="text-2xl font-bold text-indigo-800">Alfalah School</h1>
                <h2 className="text-xl font-semibold">Student Report Card</h2>
                <div className="mt-2 text-gray-600">
                  {formData.semester} - {formData.academicYear}
                </div>
              </div>
              
              {/* Student Info */}
              <div className="grid grid-cols-2 gap-4 mb-6 border-t border-b border-gray-200 py-4">
                <div>
                  <div className="grid grid-cols-3 mb-2">
                    <span className="text-sm font-medium text-gray-500">Student ID:</span>
                    <span className="text-sm font-semibold col-span-2">{formData.studentId}</span>
                  </div>
                  <div className="grid grid-cols-3 mb-2">
                    <span className="text-sm font-medium text-gray-500">Name:</span>
                    <span className="text-sm font-semibold col-span-2">{formData.studentName}</span>
                  </div>
                  <div className="grid grid-cols-3 mb-2">
                    <span className="text-sm font-medium text-gray-500">Class:</span>
                    <span className="text-sm font-semibold col-span-2">{formData.class}</span>
                  </div>
                  <div className="grid grid-cols-3 mb-2">
                    <span className="text-sm font-medium text-gray-500">Academic Year:</span>
                    <span className="text-sm font-semibold col-span-2">{formData.academicYear}</span>
                  </div>
                  <div className="grid grid-cols-3 mb-2">
                    <span className="text-sm font-medium text-gray-500">Semester:</span>
                    <span className="text-sm font-semibold col-span-2">{formData.semester}</span>
                  </div>
                </div>
              </div>
              
              {/* Academic Performance */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-purple-800 mb-3">Academic Performance</h3>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead>
                      <tr className="bg-gray-100">
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
                        <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Marks Obtained</th>
                        <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Total Marks</th>
                        <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Grade</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remarks</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {reportData.subjects.map((subject, index) => (
                        <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900 font-medium">{subject.name}</td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-center text-gray-500">{subject.marks}</td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-center text-gray-500">{subject.totalMarks}</td>
                          <td className={`px-4 py-3 whitespace-nowrap text-sm text-center font-medium ${getGradeColor(subject.grade)}`}>{subject.grade}</td>
                          <td className="px-4 py-3 text-sm text-gray-500">{subject.remarks}</td>
                        </tr>
                      ))}
                    </tbody>
                    <tfoot>
                      <tr className="bg-gray-100">
                        <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">Total</td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-center font-medium text-purple-600">{calculateTotalMarks().obtained}</td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-center font-medium text-gray-900">{calculateTotalMarks().total}</td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-center font-medium text-purple-600">
                          {getOverallGrade(parseFloat(calculateTotalMarks().percentage))}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-purple-600">{calculateTotalMarks().percentage}%</td>
                      </tr>
                    </tfoot>
                  </table>
                </div>
              </div>
              
              {/* Attendance Record */}
              <div className="mb-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold text-purple-800 mb-3">Attendance Record</h3>
                  <div className="bg-gray-50 rounded-md p-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center p-2 bg-white rounded-md shadow-sm">
                        <p className="text-sm text-gray-500">Total Days</p>
                        <p className="text-2xl font-bold text-gray-900">{reportData.attendance.totalDays}</p>
                      </div>
                      <div className="text-center p-2 bg-white rounded-md shadow-sm">
                        <p className="text-sm text-gray-500">Present</p>
                        <p className="text-2xl font-bold text-green-600">{reportData.attendance.present}</p>
                      </div>
                      <div className="text-center p-2 bg-white rounded-md shadow-sm">
                        <p className="text-sm text-gray-500">Absent</p>
                        <p className="text-2xl font-bold text-red-600">{reportData.attendance.absent}</p>
                      </div>
                      <div className="text-center p-2 bg-white rounded-md shadow-sm">
                        <p className="text-sm text-gray-500">Percentage</p>
                        <p className="text-2xl font-bold text-purple-600">{reportData.attendance.percentage}%</p>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold text-purple-800 mb-3">Behavioral Assessment</h3>
                  <div className="bg-gray-50 rounded-md p-4">
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium">Discipline</span>
                        <span className="text-sm font-medium text-purple-600">{reportData.behavior.discipline}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium">Cooperation</span>
                        <span className="text-sm font-medium text-purple-600">{reportData.behavior.cooperation}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium">Punctuality</span>
                        <span className="text-sm font-medium text-purple-600">{reportData.behavior.punctuality}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium">Neatness</span>
                        <span className="text-sm font-medium text-purple-600">{reportData.behavior.neatness}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Remarks */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-purple-800 mb-3">Remarks</h3>
                <div className="grid grid-cols-1 gap-4">
                  <div className="bg-gray-50 p-4 rounded-md">
                    <h4 className="text-sm font-medium text-gray-700 mb-2">Class Teacher's Remarks</h4>
                    <p className="text-sm text-gray-600">{reportData.teacherRemarks}</p>
                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <p className="text-sm font-medium text-gray-700">{reportData.classTeacher}</p>
                      <p className="text-xs text-gray-500">Class Teacher</p>
                    </div>
                  </div>
                  
                  <div className="bg-gray-50 p-4 rounded-md">
                    <h4 className="text-sm font-medium text-gray-700 mb-2">Principal's Remarks</h4>
                    <p className="text-sm text-gray-600">{reportData.principalRemarks}</p>
                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <p className="text-sm font-medium text-gray-700">{reportData.principal}</p>
                      <p className="text-xs text-gray-500">Principal</p>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Grading System */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-purple-800 mb-3">Grading System</h3>
                <div className="grid grid-cols-4 gap-2">
                  {reportData.gradingSystem.map((grade, index) => (
                    <div key={index} className="bg-gray-50 p-2 rounded-md text-center">
                      <span className={`text-sm font-bold ${getGradeColor(grade.grade)}`}>{grade.grade}</span>
                      <p className="text-xs text-gray-500">{grade.range}</p>
                      <p className="text-xs text-gray-700">{grade.description}</p>
                    </div>
                  ))}
                </div>
              </div>
              
              {/* Footer */}
              <div className="mt-8 pt-4 border-t border-gray-200 text-center">
                <p className="text-sm text-gray-500">This report card was generated on {new Date().toLocaleDateString()}</p>
                <p className="text-sm text-gray-500 mt-1">
                  <span className="flex items-center justify-center">
                    <Trophy className="h-4 w-4 text-yellow-500 mr-1" />
                    <span>Excellence in Education</span>
                    <Star className="h-4 w-4 text-yellow-500 ml-1" />
                  </span>
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
} 
"use client"

import React, { useState, useMemo, RefObject } from 'react'
import { Search, Filter, Edit, Trash2, MoreHorizontal, ChevronLeft, ChevronRight } from 'lucide-react'
import { Button } from '../button'
import { Input } from '../input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../dropdown-menu'
import { useDragScroll } from '../../../hooks/useTouchGestures'
import { cn } from '../../../lib/utils'
import { ResponsiveTableColumn } from './index'

interface TabletScrollViewProps<T = any> {
  data: T[]
  columns: ResponsiveTableColumn<T>[]
  isLoading: boolean
  onRowClick?: (row: T) => void
  onRowEdit?: (row: T) => void
  onRowDelete?: (row: T) => void
  searchable: boolean
  filterable: boolean
  sortable: boolean
  emptyMessage: string
  loadingMessage: string
  stickyHeader: boolean
  getRowKey: (row: T) => string
  pageSize: number
  scrollRef: RefObject<HTMLDivElement>
  isScrolling: boolean
}

export function TabletScrollView<T = any>({
  data,
  columns,
  isLoading,
  onRowClick,
  onRowEdit,
  onRowDelete,
  searchable,
  filterable,
  sortable,
  emptyMessage,
  loadingMessage,
  stickyHeader,
  getRowKey,
  pageSize,
  scrollRef,
  isScrolling
}: TabletScrollViewProps<T>) {
  const [searchTerm, setSearchTerm] = useState('')
  const [sortColumn, setSortColumn] = useState<string | null>(null)
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc')
  const [currentPage, setCurrentPage] = useState(1)

  const { isDragging } = useDragScroll()

  // Filter and sort data
  const processedData = useMemo(() => {
    let filtered = data

    // Apply search filter
    if (searchTerm) {
      filtered = data.filter(row => {
        return columns.some(column => {
          if (!column.accessorKey) return false
          const value = row[column.accessorKey]
          return String(value).toLowerCase().includes(searchTerm.toLowerCase())
        })
      })
    }

    // Apply sorting
    if (sortColumn) {
      const column = columns.find(col => col.id === sortColumn)
      if (column?.accessorKey) {
        filtered = [...filtered].sort((a, b) => {
          const aVal = a[column.accessorKey!]
          const bVal = b[column.accessorKey!]
          
          if (aVal === bVal) return 0
          
          const comparison = aVal < bVal ? -1 : 1
          return sortDirection === 'asc' ? comparison : -comparison
        })
      }
    }

    return filtered
  }, [data, searchTerm, sortColumn, sortDirection, columns])

  // Paginate data
  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize
    return processedData.slice(startIndex, startIndex + pageSize)
  }, [processedData, currentPage, pageSize])

  const totalPages = Math.ceil(processedData.length / pageSize)

  const handleSort = (columnId: string) => {
    if (!sortable) return
    
    const column = columns.find(col => col.id === columnId)
    if (!column?.sortable) return

    if (sortColumn === columnId) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortColumn(columnId)
      setSortDirection('asc')
    }
  }

  const renderCellValue = (column: ResponsiveTableColumn<T>, row: T) => {
    if (column.cell) {
      return column.cell(row)
    }
    if (column.accessorKey) {
      const value = row[column.accessorKey]
      return value !== null && value !== undefined ? String(value) : '-'
    }
    return '-'
  }

  if (isLoading) {
    return (
      <div className="tablet-scroll-view p-6">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-gray-500">{loadingMessage}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="tablet-scroll-view space-y-4 p-4">
      {/* Search and controls */}
      {searchable && (
        <div className="flex gap-4 items-center">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 h-12"
            />
          </div>
          {filterable && (
            <Button variant="outline" size="sm">
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </Button>
          )}
        </div>
      )}

      {/* Horizontal scroll container with enhanced touch support */}
      <div className="relative">
        {/* Scroll shadows */}
        <div className="absolute inset-y-0 left-0 w-8 bg-gradient-to-r from-white to-transparent pointer-events-none z-10 opacity-0 transition-opacity duration-300" 
             style={{ opacity: isScrolling ? 1 : 0 }} />
        <div className="absolute inset-y-0 right-0 w-8 bg-gradient-to-l from-white to-transparent pointer-events-none z-10" />

        <div
          ref={scrollRef}
          className={cn(
            "overflow-x-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100",
            "touch-pan-x", // Enable horizontal touch scrolling
            isDragging && "cursor-grabbing select-none",
            !isDragging && "cursor-grab"
          )}
          style={{
            scrollbarWidth: 'thin',
            WebkitOverflowScrolling: 'touch'
          }}
        >
          <Table className="min-w-full">
            <TableHeader className={cn(stickyHeader && "sticky top-0 z-20 bg-white")}>
              <TableRow className="bg-gray-50 border-b-2 border-gray-200">
                {columns.map((column) => (
                  <TableHead
                    key={column.id}
                    className={cn(
                      "font-semibold text-gray-900 whitespace-nowrap px-4 py-3 text-left",
                      column.minWidth && `min-w-[${column.minWidth}px]`,
                      column.maxWidth && `max-w-[${column.maxWidth}px]`,
                      column.sticky && "sticky right-0 bg-gray-50 border-l border-gray-200 z-10",
                      column.sortable && sortable && "cursor-pointer hover:bg-gray-100",
                      column.className
                    )}
                    style={{
                      minWidth: column.minWidth || 120,
                      maxWidth: column.maxWidth
                    }}
                    onClick={() => handleSort(column.id)}
                  >
                    <div className="flex items-center gap-2">
                      {column.header}
                      {column.sortable && sortable && sortColumn === column.id && (
                        <span className="text-xs">
                          {sortDirection === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </div>
                  </TableHead>
                ))}
                {(onRowEdit || onRowDelete) && (
                  <TableHead className="sticky right-0 bg-gray-50 border-l border-gray-200 min-w-[100px] w-[100px] z-10">
                    Actions
                  </TableHead>
                )}
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginatedData.length === 0 ? (
                <TableRow>
                  <TableCell 
                    colSpan={columns.length + (onRowEdit || onRowDelete ? 1 : 0)} 
                    className="h-32 text-center text-gray-500"
                  >
                    {emptyMessage}
                  </TableCell>
                </TableRow>
              ) : (
                paginatedData.map((row) => (
                  <TableRow
                    key={getRowKey(row)}
                    className={cn(
                      "hover:bg-gray-50 transition-colors border-b border-gray-100",
                      onRowClick && "cursor-pointer"
                    )}
                    onClick={() => onRowClick?.(row)}
                  >
                    {columns.map((column) => (
                      <TableCell
                        key={column.id}
                        className={cn(
                          "px-4 py-3 whitespace-nowrap",
                          column.sticky && "sticky right-0 bg-white border-l border-gray-200 z-10",
                          column.className
                        )}
                        style={{
                          minWidth: column.minWidth || 120,
                          maxWidth: column.maxWidth
                        }}
                      >
                        <div className="truncate">
                          {renderCellValue(column, row)}
                        </div>
                      </TableCell>
                    ))}
                    {(onRowEdit || onRowDelete) && (
                      <TableCell className="sticky right-0 bg-white border-l border-gray-200 px-4 py-3 z-10">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            {onRowEdit && (
                              <DropdownMenuItem onClick={(e) => {
                                e.stopPropagation()
                                onRowEdit(row)
                              }}>
                                <Edit className="h-4 w-4 mr-2" />
                                Edit
                              </DropdownMenuItem>
                            )}
                            {onRowDelete && (
                              <DropdownMenuItem 
                                onClick={(e) => {
                                  e.stopPropagation()
                                  onRowDelete(row)
                                }}
                                className="text-red-600"
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Delete
                              </DropdownMenuItem>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    )}
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-between items-center pt-4 border-t">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
            disabled={currentPage === 1}
          >
            <ChevronLeft className="h-4 w-4 mr-1" />
            Previous
          </Button>
          
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">
              Page {currentPage} of {totalPages}
            </span>
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}
            disabled={currentPage === totalPages}
          >
            Next
            <ChevronRight className="h-4 w-4 ml-1" />
          </Button>
        </div>
      )}
    </div>
  )
}

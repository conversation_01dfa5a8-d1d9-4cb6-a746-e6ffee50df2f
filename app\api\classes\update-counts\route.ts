import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function POST() {
  try {
    // Get all classes
    const classes = await prisma.class.findMany({
      select: {
        id: true,
        name: true,
      },
    })

    // Update each class with the correct student count
    const updates = await Promise.all(
      classes.map(async (cls) => {
        // Count students in this class
        const studentCount = await prisma.student.count({
          where: {
            className: cls.name,
          },
        })

        // Update the class with the correct count
        return prisma.class.update({
          where: {
            id: cls.id,
          },
          data: {
            totalStudents: studentCount,
          },
        })
      })
    )

    return NextResponse.json({
      message: 'Class student counts updated successfully',
      updatedClasses: updates.length,
    })
  } catch (error) {
    console.error('Error updating class student counts:', error)
    return NextResponse.json(
      { error: 'Failed to update class student counts' },
      { status: 500 }
    )
  }
}

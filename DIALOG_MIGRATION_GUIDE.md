# Dialog Migration Guide

This guide explains how to migrate from Radix UI's Dialog component to our RecursionSafeDialog component to fix the "too much recursion" error.

## The Problem

After creating an announcement using the "+ Add Announcement" button, the "View Website" and action buttons (edit/delete) become unclickable, and the browser console shows the "Uncaught InternalError: too much recursion" error. This is caused by conflicting versions of `@radix-ui/react-focus-scope` being used by different Radix UI components.

## The Solution

We've created a `RecursionSafeDialog` component that doesn't use Radix UI's focus trapping mechanism, which avoids the recursion issue. We've also provided a script to help update all dialog components in your website-management pages.

## Manual Migration Steps

If you need to manually migrate a component, follow these steps:

### 1. Update Imports

Replace:
```tsx
import {
  Dialog, DialogContent, DialogDescription,
  DialogHeader, DialogTitle, DialogFooter
} from '../../components/ui/dialog'
```

With:
```tsx
import {
  RecursionSafeDialog,
  RecursionSafeDialogContent,
  RecursionSafeDialogHeader,
  RecursionSafeDialogTitle,
  RecursionSafeDialogDescription,
  RecursionSafeDialogFooter
} from '../../components/RecursionSafeDialog'
```

### 2. Replace Dialog Component

Replace:
```tsx
<Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
  <DialogContent className="sm:max-w-[600px]">
    <DialogHeader>
      <DialogTitle>Dialog Title</DialogTitle>
      <DialogDescription>
        Dialog description text
      </DialogDescription>
    </DialogHeader>

    {/* Dialog content */}
    <div className="grid gap-4 py-4">
      {/* Form fields */}
    </div>

    <DialogFooter>
      <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
        Cancel
      </Button>
      <Button onClick={handleSubmit}>
        Submit
      </Button>
    </DialogFooter>
  </DialogContent>
</Dialog>
```

With:
```tsx
<RecursionSafeDialog
  open={isDialogOpen}
  onOpenChange={setIsDialogOpen}
  maxWidth="max-w-[600px]"
>
  <RecursionSafeDialogHeader>
    <RecursionSafeDialogTitle>Dialog Title</RecursionSafeDialogTitle>
    <RecursionSafeDialogDescription>
      Dialog description text
    </RecursionSafeDialogDescription>
  </RecursionSafeDialogHeader>

  <RecursionSafeDialogContent>
    {/* Dialog content */}
    <div className="grid gap-4 py-4">
      {/* Form fields */}
    </div>
  </RecursionSafeDialogContent>

  <RecursionSafeDialogFooter>
    <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
      Cancel
    </Button>
    <Button onClick={handleSubmit}>
      Submit
    </Button>
  </RecursionSafeDialogFooter>
</RecursionSafeDialog>
```

## Using the Automated Script

We've provided a PowerShell script to help automate the migration process:

1. Open PowerShell in your project directory
2. Run the script:
   ```
   .\update-dialogs.ps1
   ```
3. The script will scan all TypeScript/TSX files in the `app\website-management` directory and update them to use RecursionSafeDialog
4. Review the changes and test the application

## Additional Notes

- The RecursionSafeDialog component accepts the following props:
  - `open`: boolean - Controls whether the dialog is open
  - `onOpenChange`: function - Called when the open state changes
  - `maxWidth`: string - CSS class for controlling the dialog width (e.g., "max-w-[600px]")
  - `className`: string - Additional CSS classes
  - `children`: ReactNode - The main content of the dialog

- RecursionSafeDialog provides these companion components:
  - `RecursionSafeDialogHeader` - Container for the dialog header
  - `RecursionSafeDialogTitle` - Dialog title component
  - `RecursionSafeDialogDescription` - Dialog description component
  - `RecursionSafeDialogContent` - Container for the dialog content
  - `RecursionSafeDialogFooter` - Container for the dialog footer

- If you encounter any issues with the migration, check the console for errors and ensure that all Dialog components have been properly converted to RecursionSafeDialog.

- After migration, test all dialog functionality to ensure everything works correctly.

## Troubleshooting

If you still encounter recursion errors after migration:

1. Make sure all Dialog components in the page have been converted to RecursionSafeDialog
2. Check for nested dialogs or other components that might be using focus trapping
3. Run the package resolution fix script:
   ```
   .\fix-recursion.ps1
   ```
4. Restart your development server

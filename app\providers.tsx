'use client'

import React from 'react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { Toaster } from './components/ui/toaster'
import { EnhancedThemeProvider } from './contexts/enhanced-theme-context'
import { AuthProvider } from './contexts/auth-context'
import { AppSettingsProvider } from './contexts/app-settings-context'

// Create a single QueryClient instance outside the component to prevent recreation
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 60000, // 1 minute
      refetchOnWindowFocus: false,
      retry: 1,
    },
    mutations: {
      retry: 1,
    },
  },
})

export function Providers({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <EnhancedThemeProvider>
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <AppSettingsProvider>
            <div className="theme-transition">
              {children}
              <Toaster />
            </div>
          </AppSettingsProvider>
        </AuthProvider>
      </QueryClientProvider>
    </EnhancedThemeProvider>
  )
}

// Export the queryClient for use in other components if needed
export { queryClient }

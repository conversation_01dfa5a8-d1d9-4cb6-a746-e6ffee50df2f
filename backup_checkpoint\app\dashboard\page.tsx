'use client'

import React, { useState, useEffect } from 'react'
import dynamic from 'next/dynamic'
import { Sidebar } from '../components/Sidebar'
import { Card } from '../components/ui/card'
import {
  Users,
  GraduationCap,
  School,
  CalendarCheck,
  BookOpen
} from 'lucide-react'

// Import Header component with no SSR to avoid hydration issues
const Header = dynamic(() => import('../components/Header').then(mod => mod.Header), {
  ssr: false
})

export default function DashboardPage() {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  // Use state to track if component is mounted (client-side only)
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    // Set mounted state to true when component mounts (client-side only)
    setIsMounted(true)
    console.log('Dashboard useEffect triggered')

    // Check authentication status
    const checkAuth = async () => {
      try {
        const response = await fetch('/api/auth/debug')
        const data = await response.json()
        console.log('Auth status:', data)

        if (data.authenticated) {
          console.log(`Authenticated with ${data.authType || 'unknown'} method`)
        } else {
          console.warn('Not authenticated, but still on dashboard page')
          // We're on the dashboard but not authenticated - this shouldn't happen
          // Let's check if we need to redirect back to login
          if (window.location.pathname === '/dashboard') {
            console.log('Redirecting back to login page...')
            // Redirect to login when not authenticated
            window.location.href = '/login'
          }
        }
      } catch (error) {
        console.error('Error checking auth status:', error)
      }
    }

    checkAuth()

    // Simulate loading for a better user experience
    setTimeout(() => {
      setIsLoading(false)
    }, 500)

    // Clean up URL by removing query parameters if any
    if (window.history && window.history.replaceState) {
      const cleanUrl = window.location.pathname
      window.history.replaceState({}, document.title, cleanUrl)
    }
  }, [])

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  const toggleMobileSidebar = () => {
    setMobileSidebarOpen(!mobileSidebarOpen)
  }

  // Only render the full UI when on the client side
  if (!isMounted || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-600"></div>
      </div>
    )
  }

  return (
    <div className="flex h-screen bg-gray-100 dark:bg-gray-900">
      <Sidebar
        isCollapsed={sidebarCollapsed}
        toggleSidebar={toggleSidebar}
        isMobileOpen={mobileSidebarOpen}
      />

      <main className={`main-content ${sidebarCollapsed ? 'sidebar-collapsed' : ''} w-full h-screen overflow-y-auto`}>
        <Header toggleMobileSidebar={toggleMobileSidebar} />

        <div className="p-6">
          <h1 className="text-2xl font-semibold mb-6 bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent inline-block">Welcome to the Dashboard</h1>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Students Card */}
            <Card className="overflow-hidden border-0 shadow-lg hover:shadow-xl transition-shadow duration-300 dark:bg-gray-800 dark:border-gray-700">
              <div className="relative">
                <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-400 to-blue-600"></div>
                <div className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Total Students</p>
                      <h3 className="text-3xl font-bold text-gray-900 dark:text-white">1,234</h3>
                      <p className="text-xs text-green-500 mt-2 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
                        </svg>
                        12% increase
                      </p>
                    </div>
                    <div className="p-4 rounded-lg bg-blue-50 dark:bg-blue-900/30">
                      <Users size={28} className="text-blue-500 dark:text-blue-400" />
                    </div>
                  </div>
                </div>
              </div>
            </Card>

            {/* Teachers Card */}
            <Card className="overflow-hidden border-0 shadow-lg hover:shadow-xl transition-shadow duration-300 dark:bg-gray-800 dark:border-gray-700">
              <div className="relative">
                <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-green-400 to-green-600"></div>
                <div className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Total Teachers</p>
                      <h3 className="text-3xl font-bold text-gray-900 dark:text-white">42</h3>
                      <p className="text-xs text-green-500 mt-2 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
                        </svg>
                        5% increase
                      </p>
                    </div>
                    <div className="p-4 rounded-lg bg-green-50 dark:bg-green-900/30">
                      <GraduationCap size={28} className="text-green-500 dark:text-green-400" />
                    </div>
                  </div>
                </div>
              </div>
            </Card>

            {/* Classes Card */}
            <Card className="overflow-hidden border-0 shadow-lg hover:shadow-xl transition-shadow duration-300 dark:bg-gray-800 dark:border-gray-700">
              <div className="relative">
                <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-purple-400 to-purple-600"></div>
                <div className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Total Classes</p>
                      <h3 className="text-3xl font-bold text-gray-900 dark:text-white">12</h3>
                      <p className="text-xs text-purple-500 mt-2 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
                        </svg>
                        New class added
                      </p>
                    </div>
                    <div className="p-4 rounded-lg bg-purple-50 dark:bg-purple-900/30">
                      <School size={28} className="text-purple-500 dark:text-purple-400" />
                    </div>
                  </div>
                </div>
              </div>
            </Card>

            {/* Attendance Card */}
            <Card className="overflow-hidden border-0 shadow-lg hover:shadow-xl transition-shadow duration-300 dark:bg-gray-800 dark:border-gray-700">
              <div className="relative">
                <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-yellow-400 to-yellow-600"></div>
                <div className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Attendance Today</p>
                      <h3 className="text-3xl font-bold text-gray-900 dark:text-white">95%</h3>
                      <p className="text-xs text-yellow-600 dark:text-yellow-500 mt-2 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                        </svg>
                        2% higher than yesterday
                      </p>
                    </div>
                    <div className="p-4 rounded-lg bg-yellow-50 dark:bg-yellow-900/30">
                      <CalendarCheck size={28} className="text-yellow-500 dark:text-yellow-400" />
                    </div>
                  </div>
                </div>
              </div>
            </Card>

            {/* Subjects Card */}
            <Card className="overflow-hidden border-0 shadow-lg hover:shadow-xl transition-shadow duration-300 dark:bg-gray-800 dark:border-gray-700">
              <div className="relative">
                <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-red-400 to-red-600"></div>
                <div className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Total Subjects</p>
                      <h3 className="text-3xl font-bold text-gray-900 dark:text-white">24</h3>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-2 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                        View all subjects
                      </p>
                    </div>
                    <div className="p-4 rounded-lg bg-red-50 dark:bg-red-900/30">
                      <BookOpen size={28} className="text-red-500 dark:text-red-400" />
                    </div>
                  </div>
                </div>
              </div>
            </Card>

            {/* Performance Card */}
            <Card className="overflow-hidden border-0 shadow-lg hover:shadow-xl transition-shadow duration-300 dark:bg-gray-800 dark:border-gray-700">
              <div className="relative">
                <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-indigo-400 to-indigo-600"></div>
                <div className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Overall Performance</p>
                      <h3 className="text-3xl font-bold text-gray-900 dark:text-white">87%</h3>
                      <p className="text-xs text-indigo-500 mt-2 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                        </svg>
                        3% improvement
                      </p>
                    </div>
                    <div className="p-4 rounded-lg bg-indigo-50 dark:bg-indigo-900/30">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-7 w-7 text-indigo-500 dark:text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </main>

      {/* Overlay for mobile menu */}
      {mobileSidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 md:hidden z-40"
          onClick={toggleMobileSidebar}
        ></div>
      )}
    </div>
  )
}
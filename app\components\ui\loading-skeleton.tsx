"use client"

import React from 'react'
import { cn } from '../../lib/utils'

interface SkeletonProps {
  className?: string
  variant?: 'default' | 'circular' | 'rectangular' | 'text'
  width?: string | number
  height?: string | number
  animation?: 'pulse' | 'wave' | 'none'
}

export function Skeleton({
  className,
  variant = 'default',
  width,
  height,
  animation = 'pulse'
}: SkeletonProps) {
  const baseClasses = cn(
    'bg-gray-200 dark:bg-gray-700',
    {
      'animate-pulse': animation === 'pulse',
      'skeleton': animation === 'wave',
      'rounded-md': variant === 'default' || variant === 'rectangular',
      'rounded-full': variant === 'circular',
      'rounded': variant === 'text',
      'h-4': variant === 'text' && !height,
    },
    className
  )

  const style = {
    width: typeof width === 'number' ? `${width}px` : width,
    height: typeof height === 'number' ? `${height}px` : height,
  }

  return <div className={baseClasses} style={style} />
}

// Table skeleton components
export function TableSkeleton({
  rows = 5,
  columns = 4,
  showHeader = true,
  className
}: {
  rows?: number
  columns?: number
  showHeader?: boolean
  className?: string
}) {
  return (
    <div className={cn('space-y-3', className)}>
      {showHeader && (
        <div className="flex gap-4 p-4 bg-gray-50 rounded-t-lg">
          {Array.from({ length: columns }).map((_, i) => (
            <Skeleton key={i} className="h-4 flex-1" />
          ))}
        </div>
      )}
      <div className="space-y-2">
        {Array.from({ length: rows }).map((_, rowIndex) => (
          <div key={rowIndex} className="flex gap-4 p-4 border-b">
            {Array.from({ length: columns }).map((_, colIndex) => (
              <Skeleton 
                key={colIndex} 
                className="h-4 flex-1"
                width={colIndex === 0 ? '60%' : undefined}
              />
            ))}
          </div>
        ))}
      </div>
    </div>
  )
}

// Card skeleton for mobile view
export function CardSkeleton({
  count = 3,
  className
}: {
  count?: number
  className?: string
}) {
  return (
    <div className={cn('space-y-4', className)}>
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className="border rounded-lg p-4 space-y-3">
          <div className="flex items-center gap-3">
            <Skeleton variant="circular" width={40} height={40} />
            <div className="flex-1 space-y-2">
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-3 w-1/2" />
            </div>
          </div>
          <div className="space-y-2">
            <Skeleton className="h-3 w-full" />
            <Skeleton className="h-3 w-2/3" />
          </div>
          <div className="flex gap-2">
            <Skeleton className="h-6 w-16" />
            <Skeleton className="h-6 w-20" />
          </div>
        </div>
      ))}
    </div>
  )
}

// Search skeleton
export function SearchSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn('space-y-4', className)}>
      <Skeleton className="h-12 w-full rounded-lg" />
      <div className="flex gap-2">
        <Skeleton className="h-8 w-20" />
        <Skeleton className="h-8 w-24" />
        <Skeleton className="h-8 w-16" />
      </div>
    </div>
  )
}

// Avatar skeleton
export function AvatarSkeleton({
  size = 40,
  className
}: {
  size?: number
  className?: string
}) {
  return (
    <Skeleton
      variant="circular"
      width={size}
      height={size}
      className={className}
    />
  )
}

// Text skeleton with multiple lines
export function TextSkeleton({
  lines = 3,
  className
}: {
  lines?: number
  className?: string
}) {
  return (
    <div className={cn('space-y-2', className)}>
      {Array.from({ length: lines }).map((_, index) => (
        <Skeleton
          key={index}
          variant="text"
          className={cn(
            'h-4',
            index === lines - 1 ? 'w-3/4' : 'w-full'
          )}
        />
      ))}
    </div>
  )
}

// Button skeleton
export function ButtonSkeleton({
  variant = 'default',
  size = 'default',
  className
}: {
  variant?: 'default' | 'outline' | 'ghost'
  size?: 'default' | 'sm' | 'lg'
  className?: string
}) {
  const sizeClasses = {
    sm: 'h-8 w-16',
    default: 'h-10 w-20',
    lg: 'h-12 w-24'
  }

  return (
    <Skeleton
      className={cn(
        'rounded-md',
        sizeClasses[size],
        className
      )}
    />
  )
}

// Badge skeleton
export function BadgeSkeleton({
  count = 1,
  className
}: {
  count?: number
  className?: string
}) {
  return (
    <div className={cn('flex gap-2', className)}>
      {Array.from({ length: count }).map((_, index) => (
        <Skeleton
          key={index}
          className="h-5 w-12 rounded-full"
        />
      ))}
    </div>
  )
}

// Complete page skeleton
export function PageSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn('space-y-6 p-6', className)}>
      {/* Header */}
      <div className="space-y-2">
        <Skeleton className="h-8 w-1/3" />
        <Skeleton className="h-4 w-2/3" />
      </div>

      {/* Controls */}
      <div className="flex gap-4 items-center">
        <Skeleton className="h-10 w-64" />
        <Skeleton className="h-10 w-20" />
        <Skeleton className="h-10 w-24" />
      </div>

      {/* Content */}
      <TableSkeleton rows={8} columns={6} />

      {/* Pagination */}
      <div className="flex justify-between items-center">
        <Skeleton className="h-4 w-32" />
        <div className="flex gap-2">
          <Skeleton className="h-8 w-20" />
          <Skeleton className="h-8 w-16" />
          <Skeleton className="h-8 w-20" />
        </div>
      </div>
    </div>
  )
}

// Responsive skeleton that adapts to screen size
export function ResponsiveSkeleton({
  mobileComponent,
  tabletComponent,
  desktopComponent,
  className
}: {
  mobileComponent?: React.ReactNode
  tabletComponent?: React.ReactNode
  desktopComponent?: React.ReactNode
  className?: string
}) {
  return (
    <div className={className}>
      {/* Mobile */}
      <div className="block md:hidden">
        {mobileComponent || <CardSkeleton count={5} />}
      </div>

      {/* Tablet */}
      <div className="hidden md:block lg:hidden">
        {tabletComponent || <TableSkeleton rows={6} columns={4} />}
      </div>

      {/* Desktop */}
      <div className="hidden lg:block">
        {desktopComponent || <TableSkeleton rows={8} columns={6} />}
      </div>
    </div>
  )
}

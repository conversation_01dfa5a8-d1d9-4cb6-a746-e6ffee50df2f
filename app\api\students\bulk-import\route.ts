import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { parse } from 'csv-parse/sync'

interface StudentImport {
  name: string
  fatherName: string
  gfName: string
  className: string
  age: string
  gender: string
  academicYear?: string
  photoUrl?: string
}

export async function POST(req: Request) {
  try {
    const formData = await req.formData()
    const file = formData.get('file') as File
    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      )
    }

    const text = await file.text()
    const records = parse(text, {
      columns: true,
      skip_empty_lines: true,
      trim: true
    }) as StudentImport[]

    // Group students by class for batch processing
    const studentsByClass = records.reduce((acc, student) => {
      const className = student.className
      if (!acc[className]) {
        acc[className] = []
      }
      acc[className].push(student)
      return acc
    }, {} as Record<string, StudentImport[]>)

    const results = {
      total: records.length,
      success: 0,
      failed: 0,
      errors: [] as string[]
    }

    // Process each class group
    for (const [className, students] of Object.entries(studentsByClass)) {
      try {
        // Find the class and its current students
        const classRecord = await prisma.class.findFirst({
          where: { name: className },
          include: {
            students: {
              orderBy: { sid: 'desc' },
              take: 1,
              where: { className }
            }
          }
        })

        if (!classRecord) {
          students.forEach(() => {
            results.failed++
            results.errors.push(`Class ${className} not found`)
          })
          continue
        }

        // Extract class prefix and get the last student number
        const classPrefix = className.match(/^\d+[A-Z]+/)?.[0]
        if (!classPrefix) {
          students.forEach(() => {
            results.failed++
            results.errors.push(`Invalid class name format: ${className}`)
          })
          continue
        }

        let nextNumber = 1
        if (classRecord.students[0]) {
          const lastNumber = parseInt(classRecord.students[0].sid.replace(classPrefix, ''))
          if (!isNaN(lastNumber)) {
            nextNumber = lastNumber + 1
          }
        }

        // Create students in batch
        for (const student of students) {
          try {
            const newSid = `${classPrefix}${nextNumber++}`
            // Create student data object
            const studentData = {
              sid: newSid,
              name: student.name,
              fatherName: student.fatherName,
              gfName: student.gfName,
              className: student.className,
              age: parseInt(student.age),
              gender: student.gender.toLowerCase(),
              academicYear: student.academicYear || '2023-2024'
            }

            // Add photoUrl if provided
            if (student.photoUrl) {
              await prisma.student.create({
                data: {
                  ...studentData,
                  photoUrl: student.photoUrl
                }
              })
            } else {
              // Create without photoUrl
              await prisma.student.create({
                data: studentData
              })
            }
            results.success++
          } catch (error) {
            results.failed++
            results.errors.push(
              `Failed to create student ${student.name} in class ${className}: ${
                error instanceof Error ? error.message : 'Unknown error'
              }`
            )
          }
        }
      } catch (error) {
        students.forEach(() => {
          results.failed++
          results.errors.push(
            `Failed to process class ${className}: ${
              error instanceof Error ? error.message : 'Unknown error'
            }`
          )
        })
      }
    }

    return NextResponse.json(results)
  } catch (error) {
    console.error('Failed to import students:', error)
    return NextResponse.json(
      { error: 'Failed to import students' },
      { status: 500 }
    )
  }
}

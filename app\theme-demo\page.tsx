'use client';

import React from 'react';
import { ThemeSettings } from '../components/ThemeSettings';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '../components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { Table, TableBody, TableCaption, TableCell, TableHead, TableHeader, TableRow } from '../components/ui/table';
import { useEnhancedTheme } from '../contexts/enhanced-theme-context';

export default function ThemeDemoPage() {
  const { currentTheme } = useEnhancedTheme();

  return (
    <div className="container mx-auto py-10 space-y-10">
      <div className="text-center">
        <h1 className="text-4xl font-bold mb-4">Theme System Demo</h1>
        <p className="text-xl mb-8">
          Showcasing the four visual themes: Light, Dark, Solarized, and High-Contrast
        </p>
        <div className="inline-block mb-8 px-4 py-2 rounded-full" style={{
          backgroundColor: 'var(--color-primary)',
          color: 'var(--color-on-primary)'
        }}>
          Current Theme: {currentTheme.name.charAt(0).toUpperCase() + currentTheme.name.slice(1)}
        </div>
      </div>

      <ThemeSettings />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <Card>
          <CardHeader>
            <CardTitle>Button Components</CardTitle>
            <CardDescription>Various button styles with the current theme</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-wrap gap-4">
              <Button variant="default">Primary Button</Button>
              <Button variant="secondary">Secondary Button</Button>
              <Button variant="outline">Outline Button</Button>
              <Button variant="ghost">Ghost Button</Button>
              <Button variant="link">Link Button</Button>
              <Button variant="destructive">Destructive Button</Button>
            </div>
            <div className="flex flex-wrap gap-4">
              <Button size="lg">Large Button</Button>
              <Button size="default">Default Button</Button>
              <Button size="sm">Small Button</Button>
            </div>
            <div className="flex flex-wrap gap-4">
              <Button disabled>Disabled Button</Button>
              <Button variant="outline" disabled>Disabled Outline</Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Form Components</CardTitle>
            <CardDescription>Input fields and form controls</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4">
              <Input placeholder="Default input" />
              <Input placeholder="Disabled input" disabled />
              <div className="flex gap-2">
                <Input placeholder="With button" />
                <Button>Submit</Button>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Table Component</CardTitle>
            <CardDescription>Data display with the current theme</CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableCaption>A list of recent transactions</TableCaption>
              <TableHeader>
                <TableRow>
                  <TableHead>Transaction ID</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell>INV-001</TableCell>
                  <TableCell>2023-04-05</TableCell>
                  <TableCell>$250.00</TableCell>
                  <TableCell>Completed</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>INV-002</TableCell>
                  <TableCell>2023-04-12</TableCell>
                  <TableCell>$125.50</TableCell>
                  <TableCell>Processing</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>INV-003</TableCell>
                  <TableCell>2023-04-18</TableCell>
                  <TableCell>$350.00</TableCell>
                  <TableCell>Completed</TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Accessibility Features</CardTitle>
          <CardDescription>Demonstrating focus states and reduced motion</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-2">Focus States</h3>
            <p className="mb-4">Tab through these elements to see focus styles:</p>
            <div className="flex flex-wrap gap-4">
              <Button>Focus Me</Button>
              <Input placeholder="Focus Me Too" className="max-w-xs" />
              <a href="#" className="underline">Focusable Link</a>
            </div>
          </div>
          
          <div>
            <h3 className="text-lg font-medium mb-2">Color Contrast</h3>
            <p className="mb-4">Text with various contrast levels:</p>
            <div className="space-y-2">
              <p className="text-sm" style={{ opacity: 0.9 }}>High contrast text (90% opacity)</p>
              <p className="text-sm" style={{ opacity: 0.7 }}>Medium contrast text (70% opacity)</p>
              <p className="text-sm" style={{ opacity: 0.5 }}>Low contrast text (50% opacity)</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

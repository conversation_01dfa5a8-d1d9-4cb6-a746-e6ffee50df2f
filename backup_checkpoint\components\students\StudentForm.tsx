'use client'

import { useState, useEffect } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useToast } from '@/components/ui/use-toast'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Loader2 } from 'lucide-react'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import * as z from 'zod'
import { formatStudentName } from '@/lib/utils'

// Define the form schema with validation
const formSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  fatherName: z.string().min(2, 'Father\'s name must be at least 2 characters'),
  gfName: z.string().min(2, 'Grandfather\'s name must be at least 2 characters'),
  className: z.string().min(1, 'Class is required'),
  age: z.coerce.number().int().min(5, 'Age must be at least 5').max(20, 'Age must be at most 20'),
  gender: z.enum(['Male', 'Female']),
})

type FormValues = z.infer<typeof formSchema>

interface Student {
  id: string
  sid: string
  name: string
  fatherName: string
  gfName: string
  className: string
  age: number
  gender: string
  class?: {
    name: string
  }
}

interface StudentFormProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  student?: Student
}

export function StudentForm({ open, onOpenChange, student }: StudentFormProps) {
  const isEditing = !!student
  const { toast } = useToast()
  const queryClient = useQueryClient()
  const [newClassName, setNewClassName] = useState('')
  const [showClassInput, setShowClassInput] = useState(false)

  // Fetch classes for the dropdown
  const { data: classes, isLoading: isLoadingClasses } = useQuery({
    queryKey: ['classes'],
    queryFn: async () => {
      const res = await fetch('/api/classes')
      if (!res.ok) throw new Error('Failed to fetch classes')
      return res.json()
    }
  })

  // Initialize the form with default values or student data if editing
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      fatherName: '',
      gfName: '',
      className: '',
      age: 10,
      gender: 'Male',
    },
  })

  // Update form values when student prop changes
  useEffect(() => {
    if (student) {
      form.reset({
        name: student.name,
        fatherName: student.fatherName,
        gfName: student.gfName,
        className: student.className || (student.class ? student.class.name : ''),
        age: student.age,
        gender: student.gender,
      })
    } else {
      form.reset({
        name: '',
        fatherName: '',
        gfName: '',
        className: '',
        age: 10,
        gender: 'Male',
      })
    }
  }, [student, form])

  // Create student mutation
  const createStudentMutation = useMutation({
    mutationFn: async (data: FormValues) => {
      console.log('Creating student with data:', data);

      const requestData = {
        name: formatStudentName(data.name),
        fatherName: formatStudentName(data.fatherName),
        gfName: formatStudentName(data.gfName),
        className: data.className,
        age: data.age,
        gender: data.gender,
      };

      console.log('Formatted request data:', requestData);

      const res = await fetch('/api/students', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      })

      const responseData = await res.json();
      console.log('Response status:', res.status, 'Response data:', responseData);

      if (!res.ok) {
        throw new Error(responseData.error || responseData.details || 'Failed to create student')
      }

      return responseData;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['students'] })
      queryClient.invalidateQueries({ queryKey: ['classes'] })
      toast({
        title: 'Success',
        description: 'Student added successfully',
      })
      onOpenChange(false)
      form.reset()
    },
    onError: (error) => {
      console.error('Failed to add student:', error)

      // Check if the error message contains information about the class not existing
      const errorMessage = error instanceof Error ? error.message : 'Failed to add student';

      if (errorMessage.includes('Class') && errorMessage.includes('does not exist')) {
        toast({
          title: 'Class Not Found',
          description: 'The selected class does not exist. Please create the class first or select a different class.',
          variant: 'destructive',
        })
      } else {
        toast({
          title: 'Error',
          description: errorMessage,
          variant: 'destructive',
        })
      }
    },
  })

  // Create class mutation
  const createClassMutation = useMutation({
    mutationFn: async (className: string) => {
      console.log('Creating class with name:', className);

      const res = await fetch('/api/classes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: className,
          totalStudents: 0,
          totalSubjects: 0,
        }),
      })

      const responseData = await res.json();
      console.log('Response status:', res.status, 'Response data:', responseData);

      if (!res.ok) {
        throw new Error(responseData.error || responseData.details || 'Failed to create class')
      }

      return responseData;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['classes'] })
      toast({
        title: 'Success',
        description: `Class ${data.name} created successfully`,
      })
      setShowClassInput(false)
      setNewClassName('')

      // Set the newly created class as the selected class
      form.setValue('className', data.name)
    },
    onError: (error) => {
      console.error('Failed to create class:', error)
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to create class',
        variant: 'destructive',
      })
    },
  })

  // Update student mutation
  const updateStudentMutation = useMutation({
    mutationFn: async (data: FormValues & { id: string }) => {
      const res = await fetch(`/api/students/${data.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formatStudentName(data.name),
          fatherName: formatStudentName(data.fatherName),
          gfName: formatStudentName(data.gfName),
          className: data.className,
          age: data.age,
          gender: data.gender,
        }),
      })

      if (!res.ok) {
        const errorData = await res.json()
        throw new Error(errorData.error || 'Failed to update student')
      }

      return res.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['students'] })
      queryClient.invalidateQueries({ queryKey: ['classes'] })
      toast({
        title: 'Success',
        description: 'Student updated successfully',
      })
      onOpenChange(false)
    },
    onError: (error) => {
      console.error('Failed to update student:', error)
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to update student',
        variant: 'destructive',
      })
    },
  })

  // Handle form submission
  const onSubmit = (data: FormValues) => {
    if (isEditing && student) {
      updateStudentMutation.mutate({ ...data, id: student.id })
    } else {
      createStudentMutation.mutate(data)
    }
  }

  const isPending = createStudentMutation.isPending || updateStudentMutation.isPending || createClassMutation.isPending

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{isEditing ? 'Edit Student' : 'Add New Student'}</DialogTitle>
          <DialogDescription>
            {isEditing
              ? 'Update the student information in the form below.'
              : 'Fill in the student details to add them to the system.'}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 py-2">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Full Name</FormLabel>
                    <FormControl>
                      <Input placeholder="John Doe" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="className"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Class</FormLabel>
                    {showClassInput ? (
                      <div className="space-y-2">
                        <div className="flex gap-2">
                          <Input
                            placeholder="Enter new class name (e.g. 1A)"
                            value={newClassName}
                            onChange={(e) => setNewClassName(e.target.value)}
                            className="flex-1"
                          />
                          <Button
                            type="button"
                            size="sm"
                            onClick={() => {
                              if (newClassName.trim()) {
                                createClassMutation.mutate(newClassName.trim());
                              } else {
                                toast({
                                  title: "Error",
                                  description: "Please enter a class name",
                                  variant: "destructive",
                                });
                              }
                            }}
                            disabled={createClassMutation.isPending}
                          >
                            {createClassMutation.isPending ? (
                              <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Creating...
                              </>
                            ) : (
                              'Create'
                            )}
                          </Button>
                        </div>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="text-xs"
                          onClick={() => {
                            setShowClassInput(false);
                            setNewClassName('');
                          }}
                        >
                          Cancel
                        </Button>
                      </div>
                    ) : (
                      <Select
                        onValueChange={(value) => {
                          if (value === 'new-class') {
                            setShowClassInput(true);
                          } else {
                            field.onChange(value);
                          }
                        }}
                        defaultValue={field.value}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a class" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {isLoadingClasses ? (
                            <div className="flex items-center justify-center p-2">
                              <Loader2 className="h-4 w-4 animate-spin mr-2" />
                              <span>Loading...</span>
                            </div>
                          ) : (
                            <>
                              {classes?.map((cls: any) => (
                                <SelectItem key={cls.id} value={cls.name}>
                                  {cls.name}
                                </SelectItem>
                              ))}
                              <SelectItem value="new-class" className="text-primary font-medium">
                                + Create New Class
                              </SelectItem>
                            </>
                          )}
                        </SelectContent>
                      </Select>
                    )}
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="fatherName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Father's Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Father's name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="gfName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Grandfather's Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Grandfather's name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="age"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Age</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min={5}
                        max={20}
                        placeholder="Age"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Student age (5-20)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="gender"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Gender</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select gender" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="Male">Male</SelectItem>
                        <SelectItem value="Female">Female</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter className="pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isPending}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isPending}>
                {isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {isEditing ? 'Updating...' : 'Adding...'}
                  </>
                ) : (
                  isEditing ? 'Update Student' : 'Add Student'
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}

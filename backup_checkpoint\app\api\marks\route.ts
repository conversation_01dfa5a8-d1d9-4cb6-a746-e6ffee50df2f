import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// Helper function to calculate grade based on marks
function calculateGrade(marks: number): string {
  if (marks >= 90) return 'A+';
  if (marks >= 80) return 'A';
  if (marks >= 70) return 'B';
  if (marks >= 60) return 'C';
  if (marks >= 50) return 'D';
  return 'F';
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const studentId = searchParams.get('studentId')
    const className = searchParams.get('className')
    const subject = searchParams.get('subject')
    const term = searchParams.get('term')

    // Build where clause based on provided filters
    const whereClause: any = {}

    if (studentId) whereClause.studentId = studentId
    if (className) whereClause.className = className
    if (subject) whereClause.subject = subject
    if (term) whereClause.term = term

    const marks = await prisma.mark.findMany({
      where: whereClause,
      include: {
        student: {
          select: {
            name: true,
            sid: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    // Format the response to include student name and match UI expectations
    const formattedMarks = marks.map(mark => ({
      id: mark.id,
      studentId: mark.studentId,
      studentName: mark.student.name,
      studentSid: mark.student.sid,
      subject: mark.subject,
      obtainedMarks: mark.marks, // Map marks to obtainedMarks for UI
      totalMarks: 100, // Default total marks
      class: mark.className, // Map className to class for UI
      term: mark.term,
      academicYear: new Date(mark.createdAt).getFullYear() + '-' + (new Date(mark.createdAt).getFullYear() + 1), // Generate academic year
      grade: calculateGrade(mark.marks), // Calculate grade
      dateRecorded: mark.createdAt,
      recordedBy: 'Teacher', // Default recorded by
      createdAt: mark.createdAt,
      updatedAt: mark.updatedAt
    }))

    return NextResponse.json(formattedMarks)
  } catch (error) {
    console.error('Error fetching marks:', error)
    return NextResponse.json(
      { error: 'Failed to fetch marks' },
      { status: 500 }
    )
  }
}

export async function POST(request: Request) {
  try {
    const data = await request.json()

    // Validate required fields
    if (!data.studentId || !data.subject || !data.class || !data.term || (data.marks === undefined && data.obtainedMarks === undefined)) {
      return NextResponse.json(
        { error: 'Missing required fields', details: JSON.stringify(data) },
        { status: 400 }
      )
    }

    // Map fields to expected names
    const className = data.className || data.class;
    const marks = data.marks !== undefined ? data.marks : data.obtainedMarks;

    // Check if student exists
    const student = await prisma.student.findUnique({
      where: { id: data.studentId },
      select: { name: true }
    })

    if (!student) {
      return NextResponse.json(
        { error: 'Student not found' },
        { status: 404 }
      )
    }

    // Create the mark
    const mark = await prisma.mark.create({
      data: {
        studentId: data.studentId,
        subject: data.subject,
        marks: marks,
        className: className,
        term: data.term
      },
      include: {
        student: {
          select: {
            name: true,
            sid: true
          }
        }
      }
    })

    // Format the response to include student name and match UI expectations
    const formattedMark = {
      id: mark.id,
      studentId: mark.studentId,
      studentName: mark.student.name,
      studentSid: mark.student.sid,
      subject: mark.subject,
      obtainedMarks: mark.marks, // Map marks to obtainedMarks for UI
      totalMarks: 100, // Default total marks
      class: mark.className, // Map className to class for UI
      term: mark.term,
      academicYear: new Date(mark.createdAt).getFullYear() + '-' + (new Date(mark.createdAt).getFullYear() + 1), // Generate academic year
      grade: calculateGrade(mark.marks), // Calculate grade
      dateRecorded: mark.createdAt,
      recordedBy: 'Teacher', // Default recorded by
      createdAt: mark.createdAt,
      updatedAt: mark.updatedAt
    }

    return NextResponse.json(formattedMark)
  } catch (error) {
    console.error('Error creating mark:', error)
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json(
      { error: 'Failed to create mark', details: errorMessage },
      { status: 500 }
    )
  }
}

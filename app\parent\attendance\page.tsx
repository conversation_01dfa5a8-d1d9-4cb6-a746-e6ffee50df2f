'use client'

import React, { useState, useMemo } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useSearchParams } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/app/components/ui/card'
import { Badge } from '@/app/components/ui/badge'
import { Button } from '@/app/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/app/components/ui/table'
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/app/components/ui/tabs'
import { Progress } from '@/app/components/ui/progress'
import { Input } from '@/app/components/ui/input'
import { Label } from '@/app/components/ui/label'
import {
  Calendar,
  CalendarCheck,
  CalendarX,
  Clock,
  TrendingUp,
  <PERSON>f<PERSON><PERSON><PERSON>,
  User,
  AlertCircle
} from 'lucide-react'

interface AttendanceRecord {
  id: string
  date: string
  studentId: string
  className: string
  status: 'present' | 'absent' | 'permission'
  student: {
    id: string
    sid: string
    name: string
    className: string
  }
}

interface AttendanceStats {
  studentId: string
  total: number
  present: number
  absent: number
  permission: number
  presentPercentage: number
  absentPercentage: number
  permissionPercentage: number
}

interface Student {
  id: string
  sid: string
  name: string
  className: string
  photoUrl?: string
}

interface AttendanceData {
  attendance: AttendanceRecord[]
  students: Student[]
  stats: AttendanceStats[]
  period: string
  message: string
}

export default function ParentAttendancePage() {
  const searchParams = useSearchParams()
  const initialStudentId = searchParams.get('studentId')

  const [selectedStudent, setSelectedStudent] = useState<string>(initialStudentId || 'all')
  const [selectedPeriod, setSelectedPeriod] = useState<string>('monthly')
  const [selectedMonth, setSelectedMonth] = useState<string>(new Date().getMonth() + 1 + '')
  const [selectedWeek, setSelectedWeek] = useState<string>('')
  const [selectedDate, setSelectedDate] = useState<Date>(new Date())
  const [selectedYear, setSelectedYear] = useState<string>(new Date().getFullYear().toString())

  // Generate weeks for the current year
  const weeks = useMemo(() => {
    const year = parseInt(selectedYear)
    const weeks = []
    const startOfYear = new Date(year, 0, 1)

    // Find the first Monday of the year
    const firstMonday = new Date(startOfYear)
    const dayOfWeek = startOfYear.getDay()
    const daysToAdd = dayOfWeek === 0 ? 1 : 8 - dayOfWeek
    firstMonday.setDate(startOfYear.getDate() + daysToAdd)

    let currentWeek = new Date(firstMonday)
    let weekNumber = 1

    while (currentWeek.getFullYear() === year) {
      const endOfWeek = new Date(currentWeek)
      endOfWeek.setDate(currentWeek.getDate() + 6)

      weeks.push({
        value: `${currentWeek.toISOString().split('T')[0]}_${endOfWeek.toISOString().split('T')[0]}`,
        label: `Week ${weekNumber}: ${currentWeek.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${endOfWeek.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}`,
        startDate: currentWeek.toISOString().split('T')[0],
        endDate: endOfWeek.toISOString().split('T')[0]
      })

      currentWeek.setDate(currentWeek.getDate() + 7)
      weekNumber++
    }

    return weeks
  }, [selectedYear])

  // Set default week if not selected
  React.useEffect(() => {
    if (selectedPeriod === 'weekly' && !selectedWeek && weeks.length > 0) {
      // Find current week
      const today = new Date()
      const currentWeek = weeks.find(week => {
        const start = new Date(week.startDate)
        const end = new Date(week.endDate)
        return today >= start && today <= end
      })
      setSelectedWeek(currentWeek?.value || weeks[0].value)
    }
  }, [selectedPeriod, selectedWeek, weeks])

  // Fetch attendance data
  const {
    data: attendanceData,
    isLoading,
    error,
    refetch
  } = useQuery<AttendanceData>({
    queryKey: ['parent-attendance', selectedStudent, selectedPeriod, selectedMonth, selectedWeek, selectedDate.toISOString().split('T')[0], selectedYear],
    queryFn: async () => {
      const params = new URLSearchParams({
        period: selectedPeriod,
        year: selectedYear,
        ...(selectedStudent !== 'all' && { studentId: selectedStudent }),
        ...(selectedPeriod === 'monthly' && { month: selectedMonth.padStart(2, '0') }),
        ...(selectedPeriod === 'weekly' && selectedWeek && {
          startDate: selectedWeek.split('_')[0],
          endDate: selectedWeek.split('_')[1]
        }),
        ...(selectedPeriod === 'daily' && { date: selectedDate.toISOString().split('T')[0] })
      })

      const res = await fetch(`/api/parent/attendance?${params}`)
      if (!res.ok) {
        const errorData = await res.json()
        throw new Error(errorData.error || 'Failed to fetch attendance data')
      }
      return res.json()
    }
  })

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'present':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            <CalendarCheck className="h-3 w-3 mr-1" />
            Present
          </Badge>
        )
      case 'absent':
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            <CalendarX className="h-3 w-3 mr-1" />
            Absent
          </Badge>
        )
      case 'permission':
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
            <Clock className="h-3 w-3 mr-1" />
            Permission
          </Badge>
        )
      default:
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
            {status}
          </Badge>
        )
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const months = [
    { value: '1', label: 'January' },
    { value: '2', label: 'February' },
    { value: '3', label: 'March' },
    { value: '4', label: 'April' },
    { value: '5', label: 'May' },
    { value: '6', label: 'June' },
    { value: '7', label: 'July' },
    { value: '8', label: 'August' },
    { value: '9', label: 'September' },
    { value: '10', label: 'October' },
    { value: '11', label: 'November' },
    { value: '12', label: 'December' }
  ]

  const years = [
    { value: '2023', label: '2023' },
    { value: '2024', label: '2024' },
    { value: '2025', label: '2025' }
  ]

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-gray-400" />
          <p className="text-gray-500">Loading attendance data...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 mx-auto mb-4 text-red-500" />
          <h2 className="text-xl font-semibold text-gray-700 mb-2">Error Loading Attendance</h2>
          <p className="text-gray-500 mb-4">
            {error instanceof Error ? error.message : 'Something went wrong'}
          </p>
          <Button onClick={() => refetch()} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  const students = attendanceData?.students || []
  const attendance = attendanceData?.attendance || []
  const stats = attendanceData?.stats || []

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-800">Attendance</h1>
          <p className="text-gray-500">Track your children's attendance records</p>
        </div>
        <Button onClick={() => refetch()} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Student</label>
              <Select value={selectedStudent} onValueChange={setSelectedStudent}>
                <SelectTrigger>
                  <SelectValue placeholder="Select student" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Children</SelectItem>
                  {students.map((student) => (
                    <SelectItem key={student.id} value={student.id}>
                      {student.name} ({student.sid})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Period</label>
              <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                <SelectTrigger>
                  <SelectValue placeholder="Select period" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">Daily</SelectItem>
                  <SelectItem value="weekly">Weekly</SelectItem>
                  <SelectItem value="monthly">Monthly</SelectItem>
                  <SelectItem value="semester">Semester</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Year</label>
              <Select value={selectedYear} onValueChange={setSelectedYear}>
                <SelectTrigger>
                  <SelectValue placeholder="Select year" />
                </SelectTrigger>
                <SelectContent>
                  {years.map((year) => (
                    <SelectItem key={year.value} value={year.value}>
                      {year.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {selectedPeriod === 'daily' && (
              <div className="space-y-2">
                <Label htmlFor="attendance-date" className="text-sm font-medium flex items-center">
                  <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                  Date
                </Label>
                <Input
                  id="attendance-date"
                  type="date"
                  value={selectedDate.toISOString().split('T')[0]}
                  onChange={(e) => setSelectedDate(new Date(e.target.value))}
                  className="h-10"
                />
              </div>
            )}

            {selectedPeriod === 'weekly' && (
              <div className="space-y-2">
                <label className="text-sm font-medium">Week</label>
                <Select value={selectedWeek} onValueChange={setSelectedWeek}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select week" />
                  </SelectTrigger>
                  <SelectContent>
                    {weeks.map((week) => (
                      <SelectItem key={week.value} value={week.value}>
                        {week.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {selectedPeriod === 'monthly' && (
              <div className="space-y-2">
                <label className="text-sm font-medium">Month</label>
                <Select value={selectedMonth} onValueChange={setSelectedMonth}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select month" />
                  </SelectTrigger>
                  <SelectContent>
                    {months.map((month) => (
                      <SelectItem key={month.value} value={month.value}>
                        {month.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Statistics Cards */}
      {stats.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {stats.map((stat) => {
            const student = students.find(s => s.id === stat.studentId)
            return (
              <Card key={stat.studentId}>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center">
                    <User className="h-4 w-4 mr-2" />
                    {student?.name || 'Unknown Student'}
                  </CardTitle>
                  <CardDescription>
                    Class {student?.className} • SID: {student?.sid}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Attendance Rate</span>
                      <span className="text-sm text-gray-500">{stat.presentPercentage}%</span>
                    </div>
                    <Progress value={stat.presentPercentage} className="h-2" />
                  </div>
                  
                  <div className="grid grid-cols-3 gap-2 text-center text-xs">
                    <div className="bg-green-50 p-2 rounded">
                      <div className="font-semibold text-green-600">{stat.present}</div>
                      <div className="text-green-600">Present</div>
                    </div>
                    <div className="bg-red-50 p-2 rounded">
                      <div className="font-semibold text-red-600">{stat.absent}</div>
                      <div className="text-red-600">Absent</div>
                    </div>
                    <div className="bg-yellow-50 p-2 rounded">
                      <div className="font-semibold text-yellow-600">{stat.permission}</div>
                      <div className="text-yellow-600">Permission</div>
                    </div>
                  </div>
                  
                  <div className="text-center text-sm text-gray-500">
                    Total Days: {stat.total}
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>
      )}

      {/* Attendance Records */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="h-5 w-5 mr-2" />
            Attendance Records
          </CardTitle>
          <CardDescription>
            Detailed attendance records for the selected period
          </CardDescription>
        </CardHeader>
        <CardContent>
          {attendance.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Calendar className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No attendance records found for the selected period</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Student</TableHead>
                  <TableHead>Class</TableHead>
                  <TableHead>Status</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {attendance.map((record) => (
                  <TableRow key={record.id}>
                    <TableCell className="font-medium">
                      {formatDate(record.date)}
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{record.student.name}</div>
                        <div className="text-sm text-gray-500">SID: {record.student.sid}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{record.className}</Badge>
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(record.status)}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

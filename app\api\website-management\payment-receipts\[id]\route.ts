import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET a specific payment receipt
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id
    
    const paymentReceipt = await prisma.paymentReceipt.findUnique({
      where: { id },
    })
    
    if (!paymentReceipt) {
      return NextResponse.json(
        { error: 'Payment receipt not found' },
        { status: 404 }
      )
    }
    
    return NextResponse.json(paymentReceipt)
  } catch (error) {
    console.error('Error fetching payment receipt:', error)
    return NextResponse.json(
      { error: 'Failed to fetch payment receipt' },
      { status: 500 }
    )
  }
}

// UPDATE a payment receipt
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id
    const data = await request.json()
    
    // Check if payment receipt exists
    const existingPaymentReceipt = await prisma.paymentReceipt.findUnique({
      where: { id },
    })
    
    if (!existingPaymentReceipt) {
      return NextResponse.json(
        { error: 'Payment receipt not found' },
        { status: 404 }
      )
    }
    
    // Validate status field
    if (data.status && !['pending', 'verified', 'rejected'].includes(data.status)) {
      return NextResponse.json(
        { error: 'Status must be one of: pending, verified, rejected' },
        { status: 400 }
      )
    }
    
    // Update the payment receipt
    const updatedPaymentReceipt = await prisma.paymentReceipt.update({
      where: { id },
      data: {
        status: data.status || undefined,
        // Allow updating other fields if needed
        studentName: data.studentName || undefined,
        className: data.className || undefined,
        month: data.month || undefined,
        bankName: data.bankName || undefined,
        receiptImageUrl: data.receiptImageUrl || undefined,
      },
    })
    
    return NextResponse.json(updatedPaymentReceipt)
  } catch (error) {
    console.error('Error updating payment receipt:', error)
    return NextResponse.json(
      { error: 'Failed to update payment receipt' },
      { status: 500 }
    )
  }
}

// DELETE a payment receipt
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id
    
    // Check if payment receipt exists
    const existingPaymentReceipt = await prisma.paymentReceipt.findUnique({
      where: { id },
    })
    
    if (!existingPaymentReceipt) {
      return NextResponse.json(
        { error: 'Payment receipt not found' },
        { status: 404 }
      )
    }
    
    // Delete the payment receipt
    await prisma.paymentReceipt.delete({
      where: { id },
    })
    
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting payment receipt:', error)
    return NextResponse.json(
      { error: 'Failed to delete payment receipt' },
      { status: 500 }
    )
  }
}

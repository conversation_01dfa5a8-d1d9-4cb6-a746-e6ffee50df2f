"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/app/components/ui/button';
import { useRouter } from 'next/navigation';
import { FaGraduationCap, FaPhone } from 'react-icons/fa';

// Call to Action interface
interface CallToAction {
  id: string;
  title: string;
  description: string;
  primaryBtnText: string;
  primaryBtnLink: string;
  secondaryBtnText: string | null;
  secondaryBtnLink: string | null;
  isActive: boolean;
}

export default function CallToAction() {
  const router = useRouter();
  const [callToAction, setCallToAction] = useState<CallToAction | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch call to action from API
  useEffect(() => {
    const fetchCallToAction = async () => {
      try {
        const response = await fetch('/api/website-management/call-to-action');

        if (!response.ok) {
          throw new Error('Failed to fetch call to action');
        }

        const data = await response.json();

        // Check if data is an array or an object with a data property
        const ctaArray = Array.isArray(data) ? data : (data.data && Array.isArray(data.data) ? data.data : []);

        // Find the first active call to action
        const activeCTA = ctaArray.find((cta: CallToAction) => cta.isActive);

        if (activeCTA) {
          setCallToAction(activeCTA);
        } else {
          // Fallback to default if no active CTA found
          setCallToAction({
            id: 'default',
            title: 'Ready to Join Our School Community?',
            description: 'Take the first step towards providing your child with an excellent education grounded in Islamic values. Apply for admission or schedule a visit to our campus today.',
            primaryBtnText: 'Apply Now',
            primaryBtnLink: '/dashboard',
            secondaryBtnText: 'Contact Us',
            secondaryBtnLink: '/website/contact',
            isActive: true
          });
        }

        setIsLoading(false);
      } catch (err) {
        console.error('Error fetching call to action:', err);
        setError('Failed to load call to action');
        setIsLoading(false);

        // Fallback to default data if API fails
        setCallToAction({
          id: 'default',
          title: 'Ready to Join Our School Community?',
          description: 'Take the first step towards providing your child with an excellent education grounded in Islamic values. Apply for admission or schedule a visit to our campus today.',
          primaryBtnText: 'Apply Now',
          primaryBtnLink: '/dashboard',
          secondaryBtnText: 'Contact Us',
          secondaryBtnLink: '/website/contact',
          isActive: true
        });
      }
    };

    fetchCallToAction();
  }, []);

  // Handle button clicks
  const handlePrimaryButtonClick = () => {
    if (callToAction?.primaryBtnLink) {
      router.push(callToAction.primaryBtnLink);
    }
  };

  const handleSecondaryButtonClick = () => {
    if (callToAction?.secondaryBtnLink) {
      router.push(callToAction.secondaryBtnLink);
    }
  };

  return (
    <section className="py-16 bg-white dark:bg-gray-900">
      <div className="container mx-auto px-4">
        <div className="bg-gradient-to-r from-blue-600 to-indigo-700 rounded-2xl overflow-hidden shadow-xl">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
            {/* Content */}
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.7 }}
              viewport={{ once: true }}
              className="p-8 md:p-12"
            >
              {isLoading ? (
                <div className="flex justify-center items-center h-[200px]">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-white"></div>
                </div>
              ) : error ? (
                <div className="text-white">
                  <h2 className="text-3xl md:text-4xl font-bold mb-4">Ready to Join Our School Community?</h2>
                  <p className="text-blue-100 mb-8">
                    Take the first step towards providing your child with an excellent education grounded in Islamic values.
                  </p>
                </div>
              ) : callToAction ? (
                <>
                  <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">{callToAction.title}</h2>
                  <p className="text-blue-100 mb-8">
                    {callToAction.description}
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4">
                    <Button
                      onClick={handlePrimaryButtonClick}
                      className="bg-white text-blue-700 hover:bg-blue-50"
                    >
                      <FaGraduationCap className="mr-2" />
                      {callToAction.primaryBtnText}
                    </Button>
                    {callToAction.secondaryBtnText && (
                      <Button
                        onClick={handleSecondaryButtonClick}
                        variant="outline"
                        className="border-white text-white hover:bg-white/10"
                      >
                        <FaPhone className="mr-2" />
                        {callToAction.secondaryBtnText}
                      </Button>
                    )}
                  </div>
                </>
              ) : null}
            </motion.div>

            {/* Decorative Element */}
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.7 }}
              viewport={{ once: true }}
              className="relative h-full min-h-[300px] hidden md:block"
            >
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="relative">
                  {/* Decorative circles */}
                  <div className="absolute -top-20 -left-20 w-40 h-40 rounded-full bg-blue-500 opacity-20"></div>
                  <div className="absolute -bottom-10 -right-10 w-32 h-32 rounded-full bg-indigo-500 opacity-20"></div>
                  <div className="absolute top-10 right-10 w-24 h-24 rounded-full bg-white opacity-10"></div>

                  {/* Main decorative element */}
                  <div className="relative z-10 bg-white/10 backdrop-blur-sm p-8 rounded-lg border border-white/20 shadow-lg">
                    <div className="text-white text-center">
                      <div className="text-5xl font-bold mb-2">25+</div>
                      <div className="text-blue-100 mb-6">Years of Excellence</div>
                      <div className="grid grid-cols-2 gap-4 text-center">
                        <div>
                          <div className="text-3xl font-bold mb-1">500+</div>
                          <div className="text-sm text-blue-100">Students</div>
                        </div>
                        <div>
                          <div className="text-3xl font-bold mb-1">50+</div>
                          <div className="text-sm text-blue-100">Teachers</div>
                        </div>
                        <div>
                          <div className="text-3xl font-bold mb-1">98%</div>
                          <div className="text-sm text-blue-100">Success Rate</div>
                        </div>
                        <div>
                          <div className="text-3xl font-bold mb-1">15+</div>
                          <div className="text-sm text-blue-100">Programs</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
}

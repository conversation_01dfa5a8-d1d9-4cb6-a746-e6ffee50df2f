import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET() {
  try {
    console.log('Verifying user existence...');

    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        status: true,
        createdAt: true
      }
    });

    if (user) {
      console.log('User found:', user);
      return NextResponse.json({
        status: 'success',
        message: 'User exists',
        user: user
      });
    } else {
      console.log('User not found');
      return NextResponse.json({
        status: 'error',
        message: 'User not found'
      });
    }
  } catch (error) {
    console.error('Error verifying user:', error);
    
    return NextResponse.json({
      status: 'error',
      message: 'Failed to verify user',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

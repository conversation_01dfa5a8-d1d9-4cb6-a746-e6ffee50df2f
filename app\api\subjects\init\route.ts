import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET() {
  try {
    console.log('Initializing subjects...');
    
    // Check if we already have subjects
    const subjectCount = await prisma.subject.count();
    
    if (subjectCount > 0) {
      console.log(`Database already has ${subjectCount} subjects`);
      return NextResponse.json({
        status: 'success',
        message: 'Subjects already initialized',
        count: subjectCount
      });
    }
    
    // Get class IDs
    const classes = await prisma.class.findMany({
      select: { id: true, name: true }
    });
    
    if (classes.length === 0) {
      return NextResponse.json({
        status: 'error',
        message: 'No classes found. Please create classes first.'
      }, { status: 400 });
    }
    
    // ⚠️  DISABLED AUTO-SUBJECT CREATION
    // Subjects should only be created when explicitly selected in Class Management
    // This prevents unwanted subjects like "Science" and "Mathematics" from being auto-added

    console.log('Subject auto-initialization is disabled. Subjects should be managed through Class Management page.')

    const createdSubjects = []  // No subjects created automatically
    
    // Update class subject counts
    for (const cls of classes) {
      const count = await prisma.subject.count({
        where: { classId: cls.id }
      });
      
      await prisma.class.update({
        where: { id: cls.id },
        data: { totalSubjects: count }
      });
      
      console.log(`Updated class ${cls.name} with ${count} subjects`);
    }
    
    return NextResponse.json({
      status: 'success',
      message: `Created ${createdSubjects.length} subjects`,
      subjects: createdSubjects
    });
  } catch (error) {
    console.error('Error initializing subjects:', error);
    return NextResponse.json({
      status: 'error',
      message: 'Failed to initialize subjects',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

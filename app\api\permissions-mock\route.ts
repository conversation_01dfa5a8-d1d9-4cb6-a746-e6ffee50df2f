import { NextResponse } from 'next/server'
import { verifyJWT } from '@/lib/jwt'
import { cookies } from 'next/headers'

// GET all available permissions
export async function GET() {
  try {
    console.log('Fetching all available permissions')

    // Skip authentication checks for development
    if (process.env.NODE_ENV !== 'production') {
      console.log('Development mode - bypassing authentication checks');

      // Define available permissions grouped by module
      const permissions = {
        students: [
          { id: 'view_students', name: 'View Students', description: 'View student information' },
          { id: 'add_students', name: 'Add Students', description: 'Add new students' },
          { id: 'edit_students', name: 'Edit Students', description: 'Edit student information' },
          { id: 'delete_students', name: 'Delete Students', description: 'Delete students' },
          { id: 'import_students', name: 'Import Students', description: 'Import students from CSV' }
        ],
        classes: [
          { id: 'view_classes', name: 'View Classes', description: 'View class information' },
          { id: 'add_classes', name: 'Add Classes', description: 'Add new classes' },
          { id: 'edit_classes', name: 'Edit Classes', description: 'Edit class information' },
          { id: 'delete_classes', name: 'Delete Classes', description: 'Delete classes' }
        ],
        teachers: [
          { id: 'view_teachers', name: 'View Teachers', description: 'View teacher information' },
          { id: 'add_teachers', name: 'Add Teachers', description: 'Add new teachers' },
          { id: 'edit_teachers', name: 'Edit Teachers', description: 'Edit teacher information' },
          { id: 'delete_teachers', name: 'Delete Teachers', description: 'Delete teachers' },
          { id: 'assign_teachers', name: 'Assign Teachers', description: 'Assign teachers to classes and subjects' }
        ],
        marks: [
          { id: 'view_marks', name: 'View Marks', description: 'View student marks' },
          { id: 'add_marks', name: 'Add Marks', description: 'Add new marks' },
          { id: 'edit_marks', name: 'Edit Marks', description: 'Edit marks' },
          { id: 'delete_marks', name: 'Delete Marks', description: 'Delete marks' },
          { id: 'import_marks', name: 'Import Marks', description: 'Import marks from CSV' }
        ],
        attendance: [
          { id: 'view_attendance', name: 'View Attendance', description: 'View student attendance' },
          { id: 'add_attendance', name: 'Add Attendance', description: 'Add new attendance records' },
          { id: 'edit_attendance', name: 'Edit Attendance', description: 'Edit attendance records' }
        ],
        reports: [
          { id: 'generate_reports', name: 'Generate Reports', description: 'Generate various reports' },
          { id: 'view_reports', name: 'View Reports', description: 'View generated reports' },
          { id: 'export_reports', name: 'Export Reports', description: 'Export reports to PDF/Excel' }
        ],
        system: [
          { id: 'system_settings', name: 'System Settings', description: 'Manage system settings' },
          { id: 'manage_users', name: 'Manage Users', description: 'Manage user accounts' },
          { id: 'manage_roles', name: 'Manage Roles', description: 'Manage roles and permissions' }
        ]
      };

      console.log('Returning all permissions');
      return NextResponse.json(permissions);
    }

    try {
      // This code will run in production mode
      // Verify authentication
      const cookieStore = cookies();
      const token = cookieStore.get('token')?.value;

      if (!token) {
        console.error('No authentication token found');
        return NextResponse.json(
          { error: 'Unauthorized - Authentication required' },
          { status: 401 }
        );
      }

      // Verify the token
      const decoded = await verifyJWT(token);

      // Only allow admins to view all permissions
      if (decoded.role !== 'ADMIN') {
        console.error('Unauthorized permissions access attempt', {
          requesterId: decoded.id,
          requesterRole: decoded.role
        });
        return NextResponse.json(
          { error: 'Forbidden - Only administrators can view all permissions' },
          { status: 403 }
        );
      }

      // Define available permissions grouped by module
      const permissions = {
        // Same permissions as above
      };

      console.log('Returning all permissions');
      return NextResponse.json(permissions);
    } catch (jwtError) {
      console.error('JWT verification error:', jwtError)
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      )
    }
  } catch (error) {
    console.error('Error fetching permissions:', error)
    return NextResponse.json(
      { error: 'Failed to fetch permissions' },
      { status: 500 }
    )
  }
}

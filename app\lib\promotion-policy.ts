// Promotion Policy Utilities

export interface PromotionPolicyData {
  id?: string
  passingScore: number
  maxFailedSubjects: number
  applyToAllClasses: boolean
  description?: string
  isActive?: boolean
  createdBy?: string
  createdAt?: string
  updatedAt?: string
}

// Default promotion policy
export const DEFAULT_PROMOTION_POLICY: PromotionPolicyData = {
  passingScore: 50,
  maxFailedSubjects: 2, // Student fails if they fail in 3 or more subjects
  applyToAllClasses: true,
  description: 'Default promotion policy: Students who score below 50 in three or more subjects will be detained.',
  isActive: true
}

/**
 * Fetch the current active promotion policy
 */
export async function getPromotionPolicy(): Promise<PromotionPolicyData> {
  try {
    const response = await fetch('/api/promotion-policy')
    if (!response.ok) {
      console.warn('Failed to fetch promotion policy, using default')
      return DEFAULT_PROMOTION_POLICY
    }
    
    const policy = await response.json()
    return policy || DEFAULT_PROMOTION_POLICY
  } catch (error) {
    console.error('Error fetching promotion policy:', error)
    return DEFAULT_PROMOTION_POLICY
  }
}

/**
 * Calculate promotion status based on subject marks and promotion policy
 */
export function calculatePromotionStatus(
  subjects: any[], 
  semester: string, 
  policy?: PromotionPolicyData
): boolean {
  if (!subjects || subjects.length === 0) return true // Default to promoted if no data
  
  // Use provided policy or default
  const promotionPolicy = policy || DEFAULT_PROMOTION_POLICY
  
  const failedSubjectsCount = subjects.filter(subject => {
    let score = 0
    
    if (semester === 'Annual') {
      // For annual view, check the average
      score = subject.average ||
        (subject['First Semester'] && subject['Second Semester']
          ? (parseInt(subject['First Semester']) + parseInt(subject['Second Semester'])) / 2
          : (subject['First Semester'] || subject['Second Semester'] || 0))
    } else {
      // For single semester view
      score = subject[semester] || 0
    }
    
    return score < promotionPolicy.passingScore
  }).length

  // Student is promoted if they fail in maxFailedSubjects or fewer subjects
  // Student is detained if they fail in more than maxFailedSubjects subjects
  return failedSubjectsCount <= promotionPolicy.maxFailedSubjects
}

/**
 * Get promotion policy description for display
 */
export function getPromotionPolicyDescription(policy?: PromotionPolicyData): string {
  const promotionPolicy = policy || DEFAULT_PROMOTION_POLICY
  const detentionThreshold = promotionPolicy.maxFailedSubjects + 1
  
  return `Students who score below ${promotionPolicy.passingScore} in ${detentionThreshold} or more subjects will be detained.`
}

/**
 * Get promotion status text
 */
export function getPromotionStatusText(isPromoted: boolean): { status: string; description: string; color: string } {
  if (isPromoted) {
    return {
      status: 'PROMOTED',
      description: 'Student has qualified for promotion',
      color: 'green'
    }
  } else {
    return {
      status: 'DETAINED',
      description: 'Student has failed in too many subjects',
      color: 'red'
    }
  }
}

/**
 * Validate promotion policy data
 */
export function validatePromotionPolicy(policy: Partial<PromotionPolicyData>): { isValid: boolean; errors: string[] } {
  const errors: string[] = []
  
  if (typeof policy.passingScore !== 'number' || policy.passingScore < 0 || policy.passingScore > 100) {
    errors.push('Passing score must be a number between 0 and 100')
  }
  
  if (typeof policy.maxFailedSubjects !== 'number' || policy.maxFailedSubjects < 0 || policy.maxFailedSubjects > 10) {
    errors.push('Max failed subjects must be a number between 0 and 10')
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

"use client";

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { FaCheckCircle, FaFileAlt, FaCalendarAlt, FaUserFriends, FaChalkboardTeacher, FaGraduationCap } from 'react-icons/fa';
import { Button } from '@/app/components/ui/button';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.5 },
  },
};

// Application steps data
const applicationSteps = [
  {
    id: 1,
    title: "Submit Application",
    description: "Complete and submit the online application form along with the application fee. You'll need to provide basic information about your child and family.",
    icon: <FaFileAlt className="text-4xl text-blue-600" />,
    deadline: "Rolling admissions, priority deadline: March 1",
  },
  {
    id: 2,
    title: "Provide Documentation",
    description: "Submit required documents including birth certificate, academic records, recommendation letters, and health records.",
    icon: <FaFileAlt className="text-4xl text-green-600" />,
    deadline: "Within 2 weeks of application submission",
  },
  {
    id: 3,
    title: "Entrance Assessment",
    description: "Students will complete an age-appropriate assessment to determine academic readiness and proper grade placement.",
    icon: <FaChalkboardTeacher className="text-4xl text-purple-600" />,
    deadline: "Scheduled after document submission",
  },
  {
    id: 4,
    title: "Family Interview",
    description: "Parents and the prospective student will meet with school administrators to discuss expectations, goals, and alignment with the school's mission.",
    icon: <FaUserFriends className="text-4xl text-amber-600" />,
    deadline: "Scheduled after entrance assessment",
  },
  {
    id: 5,
    title: "Admission Decision",
    description: "The Admissions Committee will review the complete application and notify families of the admission decision.",
    icon: <FaGraduationCap className="text-4xl text-red-600" />,
    deadline: "Within 2-3 weeks of completed application process",
  },
];

// Grade level requirements
const gradeLevelRequirements = [
  {
    level: "Kindergarten",
    ageRequirement: "Must be 5 years old by September 1",
    academicRequirement: "Basic readiness assessment",
    additionalNotes: "Toilet trained and able to follow simple instructions",
  },
  {
    level: "Elementary School (Grades 1-5)",
    ageRequirement: "Appropriate age for grade level",
    academicRequirement: "Grade-level assessment in reading, writing, and mathematics",
    additionalNotes: "Previous school records required",
  },
  {
    level: "Middle School (Grades 6-8)",
    ageRequirement: "Appropriate age for grade level",
    academicRequirement: "Grade-level assessment in core subjects",
    additionalNotes: "Previous school records and teacher recommendations required",
  },
  {
    level: "High School (Grades 9-12)",
    ageRequirement: "Appropriate age for grade level",
    academicRequirement: "Comprehensive assessment in core subjects",
    additionalNotes: "Transcript review, minimum GPA requirements, and interview with department heads",
  },
];

export default function HowToApplyPage() {
  return (
    <div className="min-h-screen pt-20">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-blue-900 to-indigo-900 text-white">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{ backgroundImage: 'url(/images/pattern.svg)', backgroundSize: '20px' }}></div>
        </div>
        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center max-w-3xl mx-auto"
          >
            <h1 className="text-4xl md:text-5xl font-bold mb-6">How to Apply</h1>
            <p className="text-xl text-blue-100">
              A step-by-step guide to joining the Alfalah Islamic School community
            </p>
          </motion.div>
        </div>
      </section>

      {/* Introduction Section */}
      <section className="py-16 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.7 }}
              viewport={{ once: true }}
            >
              <div className="relative h-[400px] rounded-lg overflow-hidden shadow-xl">
                <Image
                  src="/images/portrait.jpg" // Replace with actual image
                  alt="Students at Alfalah Islamic School"
                  fill
                  className="object-cover"
                />
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.7 }}
              viewport={{ once: true }}
              className="space-y-6"
            >
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Join Our School Community</h2>
              <p className="text-gray-600 dark:text-gray-300">
                Thank you for your interest in Alfalah Islamic School. We are committed to providing a nurturing educational environment that combines academic excellence with Islamic values.
              </p>
              <p className="text-gray-600 dark:text-gray-300">
                Our admissions process is designed to ensure that we can meet the educational needs of each student and that families understand and support our school's mission and values.
              </p>
              <p className="text-gray-600 dark:text-gray-300">
                We welcome applications from families of all backgrounds who share our commitment to academic excellence and Islamic principles.
              </p>
              <div className="pt-4 flex flex-wrap gap-4">
                <Link href="/website/admissions/requirements">
                  <Button variant="outline">
                    Admission Requirements
                  </Button>
                </Link>
                <Link href="/website/admissions/tuition-fees">
                  <Button variant="outline">
                    Tuition & Fees
                  </Button>
                </Link>
                <Link href="/website/admissions/forms">
                  <Button variant="outline">
                    Download Forms
                  </Button>
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Application Process */}
      <section id="application-process" className="py-16 bg-gray-50 dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Application Process</h2>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Follow these steps to apply for admission to Alfalah Islamic School
            </p>
          </motion.div>

          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="max-w-4xl mx-auto"
          >
            <div className="relative">
              {/* Process Line */}
              <div className="absolute left-0 md:left-[40px] top-0 bottom-0 w-1 bg-blue-600 hidden md:block"></div>

              {/* Steps */}
              {applicationSteps.map((step) => (
                <motion.div
                  key={step.id}
                  variants={itemVariants}
                  className="mb-12 last:mb-0 relative"
                >
                  <div className="flex flex-col md:flex-row items-start gap-6">
                    <div className="hidden md:flex items-center justify-center w-20 h-20 rounded-full bg-white dark:bg-gray-900 border-4 border-blue-600 z-10 flex-shrink-0">
                      {step.icon}
                    </div>
                    <div className="md:hidden flex items-center justify-center w-12 h-12 rounded-full bg-blue-600 text-white mb-4">
                      <span className="text-lg font-bold">{step.id}</span>
                    </div>
                    <div className="bg-white dark:bg-gray-900 rounded-lg shadow-md p-6 flex-grow">
                      <div className="flex items-center mb-4">
                        <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Step {step.id}: {step.title}</h3>
                      </div>
                      <p className="text-gray-600 dark:text-gray-300 mb-4">{step.description}</p>
                      <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                        <FaCalendarAlt className="mr-2 text-blue-600" />
                        <span><strong>Deadline:</strong> {step.deadline}</span>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Grade Level Requirements */}
      <section id="grade-requirements" className="py-16 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Grade Level Requirements</h2>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Specific requirements vary by grade level to ensure students are properly placed for academic success
            </p>
          </motion.div>

          <div className="max-w-4xl mx-auto">
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="bg-gray-100 dark:bg-gray-800">
                    <th className="px-6 py-3 text-left text-sm font-semibold text-gray-900 dark:text-white border border-gray-200 dark:border-gray-700">Grade Level</th>
                    <th className="px-6 py-3 text-left text-sm font-semibold text-gray-900 dark:text-white border border-gray-200 dark:border-gray-700">Age Requirement</th>
                    <th className="px-6 py-3 text-left text-sm font-semibold text-gray-900 dark:text-white border border-gray-200 dark:border-gray-700">Academic Requirement</th>
                    <th className="px-6 py-3 text-left text-sm font-semibold text-gray-900 dark:text-white border border-gray-200 dark:border-gray-700">Additional Notes</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                  {gradeLevelRequirements.map((grade, index) => (
                    <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                      <td className="px-6 py-4 text-sm text-gray-900 dark:text-white border border-gray-200 dark:border-gray-700 font-medium">{grade.level}</td>
                      <td className="px-6 py-4 text-sm text-gray-600 dark:text-gray-300 border border-gray-200 dark:border-gray-700">{grade.ageRequirement}</td>
                      <td className="px-6 py-4 text-sm text-gray-600 dark:text-gray-300 border border-gray-200 dark:border-gray-700">{grade.academicRequirement}</td>
                      <td className="px-6 py-4 text-sm text-gray-600 dark:text-gray-300 border border-gray-200 dark:border-gray-700">{grade.additionalNotes}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </section>

      {/* Important Dates */}
      <section className="py-16 bg-gray-50 dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Important Admissions Dates</h2>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Key dates to remember for the 2023-2024 academic year admissions process
            </p>
          </motion.div>

          <div className="max-w-3xl mx-auto">
            <div className="bg-white dark:bg-gray-900 rounded-lg shadow-md overflow-hidden">
              <div className="grid grid-cols-1 md:grid-cols-2 divide-y md:divide-y-0 md:divide-x divide-gray-200 dark:divide-gray-700">
                <div className="p-6">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Priority Application Period</h3>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <FaCheckCircle className="text-green-600 mt-1 mr-2 flex-shrink-0" />
                      <div>
                        <p className="text-gray-900 dark:text-white font-medium">November 1, 2023</p>
                        <p className="text-sm text-gray-600 dark:text-gray-300">Applications open for 2024-2025</p>
                      </div>
                    </li>
                    <li className="flex items-start">
                      <FaCheckCircle className="text-green-600 mt-1 mr-2 flex-shrink-0" />
                      <div>
                        <p className="text-gray-900 dark:text-white font-medium">March 1, 2024</p>
                        <p className="text-sm text-gray-600 dark:text-gray-300">Priority application deadline</p>
                      </div>
                    </li>
                    <li className="flex items-start">
                      <FaCheckCircle className="text-green-600 mt-1 mr-2 flex-shrink-0" />
                      <div>
                        <p className="text-gray-900 dark:text-white font-medium">March 15-30, 2024</p>
                        <p className="text-sm text-gray-600 dark:text-gray-300">Entrance assessments</p>
                      </div>
                    </li>
                    <li className="flex items-start">
                      <FaCheckCircle className="text-green-600 mt-1 mr-2 flex-shrink-0" />
                      <div>
                        <p className="text-gray-900 dark:text-white font-medium">April 15, 2024</p>
                        <p className="text-sm text-gray-600 dark:text-gray-300">Admission decisions sent</p>
                      </div>
                    </li>
                    <li className="flex items-start">
                      <FaCheckCircle className="text-green-600 mt-1 mr-2 flex-shrink-0" />
                      <div>
                        <p className="text-gray-900 dark:text-white font-medium">May 1, 2024</p>
                        <p className="text-sm text-gray-600 dark:text-gray-300">Enrollment deposit due</p>
                      </div>
                    </li>
                  </ul>
                </div>
                <div className="p-6">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Rolling Admissions Period</h3>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <FaCheckCircle className="text-blue-600 mt-1 mr-2 flex-shrink-0" />
                      <div>
                        <p className="text-gray-900 dark:text-white font-medium">After March 1, 2024</p>
                        <p className="text-sm text-gray-600 dark:text-gray-300">Applications accepted on a space-available basis</p>
                      </div>
                    </li>
                    <li className="flex items-start">
                      <FaCheckCircle className="text-blue-600 mt-1 mr-2 flex-shrink-0" />
                      <div>
                        <p className="text-gray-900 dark:text-white font-medium">Ongoing</p>
                        <p className="text-sm text-gray-600 dark:text-gray-300">Entrance assessments scheduled within 2 weeks of application</p>
                      </div>
                    </li>
                    <li className="flex items-start">
                      <FaCheckCircle className="text-blue-600 mt-1 mr-2 flex-shrink-0" />
                      <div>
                        <p className="text-gray-900 dark:text-white font-medium">Ongoing</p>
                        <p className="text-sm text-gray-600 dark:text-gray-300">Admission decisions within 3 weeks of completed application</p>
                      </div>
                    </li>
                    <li className="flex items-start">
                      <FaCheckCircle className="text-blue-600 mt-1 mr-2 flex-shrink-0" />
                      <div>
                        <p className="text-gray-900 dark:text-white font-medium">2 weeks after acceptance</p>
                        <p className="text-sm text-gray-600 dark:text-gray-300">Enrollment deposit due</p>
                      </div>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-12 bg-blue-600">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="text-white mb-6 md:mb-0">
              <h2 className="text-2xl font-bold mb-2">Ready to Apply?</h2>
              <p className="text-blue-100">Start your application process today or contact us with any questions.</p>
            </div>
            <div className="flex flex-col sm:flex-row gap-4">
              <Button className="bg-white text-blue-600 hover:bg-blue-50">
                Apply Now
              </Button>
              <Link href="/website/contact">
                <Button variant="outline" className="border-white text-white hover:bg-blue-700">
                  Contact Admissions
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

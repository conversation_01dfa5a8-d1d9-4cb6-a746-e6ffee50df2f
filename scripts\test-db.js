const { PrismaClient } = require('@prisma/client')

async function testConnection() {
  const prisma = new PrismaClient()
  
  try {
    console.log('Attempting to connect to the database...')
    
    // Try a simple query
    const userCount = await prisma.user.count()
    
    console.log('Connection successful!')
    console.log(`Number of users in the database: ${userCount}`)
    
    // Try to create a sample user
    const existingUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })
    
    if (existingUser) {
      console.log('Sample admin user already exists:', existingUser.email)
    } else {
      console.log('Creating sample admin user...')
      const newUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Admin User',
          password: 'admin123', // In a real app, this would be hashed
          role: 'ADMIN',
          status: 'active',
          createdAt: new Date(),
          updatedAt: new Date()
        }
      })
      console.log('Sample admin user created:', newUser.email)
    }
    
  } catch (error) {
    console.error('Database connection error:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testConnection()

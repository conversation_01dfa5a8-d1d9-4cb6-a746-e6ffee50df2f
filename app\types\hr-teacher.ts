// HR Teacher related types

export interface HRTeacher {
  id: string
  name: string
  email: string
  subject?: string
  mobile?: string
  fatherName?: string
  gender?: string
  source?: 'teacher' | 'user'
}

export interface HRClass {
  id: string
  name: string
  hrTeacherId: string | null
  hrTeacher?: {
    id: string
    name: string
    email: string
  } | null
  totalStudents?: number
  totalSubjects?: number
}

export interface HRAssignmentRequest {
  teacherId: string
  classId: string
}

export interface HRAssignmentResponse {
  message: string
  class: HRClass
  teacher: HRTeach<PERSON>
}

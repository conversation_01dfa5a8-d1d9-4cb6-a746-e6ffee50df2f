const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function main() {
  try {
    console.log('=== Checking <PERSON> Data ===\n')

    // 1. Check if <PERSON> exists as a teacher
    console.log('1. Looking for <PERSON> in teachers...')
    const emilyTeacher = await prisma.teacher.findFirst({
      where: {
        name: {
          contains: '<PERSON>'
        }
      }
    })
    
    if (emilyTeacher) {
      console.log('✅ Found <PERSON> as teacher:')
      console.log(`   ID: ${emilyTeacher.id}`)
      console.log(`   Name: ${emilyTeacher.name}`)
      console.log(`   Email: ${emilyTeacher.email}`)
      console.log(`   Subject: ${emilyTeacher.subject}`)
    } else {
      console.log('❌ <PERSON> not found in teachers table')
    }

    // 2. Check if Grade 2B exists
    console.log('\n2. Looking for Grade 2B class...')
    const grade2B = await prisma.class.findFirst({
      where: {
        name: {
          contains: '2B'
        }
      }
    })
    
    if (grade2B) {
      console.log('✅ Found Grade 2B class:')
      console.log(`   ID: ${grade2B.id}`)
      console.log(`   Name: ${grade2B.name}`)
      console.log(`   HR Teacher ID: ${grade2B.hrTeacherId || 'None'}`)
    } else {
      console.log('❌ Grade 2B not found in classes table')
    }

    // 3. Check existing teacher permissions for Emily Davis
    if (emilyTeacher) {
      console.log('\n3. Checking existing teacher permissions for Emily Davis...')
      const emilyPermissions = await prisma.teacherPermission.findMany({
        where: {
          teacherId: emilyTeacher.id
        },
        include: {
          // We'll get class details separately since there's no direct relation
        }
      })
      
      if (emilyPermissions.length > 0) {
        console.log(`✅ Found ${emilyPermissions.length} existing permissions:`)
        for (const perm of emilyPermissions) {
          // Get class details
          const classDetails = await prisma.class.findUnique({
            where: { id: perm.classId },
            select: { name: true }
          })
          
          console.log(`   - Class: ${classDetails?.name || perm.classId}`)
          console.log(`     View Attendance: ${perm.canViewAttendance}`)
          console.log(`     Take Attendance: ${perm.canTakeAttendance}`)
          console.log(`     Add Marks: ${perm.canAddMarks}`)
          console.log(`     Edit Marks: ${perm.canEditMarks}`)
        }
      } else {
        console.log('❌ No teacher permissions found for Emily Davis')
      }
    }

    // 4. Check if Emily is assigned as HR teacher for Grade 2B
    if (emilyTeacher && grade2B) {
      console.log('\n4. Checking HR teacher assignment...')
      if (grade2B.hrTeacherId === emilyTeacher.id) {
        console.log('✅ Emily Davis is assigned as HR teacher for Grade 2B')
      } else {
        console.log('❌ Emily Davis is NOT assigned as HR teacher for Grade 2B')
        
        // Check who is the HR teacher for Grade 2B
        if (grade2B.hrTeacherId) {
          const hrTeacher = await prisma.teacher.findUnique({
            where: { id: grade2B.hrTeacherId },
            select: { name: true, email: true }
          })
          console.log(`   Current HR teacher: ${hrTeacher?.name || 'Unknown'}`)
        }
      }
    }

    // 5. Create teacher permission for Emily Davis in Grade 2B if both exist
    if (emilyTeacher && grade2B) {
      console.log('\n5. Creating teacher permission for Emily Davis in Grade 2B...')
      
      // Check if permission already exists
      const existingPermission = await prisma.teacherPermission.findUnique({
        where: {
          teacherId_classId: {
            teacherId: emilyTeacher.id,
            classId: grade2B.id
          }
        }
      })
      
      if (existingPermission) {
        console.log('⚠️  Permission already exists, updating...')
        const updatedPermission = await prisma.teacherPermission.update({
          where: {
            teacherId_classId: {
              teacherId: emilyTeacher.id,
              classId: grade2B.id
            }
          },
          data: {
            canViewAttendance: true,
            canTakeAttendance: true,
            canAddMarks: true,
            canEditMarks: true,
            updatedAt: new Date()
          }
        })
        console.log('✅ Updated teacher permission successfully')
      } else {
        console.log('Creating new permission...')
        const newPermission = await prisma.teacherPermission.create({
          data: {
            teacherId: emilyTeacher.id,
            classId: grade2B.id,
            canViewAttendance: true,
            canTakeAttendance: true,
            canAddMarks: true,
            canEditMarks: true
          }
        })
        console.log('✅ Created teacher permission successfully')
        console.log(`   Permission ID: ${newPermission.id}`)
      }
    }

    // 6. Show all teachers and their permissions
    console.log('\n6. Summary of all teachers and their permissions...')
    const allTeachers = await prisma.teacher.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        subject: true
      },
      orderBy: {
        name: 'asc'
      }
    })
    
    console.log(`\nFound ${allTeachers.length} teachers:`)
    for (const teacher of allTeachers) {
      const permissions = await prisma.teacherPermission.findMany({
        where: { teacherId: teacher.id }
      })
      
      console.log(`\n📚 ${teacher.name} (${teacher.subject})`)
      console.log(`   Email: ${teacher.email}`)
      console.log(`   ID: ${teacher.id}`)
      
      if (permissions.length > 0) {
        console.log(`   Permissions in ${permissions.length} class(es):`)
        for (const perm of permissions) {
          const classDetails = await prisma.class.findUnique({
            where: { id: perm.classId },
            select: { name: true }
          })
          console.log(`   - ${classDetails?.name || perm.classId}: View=${perm.canViewAttendance}, Take=${perm.canTakeAttendance}, Add=${perm.canAddMarks}, Edit=${perm.canEditMarks}`)
        }
      } else {
        console.log('   No permissions assigned')
      }
    }

  } catch (error) {
    console.error('Error:', error)
  } finally {
    await prisma.$disconnect()
  }
}

main()

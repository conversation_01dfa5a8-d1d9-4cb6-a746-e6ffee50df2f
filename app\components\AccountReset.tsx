'use client'

import React, { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { Button } from './ui/button'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/table'
import { Input } from './ui/input'
import { 
  Search, 
  RefreshCw, 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  Clock, 
  Filter,
  UserCheck
} from 'lucide-react'
import { Badge } from './ui/badge'
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from './ui/select'
import { toast } from './ui/use-toast'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "./ui/card"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "./ui/alert-dialog"

interface User {
  id: string
  name: string
  email: string
  role: string
  status: string
  lastLogin?: string
  createdAt: string
  updatedAt: string
}

export function AccountReset() {
  const queryClient = useQueryClient()
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false)
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [actionType, setActionType] = useState<'activate' | 'reset' | 'unlock'>('activate')

  // Fetch users
  const { 
    data: users = [], 
    isLoading, 
    error, 
    refetch 
  } = useQuery({
    queryKey: ['users'],
    queryFn: async () => {
      const response = await fetch('/api/users')
      if (!response.ok) {
        throw new Error('Failed to fetch users')
      }
      return response.json()
    }
  })

  // Update user status mutation
  const updateUserStatus = useMutation({
    mutationFn: async ({ userId, status }: { userId: string, status: string }) => {
      const response = await fetch(`/api/users/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update user status')
      }

      return response.json()
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
      toast({
        title: 'Success',
        description: `User status updated to ${data.user.status}`,
      })
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      })
    }
  })

  // Reset password mutation
  const resetPassword = useMutation({
    mutationFn: async (userId: string) => {
      const response = await fetch(`/api/users/${userId}/reset-password`, {
        method: 'POST',
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to reset password')
      }

      return response.json()
    },
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Password reset email has been sent to the user',
      })
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      })
    }
  })

  // Filter users based on search term and status filter
  const filteredUsers = users.filter((user: User) => {
    const matchesSearch = 
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.role.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = statusFilter === 'all' || user.status === statusFilter
    
    return matchesSearch && matchesStatus
  })

  // Handle confirm action
  const handleConfirmAction = () => {
    if (!selectedUser) return
    
    switch (actionType) {
      case 'activate':
        updateUserStatus.mutate({ userId: selectedUser.id, status: 'active' })
        break
      case 'reset':
        resetPassword.mutate(selectedUser.id)
        break
      case 'unlock':
        updateUserStatus.mutate({ userId: selectedUser.id, status: 'active' })
        break
    }
    
    setConfirmDialogOpen(false)
    setSelectedUser(null)
  }

  // Handle action button click
  const handleActionClick = (user: User, action: 'activate' | 'reset' | 'unlock') => {
    setSelectedUser(user)
    setActionType(action)
    setConfirmDialogOpen(true)
  }

  // Get action button based on user status
  const getActionButton = (user: User) => {
    switch (user.status) {
      case 'pending':
        return (
          <Button 
            variant="default" 
            size="sm"
            onClick={() => handleActionClick(user, 'activate')}
            className="bg-green-600 hover:bg-green-700"
          >
            <UserCheck className="h-4 w-4 mr-2" />
            Activate Account
          </Button>
        )
      case 'locked':
        return (
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => handleActionClick(user, 'unlock')}
            className="border-amber-200 text-amber-800 hover:bg-amber-50"
          >
            <CheckCircle className="h-4 w-4 mr-2" />
            Unlock Account
          </Button>
        )
      default:
        return (
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => handleActionClick(user, 'reset')}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Reset Password
          </Button>
        )
    }
  }

  // Render status badge
  const renderStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            <CheckCircle className="h-3.5 w-3.5 mr-1" />
            Active
          </Badge>
        )
      case 'inactive':
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
            <XCircle className="h-3.5 w-3.5 mr-1" />
            Inactive
          </Badge>
        )
      case 'locked':
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            <AlertCircle className="h-3.5 w-3.5 mr-1" />
            Locked
          </Badge>
        )
      case 'pending':
        return (
          <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
            <Clock className="h-3.5 w-3.5 mr-1" />
            Pending
          </Badge>
        )
      default:
        return (
          <Badge variant="outline">{status}</Badge>
        )
    }
  }

  // Get confirmation dialog content
  const getConfirmDialogContent = () => {
    if (!selectedUser) return null

    switch (actionType) {
      case 'activate':
        return {
          title: 'Activate Account',
          description: `Are you sure you want to activate ${selectedUser.name}'s account? This will change their status from "Pending" to "Active".`
        }
      case 'reset':
        return {
          title: 'Reset Password',
          description: `Are you sure you want to reset the password for ${selectedUser.name}? A password reset email will be sent to their email address.`
        }
      case 'unlock':
        return {
          title: 'Unlock Account',
          description: `Are you sure you want to unlock ${selectedUser.name}'s account? This will change their status from "Locked" to "Active".`
        }
      default:
        return {
          title: 'Confirm Action',
          description: 'Are you sure you want to proceed with this action?'
        }
    }
  }

  const dialogContent = getConfirmDialogContent()

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold">Account Management</h1>
        <p className="text-gray-600 mt-1">Activate pending accounts, reset passwords, or unlock accounts</p>
      </div>
      
      {/* Search and filter */}
      <div className="flex flex-col sm:flex-row items-center gap-4">
        <div className="relative w-full sm:w-96">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search users by name, email or role..."
            className="pl-10"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        
        <div className="flex items-center gap-2 ml-auto">
          <Filter className="h-4 w-4 text-gray-500" />
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
              <SelectItem value="locked">Locked</SelectItem>
            </SelectContent>
          </Select>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => refetch()}
            className="ml-2"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>
      
      {/* Help notice */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Account Management Guide</CardTitle>
          <CardDescription>
            Manage user accounts and their access to the system
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-start gap-2">
              <div className="bg-amber-50 p-2 rounded-full">
                <Clock className="h-5 w-5 text-amber-600" />
              </div>
              <div>
                <h3 className="font-medium text-sm">Pending Accounts</h3>
                <p className="text-xs text-gray-500">
                  New accounts that need to be activated before users can log in
                </p>
              </div>
            </div>
            <div className="flex items-start gap-2">
              <div className="bg-red-50 p-2 rounded-full">
                <AlertCircle className="h-5 w-5 text-red-600" />
              </div>
              <div>
                <h3 className="font-medium text-sm">Locked Accounts</h3>
                <p className="text-xs text-gray-500">
                  Accounts that have been locked due to multiple failed login attempts
                </p>
              </div>
            </div>
            <div className="flex items-start gap-2">
              <div className="bg-blue-50 p-2 rounded-full">
                <RefreshCw className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h3 className="font-medium text-sm">Password Reset</h3>
                <p className="text-xs text-gray-500">
                  Send password reset emails to users who need to change their password
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* Users table */}
      <div className="bg-white rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>User</TableHead>
              <TableHead>Role</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Created</TableHead>
              <TableHead className="text-right">Action</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={5} className="h-24 text-center">
                  <div className="flex justify-center items-center">
                    <RefreshCw className="h-5 w-5 animate-spin text-gray-400 mr-2" />
                    <span>Loading users...</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : error ? (
              <TableRow>
                <TableCell colSpan={5} className="h-24 text-center">
                  <div className="flex flex-col items-center justify-center text-red-500">
                    <XCircle className="h-5 w-5 mb-1" />
                    <span>Error loading users</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => refetch()}
                      className="mt-2"
                    >
                      Try Again
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ) : filteredUsers.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="h-24 text-center text-gray-500">
                  No users found matching your criteria
                </TableCell>
              </TableRow>
            ) : (
              filteredUsers.map((user: User) => (
                <TableRow key={user.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{user.name}</div>
                      <div className="text-sm text-gray-500">{user.email}</div>
                    </div>
                  </TableCell>
                  <TableCell>{user.role}</TableCell>
                  <TableCell>{renderStatusBadge(user.status)}</TableCell>
                  <TableCell>{new Date(user.createdAt).toLocaleDateString()}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex items-center justify-end gap-2">
                      {updateUserStatus.isPending && selectedUser?.id === user.id ? (
                        <RefreshCw className="h-4 w-4 animate-spin text-gray-400" />
                      ) : (
                        getActionButton(user)
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Confirmation Dialog */}
      <AlertDialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{dialogContent?.title}</AlertDialogTitle>
            <AlertDialogDescription>
              {dialogContent?.description}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleConfirmAction}>
              Confirm
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}

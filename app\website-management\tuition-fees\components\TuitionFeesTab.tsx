"use client"

import React, { useState } from 'react'
import { But<PERSON> } from '@/app/components/ui/button'
import { Card, CardContent } from '@/app/components/ui/card'
import { Label } from '@/app/components/ui/label'
import { Textarea } from '@/app/components/ui/textarea'
import { useToast } from '@/app/components/ui/use-toast'
import { Info, Save } from 'lucide-react'

// Mock data for initial content
const initialContent = {
  introText: `The information below outlines our tuition rates, fees, and payment options for the 2023-2024 academic year. If you have any questions, please don't hesitate to contact our Business Office.`,
  paymentMethodsText: `We accept payments by check, ACH bank transfer, or credit card (a 2.5% processing fee applies to credit card payments). Monthly payments require enrollment in our automatic payment system.`,
  contactText: `Our Business Office is here to help you navigate the financial aspects of enrollment.`
}

export default function TuitionFeesTab() {
  const { toast } = useToast()
  const [content, setContent] = useState(initialContent)
  const [isLoading, setIsLoading] = useState(false)

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setContent(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSave = async () => {
    setIsLoading(true)
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // In a real implementation, you would save to the database here
      // const response = await fetch('/api/website/tuition-fees', {
      //   method: 'PUT',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(content)
      // })
      
      toast({
        title: "Content Updated",
        description: "Tuition & Fees content has been updated successfully.",
        variant: "default",
      })
    } catch (error) {
      console.error('Error saving content:', error)
      toast({
        title: "Error",
        description: "Failed to update content. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-start space-x-2 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <Info className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5" />
        <div className="text-sm text-gray-600 dark:text-gray-300">
          <p>This tab allows you to edit the main content sections of the Tuition & Fees page. Changes will be reflected on the website immediately after saving.</p>
        </div>
      </div>

      <Card>
        <CardContent className="p-6 space-y-4">
          <div className="space-y-2">
            <Label htmlFor="introText">Introduction Text</Label>
            <Textarea
              id="introText"
              name="introText"
              value={content.introText}
              onChange={handleInputChange}
              rows={4}
              className="resize-y"
              placeholder="Enter introduction text for the tuition & fees page"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="paymentMethodsText">Payment Methods Text</Label>
            <Textarea
              id="paymentMethodsText"
              name="paymentMethodsText"
              value={content.paymentMethodsText}
              onChange={handleInputChange}
              rows={4}
              className="resize-y"
              placeholder="Describe accepted payment methods"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="contactText">Contact Information Text</Label>
            <Textarea
              id="contactText"
              name="contactText"
              value={content.contactText}
              onChange={handleInputChange}
              rows={3}
              className="resize-y"
              placeholder="Enter contact information for financial inquiries"
            />
          </div>

          <div className="flex justify-end">
            <Button 
              onClick={handleSave} 
              disabled={isLoading}
              className="flex items-center"
            >
              {isLoading ? (
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
              ) : (
                <Save className="mr-2 h-4 w-4" />
              )}
              Save Changes
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

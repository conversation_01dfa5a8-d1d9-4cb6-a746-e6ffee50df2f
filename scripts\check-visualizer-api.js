// <PERSON>ript to check the visualizer API endpoints
const fetch = require('node-fetch');

async function main() {
  try {
    // Check the visualizer/subjects API
    console.log('Checking visualizer/subjects API...');
    const subjectsResponse = await fetch('http://localhost:3000/api/visualizer/subjects');
    const subjects = await subjectsResponse.json();
    console.log(`Found ${subjects.length} subjects`);
    
    // Check if Maths (8E) is in the response
    const mathsSubject = subjects.find(subj => subj.name === 'Maths (8E)');
    console.log('Maths (8E) in API response:', mathsSubject ? 'Yes' : 'No');
    
    if (mathsSubject) {
      console.log('Maths (8E) details:', mathsSubject);
    }
    
    // Check for any subjects with "Maths" in the name
    const mathsSubjects = subjects.filter(subj => subj.name.includes('Maths'));
    console.log(`Found ${mathsSubjects.length} subjects containing "Maths"`);
    mathsSubjects.forEach(subj => {
      console.log(`- ${subj.name} (ID: ${subj.id}, ClassID: ${subj.classId})`);
    });
    
    // Check for subjects associated with Class 8E
    console.log('\nChecking for subjects associated with Class 8E...');
    const class8ESubjects = subjects.filter(subj => subj.class && subj.class.name === '8E');
    console.log(`Found ${class8ESubjects.length} subjects for Class 8E in API response`);
    class8ESubjects.forEach(subj => {
      console.log(`- ${subj.name} (ID: ${subj.id}, ClassID: ${subj.classId})`);
    });
    
    // List all subjects to see what's actually being returned
    console.log('\nAll subjects from API (first 10):');
    subjects.slice(0, 10).forEach(subj => {
      console.log(`- ${subj.name} (ID: ${subj.id}, ClassID: ${subj.classId}, ClassName: ${subj.class?.name || 'N/A'})`);
    });
    
  } catch (error) {
    console.error('Error:', error);
  }
}

main();

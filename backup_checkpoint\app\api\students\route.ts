import { NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'
import { formatStudentName } from '@/lib/utils'

// Helper function to generate a student SID
async function generateStudentSID(prisma: PrismaClient, className: string): Promise<string> {
  console.log(`Generating SID for class: ${className}`);

  // Get all students in the class, sorted by SID
  const classStudents = await prisma.student.findMany({
    where: { className },
    orderBy: { sid: 'desc' },
    take: 1,
  });

  // If no students exist in this class, start with 1
  if (classStudents.length === 0) {
    console.log(`No students found in class ${className}, starting with SID: ${className}1`);
    return `${className}1`;
  }

  // Get the highest SID
  const highestSid = classStudents[0].sid;
  console.log(`Highest existing SID: ${highestSid}`);

  // Extract the number part
  const match = highestSid.match(/\d+$/);
  if (!match) {
    console.log(`Could not extract number from SID: ${highestSid}, defaulting to 1`);
    return `${className}1`;
  }

  const highestNumber = parseInt(match[0], 10);
  const nextNumber = highestNumber + 1;
  console.log(`Next SID number: ${nextNumber}`);

  // Return the next SID in sequence
  return `${className}${nextNumber}`;
}

export async function GET(request: Request) {
  let prisma: PrismaClient | null = null;

  try {
    const { searchParams } = new URL(request.url)
    const className = searchParams.get('class')

    console.log(`Fetching students${className ? ` for class ${className}` : ''}`)

    // Create a new PrismaClient instance specifically for this request
    prisma = new PrismaClient({
      log: ['error', 'warn'],
      datasources: {
        db: {
          url: process.env.DATABASE_URL
        }
      }
    });

    // Test database connection first
    try {
      await prisma.$connect()
      console.log('Database connection successful')
    } catch (connectionError) {
      console.error('Database connection failed:', connectionError)
      return NextResponse.json(
        {
          error: 'Database connection failed',
          details: connectionError instanceof Error ? connectionError.message : 'Unknown error',
          suggestion: 'Please check your database connection string and ensure the database server is running. You may need to run /api/db-setup to create the database schema.'
        },
        { status: 500 }
      )
    }

    try {
      // Filter students by class if provided
      const students = await prisma.student.findMany({
        where: className ? { className } : undefined,
        include: {
          class: true,
        },
        orderBy: {
          sid: 'asc', // Order by SID
        },
      })

      console.log(`Found ${students.length} students${className ? ` in class ${className}` : ''}`)

      // If no students were found, suggest initializing the database
      if (students.length === 0) {
        console.log('No students found in the database')
        return NextResponse.json(
          {
            students: [],
            message: 'No students found in the database',
            suggestion: 'You may want to initialize the database with sample data by visiting /api/db-init'
          }
        )
      }

      return NextResponse.json(students)
    } catch (queryError) {
      console.error('Error querying students:', queryError)
      return NextResponse.json(
        {
          error: 'Failed to query students',
          details: queryError instanceof Error ? queryError.message : 'Unknown error',
          suggestion: 'The database tables may not exist. Try running /api/db-setup to create the database schema.'
        },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Error fetching students:', error)
    return NextResponse.json(
      {
        error: 'Failed to fetch students',
        details: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        suggestion: 'Please check the server logs for more details.'
      },
      { status: 500 }
    )
  } finally {
    // Clean up the connection
    if (prisma) {
      await prisma.$disconnect().catch(e => console.error('Error disconnecting from database:', e));
    }
  }
}

export async function POST(request: Request) {
  let prisma: PrismaClient | null = null;

  try {
    const data = await request.json()
    console.log('Creating student with data:', data);

    // Create a new PrismaClient instance specifically for this request
    prisma = new PrismaClient({
      log: ['error', 'warn'],
      datasources: {
        db: {
          url: process.env.DATABASE_URL
        }
      }
    });

    // Connect to the database
    await prisma.$connect();
    console.log('Database connection successful');

    // Start a transaction to ensure both operations succeed or fail together
    const result = await prisma.$transaction(async (tx) => {
      // 0. First check if the class exists
      const className = data.className || data.class
      console.log('Checking if class exists:', className);

      const classExists = await tx.class.findFirst({
        where: { name: className },
      })

      if (!classExists) {
        console.log(`Class '${className}' does not exist`);
        throw new Error(`Class '${className}' does not exist. Please create the class first.`)
      }

      console.log('Class exists:', classExists);

      // Generate a unique SID for the student
      const sid = await generateStudentSID(prisma, className)
      console.log('Generated SID:', sid);

      // 1. Create the student with formatted names and SID
      const student = await tx.student.create({
        data: {
          sid,
          name: formatStudentName(data.name),
          fatherName: formatStudentName(data.fatherName),
          gfName: formatStudentName(data.gfName),
          className: className,
          age: parseInt(data.age),
          gender: data.gender,
        },
      })

      console.log('Created student:', student);

      // 2. Update the class student count
      if (className) {
        // Find the class by name
        const classRecord = await tx.class.findFirst({
          where: { name: className },
        })

        if (classRecord) {
          // Count students in this class
          const studentCount = await tx.student.count({
            where: {
              className: className,
            },
          })

          console.log('Updated student count for class:', studentCount);

          // Update with the actual count
          await tx.class.update({
            where: { id: classRecord.id },
            data: { totalStudents: studentCount },
          })
        }
      }

      return student
    })

    console.log('Student created successfully:', result);
    return NextResponse.json(result)
  } catch (error) {
    console.error('Error creating student:', error)
    return NextResponse.json(
      {
        error: 'Failed to create student',
        details: error instanceof Error ? error.message : 'Unknown error',
        suggestion: 'Please check the database connection and ensure the class exists.'
      },
      { status: 500 }
    )
  } finally {
    // Clean up the connection
    if (prisma) {
      await prisma.$disconnect().catch(e => console.error('Error disconnecting from database:', e));
    }
  }
}



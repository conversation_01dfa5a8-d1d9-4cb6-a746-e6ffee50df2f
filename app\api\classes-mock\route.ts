import { NextResponse } from 'next/server'
import { verifyJWT } from '@/lib/jwt'
import { cookies } from 'next/headers'

// GET all classes
export async function GET() {
  try {
    console.log('Fetching all classes')

    // Skip authentication checks for development
    if (process.env.NODE_ENV !== 'production') {
      console.log('Development mode - bypassing authentication checks');

      // Mock data for classes
      const classes = [
        { id: '1', name: '10A', hrTeacherId: '1', hrTeacherName: '<PERSON>', totalStudents: 32 },
        { id: '2', name: '9B', hrTeacherId: '2', hrTeacherName: '<PERSON>', totalStudents: 28 },
        { id: '3', name: '8C', hrTeacherId: '4', hrTeacherName: '<PERSON>', totalStudents: 30 },
        { id: '4', name: '7A', hrTeacherId: null, hrTeacherName: null, totalStudents: 25 },
        { id: '5', name: '6B', hrTeacherId: null, hrTeacherName: null, totalStudents: 27 }
      ];

      console.log(`Returning ${classes.length} classes`);
      return NextResponse.json(classes);
    }

    try {
      // This part would handle production authentication
      // For now, we'll just return an error
      throw new Error("Production authentication not implemented");
    } catch (jwtError) {
      console.error('JWT verification error:', jwtError);
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      );
    }
  } catch (error) {
    console.error('Error fetching classes:', error);
    return NextResponse.json(
      { error: 'Failed to fetch classes' },
      { status: 500 }
    );
  }
}

import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// Helper function to calculate grade based on marks
function calculateGrade(marks: number): string {
  if (marks >= 90) return 'A+';
  if (marks >= 80) return 'A';
  if (marks >= 70) return 'B';
  if (marks >= 60) return 'C';
  if (marks >= 50) return 'D';
  return 'F';
}

export async function POST(request: Request) {
  try {
    const data = await request.json()

    if (!Array.isArray(data) || data.length === 0) {
      return NextResponse.json(
        { error: 'Invalid input: expected an array of marks' },
        { status: 400 }
      )
    }

    // Validate each mark entry
    for (const mark of data) {
      if (!mark.studentId || !mark.subject || (!mark.className && !mark.class) || !mark.term || (mark.marks === undefined && mark.obtainedMarks === undefined)) {
        return NextResponse.json(
          { error: 'Missing required fields in one or more mark entries', details: JSON.stringify(mark) },
          { status: 400 }
        )
      }
    }

    // Create all marks in a transaction
    const createdMarks = await prisma.$transaction(async (tx) => {
      const results = []

      for (const mark of data) {
        // Check if student exists
        const student = await tx.student.findUnique({
          where: { id: mark.studentId },
          select: { name: true }
        })

        // Get student SID separately
        const studentWithSid = await tx.student.findUnique({
          where: { id: mark.studentId }
        })

        if (!student) {
          throw new Error(`Student with ID ${mark.studentId} not found`)
        }

        // Map fields to expected names
        const className = mark.className || mark.class;
        const marks = mark.marks !== undefined ? mark.marks : mark.obtainedMarks;

        // Create the mark
        const createdMark = await tx.mark.create({
          data: {
            studentId: mark.studentId,
            subject: mark.subject,
            marks: marks, // Use the mapped marks field
            className: className, // Use the mapped className field
            term: mark.term
          }
        })

        // Add formatted mark to results that match UI expectations
        results.push({
          id: createdMark.id,
          studentId: createdMark.studentId,
          studentName: student.name,
          studentSid: (studentWithSid as any)?.sid || '',
          subject: createdMark.subject,
          obtainedMarks: createdMark.marks, // Map marks to obtainedMarks for UI
          totalMarks: 100, // Default total marks
          class: createdMark.className, // Map className to class for UI
          term: createdMark.term,
          academicYear: new Date(createdMark.createdAt).getFullYear() + '-' + (new Date(createdMark.createdAt).getFullYear() + 1), // Generate academic year
          grade: calculateGrade(createdMark.marks), // Calculate grade
          dateRecorded: createdMark.createdAt,
          recordedBy: 'Teacher', // Default recorded by
          createdAt: createdMark.createdAt,
          updatedAt: createdMark.updatedAt
        })
      }

      return results
    })

    return NextResponse.json(createdMarks)
  } catch (error) {
    console.error('Error creating bulk marks:', error)
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json(
      { error: 'Failed to create bulk marks', details: errorMessage },
      { status: 500 }
    )
  }
}

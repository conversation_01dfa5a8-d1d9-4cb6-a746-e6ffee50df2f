'use client'

import React, { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/app/components/ui/tabs'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/app/components/ui/card'
import { UserRolesTab } from './UserRolesTab'
import { RoleDefinitionsTab } from './RoleDefinitionsTab'
import { TeacherAssignmentsTab } from './TeacherAssignmentsTab'

export function RoleManagement() {
  const [activeTab, setActiveTab] = useState('users')

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-800">Role Management</h1>
        <p className="text-sm text-gray-500">Manage user roles, permissions, and teacher assignments</p>
      </div>

      <Tabs
        defaultValue="users"
        value={activeTab}
        onValueChange={setActiveTab}
        className="w-full"
      >
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="users">User Roles</TabsTrigger>
          <TabsTrigger value="roles">Role Definitions</TabsTrigger>
          <TabsTrigger value="teachers">Teacher Assignments</TabsTrigger>
        </TabsList>
        
        <TabsContent value="users" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>User Role Management</CardTitle>
              <CardDescription>
                Assign roles to users and manage their permissions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <UserRolesTab />
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="roles" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Role Definitions</CardTitle>
              <CardDescription>
                Define roles and their associated permissions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <RoleDefinitionsTab />
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="teachers" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Teacher Assignments</CardTitle>
              <CardDescription>
                Assign teachers to classes and subjects
              </CardDescription>
            </CardHeader>
            <CardContent>
              <TeacherAssignmentsTab />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

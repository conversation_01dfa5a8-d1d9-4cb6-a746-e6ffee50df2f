"use client"

import React, { useState } from 'react'

const generateWaveData = (length: number, amplitude: number, frequency: number, phase: number) => {
  return Array.from({ length }).map((_, i) => {
    return {
      x: i,
      y: 160 + amplitude * Math.sin((i * frequency + phase) * Math.PI / 180)
    }
  })
}

export const PerformanceChart: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('this')
  
  // Generate data for chart
  const thisWeekData = generateWaveData(100, 270, 2, 0)
  const lastWeekData = generateWaveData(100, 270, 2, 90)
  
  const data = selectedPeriod === 'this' ? thisWeekData : lastWeekData
  
  // Find min and max Y values
  const minY = Math.min(...data.map(point => point.y)) - 10
  const maxY = Math.max(...data.map(point => point.y)) + 10
  
  // Create SVG path for the chart
  let pathD = ''
  data.forEach((point, i) => {
    if (i === 0) {
      pathD += `M${point.x * 6},${550 - point.y}`
    } else {
      pathD += ` L${point.x * 6},${550 - point.y}`
    }
  })
  
  return (
    <div className="chart-container">
      <div className="flex items-center justify-between mb-6">
        <h2 className="chart-title">School Performance</h2>
        <div className="flex items-center space-x-3">
          <button
            className={`flex items-center rounded-full px-3 py-1 text-sm ${
              selectedPeriod === 'this' 
                ? 'bg-red-500 text-white' 
                : 'bg-white text-gray-800 border border-gray-200'
            }`}
            onClick={() => setSelectedPeriod('this')}
          >
            <span className="h-2 w-2 rounded-full bg-white mr-2"></span>
            This Week
          </button>
          <button
            className={`flex items-center rounded-full px-3 py-1 text-sm ${
              selectedPeriod === 'last' 
                ? 'bg-orange-500 text-white' 
                : 'bg-white text-gray-800 border border-gray-200'
            }`}
            onClick={() => setSelectedPeriod('last')}
          >
            <span className="h-2 w-2 rounded-full bg-white mr-2"></span>
            Last Week
          </button>
          
          <div className="ml-4 flex flex-col">
            <span className="font-bold">{selectedPeriod === 'this' ? '1.245' : '1.356'}</span>
          </div>
        </div>
      </div>
      
      <div className="relative h-64">
        {/* Y-axis labels */}
        <div className="absolute left-0 top-0 h-full flex flex-col justify-between">
          <span className="text-xs text-gray-500">500k</span>
          <span className="text-xs text-gray-500">480k</span>
          <span className="text-xs text-gray-500">400k</span>
          <span className="text-xs text-gray-500">320k</span>
          <span className="text-xs text-gray-500">240k</span>
          <span className="text-xs text-gray-500">160k</span>
        </div>
        
        {/* Chart SVG */}
        <div className="ml-12 h-full">
          <svg width="100%" height="100%" viewBox="0 0 600 300" preserveAspectRatio="none">
            {/* Gradient for this week */}
            <defs>
              <linearGradient id="gradientThis" x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" stopColor="rgba(248, 38, 73, 0.2)" />
                <stop offset="100%" stopColor="rgba(248, 38, 73, 0)" />
              </linearGradient>
              <linearGradient id="gradientLast" x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" stopColor="rgba(255, 112, 67, 0.2)" />
                <stop offset="100%" stopColor="rgba(255, 112, 67, 0)" />
              </linearGradient>
            </defs>
            
            {/* This week line */}
            <path
              d={pathD}
              fill="none"
              stroke={selectedPeriod === 'this' ? "#f82649" : "#ff7043"}
              strokeWidth="2"
            />
            
            {/* Area under the line */}
            <path
              d={`${pathD} L${data[data.length - 1].x * 6},300 L0,300 Z`}
              fill={selectedPeriod === 'this' ? "url(#gradientThis)" : "url(#gradientLast)"}
            />
          </svg>
        </div>
        
        {/* X-axis labels */}
        <div className="ml-12 mt-2 flex justify-between">
          <span className="text-xs text-gray-500">Week 01</span>
          <span className="text-xs text-gray-500">Week 02</span>
          <span className="text-xs text-gray-500">Week 03</span>
          <span className="text-xs text-gray-500">Week 04</span>
          <span className="text-xs text-gray-500">Week 05</span>
          <span className="text-xs text-gray-500">Week 06</span>
        </div>
      </div>
    </div>
  )
} 
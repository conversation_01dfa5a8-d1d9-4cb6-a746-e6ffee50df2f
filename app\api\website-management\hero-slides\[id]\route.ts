import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET a specific hero slide
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id
    
    const heroSlide = await prisma.heroSlide.findUnique({
      where: { id }
    })
    
    if (!heroSlide) {
      return NextResponse.json({
        error: 'Hero slide not found'
      }, { status: 404 })
    }
    
    return NextResponse.json(heroSlide)
  } catch (error) {
    console.error('Error fetching hero slide:', error)
    return NextResponse.json({
      error: error.message || 'Failed to fetch hero slide'
    }, { status: 500 })
  }
}

// PUT (update) a hero slide
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id
    const data = await request.json()
    
    // Check if the slide exists
    const existingSlide = await prisma.heroSlide.findUnique({
      where: { id }
    })
    
    if (!existingSlide) {
      return NextResponse.json({
        error: 'Hero slide not found'
      }, { status: 404 })
    }
    
    // Update the slide
    const updatedSlide = await prisma.heroSlide.update({
      where: { id },
      data: {
        title: data.title !== undefined ? data.title : existingSlide.title,
        subtitle: data.subtitle !== undefined ? data.subtitle : existingSlide.subtitle,
        imageUrl: data.imageUrl !== undefined ? data.imageUrl : existingSlide.imageUrl,
        ctaText: data.ctaText !== undefined ? data.ctaText : existingSlide.ctaText,
        ctaLink: data.ctaLink !== undefined ? data.ctaLink : existingSlide.ctaLink,
        isActive: data.isActive !== undefined ? data.isActive : existingSlide.isActive,
        order: data.order !== undefined ? data.order : existingSlide.order
      }
    })
    
    return NextResponse.json(updatedSlide)
  } catch (error) {
    console.error('Error updating hero slide:', error)
    return NextResponse.json({
      error: error.message || 'Failed to update hero slide'
    }, { status: 500 })
  }
}

// DELETE a hero slide
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id
    
    // Check if the slide exists
    const existingSlide = await prisma.heroSlide.findUnique({
      where: { id }
    })
    
    if (!existingSlide) {
      return NextResponse.json({
        error: 'Hero slide not found'
      }, { status: 404 })
    }
    
    // Delete the slide
    await prisma.heroSlide.delete({
      where: { id }
    })
    
    // Reorder remaining slides to ensure no gaps
    const remainingSlides = await prisma.heroSlide.findMany({
      orderBy: {
        order: 'asc'
      }
    })
    
    // Update order for all remaining slides
    for (let i = 0; i < remainingSlides.length; i++) {
      await prisma.heroSlide.update({
        where: { id: remainingSlides[i].id },
        data: { order: i }
      })
    }
    
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting hero slide:', error)
    return NextResponse.json({
      error: error.message || 'Failed to delete hero slide'
    }, { status: 500 })
  }
}

import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// DELETE a teacher assignment for a user with TEACHER role
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id
    console.log(`Deleting teacher assignment with ID: ${id}`)

    // Check if the assignment exists
    const assignment = await prisma.teacherAssignment.findUnique({
      where: { id },
      include: {
        teacher: true
      }
    })

    if (!assignment) {
      return NextResponse.json(
        { error: 'Teacher assignment not found' },
        { status: 404 }
      )
    }

    // Delete the assignment
    await prisma.teacherAssignment.delete({
      where: { id }
    })

    console.log(`Deleted teacher assignment for ${assignment.teacher?.name || 'Unknown Teacher'}`)
    return NextResponse.json({
      message: 'Teacher assignment deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting teacher assignment:', error)
    return NextResponse.json(
      { error: 'Failed to delete teacher assignment' },
      { status: 500 }
    )
  }
}

'use client'

import React, { useState, useEffect, useRef } from 'react'
import dynamic from 'next/dynamic'
import { useQuery } from '@tanstack/react-query'
import { useAuth } from '../contexts/auth-context'
import { useRouter } from 'next/navigation'
import DashboardLayout from '../components/DashboardLayout'

// Import our new dashboard components
import { OverviewCards } from '../components/dashboard/OverviewCards'
import { AcademicPerformance } from '../components/dashboard/AcademicPerformance'
import { AcademicCalendar } from '../components/dashboard/AcademicCalendar'

// Import dashboard styles
import './dashboard.css'

// Import Header component with no SSR to avoid hydration issues
const Header = dynamic(() => import('../components/Header'), {
  ssr: false
})

// Type definition for dashboard stats
interface DashboardStats {
  totalStudents: number;
  attendance: {
    present: number;
    absent: number;
    permission: number;
    total: number;
    hasRealData: boolean;
  };
  performance: {
    recentMarks: any[];
    topPerformers: any[];
    failingStudents: any[];
    averageScore: number;
  };
  events: any[];
}

export default function Dashboard() {
  const [isMounted, setIsMounted] = useState(false)
  const { user, isAuthenticated, isLoading: authLoading } = useAuth()
  const router = useRouter()

  // Fetch dashboard stats from the API
  const { data: dashboardStats, isLoading } = useQuery<DashboardStats>({
    queryKey: ['dashboardStats'],
    queryFn: async () => {
      const res = await fetch('/api/dashboard/stats')
      if (!res.ok) {
        throw new Error('Failed to fetch dashboard stats')
      }
      return res.json()
    },
    staleTime: 60000, // 1 minute
    refetchOnWindowFocus: false,
  })

  // Fallback data if API fails
  const fallbackData = {
    totalStudents: 0,
    attendance: {
      present: 0,
      absent: 0,
      permission: 0,
      total: 0,
      hasRealData: false
    },
    performance: {
      recentMarks: [],
      topPerformers: [],
      failingStudents: [],
      averageScore: 0
    },
    events: []
  }

  // Reference to the main content area
  const mainRef = useRef<HTMLDivElement>(null)

  // Check authentication and redirect if not authenticated
  useEffect(() => {
    if (isMounted && !authLoading && !isAuthenticated) {
      router.push('/login')
    }
  }, [isMounted, authLoading, isAuthenticated, router])

  useEffect(() => {
    setIsMounted(true)

    // Add mouse tracking for card glow effects
    const handleMouseMove = (e: MouseEvent) => {
      if (mainRef.current) {
        const cards = mainRef.current.querySelectorAll('.hover-glow')
        cards.forEach((card) => {
          const rect = card.getBoundingClientRect()
          const x = e.clientX - rect.left
          const y = e.clientY - rect.top

          ;(card as HTMLElement).style.setProperty('--mouse-x', `${x}px`)
          ;(card as HTMLElement).style.setProperty('--mouse-y', `${y}px`)
        })
      }
    }

    document.addEventListener('mousemove', handleMouseMove)

    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
    }
  }, [])

  // Dashboard component now uses DashboardLayout which handles sidebar state

  // Show loading while mounting or auth loading
  if (!isMounted || isLoading || authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-600"></div>
      </div>
    )
  }

  // Redirect if not authenticated
  if (!isAuthenticated) {
    return null // Will be redirected by the useEffect
  }

  // Use data from API or fallback
  const data = dashboardStats || fallbackData

  return (
    <DashboardLayout>
      {/* Enhanced Hero Header */}
      <div className="relative overflow-hidden rounded-3xl bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 p-8 text-white shadow-2xl mb-8">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative flex items-center justify-between">
          <div>
            <h1 className="text-4xl font-bold tracking-tight mb-2 bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent">
              Welcome back, {user?.name?.split(' ')[0] || 'User'}!
            </h1>
            <p className="text-blue-100 text-lg">
              {new Date().toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </p>
          </div>
          <div className="hidden md:flex items-center gap-4">
            <div className="h-16 w-16 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center">
              <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
          </div>
        </div>
        {/* Decorative elements */}
        <div className="absolute -top-8 -right-8 w-32 h-32 bg-white/10 rounded-full blur-xl"></div>
        <div className="absolute -bottom-12 -left-12 w-40 h-40 bg-white/5 rounded-full blur-2xl"></div>
      </div>

      {/* Overview Cards */}
      <OverviewCards
        totalStudents={data.totalStudents}
        presentStudents={data.attendance.present}
        absentStudents={data.attendance.absent}
        permissionStudents={data.attendance.permission}
        hasRealData={data.attendance.hasRealData}
        averageScore={data.performance.averageScore}
      />

      {/* Academic Performance */}
      <AcademicPerformance
        recentMarks={data.performance.recentMarks}
        topPerformers={data.performance.topPerformers}
        failingStudents={data.performance.failingStudents}
      />

      {/* Academic Calendar */}
      <div className="mt-6">
        <AcademicCalendar
          events={data.events}
        />
      </div>
    </DashboardLayout>
  )
}


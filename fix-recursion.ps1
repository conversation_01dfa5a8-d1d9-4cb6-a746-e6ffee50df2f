# PowerShell script to fix the recursion issue in Radix UI components

Write-Host "Starting the fix for Radix UI recursion issue..." -ForegroundColor Green

# Check if node_modules exists and remove it
if (Test-Path -Path "node_modules") {
    Write-Host "Removing node_modules folder..." -ForegroundColor Yellow
    Remove-Item -Recurse -Force "node_modules"
}

# Check if package-lock.json exists and remove it
if (Test-Path -Path "package-lock.json") {
    Write-Host "Removing package-lock.json..." -ForegroundColor Yellow
    Remove-Item -Force "package-lock.json"
}

# Run npm install to reinstall packages with the correct resolutions
Write-Host "Reinstalling packages with the correct resolutions..." -ForegroundColor Yellow
npm install

Write-Host "Fix completed! The recursion issue should now be resolved." -ForegroundColor Green
Write-Host "Please restart your development server with 'npm run dev'." -ForegroundColor Green

'use client'

import { useState, useEffect, useCallback, useMemo } from 'react'
import { useQuery } from '@tanstack/react-query'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/app/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/app/components/ui/table'
import { Button } from '@/app/components/ui/button'
import { Input } from '@/app/components/ui/input'
import { Label } from '@/app/components/ui/label'
import { Loader2, Users, BookOpen, ArrowUp, ArrowDown, GraduationCap } from 'lucide-react'
import { toast } from '@/app/components/ui/use-toast'

// Types based on Prisma schema
interface Class {
  id: string
  name: string
  totalStudents: number
  totalSubjects: number
  hasSubjects: boolean
}

interface Subject {
  id: string
  name: string
  classId: string
}

interface Student {
  id: string
  name: string
  sid: string
  className: string
  academicYear: string
}

interface StudentMark {
  studentId: string
  studentName: string
  studentSid: string
  obtainedMarks: number
  hasExistingMark?: boolean
  markId?: string | null
}

interface BulkEntryFormProps {
  onSubmit: (marks: any[]) => Promise<void>
  onCancel: () => void
}

// Academic years and terms based on Prisma schema
const academicYears = [
  '2020-2021', '2021-2022', '2022-2023', '2023-2024',
  '2024-2025', '2025-2026', '2026-2027', '2027-2028'
]

const terms = [
  'First Semester',
  'Second Semester'
]

export function BulkEntryForm({ onSubmit, onCancel }: BulkEntryFormProps) {
  // Form settings state
  const [formSettings, setFormSettings] = useState({
    className: '',
    subject: '',
    term: 'First Semester',
    academicYear: '2023-2024',
    totalMarks: 100
  })

  // Student marks state
  const [studentMarks, setStudentMarks] = useState<StudentMark[]>([])
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc')
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Fetch classes using existing API endpoint
  const {
    data: classes = [],
    isLoading: isLoadingClasses,
    error: classesError
  } = useQuery<Class[]>({
    queryKey: ['classes-for-marks'],
    queryFn: async () => {
      const res = await fetch('/api/classes/for-marks')
      if (!res.ok) throw new Error('Failed to fetch classes')
      return res.json()
    }
  })

  // Fetch subjects for selected class
  const {
    data: subjects = [],
    isLoading: isLoadingSubjects,
    error: subjectsError
  } = useQuery<Subject[]>({
    queryKey: ['subjects-for-class', formSettings.className],
    queryFn: async () => {
      if (!formSettings.className) return []
      const res = await fetch(`/api/subjects/for-class?className=${formSettings.className}`)
      if (!res.ok) {
        if (res.status === 404) {
          throw new Error(`No subjects found for class '${formSettings.className}'. Please add subjects in Class Management first.`)
        }
        throw new Error('Failed to fetch subjects')
      }
      return res.json()
    },
    enabled: !!formSettings.className
  })

  // Fetch students for selected class
  const {
    data: students = [],
    isLoading: isLoadingStudents
  } = useQuery<Student[]>({
    queryKey: ['students-by-class', formSettings.className],
    queryFn: async () => {
      if (!formSettings.className) return []
      const res = await fetch(`/api/students?class=${formSettings.className}`)
      if (!res.ok) throw new Error('Failed to fetch students')
      return res.json()
    },
    enabled: !!formSettings.className
  })

  // Fetch existing marks for the selected criteria
  const {
    data: existingMarks = [],
    isLoading: isLoadingExistingMarks
  } = useQuery({
    queryKey: ['existing-marks', formSettings.className, formSettings.subject, formSettings.term, formSettings.academicYear],
    queryFn: async () => {
      if (!formSettings.className || !formSettings.subject || !formSettings.term) return []
      
      const params = new URLSearchParams({
        className: formSettings.className,
        subject: formSettings.subject,
        term: formSettings.term,
        academicYear: formSettings.academicYear
      })
      
      const res = await fetch(`/api/marks/bulk?${params}`)
      if (!res.ok) {
        if (res.status === 404) return [] // No existing marks found
        throw new Error('Failed to fetch existing marks')
      }
      return res.json()
    },
    enabled: !!(formSettings.className && formSettings.subject && formSettings.term)
  })

  // Handle form setting changes
  const handleSettingChange = useCallback((name: string, value: string | number) => {
    setFormSettings(prev => ({
      ...prev,
      [name]: value
    }))

    // Clear student marks when class or subject changes
    if (name === 'className' || name === 'subject') {
      setStudentMarks([])
    }
  }, [])

  // Load students and merge with existing marks
  useEffect(() => {
    if (formSettings.className && students && students.length > 0) {
      console.log('Loading students and existing marks...', {
        studentsCount: students.length,
        existingMarksCount: existingMarks?.length || 0
      })

      const studentMarksData = students.map((student: Student) => {
        // Find existing mark for this student
        const existingMark = existingMarks?.find((mark: any) => mark.studentId === student.id)
        
        return {
          studentId: student.id,
          studentName: student.name,
          studentSid: student.sid,
          obtainedMarks: existingMark ? existingMark.marks : 0,
          hasExistingMark: !!existingMark,
          markId: existingMark?.id || null
        }
      })

      // Sort students by SID
      const sortedStudentMarks = [...studentMarksData].sort((a, b) => {
        return sortDirection === 'asc' 
          ? a.studentSid.localeCompare(b.studentSid, undefined, { numeric: true })
          : b.studentSid.localeCompare(a.studentSid, undefined, { numeric: true })
      })

      console.log('Student marks loaded:', {
        total: sortedStudentMarks.length,
        withExistingMarks: sortedStudentMarks.filter(s => s.hasExistingMark).length
      })

      setStudentMarks(sortedStudentMarks)
    } else if (!formSettings.className) {
      setStudentMarks([])
    }
  }, [formSettings.className, students, existingMarks, sortDirection])

  // Handle mark change for individual student
  const handleMarkChange = useCallback((studentId: string, marks: number) => {
    setStudentMarks(prev => 
      prev.map(student => 
        student.studentId === studentId 
          ? { ...student, obtainedMarks: marks }
          : student
      )
    )
  }, [])

  // Toggle sort direction
  const toggleSortDirection = useCallback(() => {
    setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc')
  }, [])

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validate form settings
    if (!formSettings.className || !formSettings.subject || !formSettings.term) {
      toast({
        title: "Validation Error",
        description: "Please select class, subject, and term",
        variant: "destructive"
      })
      return
    }

    console.log('Submitting bulk marks...', {
      totalStudents: studentMarks.length,
      studentsWithMarks: studentMarks.filter(s => s.obtainedMarks > 0).length
    })

    // Create mark objects for each student with marks > 0
    const marks = studentMarks
      .filter(student => student.obtainedMarks > 0) // Only include students with marks
      .map(student => ({
        studentId: student.studentId,
        studentName: student.studentName,
        className: formSettings.className,
        subject: formSettings.subject,
        term: formSettings.term,
        academicYear: formSettings.academicYear,
        totalMarks: formSettings.totalMarks,
        marks: student.obtainedMarks, // Using 'marks' to match Prisma schema
        obtainedMarks: student.obtainedMarks, // Keep for UI compatibility
        remarks: '',
        markId: student.markId // Include existing mark ID for updates
      }))

    if (marks.length === 0) {
      toast({
        title: "No Marks to Submit",
        description: "Please enter marks for at least one student",
        variant: "destructive"
      })
      return
    }

    setIsSubmitting(true)
    try {
      console.log('Marks to submit:', marks.length)
      await onSubmit(marks)
      
      toast({
        title: "Success",
        description: `Successfully processed ${marks.length} marks`
      })
    } catch (error) {
      console.error('Error submitting bulk marks:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to submit marks",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Memoize statistics
  const statistics = useMemo(() => {
    const totalStudents = studentMarks.length
    const studentsWithMarks = studentMarks.filter(s => s.obtainedMarks > 0).length
    const existingMarksCount = studentMarks.filter(s => s.hasExistingMark).length
    
    return {
      totalStudents,
      studentsWithMarks,
      existingMarksCount,
      newMarksCount: studentsWithMarks - existingMarksCount
    }
  }, [studentMarks])

  return (
    <form onSubmit={handleSubmit} className="space-y-6 h-full flex flex-col">
      {/* Form Settings */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pb-4 border-b">
        {/* Class */}
        <div className="grid gap-2" key="class-select">
          <Label htmlFor="className" className="text-sm font-medium">
            Class <span className="text-red-500">*</span>
          </Label>
          <Select
            key={`class-select-${formSettings.className || 'empty'}`}
            value={formSettings.className || ""}
            onValueChange={(value) => handleSettingChange('className', value)}
          >
            <SelectTrigger>
              {isLoadingClasses ? (
                <div className="flex items-center">
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Loading classes...
                </div>
              ) : (
                <>
                  <Users className="mr-2 h-4 w-4 text-gray-400" />
                  <SelectValue placeholder="Select Class" />
                </>
              )}
            </SelectTrigger>
            <SelectContent>
              {classesError ? (
                <div className="p-2 text-center text-sm text-red-500">
                  Error loading classes
                </div>
              ) : classes && classes.length > 0 ? (
                classes.map((cls) => (
                  <SelectItem key={cls.id} value={cls.name}>
                    <div className="flex items-center justify-between w-full">
                      <span>{cls.name}</span>
                      <span className="text-xs text-gray-500 ml-2">
                        {cls.totalSubjects} subjects
                      </span>
                    </div>
                  </SelectItem>
                ))
              ) : (
                <div className="p-2 text-center text-sm text-gray-500">
                  No classes found
                </div>
              )}
            </SelectContent>
          </Select>
        </div>

        {/* Subject */}
        <div className="grid gap-2" key="subject-select">
          <Label htmlFor="subject" className="text-sm font-medium">
            Subject <span className="text-red-500">*</span>
          </Label>
          <Select
            key={`subject-select-${formSettings.className || 'empty'}-${formSettings.subject || 'empty'}`}
            value={formSettings.subject || ""}
            onValueChange={(value) => handleSettingChange('subject', value)}
            disabled={!formSettings.className || isLoadingSubjects}
          >
            <SelectTrigger>
              {isLoadingSubjects ? (
                <div className="flex items-center">
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Loading subjects...
                </div>
              ) : (
                <>
                  <BookOpen className="mr-2 h-4 w-4" />
                  <SelectValue placeholder="Select Subject" />
                </>
              )}
            </SelectTrigger>
            <SelectContent>
              {subjectsError ? (
                <div className="p-2 text-center text-sm text-red-500">
                  <div className="font-medium">Error loading subjects</div>
                  <div className="text-xs mt-1">
                    {subjectsError.message.includes('No subjects found') 
                      ? 'No subjects assigned to this class. Please add subjects in Class Management.'
                      : 'Failed to load subjects. Please try again.'
                    }
                  </div>
                </div>
              ) : subjects && subjects.length > 0 ? (
                subjects.map((subject) => (
                  <SelectItem key={subject.id} value={subject.name}>
                    {subject.name}
                  </SelectItem>
                ))
              ) : (
                <div className="p-2 text-center text-sm text-gray-500">
                  {formSettings.className 
                    ? 'No subjects assigned to this class. Please add subjects in Class Management first.' 
                    : 'Select a class first'
                  }
                </div>
              )}
            </SelectContent>
          </Select>
        </div>

        {/* Semester */}
        <div className="grid gap-2">
          <Label htmlFor="term" className="text-sm font-medium">
            Semester <span className="text-red-500">*</span>
          </Label>
          <Select
            value={formSettings.term || ""}
            onValueChange={(value) => handleSettingChange('term', value)}
          >
            <SelectTrigger>
              <GraduationCap className="mr-2 h-4 w-4 text-gray-400" />
              <SelectValue placeholder="Select semester" />
            </SelectTrigger>
            <SelectContent>
              {terms.map(term => (
                <SelectItem key={term} value={term}>
                  {term}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Academic Year and Total Marks */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pb-4 border-b">
        <div className="grid gap-2">
          <Label htmlFor="academicYear" className="text-sm font-medium">
            Academic Year <span className="text-red-500">*</span>
          </Label>
          <Select
            value={formSettings.academicYear || ""}
            onValueChange={(value) => handleSettingChange('academicYear', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select academic year" />
            </SelectTrigger>
            <SelectContent>
              {academicYears.map(year => (
                <SelectItem key={year} value={year}>
                  {year}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="grid gap-2">
          <Label htmlFor="totalMarks" className="text-sm font-medium">
            Total Marks <span className="text-red-500">*</span>
          </Label>
          <Input
            id="totalMarks"
            type="number"
            min="1"
            max="1000"
            value={formSettings.totalMarks}
            onChange={(e) => handleSettingChange('totalMarks', parseInt(e.target.value) || 100)}
            required
          />
        </div>
      </div>

      {/* Statistics */}
      {studentMarks.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{statistics.totalStudents}</div>
            <div className="text-sm text-gray-600">Total Students</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{statistics.studentsWithMarks}</div>
            <div className="text-sm text-gray-600">With Marks</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">{statistics.existingMarksCount}</div>
            <div className="text-sm text-gray-600">Existing</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">{statistics.newMarksCount}</div>
            <div className="text-sm text-gray-600">New</div>
          </div>
        </div>
      )}

      {/* Student List */}
      <div className="flex-1 min-h-0">
        {isLoadingStudents || isLoadingExistingMarks ? (
          <div className="mt-4 p-8 text-center border rounded-md">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
            <p className="text-gray-500">
              {isLoadingStudents ? 'Loading students...' : 'Loading existing marks...'}
            </p>
          </div>
        ) : formSettings.className && studentMarks.length > 0 ? (
          <div className="mt-4 border rounded-md overflow-hidden">
            <div className="max-h-[40vh] overflow-y-auto">
              <Table>
                <TableHeader className="sticky top-0 bg-white z-10">
                  <TableRow>
                    <TableHead className="w-12">#</TableHead>
                    <TableHead className="w-24">
                      <div className="flex items-center space-x-1">
                        <span>SID</span>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-5 w-5 ml-1 -mr-2"
                          onClick={(e) => {
                            e.preventDefault()
                            toggleSortDirection()
                          }}
                          title={`Sort by SID ${sortDirection === 'asc' ? 'Z-A' : 'A-Z'}`}
                        >
                          {sortDirection === 'asc' ?
                            <ArrowUp className="h-3 w-3" /> :
                            <ArrowDown className="h-3 w-3" />
                          }
                        </Button>
                      </div>
                    </TableHead>
                    <TableHead>Student</TableHead>
                    <TableHead>Marks (out of {formSettings.totalMarks})</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {studentMarks.map((student, index) => {
                    const hasExistingMark = student.hasExistingMark
                    
                    return (
                      <TableRow 
                        key={student.studentId}
                        className={hasExistingMark ? 'bg-blue-50 border-l-4 border-l-blue-400' : ''}
                      >
                        <TableCell className="text-center">{index + 1}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <span className="font-mono text-xs font-medium bg-gray-100 px-2 py-1 rounded">
                              {student.studentSid}
                            </span>
                            {hasExistingMark && (
                              <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full font-medium">
                                Updated
                              </span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <span>{student.studentName}</span>
                            {hasExistingMark && (
                              <span className="text-blue-600" title="This student already has marks for this subject">
                                📝
                              </span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Input
                            type="number"
                            min="0"
                            max={formSettings.totalMarks}
                            value={student.obtainedMarks || ''}
                            onChange={(e) => handleMarkChange(student.studentId, parseInt(e.target.value) || 0)}
                            className={`w-24 text-right ${hasExistingMark ? 'border-blue-300 bg-blue-50' : ''}`}
                            placeholder={hasExistingMark ? 'Update' : 'Enter'}
                          />
                        </TableCell>
                      </TableRow>
                    )
                  })}
                </TableBody>
              </Table>
            </div>
          </div>
        ) : formSettings.className ? (
          <div className="py-8 text-center text-gray-500">
            No students found in this class
          </div>
        ) : (
          <div className="py-8 text-center text-gray-500">
            Please select a class to load students
          </div>
        )}
      </div>

      {/* Form Actions */}
      <div className="sticky bottom-0 bg-white pt-4 border-t mt-4">
        <div className="flex justify-end space-x-2">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={!formSettings.className || !formSettings.subject || studentMarks.every(s => s.obtainedMarks === 0) || isSubmitting}
          >
            {isSubmitting ? (
              <div className="flex items-center">
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </div>
            ) : (
              'Save Marks'
            )}
          </Button>
        </div>
      </div>
    </form>
  )
}

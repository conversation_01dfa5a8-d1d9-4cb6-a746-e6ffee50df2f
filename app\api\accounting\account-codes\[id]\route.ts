import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET a specific account code
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id

    const accountCode = await prisma.accountCode.findUnique({
      where: { id },
    })

    if (!accountCode) {
      return NextResponse.json(
        { error: 'Account code not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(accountCode)
  } catch (error) {
    console.error('Error fetching account code:', error)
    return NextResponse.json(
      { error: 'Failed to fetch account code' },
      { status: 500 }
    )
  }
}

// UPDATE an account code
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id
    const data = await request.json()

    console.log('Updating account code with ID:', id, 'Data:', data)

    // Validate required fields
    if (!data.code || !data.description) {
      return NextResponse.json(
        { error: 'Code and description are required' },
        { status: 400 }
      )
    }

    // Check if account code exists
    console.log(`Looking for account code with ID: ${id}`)
    let existingAccountCode;
    try {
      existingAccountCode = await prisma.accountCode.findUnique({
        where: { id },
      })

      if (!existingAccountCode) {
        console.error(`Account code with ID ${id} not found`)
        return NextResponse.json(
          { error: 'Account code not found', id: id },
          { status: 404 }
        )
      }
    } catch (findError) {
      console.error(`Error finding account code with ID ${id}:`, findError)
      return NextResponse.json(
        {
          error: 'Error finding account code',
          details: findError instanceof Error ? findError.message : 'Unknown error',
          id: id
        },
        { status: 500 }
      )
    }

    // Check if another account code with the same code exists (excluding this one)
    const duplicateCode = await prisma.accountCode.findFirst({
      where: {
        code: data.code,
        id: { not: id },
      },
    })

    if (duplicateCode) {
      return NextResponse.json(
        { error: 'Another account code with this code already exists' },
        { status: 400 }
      )
    }

    // Update the account code
    const updatedAccountCode = await prisma.accountCode.update({
      where: { id },
      data: {
        code: data.code,
        description: data.description,
        isActive: data.isActive !== undefined ? data.isActive : existingAccountCode.isActive,
      },
    })

    console.log('Account code updated successfully:', updatedAccountCode)
    return NextResponse.json(updatedAccountCode)
  } catch (error) {
    console.error('Error updating account code:', error)
    return NextResponse.json(
      {
        error: 'Failed to update account code',
        details: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      },
      { status: 500 }
    )
  }
}

// DELETE an account code
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id

    console.log('Deleting account code with ID:', id)

    // Check if account code exists
    const existingAccountCode = await prisma.accountCode.findUnique({
      where: { id },
    })

    if (!existingAccountCode) {
      console.error(`Account code with ID ${id} not found`)
      return NextResponse.json(
        { error: 'Account code not found' },
        { status: 404 }
      )
    }

    // Check if the account code is being used in journal vouchers
    const journalVoucherCount = await prisma.journalVoucher.count({
      where: {
        accountCode: existingAccountCode.code,
      },
    })

    if (journalVoucherCount > 0) {
      return NextResponse.json(
        { error: 'Cannot delete account code that is being used in journal vouchers' },
        { status: 400 }
      )
    }

    // Delete the account code
    await prisma.accountCode.delete({
      where: { id },
    })

    console.log('Account code deleted successfully')
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting account code:', error)
    return NextResponse.json(
      {
        error: 'Failed to delete account code',
        details: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      },
      { status: 500 }
    )
  }
}

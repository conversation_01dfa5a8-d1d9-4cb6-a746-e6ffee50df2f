-- CreateTable
CREATE TABLE "attendance" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "date" TEXT NOT NULL,
    "className" TEXT NOT NULL,
    "studentId" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "attendance_studentId_fkey" FOREIGN KEY ("studentId") REFERENCES "student" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "class" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "name" TEXT NOT NULL,
    "hrTeacherId" TEXT,
    "totalStudents" INTEGER NOT NULL DEFAULT 0,
    "totalSubjects" INTEGER NOT NULL DEFAULT 0,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "class_hrTeacherId_fkey" FOREIGN KEY ("hrTeacherId") REFERENCES "teacher" ("id") ON DELETE SET NULL ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "classteacher" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "classId" TEXT NOT NULL,
    "teacherId" TEXT NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "classteacher_classId_fkey" FOREIGN KEY ("classId") REFERENCES "class" ("id") ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT "classteacher_teacherId_fkey" FOREIGN KEY ("teacherId") REFERENCES "teacher" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "mark" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "studentId" TEXT NOT NULL,
    "subject" TEXT NOT NULL,
    "marks" INTEGER NOT NULL,
    "totalMarks" INTEGER NOT NULL DEFAULT 100,
    "className" TEXT NOT NULL,
    "term" TEXT NOT NULL,
    "academicYear" TEXT NOT NULL DEFAULT '2023-2024',
    "remarks" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    "createdBy" TEXT,
    CONSTRAINT "mark_createdBy_fkey" FOREIGN KEY ("createdBy") REFERENCES "user" ("id") ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT "mark_studentId_fkey" FOREIGN KEY ("studentId") REFERENCES "student" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "student" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "name" TEXT NOT NULL,
    "className" TEXT NOT NULL,
    "fatherName" TEXT NOT NULL,
    "gfName" TEXT NOT NULL,
    "age" INTEGER NOT NULL,
    "gender" TEXT NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    "sid" TEXT NOT NULL,
    "photoUrl" TEXT,
    "academicYear" TEXT NOT NULL DEFAULT '2023-2024',
    CONSTRAINT "student_className_fkey" FOREIGN KEY ("className") REFERENCES "class" ("name") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "subject" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "name" TEXT NOT NULL,
    "classId" TEXT NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "subject_classId_fkey" FOREIGN KEY ("classId") REFERENCES "class" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "subjectteacher" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "subjectId" TEXT NOT NULL,
    "teacherId" TEXT NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "subjectteacher_subjectId_fkey" FOREIGN KEY ("subjectId") REFERENCES "subject" ("id") ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT "subjectteacher_teacherId_fkey" FOREIGN KEY ("teacherId") REFERENCES "teacher" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "teacher" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "name" TEXT NOT NULL,
    "fatherName" TEXT NOT NULL,
    "gender" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "subject" TEXT NOT NULL,
    "mobile" TEXT NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "hrteacher" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "teacherId" TEXT NOT NULL,
    "classId" TEXT NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "hrteacher_teacherId_fkey" FOREIGN KEY ("teacherId") REFERENCES "teacher" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "hrteacher_classId_fkey" FOREIGN KEY ("classId") REFERENCES "class" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "user" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "email" TEXT NOT NULL,
    "password" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "role" TEXT NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    "lastLogin" DATETIME,
    "status" TEXT NOT NULL DEFAULT 'active',
    "address" TEXT,
    "appNotifications" BOOLEAN DEFAULT true,
    "bio" TEXT,
    "emailNotifications" BOOLEAN DEFAULT true,
    "language" TEXT DEFAULT 'english',
    "phone" TEXT,
    "photoUrl" TEXT,
    "theme" TEXT DEFAULT 'system'
);

-- CreateTable
CREATE TABLE "parent_student" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "parentId" TEXT NOT NULL,
    "studentId" TEXT NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "parent_student_parentId_fkey" FOREIGN KEY ("parentId") REFERENCES "user" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "parent_student_studentId_fkey" FOREIGN KEY ("studentId") REFERENCES "student" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "hero_slide" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "title" TEXT NOT NULL,
    "subtitle" TEXT NOT NULL,
    "imageUrl" TEXT NOT NULL,
    "ctaText" TEXT NOT NULL,
    "ctaLink" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "order" INTEGER NOT NULL DEFAULT 0,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "announcement" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "title" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "date" DATETIME NOT NULL,
    "type" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "quick_link" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "icon" TEXT NOT NULL,
    "url" TEXT NOT NULL,
    "color" TEXT NOT NULL,
    "borderColor" TEXT NOT NULL,
    "hoverColor" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "order" INTEGER NOT NULL DEFAULT 0,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "featured_content" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "imageUrl" TEXT NOT NULL,
    "features" JSONB NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "news_event" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "title" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "excerpt" TEXT NOT NULL,
    "imageUrl" TEXT NOT NULL,
    "date" DATETIME NOT NULL,
    "category" TEXT NOT NULL,
    "tags" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "testimonial" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "name" TEXT NOT NULL,
    "role" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "imageUrl" TEXT,
    "rating" INTEGER NOT NULL DEFAULT 5,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "order" INTEGER NOT NULL DEFAULT 0,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "call_to_action" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "primaryBtnText" TEXT NOT NULL,
    "primaryBtnLink" TEXT NOT NULL,
    "secondaryBtnText" TEXT,
    "secondaryBtnLink" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "role" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "systemDefined" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "permission" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "rolepermission" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "roleId" TEXT NOT NULL,
    "permissionId" TEXT NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "rolepermission_permissionId_fkey" FOREIGN KEY ("permissionId") REFERENCES "permission" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "rolepermission_roleId_fkey" FOREIGN KEY ("roleId") REFERENCES "role" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "teacherassignment" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "teacherId" TEXT NOT NULL,
    "classId" TEXT,
    "subjectId" TEXT,
    "isHRTeacher" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "teacherassignment_classId_fkey" FOREIGN KEY ("classId") REFERENCES "class" ("id") ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT "teacherassignment_subjectId_fkey" FOREIGN KEY ("subjectId") REFERENCES "subject" ("id") ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT "teacherassignment_teacherId_fkey" FOREIGN KEY ("teacherId") REFERENCES "user" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "fee_type" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "amount" REAL NOT NULL,
    "frequency" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "payment" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "invoiceNumber" TEXT NOT NULL,
    "studentId" TEXT NOT NULL,
    "feeTypeId" TEXT NOT NULL,
    "amount" REAL NOT NULL,
    "paymentDate" DATETIME NOT NULL,
    "forMonth" TEXT,
    "paymentMethod" TEXT NOT NULL,
    "transferId" TEXT,
    "status" TEXT NOT NULL,
    "notes" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "payment_feeTypeId_fkey" FOREIGN KEY ("feeTypeId") REFERENCES "fee_type" ("id") ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT "payment_studentId_fkey" FOREIGN KEY ("studentId") REFERENCES "student" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "bank_payment_voucher" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "voucherNo" TEXT NOT NULL,
    "date" DATETIME NOT NULL,
    "paidTo" TEXT NOT NULL,
    "accountCode" TEXT,
    "amount" REAL NOT NULL,
    "paymentMethod" TEXT NOT NULL,
    "chequeNo" TEXT,
    "accountNo" TEXT,
    "purpose" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "account_code" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "code" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "journal_voucher" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "voucherNo" TEXT NOT NULL,
    "date" DATETIME NOT NULL,
    "paidTo" TEXT NOT NULL,
    "accountCode" TEXT NOT NULL,
    "amount" REAL NOT NULL,
    "paymentMethod" TEXT NOT NULL,
    "chequeNo" TEXT,
    "purpose" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "payment_receipts" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "studentName" TEXT NOT NULL,
    "className" TEXT NOT NULL,
    "month" TEXT NOT NULL,
    "bankName" TEXT NOT NULL,
    "receiptImageUrl" TEXT NOT NULL,
    "submittedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "teacher_permissions" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "teacherId" TEXT NOT NULL,
    "classId" TEXT NOT NULL,
    "canViewAttendance" BOOLEAN NOT NULL DEFAULT false,
    "canTakeAttendance" BOOLEAN NOT NULL DEFAULT false,
    "canAddMarks" BOOLEAN NOT NULL DEFAULT false,
    "canEditMarks" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- CreateIndex
CREATE INDEX "attendance_className_date_idx" ON "attendance"("className", "date");

-- CreateIndex
CREATE INDEX "attendance_studentId_idx" ON "attendance"("studentId");

-- CreateIndex
CREATE UNIQUE INDEX "class_name_key" ON "class"("name");

-- CreateIndex
CREATE INDEX "class_hrTeacherId_idx" ON "class"("hrTeacherId");

-- CreateIndex
CREATE INDEX "classteacher_classId_idx" ON "classteacher"("classId");

-- CreateIndex
CREATE INDEX "classteacher_teacherId_idx" ON "classteacher"("teacherId");

-- CreateIndex
CREATE INDEX "mark_studentId_idx" ON "mark"("studentId");

-- CreateIndex
CREATE INDEX "mark_createdBy_idx" ON "mark"("createdBy");

-- CreateIndex
CREATE INDEX "mark_className_idx" ON "mark"("className");

-- CreateIndex
CREATE INDEX "mark_subject_idx" ON "mark"("subject");

-- CreateIndex
CREATE INDEX "mark_term_idx" ON "mark"("term");

-- CreateIndex
CREATE INDEX "mark_academicYear_idx" ON "mark"("academicYear");

-- CreateIndex
CREATE UNIQUE INDEX "mark_studentId_subject_className_term_academicYear_key" ON "mark"("studentId", "subject", "className", "term", "academicYear");

-- CreateIndex
CREATE UNIQUE INDEX "student_sid_key" ON "student"("sid");

-- CreateIndex
CREATE INDEX "student_className_idx" ON "student"("className");

-- CreateIndex
CREATE INDEX "subject_classId_idx" ON "subject"("classId");

-- CreateIndex
CREATE UNIQUE INDEX "subject_name_classId_key" ON "subject"("name", "classId");

-- CreateIndex
CREATE INDEX "subjectteacher_subjectId_idx" ON "subjectteacher"("subjectId");

-- CreateIndex
CREATE INDEX "subjectteacher_teacherId_idx" ON "subjectteacher"("teacherId");

-- CreateIndex
CREATE UNIQUE INDEX "teacher_email_key" ON "teacher"("email");

-- CreateIndex
CREATE INDEX "hrteacher_teacherId_idx" ON "hrteacher"("teacherId");

-- CreateIndex
CREATE INDEX "hrteacher_classId_idx" ON "hrteacher"("classId");

-- CreateIndex
CREATE UNIQUE INDEX "hrteacher_classId_key" ON "hrteacher"("classId");

-- CreateIndex
CREATE UNIQUE INDEX "user_email_key" ON "user"("email");

-- CreateIndex
CREATE INDEX "parent_student_parentId_idx" ON "parent_student"("parentId");

-- CreateIndex
CREATE INDEX "parent_student_studentId_idx" ON "parent_student"("studentId");

-- CreateIndex
CREATE UNIQUE INDEX "parent_student_parentId_studentId_key" ON "parent_student"("parentId", "studentId");

-- CreateIndex
CREATE UNIQUE INDEX "role_name_key" ON "role"("name");

-- CreateIndex
CREATE UNIQUE INDEX "permission_name_key" ON "permission"("name");

-- CreateIndex
CREATE INDEX "rolepermission_roleId_idx" ON "rolepermission"("roleId");

-- CreateIndex
CREATE INDEX "rolepermission_permissionId_idx" ON "rolepermission"("permissionId");

-- CreateIndex
CREATE UNIQUE INDEX "rolepermission_roleId_permissionId_key" ON "rolepermission"("roleId", "permissionId");

-- CreateIndex
CREATE INDEX "teacherassignment_teacherId_idx" ON "teacherassignment"("teacherId");

-- CreateIndex
CREATE INDEX "teacherassignment_classId_idx" ON "teacherassignment"("classId");

-- CreateIndex
CREATE INDEX "teacherassignment_subjectId_idx" ON "teacherassignment"("subjectId");

-- CreateIndex
CREATE UNIQUE INDEX "fee_type_name_key" ON "fee_type"("name");

-- CreateIndex
CREATE UNIQUE INDEX "payment_invoiceNumber_key" ON "payment"("invoiceNumber");

-- CreateIndex
CREATE INDEX "payment_studentId_idx" ON "payment"("studentId");

-- CreateIndex
CREATE INDEX "payment_feeTypeId_idx" ON "payment"("feeTypeId");

-- CreateIndex
CREATE INDEX "payment_forMonth_idx" ON "payment"("forMonth");

-- CreateIndex
CREATE UNIQUE INDEX "bank_payment_voucher_voucherNo_key" ON "bank_payment_voucher"("voucherNo");

-- CreateIndex
CREATE UNIQUE INDEX "account_code_code_key" ON "account_code"("code");

-- CreateIndex
CREATE UNIQUE INDEX "journal_voucher_voucherNo_key" ON "journal_voucher"("voucherNo");

-- CreateIndex
CREATE INDEX "teacher_permissions_teacherId_idx" ON "teacher_permissions"("teacherId");

-- CreateIndex
CREATE INDEX "teacher_permissions_classId_idx" ON "teacher_permissions"("classId");

-- CreateIndex
CREATE UNIQUE INDEX "teacher_permissions_teacherId_classId_key" ON "teacher_permissions"("teacherId", "classId");

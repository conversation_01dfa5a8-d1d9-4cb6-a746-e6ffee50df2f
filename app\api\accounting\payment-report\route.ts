import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// Mock data for payment reports (used as fallback)
const mockPaymentReports = [
  {
    id: '1',
    invoiceNumber: 'INV-2023-001',
    studentId: 'STD001',
    studentName: '<PERSON>',
    className: '8E',
    paymentDate: '2023-09-05',
    amount: 1500,
    paymentPurpose: 'Tuition Fee (Monthly)',
    paymentMethod: 'cash',
    status: 'paid',
    createdAt: '2023-09-05T10:30:00Z'
  },
  {
    id: '2',
    invoiceNumber: 'INV-2023-002',
    studentId: 'STD002',
    studentName: 'Fatima <PERSON>',
    className: '7A',
    paymentDate: '2023-09-10',
    amount: 2000,
    paymentPurpose: 'Registration Fee',
    paymentMethod: 'transfer',
    transferId: 'TRF12345',
    status: 'paid',
    createdAt: '2023-09-10T11:15:00Z'
  },
  {
    id: '3',
    invoiceNumber: 'INV-2023-003',
    studentId: 'STD003',
    studentName: '<PERSON>',
    className: '9B',
    paymentDate: '2023-09-15',
    amount: 1800,
    paymentPurpose: 'Exam Fee',
    paymentMethod: 'cash',
    status: 'paid',
    createdAt: '2023-09-15T09:45:00Z'
  },
  {
    id: '4',
    invoiceNumber: 'INV-2023-004',
    studentId: 'STD004',
    studentName: 'Amina Yusuf',
    className: '10A',
    paymentDate: '2023-09-20',
    amount: 500,
    paymentPurpose: 'Library Fee',
    paymentMethod: 'transfer',
    transferId: 'TRF67890',
    status: 'paid',
    createdAt: '2023-09-20T14:20:00Z'
  },
  // Add some unpaid records for demonstration
  {
    id: '5',
    invoiceNumber: 'INV-2023-005',
    studentId: 'STD005',
    studentName: 'Khalid Omar',
    className: '8E',
    paymentDate: '2023-10-05',
    amount: 1500,
    paymentPurpose: 'Tuition Fee (Monthly)',
    paymentMethod: 'cash',
    status: 'unpaid',
    createdAt: '2023-10-05T10:30:00Z'
  },
  {
    id: '6',
    invoiceNumber: 'INV-2023-006',
    studentId: 'STD006',
    studentName: 'Zainab Ali',
    className: '7A',
    paymentDate: '2023-10-10',
    amount: 1800,
    paymentPurpose: 'Exam Fee',
    paymentMethod: 'transfer',
    status: 'unpaid',
    createdAt: '2023-10-10T11:15:00Z'
  }
];

// Helper function to check if a model exists in Prisma
function isModelAvailable(modelName: string): boolean {
  return Object.prototype.hasOwnProperty.call(prisma, modelName);
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const className = searchParams.get('className')
    const paymentPurpose = searchParams.get('paymentPurpose')
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')
    const status = searchParams.get('status')

    // Check if Payment model is available
    if (!isModelAvailable('payment')) {
      console.warn('Payment model is not available in Prisma client. Using mock data.');

      // Filter mock data based on search parameters
      let filteredMockData = [...mockPaymentReports];

      if (className) {
        filteredMockData = filteredMockData.filter(p => p.className === className);
      }

      if (paymentPurpose) {
        filteredMockData = filteredMockData.filter(p => p.paymentPurpose === paymentPurpose);
      }

      if (status) {
        filteredMockData = filteredMockData.filter(p => p.status === status);
      }

      if (startDate || endDate) {
        filteredMockData = filteredMockData.filter(p => {
          const paymentDate = new Date(p.paymentDate);

          if (startDate && endDate) {
            return paymentDate >= new Date(startDate) && paymentDate <= new Date(endDate);
          } else if (startDate) {
            return paymentDate >= new Date(startDate);
          } else if (endDate) {
            return paymentDate <= new Date(endDate);
          }

          return true;
        });
      }

      return NextResponse.json(filteredMockData);
    }

    // Build the where clause for the database query
    const where: any = {};

    if (className) {
      where.student = {
        className
      };
    }

    if (paymentPurpose) {
      where.feeType = {
        name: paymentPurpose
      };
    }

    if (status) {
      where.status = status;
    }

    // Date range filtering
    if (startDate || endDate) {
      where.paymentDate = {};

      if (startDate) {
        where.paymentDate.gte = new Date(startDate);
      }

      if (endDate) {
        // Set the end date to the end of the day
        const endDateTime = new Date(endDate);
        endDateTime.setHours(23, 59, 59, 999);
        where.paymentDate.lte = endDateTime;
      }
    }

    try {
      // Fetch payments with related data
      const payments = await prisma.payment.findMany({
        where,
        include: {
          student: {
            select: {
              id: true,
              sid: true,
              name: true,
              className: true,
            },
          },
          feeType: true,
        },
        orderBy: {
          paymentDate: 'desc',
        },
      });

      // Transform the data to match the expected format in the frontend
      const formattedPayments = payments.map(payment => ({
        id: payment.id,
        invoiceNumber: payment.invoiceNumber,
        studentId: payment.student.sid,
        studentName: payment.student.name,
        className: payment.student.className,
        paymentDate: payment.paymentDate.toISOString().split('T')[0],
        forMonth: payment.forMonth || null, // Include the month this payment is for
        amount: payment.amount,
        paymentPurpose: payment.feeType.name,
        paymentMethod: payment.paymentMethod,
        transferId: payment.transferId || undefined,
        status: payment.status,
        createdAt: payment.createdAt.toISOString()
      }));

      return NextResponse.json(formattedPayments);
    } catch (dbError) {
      console.error('Database error fetching payments:', dbError);

      // If there's a database error, fall back to mock data
      console.warn('Falling back to mock payment report data');
      return NextResponse.json(mockPaymentReports);
    }
  } catch (error) {
    console.error('Error fetching payment report:', error);

    // Return mock data as a fallback
    console.warn('Error in payment report API, returning mock data');
    return NextResponse.json(mockPaymentReports);
  }
}

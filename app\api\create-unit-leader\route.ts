import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import bcrypt from 'bcryptjs';

export async function GET() {
  return POST();
}

export async function POST() {
  try {
    console.log('Creating Unit Leader user...');

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (existingUser) {
      console.log('Unit Leader user already exists, updating role...');
      
      // Update the existing user's role
      const hashedPassword = await bcrypt.hash('unit@123', 10);
      const updatedUser = await prisma.user.update({
        where: { email: '<EMAIL>' },
        data: {
          password: hashedPassword,
          role: 'UNIT_LEADER',
          status: 'active'
        }
      });

      return NextResponse.json({
        status: 'success',
        message: 'Unit Leader user updated successfully',
        user: {
          id: updatedUser.id,
          email: updatedUser.email,
          name: updatedUser.name,
          role: updatedUser.role,
          password: 'unit@123' // Only for testing
        }
      });
    } else {
      console.log('Creating new Unit Leader user...');
      
      // Create new Unit Leader user
      const hashedPassword = await bcrypt.hash('unit@123', 10);
      const newUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: hashedPassword,
          name: 'Unit Leader',
          role: 'UNIT_LEADER',
          status: 'active'
        }
      });

      return NextResponse.json({
        status: 'success',
        message: 'Unit Leader user created successfully',
        user: {
          id: newUser.id,
          email: newUser.email,
          name: newUser.name,
          role: newUser.role,
          password: 'unit@123' // Only for testing
        }
      });
    }
  } catch (error) {
    console.error('Error creating Unit Leader user:', error);
    
    return NextResponse.json({
      status: 'error',
      message: 'Failed to create Unit Leader user',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
});

async function createSuperAdmin() {
  console.log('🔧 Creating Super Admin user...');
  
  try {
    // Connect to database
    await prisma.$connect();
    console.log('✅ Connected to database');
    
    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: {
        email: '<EMAIL>'
      }
    });
    
    if (existingUser) {
      console.log('⚠️  User already exists. Updating password...');
      
      // Hash the new password
      const hashedPassword = await bcrypt.hash('admin@123', 12);
      
      // Update existing user
      const updatedUser = await prisma.user.update({
        where: {
          email: '<EMAIL>'
        },
        data: {
          password: hashedPassword,
          role: 'SUPER_ADMIN',
          name: 'Israel Admin',
          status: 'active'
        }
      });
      
      console.log('✅ Super Admin user updated successfully!');
      console.log('📋 User details:', {
        id: updatedUser.id,
        email: updatedUser.email,
        name: updatedUser.name,
        role: updatedUser.role,
        status: updatedUser.status
      });
      
    } else {
      console.log('🆕 Creating new Super Admin user...');
      
      // Hash the password
      const hashedPassword = await bcrypt.hash('admin@123', 12);
      
      // Create new user
      const newUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: hashedPassword,
          name: 'Israel Admin',
          role: 'SUPER_ADMIN',
          status: 'active'
        }
      });
      
      console.log('✅ Super Admin user created successfully!');
      console.log('📋 User details:', {
        id: newUser.id,
        email: newUser.email,
        name: newUser.name,
        role: newUser.role,
        status: newUser.status
      });
    }
    
    // Verify the user can be found
    const verifyUser = await prisma.user.findUnique({
      where: {
        email: '<EMAIL>'
      },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        status: true,
        createdAt: true
      }
    });
    
    console.log('\n🔍 Verification - User found in database:');
    console.log(verifyUser);
    
    // Test password verification
    const isPasswordValid = await bcrypt.compare('admin@123', verifyUser ? await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      select: { password: true }
    }).then(u => u?.password || '') : '');
    
    console.log('🔐 Password verification:', isPasswordValid ? '✅ Valid' : '❌ Invalid');
    
    console.log('\n🎉 Super Admin setup completed!');
    console.log('📝 Login credentials:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: admin@123');
    console.log('   Role: SUPER_ADMIN');
    
  } catch (error) {
    console.error('❌ Error creating Super Admin:', error.message);
    console.error('Error details:', error);
  } finally {
    await prisma.$disconnect();
    console.log('🔌 Database connection closed');
  }
}

createSuperAdmin().catch(console.error);

import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { cookies } from 'next/headers'
import { verifyJWT } from '@/lib/jwt'
import { canTeacherEditMarks } from '@/lib/teacher-permissions'
import { createPermissionDeniedResponse, PermissionErrors } from '@/app/lib/permission-errors'

export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id
    const data = await request.json()

    // Validate required fields - support both obtainedMarks and marks
    const marks = data.marks || data.obtainedMarks
    if (marks === undefined || marks === null) {
      return NextResponse.json(
        { error: 'Missing required field: marks' },
        { status: 400 }
      )
    }

    // Verify authentication
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    }

    // Get the mark to check the class
    const existingMark = await prisma.mark.findUnique({
      where: { id },
      select: { className: true }
    })

    if (!existingMark) {
      return NextResponse.json(
        { error: 'Mark not found' },
        { status: 404 }
      )
    }

    try {
      // Verify the token
      const decoded = await verifyJWT(token)

      // Only allow SUPER_ADMIN, ADMIN, SUPERVISOR, TEACHER, and DATA_ENCODER to edit marks
      if (!['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'TEACHER', 'DATA_ENCODER'].includes(decoded.role)) {
        return NextResponse.json(
          { error: 'Forbidden - You do not have permission to edit marks' },
          { status: 403 }
        )
      }

      // If the user is a teacher, check if they have permission to edit marks for this class
      if (decoded.role === 'TEACHER') {
        // Get the teacher ID from the email
        const teacher = await prisma.teacher.findUnique({
          where: { email: decoded.email },
          select: { id: true }
        })

        if (!teacher) {
          return NextResponse.json(
            { error: 'Teacher not found' },
            { status: 404 }
          )
        }

        // Check if the teacher has permission to edit marks for this class
        const classObj = await prisma.class.findUnique({
          where: { name: existingMark.className },
          select: { id: true }
        })

        if (!classObj) {
          return NextResponse.json(
            { error: 'Class not found' },
            { status: 404 }
          )
        }

        const hasPermission = await canTeacherEditMarks(teacher.id, classObj.id)

        if (!hasPermission) {
          return createPermissionDeniedResponse('edit', 'marks', `class "${existingMark.className}"`)
        }
      }
    } catch (jwtError) {
      console.error('JWT verification error:', jwtError)
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Update the mark
    const updatedMark = await prisma.mark.update({
      where: { id },
      data: {
        marks: marks,
        totalMarks: data.totalMarks || undefined, // Only update if provided
        remarks: data.remarks || undefined, // Only update if provided
        updatedAt: new Date()
      },
      include: {
        student: {
          select: {
            name: true,
            sid: true,
            className: true
          }
        }
      }
    })

    // Format the response to include student name and match UI expectations
    const formattedMark = {
      id: updatedMark.id,
      studentId: updatedMark.studentId,
      studentName: updatedMark.student.name,
      studentSid: updatedMark.student.sid,
      subject: updatedMark.subject,
      obtainedMarks: updatedMark.marks, // Map marks to obtainedMarks for UI
      totalMarks: 100, // Default total marks
      class: updatedMark.className, // Map className to class for UI
      term: updatedMark.term,
      academicYear: new Date(updatedMark.createdAt).getFullYear() + '-' + (new Date(updatedMark.createdAt).getFullYear() + 1), // Generate academic year

      dateRecorded: updatedMark.createdAt,
      recordedBy: 'Teacher', // Default recorded by
      createdAt: updatedMark.createdAt,
      updatedAt: updatedMark.updatedAt
    }

    return NextResponse.json(formattedMark)
  } catch (error) {
    console.error('Error updating mark:', error)
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json(
      { error: 'Failed to update mark', details: errorMessage },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id

    // Verify authentication
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    }

    // Get the mark to check the class
    const existingMark = await prisma.mark.findUnique({
      where: { id },
      select: { className: true }
    })

    if (!existingMark) {
      return NextResponse.json(
        { error: 'Mark not found' },
        { status: 404 }
      )
    }

    try {
      // Verify the token
      const decoded = await verifyJWT(token)

      // Only allow SUPER_ADMIN, ADMIN, SUPERVISOR, TEACHER, and DATA_ENCODER to delete marks
      if (!['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'TEACHER', 'DATA_ENCODER'].includes(decoded.role)) {
        return NextResponse.json(
          { error: 'Forbidden - You do not have permission to delete marks' },
          { status: 403 }
        )
      }

      // If the user is a teacher, check if they have permission to edit marks for this class
      // We use edit permission for delete as well
      if (decoded.role === 'TEACHER') {
        // Get the teacher ID from the email
        const teacher = await prisma.teacher.findUnique({
          where: { email: decoded.email },
          select: { id: true }
        })

        if (!teacher) {
          return NextResponse.json(
            { error: 'Teacher not found' },
            { status: 404 }
          )
        }

        // Check if the teacher has permission to edit marks for this class
        const classObj = await prisma.class.findUnique({
          where: { name: existingMark.className },
          select: { id: true }
        })

        if (!classObj) {
          return NextResponse.json(
            { error: 'Class not found' },
            { status: 404 }
          )
        }

        const hasPermission = await canTeacherEditMarks(teacher.id, classObj.id)

        if (!hasPermission) {
          return createPermissionDeniedResponse('delete', 'marks', `class "${existingMark.className}"`)
        }
      }
    } catch (jwtError) {
      console.error('JWT verification error:', jwtError)
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Delete the mark
    await prisma.mark.delete({
      where: { id }
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting mark:', error)
    return NextResponse.json(
      { error: 'Failed to delete mark' },
      { status: 500 }
    )
  }
}

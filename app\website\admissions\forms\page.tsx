"use client";

import React from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { FaFileAlt, FaDownload, FaFileSignature, FaFileMedical, FaFileInvoice, FaFileContract, FaSearch } from 'react-icons/fa';
import { Button } from '@/app/components/ui/button';
import { Input } from '@/app/components/ui/input';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/app/components/ui/tabs';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.5 },
  },
};

// Forms data
const admissionForms = [
  {
    id: 1,
    title: "Student Application Form",
    description: "The main application form for new student enrollment. Required for all applicants.",
    fileType: "PDF",
    fileSize: "250 KB",
    lastUpdated: "January 15, 2023",
    icon: <FaFileSignature className="text-4xl text-blue-600" />,
  },
  {
    id: 2,
    title: "Parent Questionnaire",
    description: "Form for parents to provide information about their child and educational expectations.",
    fileType: "PDF",
    fileSize: "180 KB",
    lastUpdated: "January 15, 2023",
    icon: <FaFileAlt className="text-4xl text-green-600" />,
  },
  {
    id: 3,
    title: "Teacher Recommendation Form",
    description: "To be completed by the student's current teacher and submitted directly to our admissions office.",
    fileType: "PDF",
    fileSize: "150 KB",
    lastUpdated: "January 15, 2023",
    icon: <FaFileAlt className="text-4xl text-purple-600" />,
  },
  {
    id: 4,
    title: "Records Release Authorization",
    description: "Authorization to request academic records from the student's previous school.",
    fileType: "PDF",
    fileSize: "120 KB",
    lastUpdated: "January 15, 2023",
    icon: <FaFileContract className="text-4xl text-amber-600" />,
  },
  {
    id: 5,
    title: "Financial Aid Application",
    description: "Application for need-based financial assistance. Includes instructions and required documentation list.",
    fileType: "PDF",
    fileSize: "300 KB",
    lastUpdated: "January 15, 2023",
    icon: <FaFileInvoice className="text-4xl text-red-600" />,
  },
  {
    id: 6,
    title: "Scholarship Application",
    description: "Application for merit-based scholarships. Includes essay prompts and recommendation requirements.",
    fileType: "PDF",
    fileSize: "280 KB",
    lastUpdated: "January 15, 2023",
    icon: <FaFileInvoice className="text-4xl text-indigo-600" />,
  },
];

const enrollmentForms = [
  {
    id: 1,
    title: "Enrollment Agreement",
    description: "Contract between the school and family outlining terms of enrollment. Required for all new and returning students.",
    fileType: "PDF",
    fileSize: "350 KB",
    lastUpdated: "February 1, 2023",
    icon: <FaFileContract className="text-4xl text-blue-600" />,
  },
  {
    id: 2,
    title: "Emergency Contact Form",
    description: "Emergency contact information and medical release authorization.",
    fileType: "PDF",
    fileSize: "150 KB",
    lastUpdated: "February 1, 2023",
    icon: <FaFileMedical className="text-4xl text-red-600" />,
  },
  {
    id: 3,
    title: "Health Information Form",
    description: "Student health information, including immunizations, allergies, and medications.",
    fileType: "PDF",
    fileSize: "200 KB",
    lastUpdated: "February 1, 2023",
    icon: <FaFileMedical className="text-4xl text-green-600" />,
  },
  {
    id: 4,
    title: "Media Release Form",
    description: "Permission for use of student photos and videos in school publications and media.",
    fileType: "PDF",
    fileSize: "120 KB",
    lastUpdated: "February 1, 2023",
    icon: <FaFileAlt className="text-4xl text-purple-600" />,
  },
  {
    id: 5,
    title: "Transportation Form",
    description: "Information about student transportation to and from school, including authorized pick-up persons.",
    fileType: "PDF",
    fileSize: "130 KB",
    lastUpdated: "February 1, 2023",
    icon: <FaFileAlt className="text-4xl text-amber-600" />,
  },
  {
    id: 6,
    title: "Payment Plan Selection Form",
    description: "Form to select tuition payment plan and set up payment method.",
    fileType: "PDF",
    fileSize: "180 KB",
    lastUpdated: "February 1, 2023",
    icon: <FaFileInvoice className="text-4xl text-indigo-600" />,
  },
];

const otherForms = [
  {
    id: 1,
    title: "Student Handbook Acknowledgment",
    description: "Acknowledgment of receipt and review of the student handbook.",
    fileType: "PDF",
    fileSize: "100 KB",
    lastUpdated: "March 1, 2023",
    icon: <FaFileAlt className="text-4xl text-blue-600" />,
  },
  {
    id: 2,
    title: "Technology Acceptable Use Policy",
    description: "Agreement regarding appropriate use of school technology resources.",
    fileType: "PDF",
    fileSize: "150 KB",
    lastUpdated: "March 1, 2023",
    icon: <FaFileAlt className="text-4xl text-green-600" />,
  },
  {
    id: 3,
    title: "Field Trip Permission Form",
    description: "General permission form for school field trips throughout the year.",
    fileType: "PDF",
    fileSize: "120 KB",
    lastUpdated: "March 1, 2023",
    icon: <FaFileAlt className="text-4xl text-purple-600" />,
  },
  {
    id: 4,
    title: "Volunteer Application",
    description: "Application for parents interested in volunteering at the school.",
    fileType: "PDF",
    fileSize: "200 KB",
    lastUpdated: "March 1, 2023",
    icon: <FaFileSignature className="text-4xl text-amber-600" />,
  },
  {
    id: 5,
    title: "Medication Authorization Form",
    description: "Authorization for school to administer medication to student.",
    fileType: "PDF",
    fileSize: "150 KB",
    lastUpdated: "March 1, 2023",
    icon: <FaFileMedical className="text-4xl text-red-600" />,
  },
  {
    id: 6,
    title: "After-School Program Registration",
    description: "Registration form for after-school activities and extended care.",
    fileType: "PDF",
    fileSize: "180 KB",
    lastUpdated: "March 1, 2023",
    icon: <FaFileAlt className="text-4xl text-indigo-600" />,
  },
];

export default function FormsPage() {
  const [searchTerm, setSearchTerm] = React.useState('');

  // Filter forms based on search term
  const filterForms = (forms: any[]) => {
    if (!searchTerm) return forms;
    return forms.filter(form => 
      form.title.toLowerCase().includes(searchTerm.toLowerCase()) || 
      form.description.toLowerCase().includes(searchTerm.toLowerCase())
    );
  };

  const filteredAdmissionForms = filterForms(admissionForms);
  const filteredEnrollmentForms = filterForms(enrollmentForms);
  const filteredOtherForms = filterForms(otherForms);

  return (
    <div className="min-h-screen pt-20">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-blue-900 to-indigo-900 text-white">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{ backgroundImage: 'url(/images/pattern.svg)', backgroundSize: '20px' }}></div>
        </div>
        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center max-w-3xl mx-auto"
          >
            <h1 className="text-4xl md:text-5xl font-bold mb-6">Forms & Documents</h1>
            <p className="text-xl text-blue-100">
              Download all the forms you need for admission and enrollment
            </p>
          </motion.div>
        </div>
      </section>

      {/* Search Section */}
      <section className="py-8 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto">
            <div className="relative">
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <Input
                type="text"
                placeholder="Search forms..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
              {searchTerm && (
                <button
                  onClick={() => setSearchTerm('')}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  Clear
                </button>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Forms Tabs */}
      <section className="py-12 bg-gray-50 dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <Tabs defaultValue="admission" className="w-full">
            <TabsList className="grid w-full max-w-md mx-auto grid-cols-3 mb-8">
              <TabsTrigger value="admission">Admission</TabsTrigger>
              <TabsTrigger value="enrollment">Enrollment</TabsTrigger>
              <TabsTrigger value="other">Other Forms</TabsTrigger>
            </TabsList>

            {/* Admission Forms Tab */}
            <TabsContent value="admission">
              {filteredAdmissionForms.length > 0 ? (
                <motion.div
                  variants={containerVariants}
                  initial="hidden"
                  animate="visible"
                  className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                >
                  {filteredAdmissionForms.map((form) => (
                    <motion.div
                      key={form.id}
                      variants={itemVariants}
                      className="bg-white dark:bg-gray-900 rounded-lg shadow-md overflow-hidden"
                    >
                      <div className="p-6">
                        <div className="flex items-start mb-4">
                          <div className="mr-4 flex-shrink-0">{form.icon}</div>
                          <div>
                            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-1">{form.title}</h3>
                            <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">{form.description}</p>
                            <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 space-x-4">
                              <span>{form.fileType}</span>
                              <span>{form.fileSize}</span>
                              <span>Updated: {form.lastUpdated}</span>
                            </div>
                          </div>
                        </div>
                        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                          <Button className="w-full flex items-center justify-center gap-2">
                            <FaDownload className="h-4 w-4" />
                            <span>Download</span>
                          </Button>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </motion.div>
              ) : (
                <div className="text-center py-12">
                  <div className="text-gray-400 text-5xl mb-4">📄</div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">No Forms Found</h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    No admission forms match your search criteria.
                  </p>
                  <Button 
                    variant="outline" 
                    onClick={() => setSearchTerm('')}
                    className="mt-4"
                  >
                    Clear Search
                  </Button>
                </div>
              )}
            </TabsContent>

            {/* Enrollment Forms Tab */}
            <TabsContent value="enrollment">
              {filteredEnrollmentForms.length > 0 ? (
                <motion.div
                  variants={containerVariants}
                  initial="hidden"
                  animate="visible"
                  className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                >
                  {filteredEnrollmentForms.map((form) => (
                    <motion.div
                      key={form.id}
                      variants={itemVariants}
                      className="bg-white dark:bg-gray-900 rounded-lg shadow-md overflow-hidden"
                    >
                      <div className="p-6">
                        <div className="flex items-start mb-4">
                          <div className="mr-4 flex-shrink-0">{form.icon}</div>
                          <div>
                            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-1">{form.title}</h3>
                            <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">{form.description}</p>
                            <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 space-x-4">
                              <span>{form.fileType}</span>
                              <span>{form.fileSize}</span>
                              <span>Updated: {form.lastUpdated}</span>
                            </div>
                          </div>
                        </div>
                        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                          <Button className="w-full flex items-center justify-center gap-2">
                            <FaDownload className="h-4 w-4" />
                            <span>Download</span>
                          </Button>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </motion.div>
              ) : (
                <div className="text-center py-12">
                  <div className="text-gray-400 text-5xl mb-4">📄</div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">No Forms Found</h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    No enrollment forms match your search criteria.
                  </p>
                  <Button 
                    variant="outline" 
                    onClick={() => setSearchTerm('')}
                    className="mt-4"
                  >
                    Clear Search
                  </Button>
                </div>
              )}
            </TabsContent>

            {/* Other Forms Tab */}
            <TabsContent value="other">
              {filteredOtherForms.length > 0 ? (
                <motion.div
                  variants={containerVariants}
                  initial="hidden"
                  animate="visible"
                  className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                >
                  {filteredOtherForms.map((form) => (
                    <motion.div
                      key={form.id}
                      variants={itemVariants}
                      className="bg-white dark:bg-gray-900 rounded-lg shadow-md overflow-hidden"
                    >
                      <div className="p-6">
                        <div className="flex items-start mb-4">
                          <div className="mr-4 flex-shrink-0">{form.icon}</div>
                          <div>
                            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-1">{form.title}</h3>
                            <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">{form.description}</p>
                            <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 space-x-4">
                              <span>{form.fileType}</span>
                              <span>{form.fileSize}</span>
                              <span>Updated: {form.lastUpdated}</span>
                            </div>
                          </div>
                        </div>
                        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                          <Button className="w-full flex items-center justify-center gap-2">
                            <FaDownload className="h-4 w-4" />
                            <span>Download</span>
                          </Button>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </motion.div>
              ) : (
                <div className="text-center py-12">
                  <div className="text-gray-400 text-5xl mb-4">📄</div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">No Forms Found</h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    No other forms match your search criteria.
                  </p>
                  <Button 
                    variant="outline" 
                    onClick={() => setSearchTerm('')}
                    className="mt-4"
                  >
                    Clear Search
                  </Button>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>
      </section>

      {/* Form Submission Instructions */}
      <section className="py-16 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Form Submission Instructions</h2>
              <p className="text-gray-600 dark:text-gray-300">
                Please follow these guidelines when submitting your completed forms
              </p>
            </motion.div>

            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg shadow-md p-6">
              <div className="space-y-6">
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">Submission Methods</h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    You may submit completed forms using any of the following methods:
                  </p>
                  <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                    <li className="flex items-start">
                      <span className="text-blue-600 mr-2">•</span>
                      <span><strong>Email:</strong> <EMAIL> (preferred method)</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-600 mr-2">•</span>
                      <span><strong>Mail:</strong> Alfalah Islamic School, Admissions Office, 123 School Street, City, State 12345</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-600 mr-2">•</span>
                      <span><strong>In Person:</strong> Deliver to the Main Office during school hours (8:00 AM - 4:00 PM, Monday-Friday)</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-600 mr-2">•</span>
                      <span><strong>Fax:</strong> (123) 456-7892</span>
                    </li>
                  </ul>
                </div>

                <div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">Important Notes</h3>
                  <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                    <li className="flex items-start">
                      <span className="text-blue-600 mr-2">•</span>
                      <span>All forms must be completed in full and signed where indicated.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-600 mr-2">•</span>
                      <span>Please include the student's full name on all submitted documents.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-600 mr-2">•</span>
                      <span>Teacher recommendation forms should be submitted directly by the teacher, not by the applicant family.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-600 mr-2">•</span>
                      <span>Financial aid and scholarship applications require additional supporting documentation as specified in the forms.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-600 mr-2">•</span>
                      <span>Applications are not considered complete until all required forms and documents have been received.</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-12 bg-blue-600">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="text-white mb-6 md:mb-0">
              <h2 className="text-2xl font-bold mb-2">Need Assistance with Forms?</h2>
              <p className="text-blue-100">Our admissions team is here to help with any questions you may have.</p>
            </div>
            <div className="flex flex-col sm:flex-row gap-4">
              <Link href="/website/contact">
                <Button className="bg-white text-blue-600 hover:bg-blue-50">
                  Contact Admissions
                </Button>
              </Link>
              <Link href="/website/admissions/how-to-apply">
                <Button variant="outline" className="border-white text-white hover:bg-blue-700">
                  How to Apply
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

// Script to check the student table schema
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    // Execute a raw query to get the table schema
    const result = await prisma.$queryRaw`DESCRIBE student`;
    console.log('Student table schema:');
    console.log(result);
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();

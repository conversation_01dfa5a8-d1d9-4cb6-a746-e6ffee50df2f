"use client"

import React, { useState } from 'react'
import { <PERSON><PERSON> } from './ui/button'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/table'
import { Input } from './ui/input'
import { Search, RefreshCw, CheckCircle, XCircle, AlertCircle } from 'lucide-react'

interface User {
  id: string
  name: string
  email: string
  role: string
  lastLogin: string
  status: 'active' | 'inactive' | 'locked'
}

// Sample data
const sampleUsers: User[] = [
  { id: '1', name: '<PERSON>', email: '<EMAIL>', role: 'Teacher', lastLogin: '2023-05-20', status: 'active' },
  { id: '2', name: '<PERSON>', email: '<EMAIL>', role: 'Teacher', lastLogin: '2023-05-18', status: 'active' },
  { id: '3', name: '<PERSON>', email: '<EMAIL>', role: 'Administrator', lastLogin: '2023-05-21', status: 'active' },
  { id: '4', name: '<PERSON>', email: '<EMAIL>', role: 'Teacher', lastLogin: '2023-04-30', status: 'locked' },
  { id: '5', name: '<PERSON>', email: '<EMAIL>', role: 'Staff', lastLogin: '2023-05-10', status: 'inactive' },
]

export function AccountReset() {
  const [users, setUsers] = useState<User[]>(sampleUsers)
  const [searchTerm, setSearchTerm] = useState('')
  const [resetStatus, setResetStatus] = useState<{[key: string]: 'pending' | 'success' | 'error' | null}>({})
  
  const filteredUsers = users.filter(user => 
    user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.role.toLowerCase().includes(searchTerm.toLowerCase())
  )
  
  const handleResetPassword = (userId: string) => {
    // Set status to pending
    setResetStatus({...resetStatus, [userId]: 'pending'})
    
    // Simulate API call with timeout
    setTimeout(() => {
      // 95% chance of success
      const success = Math.random() < 0.95
      setResetStatus({...resetStatus, [userId]: success ? 'success' : 'error'})
      
      // Clear status after 3 seconds
      setTimeout(() => {
        setResetStatus({...resetStatus, [userId]: null})
      }, 3000)
    }, 1000)
  }
  
  const handleUnlockAccount = (userId: string) => {
    // Update user status
    setUsers(users.map(user => 
      user.id === userId ? { ...user, status: 'active' as const } : user
    ))
    
    // Show success status
    setResetStatus({...resetStatus, [userId]: 'success'})
    
    // Clear status after 3 seconds
    setTimeout(() => {
      setResetStatus({...resetStatus, [userId]: null})
    }, 3000)
  }
  
  const getStatusBadgeClass = (status: User['status']) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800'
      case 'inactive':
        return 'bg-gray-100 text-gray-800'
      case 'locked':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }
  
  const getActionButtonText = (status: User['status']) => {
    return status === 'locked' ? 'Unlock Account' : 'Reset Password'
  }
  
  const getActionButtonHandler = (user: User) => {
    return user.status === 'locked' 
      ? () => handleUnlockAccount(user.id)
      : () => handleResetPassword(user.id)
  }
  
  const getResetStatusIcon = (userId: string) => {
    const status = resetStatus[userId]
    
    if (!status) return null
    
    switch (status) {
      case 'pending':
        return <RefreshCw className="h-5 w-5 animate-spin text-indigo-600" />
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-600" />
      case 'error':
        return <XCircle className="h-5 w-5 text-red-600" />
      default:
        return null
    }
  }
  
  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold">Account Reset</h1>
        <p className="text-gray-600 mt-1">Reset passwords or unlock accounts for users</p>
      </div>
      
      {/* Search and filter */}
      <div className="flex flex-col sm:flex-row items-center gap-4">
        <div className="relative w-full sm:w-96">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search users by name, email or role..."
            className="pl-10"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        
        <div className="flex items-center gap-3 ml-auto">
          <div className="flex items-center gap-1.5">
            <span className="inline-block h-2 w-2 rounded-full bg-green-500"></span>
            <span className="text-sm text-gray-600">Active</span>
          </div>
          <div className="flex items-center gap-1.5">
            <span className="inline-block h-2 w-2 rounded-full bg-gray-400"></span>
            <span className="text-sm text-gray-600">Inactive</span>
          </div>
          <div className="flex items-center gap-1.5">
            <span className="inline-block h-2 w-2 rounded-full bg-red-500"></span>
            <span className="text-sm text-gray-600">Locked</span>
          </div>
        </div>
      </div>
      
      {/* Help notice */}
      <div className="flex items-start gap-3 p-4 bg-blue-50 border border-blue-200 rounded-md text-blue-800">
        <AlertCircle className="h-5 w-5 text-blue-500 mt-0.5" />
        <div>
          <h3 className="font-medium">Important Information</h3>
          <p className="text-sm mt-1">Reset passwords will be sent to the user's email address. Unlocking an account will also reset the failed login attempts counter.</p>
        </div>
      </div>
      
      {/* Users table */}
      <div className="bg-white rounded-md border border-gray-200">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>User</TableHead>
              <TableHead>Role</TableHead>
              <TableHead>Last Login</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Action</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredUsers.length > 0 ? (
              filteredUsers.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{user.name}</div>
                      <div className="text-sm text-gray-500">{user.email}</div>
                    </div>
                  </TableCell>
                  <TableCell>{user.role}</TableCell>
                  <TableCell>{user.lastLogin}</TableCell>
                  <TableCell>
                    <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${getStatusBadgeClass(user.status)}`}>
                      {user.status.charAt(0).toUpperCase() + user.status.slice(1)}
                    </span>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex items-center justify-end gap-2">
                      {getResetStatusIcon(user.id)}
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={getActionButtonHandler(user)}
                        disabled={!!resetStatus[user.id]}
                        className={user.status === 'locked' ? 'border-amber-200 text-amber-800 hover:bg-amber-50' : ''}
                      >
                        {getActionButtonText(user.status)}
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={5} className="h-24 text-center">
                  No users found matching your search.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
} 
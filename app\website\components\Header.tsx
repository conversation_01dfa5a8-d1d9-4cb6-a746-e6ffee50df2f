"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { FaBars, FaTimes, FaChevronDown, FaSchool } from 'react-icons/fa';
import { useAuth } from '@/app/contexts/auth-context'
import { useAppSettings } from '@/app/contexts/app-settings-context';

// School logo path
const SCHOOL_LOGO = '/images/logo.png';
import { Button } from '@/app/components/ui/button';
import { CompactThemeSwitcher } from '@/app/components/ThemeSwitcher';

const navItems = [
  { name: 'Home', path: '/website' },
  {
    name: 'About Us',
    path: '/website/about',
    submenu: [
      { name: 'Mission & Vision', path: '/website/about#mission-vision' },
      { name: 'History', path: '/website/about#history' },
      { name: 'Achievements', path: '/website/about#achievements' },
      { name: 'Leadership', path: '/website/about#leadership' },
    ]
  },
  {
    name: 'Admissions',
    path: '/website/admissions',
    submenu: [
      { name: 'How to Apply', path: '/website/admissions#apply' },
      { name: 'Requirements', path: '/website/admissions#requirements' },
      { name: 'Tuition & Fees', path: '/website/admissions#tuition' },
      { name: 'Forms', path: '/website/admissions#forms' },
    ]
  },
  {
    name: 'Academics',
    path: '/website/academics',
    submenu: [
      { name: 'Curriculum', path: '/website/academics#curriculum' },
      { name: 'Departments', path: '/website/academics#departments' },
      { name: 'Calendar', path: '/website/academics#calendar' },
      { name: 'Resources', path: '/website/academics#resources' },
    ]
  },
  { name: 'Tuition & Fees', path: '/website/admissions/tuition-fees' },
  { name: 'News & Events', path: '/website/news-events' },
  { name: 'Contact', path: '/website/contact' },
];

export default function Header() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [openSubmenu, setOpenSubmenu] = useState<string | null>(null);
  const router = useRouter();
  const { isAuthenticated, user } = useAuth();
  const { settings } = useAppSettings();

  // Handle scroll event to change header appearance
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 50) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Handle login/dashboard button click
  const handleLoginOrDashboard = () => {
    if (isAuthenticated) {
      console.log('User is authenticated, redirecting to dashboard');
      // Use window.location for a full page navigation to ensure proper redirect
      window.location.href = '/dashboard';
    } else {
      console.log('User is not authenticated, redirecting to login page');
      // Use window.location for a full page navigation to avoid any middleware issues
      window.location.href = '/login';
    }
  };

  // Toggle submenu on desktop
  const toggleSubmenu = (name: string) => {
    if (openSubmenu === name) {
      setOpenSubmenu(null);
    } else {
      setOpenSubmenu(name);
    }
  };

  return (
    <header
      className={`fixed top-0 left-0 w-full z-50 transition-all duration-300 ${
        isScrolled
          ? 'bg-white/90 dark:bg-gray-900/90 backdrop-blur-md shadow-md'
          : 'bg-transparent'
      }`}
    >
      <div className="container mx-auto px-4 md:px-6">
        <div className="flex items-center justify-between h-20">
          {/* Logo */}
          <Link href="/website" className="flex items-center">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
              className="relative h-12 w-12 mr-3"
            >
              <Image
                src={settings.schoolLogo || SCHOOL_LOGO}
                alt={`${settings.schoolName} Logo`}
                fill
                className="object-contain"
              />
            </motion.div>
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              <h1 className={`font-bold transition-colors duration-300 ${
                isScrolled
                  ? 'text-gray-900 dark:text-white'
                  : 'text-white'
              }`}>
                <span className="hidden md:inline">{settings.schoolName}</span>
                <span className="md:hidden">{settings.schoolName.length > 15 ? `${settings.schoolName.substring(0, 15)}...` : settings.schoolName}</span>
              </h1>
            </motion.div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-1">
            {navItems.map((item) => (
              <div key={item.name} className="relative group">
                {item.submenu ? (
                  <button
                    onClick={() => toggleSubmenu(item.name)}
                    className={`px-3 py-2 rounded-md text-sm font-medium flex items-center transition-colors duration-300 ${
                      isScrolled
                        ? 'text-gray-700 hover:text-blue-600 dark:text-gray-200 dark:hover:text-blue-400'
                        : 'text-white/90 hover:text-white'
                    }`}
                  >
                    {item.name}
                    <FaChevronDown className="ml-1 h-3 w-3" />
                  </button>
                ) : (
                  <Link
                    href={item.path}
                    className={`px-3 py-2 rounded-md text-sm font-medium transition-colors duration-300 ${
                      isScrolled
                        ? 'text-gray-700 hover:text-blue-600 dark:text-gray-200 dark:hover:text-blue-400'
                        : 'text-white/90 hover:text-white'
                    }`}
                  >
                    {item.name}
                  </Link>
                )}

                {/* Submenu */}
                {item.submenu && (
                  <div
                    className={`absolute left-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 transition-all duration-200 ${
                      openSubmenu === item.name
                        ? 'opacity-100 translate-y-0 pointer-events-auto'
                        : 'opacity-0 -translate-y-2 pointer-events-none'
                    }`}
                  >
                    <div className="py-1">
                      {item.submenu.map((subitem) => (
                        <Link
                          key={subitem.name}
                          href={subitem.path}
                          className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700"
                          onClick={() => setOpenSubmenu(null)}
                        >
                          {subitem.name}
                        </Link>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </nav>

          {/* Theme Switcher and Login/Dashboard Button */}
          <div className="hidden md:flex items-center space-x-3">
            <CompactThemeSwitcher />
            <Button
              onClick={handleLoginOrDashboard}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              {isAuthenticated ? 'Dashboard' : 'Login'}
            </Button>
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden">
            <button
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className={`p-2 rounded-md transition-colors duration-300 ${
                isScrolled
                  ? 'text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700'
                  : 'text-white hover:bg-white/10'
              }`}
            >
              {mobileMenuOpen ? (
                <FaTimes className="h-6 w-6" />
              ) : (
                <FaBars className="h-6 w-6" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      <div
        className={`md:hidden transition-all duration-300 ease-in-out ${
          mobileMenuOpen
            ? 'max-h-screen opacity-100'
            : 'max-h-0 opacity-0 pointer-events-none'
        } bg-white dark:bg-gray-900 overflow-hidden`}
      >
        <div className="px-2 pt-2 pb-3 space-y-1">
          {navItems.map((item) => (
            <div key={item.name}>
              {item.submenu ? (
                <button
                  onClick={() => toggleSubmenu(item.name)}
                  className="w-full text-left px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700 flex justify-between items-center"
                >
                  {item.name}
                  <FaChevronDown className={`h-3 w-3 transition-transform ${openSubmenu === item.name ? 'rotate-180' : ''}`} />
                </button>
              ) : (
                <Link
                  href={item.path}
                  className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  {item.name}
                </Link>
              )}

              {/* Mobile Submenu */}
              {item.submenu && openSubmenu === item.name && (
                <div className="pl-4 space-y-1 mt-1">
                  {item.submenu.map((subitem) => (
                    <Link
                      key={subitem.name}
                      href={subitem.path}
                      className="block px-3 py-2 rounded-md text-sm text-gray-600 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      {subitem.name}
                    </Link>
                  ))}
                </div>
              )}
            </div>
          ))}

          {/* Mobile Theme Switcher and Login/Dashboard Button */}
          <div className="pt-4 pb-3 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between mb-3 px-3">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Theme</span>
              <CompactThemeSwitcher />
            </div>
            <Button
              onClick={handleLoginOrDashboard}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white"
            >
              {isAuthenticated ? 'Dashboard' : 'Login'}
            </Button>
            {isAuthenticated && (
              <div className="mt-3 px-3 py-2 bg-gray-50 dark:bg-gray-800 rounded-md">
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Logged in as <span className="font-medium">{user?.name}</span>
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
}

"use client"

import { useState, useEffect, useRef, useMemo, useCallback } from 'react'

interface VirtualScrollOptions {
  itemHeight: number
  containerHeight: number
  overscan?: number
  scrollingDelay?: number
}

interface VirtualScrollResult<T> {
  virtualItems: Array<{
    index: number
    start: number
    end: number
    item: T
  }>
  totalHeight: number
  scrollElementRef: React.RefObject<HTMLDivElement>
  isScrolling: boolean
  scrollToIndex: (index: number) => void
  scrollToTop: () => void
  scrollToBottom: () => void
}

export function useVirtualScroll<T>(
  items: T[],
  options: VirtualScrollOptions
): VirtualScrollResult<T> {
  const {
    itemHeight,
    containerHeight,
    overscan = 5,
    scrollingDelay = 150
  } = options

  const [scrollTop, setScrollTop] = useState(0)
  const [isScrolling, setIsScrolling] = useState(false)
  const scrollElementRef = useRef<HTMLDivElement>(null)
  const scrollingTimeoutRef = useRef<NodeJS.Timeout>()

  // Calculate visible range
  const visibleRange = useMemo(() => {
    const startIndex = Math.floor(scrollTop / itemHeight)
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight),
      items.length - 1
    )

    return {
      start: Math.max(0, startIndex - overscan),
      end: Math.min(items.length - 1, endIndex + overscan)
    }
  }, [scrollTop, itemHeight, containerHeight, items.length, overscan])

  // Generate virtual items
  const virtualItems = useMemo(() => {
    const result = []
    for (let i = visibleRange.start; i <= visibleRange.end; i++) {
      result.push({
        index: i,
        start: i * itemHeight,
        end: (i + 1) * itemHeight,
        item: items[i]
      })
    }
    return result
  }, [visibleRange, itemHeight, items])

  // Total height for scrollbar
  const totalHeight = items.length * itemHeight

  // Handle scroll events
  const handleScroll = useCallback((e: Event) => {
    const target = e.target as HTMLDivElement
    setScrollTop(target.scrollTop)
    setIsScrolling(true)

    // Clear existing timeout
    if (scrollingTimeoutRef.current) {
      clearTimeout(scrollingTimeoutRef.current)
    }

    // Set new timeout
    scrollingTimeoutRef.current = setTimeout(() => {
      setIsScrolling(false)
    }, scrollingDelay)
  }, [scrollingDelay])

  // Scroll to specific index
  const scrollToIndex = useCallback((index: number) => {
    const element = scrollElementRef.current
    if (!element) return

    const targetScrollTop = index * itemHeight
    element.scrollTo({
      top: targetScrollTop,
      behavior: 'smooth'
    })
  }, [itemHeight])

  // Scroll to top
  const scrollToTop = useCallback(() => {
    const element = scrollElementRef.current
    if (!element) return

    element.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
  }, [])

  // Scroll to bottom
  const scrollToBottom = useCallback(() => {
    const element = scrollElementRef.current
    if (!element) return

    element.scrollTo({
      top: totalHeight,
      behavior: 'smooth'
    })
  }, [totalHeight])

  // Setup scroll listener
  useEffect(() => {
    const element = scrollElementRef.current
    if (!element) return

    element.addEventListener('scroll', handleScroll, { passive: true })

    return () => {
      element.removeEventListener('scroll', handleScroll)
      if (scrollingTimeoutRef.current) {
        clearTimeout(scrollingTimeoutRef.current)
      }
    }
  }, [handleScroll])

  return {
    virtualItems,
    totalHeight,
    scrollElementRef,
    isScrolling,
    scrollToIndex,
    scrollToTop,
    scrollToBottom
  }
}

// Hook for virtual table rows
export function useVirtualTable<T>(
  data: T[],
  rowHeight: number = 50,
  containerHeight: number = 400
) {
  const virtualScroll = useVirtualScroll(data, {
    itemHeight: rowHeight,
    containerHeight,
    overscan: 3
  })

  const getRowProps = useCallback((index: number) => ({
    style: {
      position: 'absolute' as const,
      top: index * rowHeight,
      left: 0,
      right: 0,
      height: rowHeight
    }
  }), [rowHeight])

  return {
    ...virtualScroll,
    getRowProps
  }
}

// Hook for infinite scrolling
export function useInfiniteScroll<T>(
  items: T[],
  loadMore: () => Promise<void>,
  hasMore: boolean = true,
  threshold: number = 100
) {
  const [isLoading, setIsLoading] = useState(false)
  const scrollElementRef = useRef<HTMLDivElement>(null)

  const handleScroll = useCallback(async () => {
    const element = scrollElementRef.current
    if (!element || isLoading || !hasMore) return

    const { scrollTop, scrollHeight, clientHeight } = element
    const distanceFromBottom = scrollHeight - scrollTop - clientHeight

    if (distanceFromBottom < threshold) {
      setIsLoading(true)
      try {
        await loadMore()
      } finally {
        setIsLoading(false)
      }
    }
  }, [isLoading, hasMore, threshold, loadMore])

  useEffect(() => {
    const element = scrollElementRef.current
    if (!element) return

    element.addEventListener('scroll', handleScroll, { passive: true })
    return () => element.removeEventListener('scroll', handleScroll)
  }, [handleScroll])

  return {
    scrollElementRef,
    isLoading,
    hasMore
  }
}

// Hook for smooth scrolling with easing
export function useSmoothScroll() {
  const scrollToElement = useCallback((
    element: HTMLElement,
    options: {
      behavior?: 'smooth' | 'auto'
      block?: 'start' | 'center' | 'end' | 'nearest'
      inline?: 'start' | 'center' | 'end' | 'nearest'
    } = {}
  ) => {
    element.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
      inline: 'nearest',
      ...options
    })
  }, [])

  const scrollToPosition = useCallback((
    container: HTMLElement,
    position: { x?: number; y?: number },
    smooth: boolean = true
  ) => {
    container.scrollTo({
      left: position.x,
      top: position.y,
      behavior: smooth ? 'smooth' : 'auto'
    })
  }, [])

  return {
    scrollToElement,
    scrollToPosition
  }
}

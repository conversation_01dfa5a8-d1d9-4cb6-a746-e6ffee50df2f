'use client'

import React, { useState, useEffect } from 'react'
import { useAuth } from '@/app/contexts/auth-context'
import { Button } from '@/app/components/ui/button'
import { Input } from '@/app/components/ui/input'
import { Label } from '@/app/components/ui/label'
import { Textarea } from '@/app/components/ui/textarea'
import { Switch } from '@/app/components/ui/switch'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/app/components/ui/tabs'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/app/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/ui/select'
import { toast } from '@/app/components/ui/use-toast'
import { Avatar, AvatarFallback, AvatarImage } from '@/app/components/ui/avatar'
import { Separator } from '@/app/components/ui/separator'
import { useTheme } from 'next-themes'
import { formatUserName } from '@/app/utils/formatters'
import { User, Mail, Phone, MapPin, Bell, Globe, Upload, Loader2, Shield, Calendar, Lock } from 'lucide-react'
import dynamic from 'next/dynamic'

// Import Header and Sidebar with no SSR to avoid hydration issues
const Header = dynamic(() => import('@/app/components/Header'), { ssr: false })
const Sidebar = dynamic(() => import('@/app/components/Sidebar'), { ssr: false })

export default function ProfilePage() {
  const { user, isLoading, updateProfile } = useAuth()
  const { theme, setTheme } = useTheme()
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [activeTab, setActiveTab] = useState('personal')
  const [avatarFile, setAvatarFile] = useState<File | null>(null)
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null)

  // Profile form state
  const [profileData, setProfileData] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    bio: '',
    language: 'english',
    emailNotifications: true,
    appNotifications: true,
  })

  // Password change state
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  })

  // Account preferences state
  const [preferences, setPreferences] = useState({
    theme: 'system',
    language: 'english',
    timezone: 'UTC',
    dateFormat: 'MM/DD/YYYY',
    timeFormat: '12h',
  })

  // Notification settings state
  const [notifications, setNotifications] = useState({
    emailNotifications: true,
    appNotifications: true,
    attendanceAlerts: true,
    markUpdates: true,
    paymentReminders: true,
    systemUpdates: false,
  })

  // Initialize form with user data when available
  useEffect(() => {
    if (user) {
      setProfileData(prev => ({
        ...prev,
        name: user.name || '',
        email: user.email || '',
        phone: user.phone || '',
        address: user.address || '',
        bio: user.bio || '',
        language: user.language || 'english',
        emailNotifications: user.emailNotifications ?? true,
        appNotifications: user.appNotifications ?? true,
      }))

      setPreferences({
        theme: user.theme || 'system',
        language: user.language || 'english',
        timezone: 'UTC',
        dateFormat: 'MM/DD/YYYY',
        timeFormat: '12h',
      })

      setNotifications({
        emailNotifications: user.emailNotifications ?? true,
        appNotifications: user.appNotifications ?? true,
        attendanceAlerts: true,
        markUpdates: true,
        paymentReminders: true,
        systemUpdates: false,
      })
    }
  }, [user])

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  const toggleMobileSidebar = () => {
    setMobileSidebarOpen(!mobileSidebarOpen)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setProfileData(prev => ({
      ...prev,
      [name]: name === 'name' ? formatUserName(value) : value
    }))
  }

  const handleSwitchChange = (name: string, checked: boolean) => {
    setProfileData(prev => ({
      ...prev,
      [name]: checked
    }))
  }

  const handleSelectChange = (name: string, value: string) => {
    setProfileData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0]
      setAvatarFile(file)

      // Create preview URL
      const reader = new FileReader()
      reader.onloadend = () => {
        setAvatarPreview(reader.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // Basic validation
      if (!profileData.name || !profileData.email) {
        toast({
          title: "Error",
          description: "Name and email are required fields",
          variant: "destructive",
        })
        setIsSubmitting(false)
        return
      }

      // Email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(profileData.email)) {
        toast({
          title: "Error",
          description: "Please enter a valid email address",
          variant: "destructive",
        })
        setIsSubmitting(false)
        return
      }

      // Call the updateProfile function from auth context
      await updateProfile({
        name: profileData.name,
        email: profileData.email,
        phone: profileData.phone,
        address: profileData.address,
        bio: profileData.bio,
        language: profileData.language,
        emailNotifications: profileData.emailNotifications,
        appNotifications: profileData.appNotifications,
      })

      toast({
        title: "Success",
        description: "Profile updated successfully",
      })
    } catch (error) {
      console.error('Error updating profile:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update profile",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // Validation
      if (!passwordData.currentPassword || !passwordData.newPassword || !passwordData.confirmPassword) {
        toast({
          title: "Error",
          description: "All password fields are required",
          variant: "destructive",
        })
        setIsSubmitting(false)
        return
      }

      if (passwordData.newPassword !== passwordData.confirmPassword) {
        toast({
          title: "Error",
          description: "New passwords do not match",
          variant: "destructive",
        })
        setIsSubmitting(false)
        return
      }

      if (passwordData.newPassword.length < 6) {
        toast({
          title: "Error",
          description: "New password must be at least 6 characters long",
          variant: "destructive",
        })
        setIsSubmitting(false)
        return
      }

      // Call password change API
      const response = await fetch('/api/user/password', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          currentPassword: passwordData.currentPassword,
          newPassword: passwordData.newPassword,
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to change password')
      }

      // Reset password form
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
      })

      toast({
        title: "Success",
        description: "Password changed successfully",
      })
    } catch (error) {
      console.error('Error changing password:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to change password",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-600"></div>
      </div>
    )
  }

  return (
    <div className="flex h-screen bg-gray-100 dark:bg-gray-900">
      <Sidebar
        isCollapsed={sidebarCollapsed}
        toggleSidebar={toggleSidebar}
        isMobileOpen={mobileSidebarOpen}
      />

      <main className={`main-content ${sidebarCollapsed ? 'sidebar-collapsed' : ''} w-full h-screen overflow-y-auto`}>
        <Header toggleMobileSidebar={toggleMobileSidebar} />

        <div className="p-6">
          <h1 className="text-2xl font-semibold mb-6 bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent inline-block">Profile Settings</h1>

          <div className="max-w-4xl mx-auto">
            <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-4 mb-8">
                <TabsTrigger value="personal">Personal Info</TabsTrigger>
                <TabsTrigger value="account">Account Settings</TabsTrigger>
                <TabsTrigger value="preferences">Preferences</TabsTrigger>
                <TabsTrigger value="notifications">Notifications</TabsTrigger>
              </TabsList>

              <TabsContent value="personal" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Personal Information</CardTitle>
                    <CardDescription>Update your personal details and contact information</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="flex flex-col md:flex-row gap-6 items-start">
                      <div className="flex flex-col items-center space-y-3">
                        <Avatar className="h-24 w-24">
                          {avatarPreview ? (
                            <AvatarImage src={avatarPreview} alt={profileData.name} />
                          ) : (
                            <>
                              <AvatarImage src={user?.photoUrl || ''} alt={profileData.name} />
                              <AvatarFallback className="text-lg bg-indigo-100 text-indigo-700">
                                {profileData.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                              </AvatarFallback>
                            </>
                          )}
                        </Avatar>
                        <div className="relative">
                          <input
                            type="file"
                            id="avatar-upload"
                            className="sr-only"
                            accept="image/*"
                            onChange={handleAvatarChange}
                          />
                          <label
                            htmlFor="avatar-upload"
                            className="inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-md bg-indigo-50 text-indigo-700 hover:bg-indigo-100 cursor-pointer"
                          >
                            <Upload className="h-3.5 w-3.5 mr-1.5" />
                            Change Photo
                          </label>
                        </div>
                      </div>

                      <div className="flex-1 space-y-4 w-full">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="name" className="flex items-center">
                              <User className="h-4 w-4 mr-2 text-gray-400" />
                              Full Name
                            </Label>
                            <Input
                              id="name"
                              name="name"
                              value={profileData.name}
                              onChange={handleInputChange}
                              placeholder="Your full name"
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="email" className="flex items-center">
                              <Mail className="h-4 w-4 mr-2 text-gray-400" />
                              Email Address
                            </Label>
                            <Input
                              id="email"
                              name="email"
                              type="email"
                              value={profileData.email}
                              onChange={handleInputChange}
                              placeholder="Your email address"
                            />
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="phone" className="flex items-center">
                              <Phone className="h-4 w-4 mr-2 text-gray-400" />
                              Phone Number
                            </Label>
                            <Input
                              id="phone"
                              name="phone"
                              value={profileData.phone}
                              onChange={handleInputChange}
                              placeholder="Your phone number"
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="address" className="flex items-center">
                              <MapPin className="h-4 w-4 mr-2 text-gray-400" />
                              Address
                            </Label>
                            <Input
                              id="address"
                              name="address"
                              value={profileData.address}
                              onChange={handleInputChange}
                              placeholder="Your address"
                            />
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="bio">Bio</Label>
                          <Textarea
                            id="bio"
                            name="bio"
                            value={profileData.bio}
                            onChange={handleInputChange}
                            placeholder="Tell us a little about yourself"
                            rows={4}
                          />
                        </div>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-end">
                    <Button
                      type="submit"
                      onClick={handleSubmit}
                      disabled={isSubmitting}
                    >
                      {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                      Save Changes
                    </Button>
                  </CardFooter>
                </Card>
              </TabsContent>

              <TabsContent value="account" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Account Information</CardTitle>
                    <CardDescription>View your account details and role information</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label className="flex items-center">
                          <Shield className="h-4 w-4 mr-2 text-gray-400" />
                          Role
                        </Label>
                        <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-md">
                          <span className="font-medium text-indigo-600 dark:text-indigo-400">
                            {user?.role?.replace('_', ' ') || 'N/A'}
                          </span>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label className="flex items-center">
                          <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                          Member Since
                        </Label>
                        <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-md">
                          <span className="text-gray-700 dark:text-gray-300">
                            {user?.lastLogin ? new Date(user.lastLogin).toLocaleDateString() : 'N/A'}
                          </span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Change Password</CardTitle>
                    <CardDescription>Update your account password for security</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <form onSubmit={handlePasswordChange} className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="current-password" className="flex items-center">
                          <Lock className="h-4 w-4 mr-2 text-gray-400" />
                          Current Password
                        </Label>
                        <Input
                          id="current-password"
                          type="password"
                          value={passwordData.currentPassword}
                          onChange={(e) => setPasswordData(prev => ({ ...prev, currentPassword: e.target.value }))}
                          placeholder="Enter your current password"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="new-password">New Password</Label>
                        <Input
                          id="new-password"
                          type="password"
                          value={passwordData.newPassword}
                          onChange={(e) => setPasswordData(prev => ({ ...prev, newPassword: e.target.value }))}
                          placeholder="Enter your new password"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="confirm-password">Confirm New Password</Label>
                        <Input
                          id="confirm-password"
                          type="password"
                          value={passwordData.confirmPassword}
                          onChange={(e) => setPasswordData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                          placeholder="Confirm your new password"
                        />
                      </div>
                    </form>
                  </CardContent>
                  <CardFooter className="flex justify-end">
                    <Button
                      onClick={handlePasswordChange}
                      disabled={isSubmitting}
                    >
                      {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                      Change Password
                    </Button>
                  </CardFooter>
                </Card>
              </TabsContent>

              <TabsContent value="preferences" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Display Preferences</CardTitle>
                    <CardDescription>Customize your application appearance and language settings</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="theme">Theme</Label>
                        <select
                          id="theme"
                          value={preferences.theme}
                          onChange={(e) => setPreferences(prev => ({ ...prev, theme: e.target.value }))}
                          className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        >
                          <option value="system">System Default</option>
                          <option value="light">Light</option>
                          <option value="dark">Dark</option>
                        </select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="language">Language</Label>
                        <select
                          id="language"
                          value={preferences.language}
                          onChange={(e) => setPreferences(prev => ({ ...prev, language: e.target.value }))}
                          className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        >
                          <option value="english">English</option>
                          <option value="urdu">Urdu</option>
                          <option value="arabic">Arabic</option>
                        </select>
                      </div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="date-format">Date Format</Label>
                        <select
                          id="date-format"
                          value={preferences.dateFormat}
                          onChange={(e) => setPreferences(prev => ({ ...prev, dateFormat: e.target.value }))}
                          className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        >
                          <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                          <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                          <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                        </select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="time-format">Time Format</Label>
                        <select
                          id="time-format"
                          value={preferences.timeFormat}
                          onChange={(e) => setPreferences(prev => ({ ...prev, timeFormat: e.target.value }))}
                          className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        >
                          <option value="12h">12 Hour</option>
                          <option value="24h">24 Hour</option>
                        </select>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-end">
                    <Button
                      onClick={async () => {
                        try {
                          await updateProfile({
                            ...profileData,
                            theme: preferences.theme,
                            language: preferences.language,
                          })
                          toast({
                            title: "Success",
                            description: "Preferences updated successfully",
                          })
                        } catch (error) {
                          toast({
                            title: "Error",
                            description: "Failed to update preferences",
                            variant: "destructive",
                          })
                        }
                      }}
                      disabled={isSubmitting}
                    >
                      {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                      Save Preferences
                    </Button>
                  </CardFooter>
                </Card>
              </TabsContent>

              <TabsContent value="notifications" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Notification Settings</CardTitle>
                    <CardDescription>Manage how you receive notifications and alerts</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="space-y-1">
                          <Label className="text-base font-medium">Email Notifications</Label>
                          <p className="text-sm text-gray-500">Receive notifications via email</p>
                        </div>
                        <input
                          type="checkbox"
                          checked={notifications.emailNotifications}
                          onChange={(e) => setNotifications(prev => ({ ...prev, emailNotifications: e.target.checked }))}
                          className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="space-y-1">
                          <Label className="text-base font-medium">App Notifications</Label>
                          <p className="text-sm text-gray-500">Receive in-app notifications</p>
                        </div>
                        <input
                          type="checkbox"
                          checked={notifications.appNotifications}
                          onChange={(e) => setNotifications(prev => ({ ...prev, appNotifications: e.target.checked }))}
                          className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="space-y-1">
                          <Label className="text-base font-medium">Attendance Alerts</Label>
                          <p className="text-sm text-gray-500">Get notified about attendance updates</p>
                        </div>
                        <input
                          type="checkbox"
                          checked={notifications.attendanceAlerts}
                          onChange={(e) => setNotifications(prev => ({ ...prev, attendanceAlerts: e.target.checked }))}
                          className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="space-y-1">
                          <Label className="text-base font-medium">Mark Updates</Label>
                          <p className="text-sm text-gray-500">Notifications when marks are updated</p>
                        </div>
                        <input
                          type="checkbox"
                          checked={notifications.markUpdates}
                          onChange={(e) => setNotifications(prev => ({ ...prev, markUpdates: e.target.checked }))}
                          className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="space-y-1">
                          <Label className="text-base font-medium">Payment Reminders</Label>
                          <p className="text-sm text-gray-500">Reminders for upcoming payments</p>
                        </div>
                        <input
                          type="checkbox"
                          checked={notifications.paymentReminders}
                          onChange={(e) => setNotifications(prev => ({ ...prev, paymentReminders: e.target.checked }))}
                          className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="space-y-1">
                          <Label className="text-base font-medium">System Updates</Label>
                          <p className="text-sm text-gray-500">Notifications about system maintenance and updates</p>
                        </div>
                        <input
                          type="checkbox"
                          checked={notifications.systemUpdates}
                          onChange={(e) => setNotifications(prev => ({ ...prev, systemUpdates: e.target.checked }))}
                          className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                        />
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-end">
                    <Button
                      onClick={async () => {
                        try {
                          await updateProfile({
                            ...profileData,
                            emailNotifications: notifications.emailNotifications,
                            appNotifications: notifications.appNotifications,
                          })
                          toast({
                            title: "Success",
                            description: "Notification settings updated successfully",
                          })
                        } catch (error) {
                          toast({
                            title: "Error",
                            description: "Failed to update notification settings",
                            variant: "destructive",
                          })
                        }
                      }}
                      disabled={isSubmitting}
                    >
                      {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                      Save Notifications
                    </Button>
                  </CardFooter>
                </Card>
              </TabsContent>

            </Tabs>
          </div>
        </div>
      </main>

      {/* Overlay for mobile menu */}
      {mobileSidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 md:hidden z-40"
          onClick={toggleMobileSidebar}
        ></div>
      )}
    </div>
  )
}

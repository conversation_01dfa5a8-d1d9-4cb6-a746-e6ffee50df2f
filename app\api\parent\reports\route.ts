import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { cookies } from 'next/headers'
import { verifyJWT } from '@/lib/jwt'

// GET report card access for parent's children
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const studentId = searchParams.get('studentId')
    const semester = searchParams.get('semester') || 'First Semester'
    const academicYear = searchParams.get('academicYear') || '2023-2024'

    // Verify authentication
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    }

    const decoded = await verifyJWT(token)
    
    // Only PARENT role can access this endpoint
    if (decoded.role !== 'PARENT') {
      return NextResponse.json(
        { error: 'Forbidden - Only parents can access this endpoint' },
        { status: 403 }
      )
    }

    const parentId = decoded.id

    // If studentId is provided, verify the parent has access to this student
    if (studentId) {
      const relationship = await prisma.parentStudent.findUnique({
        where: {
          parentId_studentId: {
            parentId,
            studentId
          }
        }
      })

      if (!relationship) {
        return NextResponse.json(
          { error: 'Forbidden - You do not have access to this student' },
          { status: 403 }
        )
      }
    }

    // Check semester access restrictions
    const currentDate = new Date()
    const currentMonth = currentDate.getMonth() + 1 // 1-12
    const currentYear = currentDate.getFullYear()
    
    // Define semester periods
    const semesterPeriods = {
      'First Semester': {
        startMonth: 1,
        endMonth: 6,
        accessMonth: 6 // June - end of first semester
      },
      'Second Semester': {
        startMonth: 7,
        endMonth: 12,
        accessMonth: 12 // December - end of second semester
      }
    }

    // Check if report card access is allowed
    let accessAllowed = false
    let accessMessage = ''
    
    if (semester === 'First Semester') {
      // First semester reports available from June onwards
      accessAllowed = currentMonth >= 6
      accessMessage = accessAllowed 
        ? 'First semester report card is available'
        : 'You will have access to the first semester report card at the end of semester 1 (June)'
    } else if (semester === 'Second Semester') {
      // Second semester reports available from December onwards
      accessAllowed = currentMonth >= 12
      accessMessage = accessAllowed 
        ? 'Second semester report card is available'
        : 'You will have access to the second semester report card at the end of semester 2 (December)'
    } else if (semester === 'Annual') {
      // Annual reports available from December onwards (after both semesters)
      accessAllowed = currentMonth >= 12
      accessMessage = accessAllowed 
        ? 'Annual report card is available'
        : 'You will have access to the annual report card at the end of semester 2 (December)'
    }

    // Get children data
    let studentIds: string[] = []
    if (studentId) {
      studentIds = [studentId]
    } else {
      const relationships = await prisma.parentStudent.findMany({
        where: { parentId },
        select: { studentId: true }
      })
      studentIds = relationships.map(rel => rel.studentId)
    }

    if (studentIds.length === 0) {
      return NextResponse.json({
        students: [],
        accessAllowed: false,
        message: 'No children found for this parent'
      })
    }

    // Get student details
    const students = await prisma.student.findMany({
      where: { id: { in: studentIds } },
      select: {
        id: true,
        sid: true,
        name: true,
        className: true,
        academicYear: true,
        photoUrl: true
      }
    })

    // If access is not allowed, return limited data
    if (!accessAllowed) {
      return NextResponse.json({
        students,
        accessAllowed: false,
        accessMessage,
        semester,
        academicYear,
        message: 'Report card access restricted based on semester schedule'
      })
    }

    // If access is allowed, fetch report card data
    const reportData = await Promise.all(
      studentIds.map(async (sId) => {
        const student = students.find(s => s.id === sId)
        
        // Get marks for the requested semester/term
        let termFilter: string[] = []
        if (semester === 'First Semester') {
          termFilter = ['First Semester']
        } else if (semester === 'Second Semester') {
          termFilter = ['Second Semester']
        } else if (semester === 'Annual') {
          termFilter = ['First Semester', 'Second Semester']
        }

        const marks = await prisma.mark.findMany({
          where: {
            studentId: sId,
            term: { in: termFilter }
          },
          orderBy: [
            { subject: 'asc' },
            { term: 'asc' }
          ]
        })

        // Get attendance data for the semester
        let attendanceFilter: any = { studentId: sId }
        const year = parseInt(academicYear.split('-')[0])

        if (semester === 'First Semester') {
          // First semester: September 1st to January 30th (next year)
          attendanceFilter.date = {
            gte: `${year}-09-01`,
            lte: `${year + 1}-01-30`
          }
        } else if (semester === 'Second Semester') {
          // Second semester: February 1st to June 30th (next year)
          attendanceFilter.date = {
            gte: `${year + 1}-02-01`,
            lte: `${year + 1}-06-30`
          }
        } else {
          // Annual - full academic year: September 1st to June 30th (next year)
          attendanceFilter.date = {
            gte: `${year}-09-01`,
            lte: `${year + 1}-06-30`
          }
        }

        const attendance = await prisma.attendance.findMany({
          where: attendanceFilter
        })

        // Calculate statistics
        const totalDays = attendance.length
        const presentDays = attendance.filter(a => a.status === 'present').length
        const absentDays = attendance.filter(a => a.status === 'absent').length
        const permissionDays = attendance.filter(a => a.status === 'permission').length
        const attendancePercentage = totalDays > 0 ? Math.round((presentDays / totalDays) * 100) : 0

        // Group marks by subject
        const subjectMarks = marks.reduce((acc: any, mark) => {
          if (!acc[mark.subject]) {
            acc[mark.subject] = {}
          }
          acc[mark.subject][mark.term] = mark.marks
          return acc
        }, {})

        // Calculate overall average
        const totalMarks = marks.reduce((sum, mark) => sum + mark.marks, 0)
        const averageMarks = marks.length > 0 ? Math.round(totalMarks / marks.length) : 0

        return {
          student,
          marks: subjectMarks,
          attendance: {
            totalDays,
            presentDays,
            absentDays,
            permissionDays,
            attendancePercentage
          },
          academicPerformance: {
            averageMarks,
            totalSubjects: Object.keys(subjectMarks).length,
            highestMark: marks.length > 0 ? Math.max(...marks.map(m => m.marks)) : 0,
            lowestMark: marks.length > 0 ? Math.min(...marks.map(m => m.marks)) : 0
          }
        }
      })
    )

    return NextResponse.json({
      students,
      reportData,
      accessAllowed: true,
      accessMessage,
      semester,
      academicYear,
      message: 'Report card data retrieved successfully'
    })
  } catch (error) {
    console.error('Error fetching report card data:', error)
    return NextResponse.json(
      { error: 'Failed to fetch report card data' },
      { status: 500 }
    )
  }
}

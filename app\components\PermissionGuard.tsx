'use client'

import React, { useState, useEffect } from 'react'
import { useAuth } from '@/app/contexts/auth-context'
import PermissionDialog from './PermissionDialog'

interface PermissionGuardProps {
  permission: string | string[]
  fallback?: React.ReactNode
  children: React.ReactNode
  showDialog?: boolean
  dialogMessage?: string
  dialogTitle?: string
  resource?: string
  action?: string
}

/**
 * A component that conditionally renders its children based on user permissions.
 *
 * @param permission - A single permission string or array of permissions (any one is sufficient)
 * @param fallback - Optional element to render if user doesn't have permission
 * @param children - Elements to render if user has permission
 * @param showDialog - Whether to show a permission denied dialog when access is denied
 * @param dialogMessage - Custom message for the permission denied dialog
 * @param dialogTitle - Custom title for the permission denied dialog
 * @param resource - The resource being accessed (e.g., "marks", "attendance")
 * @param action - The action being performed (e.g., "view", "edit")
 */
export function PermissionGuard({
  permission,
  fallback = null,
  children,
  showDialog = true,
  dialogMessage,
  dialogTitle = 'Permission Denied',
  resource = 'feature',
  action = 'access'
}: PermissionGuardProps) {
  const { hasPermission } = useAuth()
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  // Check if user has any of the required permissions
  const hasAccess = Array.isArray(permission)
    ? permission.some(p => hasPermission(p))
    : hasPermission(permission)

  // Show dialog when access is denied and showDialog is true
  useEffect(() => {
    if (!hasAccess && showDialog) {
      setIsDialogOpen(true)
    }
  }, [hasAccess, showDialog])

  // Generate permission message based on the permission(s)
  const getPermissionMessage = () => {
    if (dialogMessage) return dialogMessage;

    const permissionText = Array.isArray(permission)
      ? permission.join(', ')
      : permission;

    return `You do not have permission to ${action} this ${resource}.\n\nRequired permission: ${permissionText}\n\nPlease contact your administrator if you believe this is an error.`;
  }

  return (
    <>
      {/* Permission Denied Dialog */}
      {showDialog && (
        <PermissionDialog
          isOpen={isDialogOpen}
          onClose={() => setIsDialogOpen(false)}
          title={dialogTitle}
          message={getPermissionMessage()}
          resource={resource}
          action={action}
        />
      )}

      {/* Render children if user has permission, otherwise render fallback */}
      {hasAccess ? <>{children}</> : <>{fallback}</>}
    </>
  )
}

export default PermissionGuard

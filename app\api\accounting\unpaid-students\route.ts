import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// Mock data for unpaid students (used as fallback)
const mockUnpaidStudents = [
  {
    id: 'unpaid1',
    invoiceNumber: 'UNPAID-001',
    studentId: 'STD005',
    studentName: '<PERSON>',
    className: '8E',
    paymentDate: new Date().toISOString().split('T')[0],
    amount: 1500,
    paymentPurpose: 'Tuition Fee (Monthly)',
    paymentMethod: 'pending',
    status: 'unpaid',
    createdAt: new Date().toISOString()
  },
  {
    id: 'unpaid2',
    invoiceNumber: 'UNPAID-002',
    studentId: 'STD006',
    studentName: '<PERSON><PERSON>',
    className: '7A',
    paymentDate: new Date().toISOString().split('T')[0],
    amount: 1500,
    paymentPurpose: 'Tuition Fee (Monthly)',
    paymentMethod: 'pending',
    status: 'unpaid',
    createdAt: new Date().toISOString()
  }
];

// Helper function to check if a model exists in Prisma
function isModelAvailable(modelName: string): boolean {
  return Object.prototype.hasOwnProperty.call(prisma, modelName);
}

export async function GET(request: Request) {
  try {
    // Get URL parameters
    const { searchParams } = new URL(request.url)
    const className = searchParams.get('className')
    const paymentPurpose = searchParams.get('paymentPurpose')

    // Both className and paymentPurpose are required
    if (!className || !paymentPurpose) {
      return NextResponse.json(
        { error: 'Both className and paymentPurpose are required' },
        { status: 400 }
      )
    }

    // Check if Student and Payment models are available
    if (!isModelAvailable('student') || !isModelAvailable('payment') || !isModelAvailable('feeType')) {
      console.warn('Required models are not available in Prisma client. Using mock data.');
      
      // Filter the mock data based on the parameters
      let filteredData = [...mockUnpaidStudents];
      
      if (className) {
        filteredData = filteredData.filter(student => student.className === className);
      }
      
      if (paymentPurpose) {
        filteredData = filteredData.filter(student => student.paymentPurpose === paymentPurpose);
      }
      
      return NextResponse.json(filteredData);
    }

    try {
      // Step 1: Find the fee type ID for the payment purpose
      const feeType = await prisma.feeType.findFirst({
        where: {
          name: paymentPurpose
        }
      });

      if (!feeType) {
        return NextResponse.json([], { status: 200 });
      }

      // Step 2: Get all students in the specified class
      const studentsInClass = await prisma.student.findMany({
        where: {
          className: className
        },
        select: {
          id: true,
          sid: true,
          name: true,
          className: true
        }
      });

      // Step 3: Get all students who have already paid for this fee type
      const paidStudents = await prisma.payment.findMany({
        where: {
          feeTypeId: feeType.id,
          student: {
            className: className
          },
          status: 'paid'
        },
        select: {
          studentId: true
        }
      });

      // Create a set of student IDs who have paid
      const paidStudentIds = new Set(paidStudents.map(payment => payment.studentId));

      // Step 4: Filter out students who have already paid
      const unpaidStudents = studentsInClass.filter(student => !paidStudentIds.has(student.id));

      // Step 5: Format the response to match the payment history format
      const formattedUnpaidStudents = unpaidStudents.map((student, index) => ({
        id: `unpaid-${student.id}`,
        invoiceNumber: `UNPAID-${index + 1}`,
        studentId: student.sid,
        studentName: student.name,
        className: student.className,
        paymentDate: new Date().toISOString().split('T')[0],
        amount: feeType.amount,
        paymentPurpose: feeType.name,
        paymentMethod: 'pending',
        status: 'unpaid',
        createdAt: new Date().toISOString()
      }));

      return NextResponse.json(formattedUnpaidStudents);
    } catch (dbError) {
      console.error('Database error fetching unpaid students:', dbError);
      
      // If there's a database error, fall back to mock data
      console.warn('Falling back to mock unpaid students data');
      return NextResponse.json(mockUnpaidStudents);
    }
  } catch (error) {
    console.error('Error fetching unpaid students:', error);
    
    // Return mock data as a fallback
    console.warn('Error in unpaid students API, returning mock data');
    return NextResponse.json(mockUnpaidStudents);
  }
}

import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { formatUserName } from '@/app/utils/formatters'

export async function GET() {
  try {
    console.log('Fetching all users')

    // Fetch all users from the database
    const users = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        status: true,
        createdAt: true,
        updatedAt: true,
        lastLogin: true,
      },
      orderBy: {
        name: 'asc',
      },
    })

    console.log(`Found ${users.length} users`)
    return NextResponse.json(users)
  } catch (error) {
    console.error('Error fetching users:', error)
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    )
  }
}

// POST to create a new user
export async function POST(request: Request) {
  try {
    console.log('Creating new user')

    // Get the request body
    const data = await request.json()

    // Validate the user data
    if (!data.name || !data.email || !data.password) {
      console.error('Invalid user data:', { name: !!data.name, email: !!data.email, password: !!data.password })
      return NextResponse.json(
        { error: 'Name, email, and password are required' },
        { status: 400 }
      )
    }

    // Check if the email is already in use
    const existingUser = await prisma.user.findUnique({
      where: { email: data.email },
    })

    if (existingUser) {
      console.error(`Email ${data.email} is already in use`)
      return NextResponse.json(
        { error: 'Email is already in use' },
        { status: 400 }
      )
    }

    // Create the user
    const newUser = await prisma.user.create({
      data: {
        name: formatUserName(data.name),
        email: data.email,
        password: data.password, // In a real app, this would be hashed
        role: data.role || 'USER',
        status: 'active',
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        status: true,
        createdAt: true,
      },
    })

    console.log(`Created new user: ${newUser.email}`)
    return NextResponse.json({
      message: 'User created successfully',
      user: newUser
    })
  } catch (error) {
    console.error('Error creating user:', error)
    return NextResponse.json(
      { error: 'Failed to create user' },
      { status: 500 }
    )
  }
}

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import bcrypt from 'bcryptjs';

export async function GET() {
  try {
    console.log('Initializing database with sample data...');
    console.log('Database URL:', process.env.DATABASE_URL);

    // Check if we already have data
    const userCount = await prisma.user.count();
    const classCount = await prisma.class.count();
    const teacherCount = await prisma.teacher.count();
    const studentCount = await prisma.student.count();

    if (userCount > 0 && classCount > 0 && teacherCount > 0 && studentCount > 0) {
      console.log('Database already has data, skipping initialization');
      return NextResponse.json({
        status: 'success',
        message: 'Database already initialized',
        counts: {
          users: userCount,
          classes: classCount,
          teachers: teacherCount,
          students: studentCount
        }
      });
    }

    // Create admin user if it doesn't exist
    if (userCount === 0) {
      console.log('Creating admin user');
      const hashedPassword = await bcrypt.hash('admin@123', 10);
      await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: hashedPassword,
          name: 'Israel Admin',
          role: 'SUPER_ADMIN',
          status: 'active'
        }
      });
      console.log('Admin user created successfully');

      // Create sample teacher users for HR assignment functionality
      console.log('Creating sample teacher users');
      const teacherPassword = await bcrypt.hash('teacher@123', 10);

      const teacherUsers = [
        {
          email: '<EMAIL>',
          password: teacherPassword,
          name: 'John Smith',
          role: 'TEACHER',
          status: 'active'
        },
        {
          email: '<EMAIL>',
          password: teacherPassword,
          name: 'Sarah Johnson',
          role: 'TEACHER',
          status: 'active'
        },
        {
          email: '<EMAIL>',
          password: teacherPassword,
          name: 'Michael Brown',
          role: 'TEACHER',
          status: 'active'
        },
        {
          email: '<EMAIL>',
          password: teacherPassword,
          name: 'Emma Davis',
          role: 'TEACHER',
          status: 'active'
        },
        {
          email: '<EMAIL>',
          password: teacherPassword,
          name: 'David Wilson',
          role: 'TEACHER',
          status: 'active'
        }
      ];

      await prisma.user.createMany({
        data: teacherUsers
      });
      console.log('Sample teacher users created successfully');
    }

    // Create classes if they don't exist
    if (classCount === 0) {
      console.log('Creating sample classes');
      await prisma.class.createMany({
        data: [
          { name: '1A', totalStudents: 0, totalSubjects: 0 },
          { name: '1B', totalStudents: 0, totalSubjects: 0 },
          { name: '2A', totalStudents: 0, totalSubjects: 0 },
          { name: '2B', totalStudents: 0, totalSubjects: 0 }
        ]
      });
      console.log('Sample classes created successfully');
    }

    // Create teachers if they don't exist
    if (teacherCount === 0) {
      console.log('Creating sample teachers');
      await prisma.teacher.createMany({
        data: [
          {
            name: 'John Smith',
            fatherName: 'David Smith',
            gender: 'Male',
            email: '<EMAIL>',
            subject: 'Mathematics',
            mobile: '1234567890'
          },
          {
            name: 'Sarah Johnson',
            fatherName: 'Robert Johnson',
            gender: 'Female',
            email: '<EMAIL>',
            subject: 'English',
            mobile: '2345678901'
          },
          {
            name: 'Michael Brown',
            fatherName: 'James Brown',
            gender: 'Male',
            email: '<EMAIL>',
            subject: 'Science',
            mobile: '3456789012'
          }
        ]
      });
      console.log('Sample teachers created successfully');
    }

    // Create students if they don't exist
    if (studentCount === 0) {
      console.log('Creating sample students');

      // Get class IDs
      const classes = await prisma.class.findMany({
        select: { id: true, name: true }
      });

      if (classes.length > 0) {
        // Create sample students
        const students = [
          {
            sid: '1A1',
            name: 'Alice Cooper',
            className: '1A',
            fatherName: 'John Cooper',
            gfName: 'William Cooper',
            age: 6,
            gender: 'Female'
          },
          {
            sid: '1A2',
            name: 'Bob Wilson',
            className: '1A',
            fatherName: 'Thomas Wilson',
            gfName: 'George Wilson',
            age: 6,
            gender: 'Male'
          },
          {
            sid: '1B1',
            name: 'Charlie Davis',
            className: '1B',
            fatherName: 'Richard Davis',
            gfName: 'Edward Davis',
            age: 6,
            gender: 'Male'
          },
          {
            sid: '2A1',
            name: 'Diana Evans',
            className: '2A',
            fatherName: 'Michael Evans',
            gfName: 'Joseph Evans',
            age: 7,
            gender: 'Female'
          },
        ];

        for (const studentData of students) {
          await prisma.student.create({
            data: studentData
          });
        }
        console.log('Sample students created successfully');

        // Update class student counts
        for (const cls of classes) {
          const count = await prisma.student.count({
            where: { className: cls.name }
          });

          await prisma.class.update({
            where: { id: cls.id },
            data: { totalStudents: count }
          });

          console.log(`Updated class ${cls.name} with ${count} students`);
        }
      }
    }

    // Create subjects if they don't exist
    const subjectCount = await prisma.subject.count();
    if (subjectCount === 0) {
      console.log('Creating sample subjects');

      // Get class IDs
      const classes = await prisma.class.findMany({
        select: { id: true, name: true }
      });

      if (classes.length > 0) {
        // Define subjects for each class
        const subjectsData = [];

        for (const cls of classes) {
          const classSubjects = [
            { name: 'Mathematics', classId: cls.id },
            { name: 'English', classId: cls.id },
            { name: 'Science', classId: cls.id },
            { name: 'Social Studies', classId: cls.id },
            { name: 'Physical Education', classId: cls.id }
          ];

          // Add unique identifier to subject names to avoid conflicts
          const uniqueSubjects = classSubjects.map((subject, index) => ({
            ...subject,
            name: `${subject.name} (${cls.name})`
          }));

          subjectsData.push(...uniqueSubjects);
        }

        // Create subjects
        for (const subjectData of subjectsData) {
          await prisma.subject.create({
            data: subjectData
          });
        }
        console.log('Sample subjects created successfully');

        // Update class subject counts
        for (const cls of classes) {
          const count = await prisma.subject.count({
            where: { classId: cls.id }
          });

          await prisma.class.update({
            where: { id: cls.id },
            data: { totalSubjects: count }
          });

          console.log(`Updated class ${cls.name} with ${count} subjects`);
        }
      }
    }

    // Create roles and permissions if they don't exist
    const roleCount = await prisma.role.count();
    if (roleCount === 0) {
      console.log('Creating roles and permissions');

      // Create permissions
      const permissionCategories = {
        students: [
          { name: 'View Students', description: 'View student information' },
          { name: 'Add Students', description: 'Add new students' },
          { name: 'Edit Students', description: 'Edit student information' },
          { name: 'Delete Students', description: 'Delete students' }
        ],
        classes: [
          { name: 'View Classes', description: 'View class information' },
          { name: 'Add Classes', description: 'Add new classes' },
          { name: 'Edit Classes', description: 'Edit class information' },
          { name: 'Delete Classes', description: 'Delete classes' }
        ],
        teachers: [
          { name: 'View Teachers', description: 'View teacher information' },
          { name: 'Add Teachers', description: 'Add new teachers' },
          { name: 'Edit Teachers', description: 'Edit teacher information' },
          { name: 'Delete Teachers', description: 'Delete teachers' }
        ],
        marks: [
          { name: 'View Marks', description: 'View student marks' },
          { name: 'Add Marks', description: 'Add new marks' },
          { name: 'Edit Marks', description: 'Edit marks' },
          { name: 'Delete Marks', description: 'Delete marks' }
        ],
        attendance: [
          { name: 'View Attendance', description: 'View attendance records' },
          { name: 'Add Attendance', description: 'Add attendance records' },
          { name: 'Edit Attendance', description: 'Edit attendance records' }
        ],
        reports: [
          { name: 'Generate Reports', description: 'Generate reports' }
        ],
        system: [
          { name: 'System Settings', description: 'Manage system settings' },
          { name: 'Manage Users', description: 'Manage user accounts' },
          { name: 'Manage Roles', description: 'Manage roles and permissions' }
        ]
      };

      const createdPermissions = {};

      // Create permissions
      for (const category in permissionCategories) {
        for (const permission of permissionCategories[category]) {
          const createdPermission = await prisma.permission.create({
            data: {
              name: permission.name,
              description: permission.description,
              category
            }
          });

          createdPermissions[permission.name] = createdPermission.id;
        }
      }
      console.log('Permissions created successfully');

      // Create roles
      const adminRole = await prisma.role.create({
        data: {
          name: 'Administrator',
          description: 'Full system access',
          systemDefined: true
        }
      });

      const hrTeacherRole = await prisma.role.create({
        data: {
          name: 'HR Teacher',
          description: 'Homeroom teacher with class management permissions',
          systemDefined: true
        }
      });

      const subjectTeacherRole = await prisma.role.create({
        data: {
          name: 'Subject Teacher',
          description: 'Subject teacher with limited permissions',
          systemDefined: true
        }
      });
      console.log('Roles created successfully');

      // Assign all permissions to admin role
      for (const permName in createdPermissions) {
        await prisma.rolePermission.create({
          data: {
            roleId: adminRole.id,
            permissionId: createdPermissions[permName]
          }
        });
      }

      // Assign specific permissions to HR Teacher
      const hrTeacherPermissions = [
        'View Students', 'Edit Students',
        'View Classes', 'Edit Classes',
        'View Marks', 'Add Marks', 'Edit Marks',
        'View Attendance', 'Add Attendance', 'Edit Attendance',
        'Generate Reports'
      ];

      for (const permName of hrTeacherPermissions) {
        if (createdPermissions[permName]) {
          await prisma.rolePermission.create({
            data: {
              roleId: hrTeacherRole.id,
              permissionId: createdPermissions[permName]
            }
          });
        }
      }

      // Assign specific permissions to Subject Teacher
      const subjectTeacherPermissions = [
        'View Students',
        'View Classes',
        'View Marks', 'Add Marks', 'Edit Marks',
        'View Attendance', 'Add Attendance', 'Edit Attendance'
      ];

      for (const permName of subjectTeacherPermissions) {
        if (createdPermissions[permName]) {
          await prisma.rolePermission.create({
            data: {
              roleId: subjectTeacherRole.id,
              permissionId: createdPermissions[permName]
            }
          });
        }
      }
      console.log('Role permissions assigned successfully');
    }

    // Return success response
    return NextResponse.json({
      status: 'success',
      message: 'Database initialized successfully',
      counts: {
        users: await prisma.user.count(),
        classes: await prisma.class.count(),
        teachers: await prisma.teacher.count(),
        students: await prisma.student.count(),
        subjects: await prisma.subject.count(),
        roles: await prisma.role.count(),
        permissions: await prisma.permission.count()
      }
    });
  } catch (error) {
    console.error('Error initializing database:', error);

    // Return detailed error information
    return NextResponse.json({
      status: 'error',
      message: 'Failed to initialize database',
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      databaseUrl: process.env.DATABASE_URL ? '***' + process.env.DATABASE_URL.substring(process.env.DATABASE_URL.indexOf('@')) : 'Not set',
      suggestion: 'Please check your database connection string and ensure the database server is running.'
    }, { status: 500 });
  } finally {
    // No need to disconnect since we're using the shared prisma instance
  }
}

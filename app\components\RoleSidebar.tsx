"use client"

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import { useAuth } from '../contexts/auth-context'
import { sidebarConfig, hasAccess } from '../config/sidebar-config'
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from './ui/accordion'
import {
  Home as HomeIcon,
  User as UserIcon,
  UserPlus as UserPlusIcon,
  ClipboardList,
  CalendarCheck,
  ListChecks,
  FileText,
  FileDigit,
  ShieldCheck,
  KeyRound,
  Settings,
  Menu as MenuIcon,
  BarChart,
  DollarSign,
  Receipt,
  FileSpreadsheet,
  Printer,
  CreditCard,
  ChevronDown,
  Globe,
  Image,
  Bell,
  Link2,
  Newspaper,
  MessageSquare,
  MousePointer,
  Hash,
  Wallet,
  Users,
  Eye
} from 'lucide-react'

interface SidebarProps {
  isCollapsed: boolean
  toggleSidebar: () => void
  isMobileOpen: boolean
}

// Map of icon names to icon components
const iconMap: Record<string, React.ReactNode> = {
  HomeIcon: <HomeIcon size={20} className="sidebar-icon" />,
  UserIcon: <UserIcon size={20} className="sidebar-icon" />,
  UserPlusIcon: <UserPlusIcon size={20} className="sidebar-icon" />,
  ClipboardList: <ClipboardList size={20} className="sidebar-icon" />,
  CalendarCheck: <CalendarCheck size={20} className="sidebar-icon" />,
  ListChecks: <ListChecks size={20} className="sidebar-icon" />,
  FileText: <FileText size={20} className="sidebar-icon" />,
  FileDigit: <FileDigit size={20} className="sidebar-icon" />,
  ShieldCheck: <ShieldCheck size={20} className="sidebar-icon" />,
  KeyRound: <KeyRound size={20} className="sidebar-icon" />,
  Settings: <Settings size={20} className="sidebar-icon" />,
  BarChart: <BarChart size={20} className="sidebar-icon" />,
  DollarSign: <DollarSign size={20} className="sidebar-icon" />,
  Globe: <Globe size={20} className="sidebar-icon" />,
  CreditCard: <CreditCard size={16} className="sidebar-subicon" />,
  Hash: <Hash size={16} className="sidebar-subicon" />,
  Receipt: <Receipt size={16} className="sidebar-subicon" />,
  FileSpreadsheet: <FileSpreadsheet size={16} className="sidebar-subicon" />,
  Image: <Image size={16} className="sidebar-subicon" />,
  Bell: <Bell size={16} className="sidebar-subicon" />,
  Link2: <Link2 size={16} className="sidebar-subicon" />,
  Newspaper: <Newspaper size={16} className="sidebar-subicon" />,
  MessageSquare: <MessageSquare size={16} className="sidebar-subicon" />,
  Wallet: <Wallet size={16} className="sidebar-subicon" />,
  MousePointer: <MousePointer size={16} className="sidebar-subicon" />,
  Users: <Users size={20} className="sidebar-icon" />,
  Eye: <Eye size={16} className="sidebar-subicon" />,
  User: <UserIcon size={20} className="sidebar-icon" />
}

const RoleSidebar: React.FC<SidebarProps> = ({
  isCollapsed,
  toggleSidebar,
  isMobileOpen
}) => {
  const { user, hasSidebarAccess } = useAuth()
  const [activeItem, setActiveItem] = useState('dashboard')
  const [expandedAccordions, setExpandedAccordions] = useState<string[]>([])

  // Get user initial and name from the authenticated user
  const userInitial = user?.name ? user.name.charAt(0).toUpperCase() : 'A'
  const userName = user?.name || 'User'
  const userRole = user?.role || ''

  const sidebarClass = `sidebar ${isCollapsed ? 'collapsed' : ''} ${isMobileOpen ? 'mobile-open' : ''}`

  const toggleAccordion = (value: string) => {
    if (isCollapsed) return

    setExpandedAccordions(prev =>
      prev.includes(value)
        ? prev.filter(item => item !== value)
        : [...prev, value]
    )
  }

  // Reset active item if user doesn't have access to it
  useEffect(() => {
    if (activeItem !== 'dashboard' && !hasSidebarAccess(activeItem)) {
      // If user doesn't have access to the active item, reset to dashboard
      setActiveItem('dashboard');
    }
  }, [activeItem, userRole]);

  // Filter sidebar items based on user role
  const filteredSidebarItems = sidebarConfig.filter(item =>
    hasSidebarAccess(item.id)
  )

  return (
    <aside className={sidebarClass}>
      <div className="py-4 px-4 flex items-center justify-between border-b border-white/10">
        <div className="flex items-center">
          <div className="w-8 h-8 rounded-md bg-white text-akademi-red flex items-center justify-center font-bold text-xl mr-3">
            {userInitial}
          </div>
          {!isCollapsed && <h1 className="text-xl font-bold sidebar-text">{userName}</h1>}
        </div>
        <button
          onClick={toggleSidebar}
          className="text-white p-1 rounded-md hover:bg-white/10"
        >
          <MenuIcon size={20} />
        </button>
      </div>

      <nav className="py-4">
        {filteredSidebarItems.map(item => {
          // If the item has children, render as accordion
          if (item.children && item.children.length > 0) {
            // Filter children based on access rights
            const filteredChildren = item.children.filter(child => {
              // The ID format in auth context is 'parent-child'
              return hasSidebarAccess(`${item.id}-${child.id}`);
            })

            // Skip if no children are accessible
            if (filteredChildren.length === 0) return null

            return (
              <div key={item.id} className={`${isCollapsed ? 'collapsed-accordion' : ''}`}>
                {isCollapsed ?
                  /* Only render link if user has access */
                  (hasSidebarAccess(item.id) ? (
                    <Link href={item.path} className="block">
                      <div
                        className={`sidebar-item ${activeItem.startsWith(item.id) ? 'active' : ''}`}
                        onClick={() => setActiveItem(item.id)}
                      >
                        {iconMap[item.icon]}
                      </div>
                    </Link>
                  ) : (
                    <div className="sidebar-item opacity-50 cursor-not-allowed">
                      {iconMap[item.icon]}
                    </div>
                  ))
                : (
                  <Accordion
                    type="multiple"
                    value={expandedAccordions}
                    className="sidebar-accordion"
                  >
                    <AccordionItem value={item.id} className="border-none">
                      <div
                        className={`sidebar-item accordion-trigger ${activeItem.startsWith(item.id) ? 'active' : ''}`}
                        onClick={() => {
                          // Only toggle accordion if user has access to this item
                          if (hasSidebarAccess(item.id)) {
                            toggleAccordion(item.id);
                          }
                        }}
                      >
                        <div className="flex items-center">
                          {iconMap[item.icon]}
                          <span className="sidebar-text">{item.label}</span>
                        </div>
                        <ChevronDown
                          size={16}
                          className={`transition-transform duration-200 ${expandedAccordions.includes(item.id) ? 'rotate-180' : ''}`}
                        />
                      </div>
                      <AccordionContent className="accordion-content">
                        {filteredChildren.map(child => (
                          <Link key={child.id} href={child.path} className="block">
                            <div
                              className={`sidebar-subitem ${activeItem === `${item.id}-${child.id}` ? 'active' : ''}`}
                              onClick={() => {
                                // Set the active item to the child item ID
                                setActiveItem(`${item.id}-${child.id}`);
                              }}
                            >
                              {iconMap[child.icon]}
                              <span className="sidebar-text">{child.label}</span>
                            </div>
                          </Link>
                        ))}
                      </AccordionContent>
                    </AccordionItem>
                  </Accordion>
                )}
              </div>
            )
          }

          // Otherwise, render as a simple link if user has access
          return hasSidebarAccess(item.id) ? (
            <Link key={item.id} href={item.path} className="block">
              <div
                className={`sidebar-item ${activeItem === item.id ? 'active' : ''}`}
                onClick={() => setActiveItem(item.id)}
              >
                {iconMap[item.icon]}
                {!isCollapsed && <span className="sidebar-text">{item.label}</span>}
              </div>
            </Link>
          ) : null
        })}
      </nav>
    </aside>
  )
}

export default RoleSidebar

"use client"

import { useEffect, useState } from 'react'
import { <PERSON><PERSON> } from './ui/button'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/table'
import { Input } from './ui/input'
import { Search, Edit, Trash2, SlidersHorizontal, Plus, User, Bookmark, Upload, Users, Loader2, AlertTriangle } from 'lucide-react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from './ui/use-toast'
import { formatStudentName } from '@/app/utils/formatters'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "./ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select"
import { Label } from "./ui/label"
import { CustomSelect } from './ui/custom-select'

interface Class {
  id: string
  name: string
  hrTeacherId: string | null
  totalStudents: number
  totalSubjects: number
  createdAt: string
  updatedAt: string
}

interface Student {
  id: string
  sid: string
  name: string
  fatherName: string
  gfName: string
  class?: string | Class
  className?: string
  age: number
  gender: string
}

// Helper function to get class name from student object
function getClassName(student: Student): string {
  if (typeof student.class === 'object' && student.class !== null) {
    return student.class.name;
  }
  return student.className || (student.class as string) || '';
}

// We'll fetch all data from the database with no hardcoded sample data

export function Students() {
  const queryClient = useQueryClient()
  const [sortOrder, setSortOrder] = useState<'newest' | 'oldest' | 'az'>('newest')
  const [searchTerm, setSearchTerm] = useState('')

  // Fetch classes from the database for the dropdown
  const { data: classesData, isLoading: isLoadingClasses, isError: isClassesError } = useQuery({
    queryKey: ['classes'],
    queryFn: async () => {
      console.log('Fetching classes for dropdown...')
      try {
        const res = await fetch('/api/classes', {
          cache: 'no-store',
          headers: {
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          }
        })

        if (!res.ok) {
          const errorData = await res.json().catch(() => ({}))
          console.error('Failed to fetch classes:', errorData)
          throw new Error(errorData.error || `Failed to fetch classes: ${res.status} ${res.statusText}`)
        }

        const data = await res.json()
        console.log(`Successfully fetched ${data.length} classes for dropdown`)
        return data
      } catch (error) {
        console.error('Network error fetching classes:', error)
        throw error
      }
    },
    refetchOnWindowFocus: false,
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 10000)
  })

  // Fetch students from the database with improved error handling
  const { data: studentsData, isLoading, isError, error, refetch: refetchStudents } = useQuery({
    queryKey: ['students'],
    queryFn: async () => {
      console.log('Fetching students from database...')
      try {
        const res = await fetch('/api/students', {
          cache: 'no-store',
          headers: {
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          }
        })

        if (!res.ok) {
          const errorData = await res.json().catch(() => ({}))
          console.error('Failed to fetch students:', errorData)
          throw new Error(errorData.error || `Failed to fetch students: ${res.status} ${res.statusText}`)
        }

        const data = await res.json()

        // Check if the response contains an error message
        if (data.error) {
          console.error('API returned an error:', data.error)
          throw new Error(data.error)
        }

        // Check if the response contains a message about no students
        if (data.message && data.message.includes('No students found')) {
          console.log(data.message)
          console.log(data.suggestion || 'Consider adding some students')
          // Return empty array but don't throw an error
          return data.students || []
        }

        // Normal case - we got an array of students
        console.log(`Successfully fetched ${Array.isArray(data) ? data.length : 0} students from database`)
        return data
      } catch (error) {
        console.error('Network error fetching students:', error)
        throw error
      }
    },
    refetchOnWindowFocus: false,
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 10000)
  })

  // Extract class names from the classes data
  const classOptions = classesData ? classesData.map((cls: { name: string }) => cls.name) : []

  // If we have students but no classes, we need to fix the database relations
  const needsRelationFix = Array.isArray(studentsData) && studentsData.length > 0 && classOptions.length === 0

  // Check if we have student data
  const hasStudents = Array.isArray(studentsData) && studentsData.length > 0

  // Filter students based on search
  const filteredStudents = hasStudents ? studentsData.filter((student: Student) =>
    student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    student.fatherName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    getClassName(student).toLowerCase().includes(searchTerm.toLowerCase())
  ) : []

  // Sort filtered students
  const sortedStudents = [...filteredStudents].sort((a, b) => {
    switch (sortOrder) {
      case 'newest':
        // Since we don't have createdAt, use id as a proxy for creation order
        return parseInt(b.id) - parseInt(a.id)
      case 'oldest':
        return parseInt(a.id) - parseInt(b.id)
      case 'az':
        return a.name.localeCompare(b.name)
      default:
        return 0
    }
  })

  // Create student mutation
  const createStudentMutation = useMutation({
    mutationFn: async (newStudentData: Omit<Student, 'id'>) => {
      const res = await fetch('/api/students', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newStudentData),
      })
      if (!res.ok) throw new Error('Failed to create student')
      return res.json()
    },
    onSuccess: () => {
      // Invalidate both students and classes queries to update both tables
      queryClient.invalidateQueries({ queryKey: ['students'] })
      queryClient.invalidateQueries({ queryKey: ['classes'] })
      toast({
        title: 'Success',
        description: 'Student added successfully',
      })
    },
    onError: (error) => {
      console.error('Failed to add student:', error)

      // Try to extract the error message from the response
      let errorMessage = 'Failed to add student';
      if (error instanceof Error) {
        errorMessage = error.message;
      }

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      })
    },
  })

  // Update student mutation
  const updateStudentMutation = useMutation({
    mutationFn: async (updatedStudent: Student) => {
      const res = await fetch(`/api/students/${updatedStudent.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedStudent),
      })
      if (!res.ok) throw new Error('Failed to update student')
      return res.json()
    },
    onSuccess: () => {
      // Invalidate both students and classes queries to update both tables
      queryClient.invalidateQueries({ queryKey: ['students'] })
      queryClient.invalidateQueries({ queryKey: ['classes'] })
      toast({
        title: 'Success',
        description: 'Student updated successfully',
      })
    },
    onError: (error) => {
      console.error('Failed to update student:', error)
      toast({
        title: 'Error',
        description: 'Failed to update student',
        variant: 'destructive',
      })
    },
  })

  // Delete student mutation
  const deleteStudentMutation = useMutation({
    mutationFn: async (id: string) => {
      const res = await fetch(`/api/students/${id}`, {
        method: 'DELETE',
      })
      if (!res.ok) throw new Error('Failed to delete student')
      return res.json()
    },
    onSuccess: () => {
      // Invalidate both students and classes queries to update both tables
      queryClient.invalidateQueries({ queryKey: ['students'] })
      queryClient.invalidateQueries({ queryKey: ['classes'] })
      toast({
        title: 'Success',
        description: 'Student deleted successfully',
      })
    },
    onError: (error) => {
      console.error('Failed to delete student:', error)
      toast({
        title: 'Error',
        description: 'Failed to delete student',
        variant: 'destructive',
      })
    },
  })

  // Import students mutation
  const importMutation = useMutation({
    mutationFn: async (formData: FormData) => {
      const res = await fetch('/api/students/bulk-import', {
        method: 'POST',
        body: formData,
      })
      if (!res.ok) {
        const errorData = await res.json()
        throw new Error(errorData.error || 'Failed to import students')
      }
      return res.json()
    },
    onSuccess: (data) => {
      // Invalidate both students and classes queries to update both tables
      queryClient.invalidateQueries({ queryKey: ['students'] })
      queryClient.invalidateQueries({ queryKey: ['classes'] })

      // Set import results
      setImportResults({
        total: data.total,
        success: data.success,
        failed: data.failed,
        errors: data.errors || [],
      })

      // Show success toast
      toast({
        title: 'Import Complete',
        description: `Successfully imported ${data.success} out of ${data.total} students`,
      })

      // Reset file selection
      setSelectedFile(null)

      // Close dialog after a delay to show results
      setTimeout(() => {
        setImportOpen(false)
        setImportResults(null)
      }, 3000)
    },
    onError: (error) => {
      console.error('Failed to import students:', error)
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to import students',
        variant: 'destructive',
      })
    },
  })

  const [open, setOpen] = useState(false)
  const [importOpen, setImportOpen] = useState(false)
  const [isEditing, setIsEditing] = useState(false)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [importResults, setImportResults] = useState<{
    total: number;
    success: number;
    failed: number;
    errors: string[];
  } | null>(null)
  const [newStudent, setNewStudent] = useState({
    id: '',
    name: '',
    fatherName: '',
    gfName: '',
    class: '',
    age: '',
    gender: ''
  })

  const handleDelete = (id: string) => {
    if (confirm('Are you sure you want to delete this student?')) {
      deleteStudentMutation.mutate(id)
    }
  }

  // Handle file selection for CSV import
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0]
      if (file.type !== 'text/csv' && !file.name.endsWith('.csv')) {
        toast({
          title: 'Invalid File',
          description: 'Please upload a CSV file',
          variant: 'destructive',
        })
        return
      }
      setSelectedFile(file)
    }
  }

  // Handle CSV template download
  const handleDownloadTemplate = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault()

    // Create CSV content
    const headers = ['name', 'fatherName', 'gfName', 'className', 'age', 'gender']
    const sampleData = [
      ['John Doe', 'Richard Doe', 'William Doe', '1A', '12', 'Male'],
      ['Jane Smith', 'Robert Smith', 'James Smith', '2B', '13', 'Female'],
    ]

    const csvContent = [
      headers.join(','),
      ...sampleData.map(row => row.join(','))
    ].join('\n')

    // Create a blob and download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', 'students_template.csv')
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  // Handle import submission
  const handleImportSubmit = () => {
    if (!selectedFile) return

    const formData = new FormData()
    formData.append('file', selectedFile)

    importMutation.mutate(formData)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setNewStudent({
      ...newStudent,
      [name]: name.includes('name') ? formatStudentName(value) : value
    })
  }

  const handleSelectChange = (name: string, value: string) => {
    setNewStudent({
      ...newStudent,
      [name]: value
    })
  }

  const handleEdit = (student: Student) => {
    // Get the class name, handling both string and object cases
    const className = typeof student.class === 'object' && student.class !== null
      ? student.class.name
      : student.className || student.class || ''

    setNewStudent({
      id: student.id,
      name: student.name,
      fatherName: student.fatherName,
      gfName: student.gfName,
      class: className,
      age: student.age.toString(),
      gender: student.gender
    })
    setIsEditing(true)
    setOpen(true)
  }

  // Function to ensure class exists before adding a student
  const ensureClassExists = async (className: string) => {
    try {
      const res = await fetch('/api/classes/ensure', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ className }),
      })

      if (!res.ok) {
        throw new Error('Failed to ensure class exists')
      }

      return await res.json()
    } catch (error) {
      console.error('Error ensuring class exists:', error)
      throw error
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validate required fields
    if (!newStudent.name || !newStudent.class || !newStudent.age || !newStudent.gender) {
      alert('Please fill in all required fields')
      return
    }

    // Validate age
    const age = parseInt(newStudent.age)
    if (isNaN(age) || age < 5 || age > 20) {
      alert('Please enter a valid age between 5 and 20')
      return
    }

    try {
      // Ensure the class exists before adding the student
      await ensureClassExists(newStudent.class)

      if (isEditing) {
        updateStudentMutation.mutate({
          id: newStudent.id,
          name: newStudent.name,
          fatherName: newStudent.fatherName,
          gfName: newStudent.gfName,
          className: newStudent.class,
          age: parseInt(newStudent.age),
          gender: newStudent.gender,
        })
      } else {
        createStudentMutation.mutate({
          name: newStudent.name,
          fatherName: newStudent.fatherName,
          gfName: newStudent.gfName,
          className: newStudent.class,
          age: parseInt(newStudent.age),
          gender: newStudent.gender,
        })
      }
    } catch (error) {
      console.error('Error in form submission:', error)
      toast({
        title: 'Error',
        description: 'Failed to process the form. Please try again.',
        variant: 'destructive',
      })
    }

    // Reset form and close dialog on success
    // The actual success handling is in the mutation callbacks
    setNewStudent({
      id: '',
      name: '',
      fatherName: '',
      gfName: '',
      class: '',
      age: '',
      gender: ''
    })

    setOpen(false)
    setIsEditing(false)
  }

  // Show loading state
  if (isLoading) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-sm flex items-center justify-center h-64">
        <div className="flex flex-col items-center gap-2">
          <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
          <p className="text-gray-500">Loading students...</p>
        </div>
      </div>
    )
  }

  // Show warning when students exist but classes don't
  if (needsRelationFix) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-sm flex flex-col items-center justify-center h-auto py-8">
        <div className="text-center max-w-lg">
          <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
            <div className="flex items-center">
              <AlertTriangle className="h-6 w-6 text-yellow-500 mr-3" />
              <p className="text-yellow-700 font-medium">Database Relation Issue Detected</p>
            </div>
            <p className="text-yellow-600 mt-2">
              Students exist in the database, but no classes were found. This may cause display issues.
            </p>
          </div>

          <p className="text-gray-600 mb-6">
            We need to fix the database relations by creating classes based on the existing student data.
            Click the button below to automatically fix this issue.
          </p>

          <div className="flex flex-col gap-4 items-center">
            <Button
              onClick={() => window.open('/api/db-fix-relations', '_blank')}
              className="bg-yellow-500 hover:bg-yellow-600 text-white"
            >
              Fix Database Relations
            </Button>

            <Button
              variant="outline"
              onClick={() => {
                queryClient.invalidateQueries({ queryKey: ['classes'] })
                queryClient.invalidateQueries({ queryKey: ['students'] })
              }}
              className="border-gray-300"
            >
              Refresh After Fixing
            </Button>
          </div>
        </div>
      </div>
    )
  }

  // Show error state with more details and helpful actions
  if (isError) {
    // Extract error details if available
    let errorMessage = 'Unknown error occurred';
    let errorDetails = '';
    let errorSuggestion = 'Please check your database connection and ensure the API endpoint is working correctly.';

    if (error instanceof Error) {
      errorMessage = error.message;

      // Try to parse JSON error response
      try {
        if (error.message.includes('Failed to fetch')) {
          errorDetails = 'The server may be down or unreachable.';
          errorSuggestion = 'Please check if the server is running and your internet connection is working.';
        } else if (error.message.includes('database')) {
          errorDetails = 'There might be an issue with the database connection.';
          errorSuggestion = 'Please check your database connection string and ensure the database server is running.';
        }
      } catch (e) {
        // If parsing fails, use the error message as is
        errorDetails = error.message;
      }
    }

    return (
      <div className="bg-white p-6 rounded-lg shadow-sm flex items-center justify-center h-auto">
        <div className="text-center max-w-lg">
          <p className="text-red-500 text-xl font-semibold mb-2">Failed to load students</p>
          <p className="text-sm text-gray-600 mb-2">{errorMessage}</p>
          {errorDetails && <p className="text-sm text-gray-500 mb-4">{errorDetails}</p>}

          <div className="flex flex-col gap-4 items-center mt-6">
            <div className="flex gap-3">
              <Button onClick={() => refetchStudents()} className="bg-indigo-600 hover:bg-indigo-700">
                Try Again
              </Button>
              <Button
                variant="outline"
                onClick={() => window.open('/api/db-test', '_blank')}
                className="border-indigo-300 text-indigo-600"
              >
                Test Database Connection
              </Button>
            </div>

            <div className="flex flex-col gap-3">
              <Button
                variant="outline"
                onClick={() => window.open('/api/db-setup', '_blank')}
                className="border-blue-300 text-blue-600"
              >
                Setup Database Schema
              </Button>
              <Button
                variant="outline"
                onClick={() => window.open('/api/db-init', '_blank')}
                className="border-green-300 text-green-600"
              >
                Initialize with Sample Data
              </Button>
            </div>

            <p className="text-sm text-gray-500 max-w-md mt-4">
              <strong>Suggestion:</strong> {errorSuggestion}
            </p>
          </div>
        </div>
      </div>
    )
  }

  // Show empty state when no students are found but API call was successful
  if (!isLoading && !isError && !hasStudents) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-sm flex flex-col items-center justify-center h-auto py-12">
        <div className="text-center max-w-lg">
          <Users className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-700 mb-2">No Students Found</h2>
          <p className="text-gray-500 mb-6">There are no students in the database yet. You can add students manually or initialize the database with sample data.</p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              onClick={() => setOpen(true)}
              className="bg-indigo-600 hover:bg-indigo-700"
            >
              <Plus className="h-4 w-4 mr-2" /> Add Student Manually
            </Button>
            <div className="flex flex-col sm:flex-row gap-3">
              <Button
                variant="outline"
                onClick={() => window.open('/api/db-setup', '_blank')}
                className="border-blue-300 text-blue-600"
              >
                Setup Database Schema
              </Button>
              <Button
                variant="outline"
                onClick={() => window.open('/api/db-init', '_blank')}
                className="border-green-300 text-green-600"
              >
                Initialize with Sample Data
              </Button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Students</h2>
        <div className="flex gap-2">
          <Dialog open={importOpen} onOpenChange={setImportOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" className="flex items-center gap-2">
                <Upload className="h-4 w-4" /> Import Students
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>Import Students from CSV</DialogTitle>
                <DialogDescription>
                  Upload a CSV file with student information to add multiple students at once.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-6 py-4">
                <div className="flex items-center justify-center w-full">
                  <label htmlFor="csv-file" className="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100">
                    <div className="flex flex-col items-center justify-center pt-5 pb-6">
                      <Upload className="w-8 h-8 mb-3 text-gray-400" />
                      <p className="mb-2 text-sm text-gray-500"><span className="font-semibold">Click to upload</span> or drag and drop</p>
                      <p className="text-xs text-gray-500">CSV file only (MAX. 500 students)</p>
                    </div>
                    <input
                      id="csv-file"
                      type="file"
                      accept=".csv"
                      className="hidden"
                      onChange={handleFileChange}
                    />
                  </label>
                </div>
                {selectedFile && (
                  <div className="flex items-center gap-2 p-2 bg-gray-50 rounded">
                    <div className="flex-1 truncate">{selectedFile.name}</div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setSelectedFile(null)}
                      className="h-8 w-8 p-0"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                )}
                <div className="text-sm text-gray-500">
                  <p>CSV file should have the following columns:</p>
                  <p className="font-mono text-xs mt-1">name, fatherName, gfName, className, age, gender</p>
                  <div className="mt-2">
                    <a
                      href="#"
                      onClick={handleDownloadTemplate}
                      className="text-indigo-600 hover:text-indigo-500 text-sm flex items-center gap-1"
                    >
                      <Bookmark className="h-3 w-3" /> Download template
                    </a>
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setImportOpen(false)}>Cancel</Button>
                <Button
                  type="button"
                  onClick={handleImportSubmit}
                  disabled={!selectedFile || importMutation.isPending}
                >
                  {importMutation.isPending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Importing...
                    </>
                  ) : (
                    'Import Students'
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
              <Button className="flex items-center gap-2">
                <Plus className="h-4 w-4" /> Add Student
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>{isEditing ? 'Edit Student' : 'Add New Student'}</DialogTitle>
                <DialogDescription>
                  {isEditing ? 'Edit student information below' : 'Fill in the information below to add a new student'}
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="grid gap-6 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="name" className="text-sm font-medium">
                      Student Name
                    </Label>
                    <div className="relative">
                      <User className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        id="name"
                        name="name"
                        placeholder="Full Name"
                        className="pl-9"
                        value={newStudent.name}
                        onChange={handleInputChange}
                        required
                      />
                    </div>
                  </div>
                  <div className="grid gap-2">
                    <CustomSelect
                      label="Class"
                      icon={Users}
                      value={newStudent.class}
                      options={classOptions}
                      onChange={(value) => handleSelectChange('class', value as string)}
                      placeholder="Select Class"
                      required
                      isLoading={isLoadingClasses}
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="fatherName" className="text-sm font-medium">
                      Father's Name
                    </Label>
                    <Input
                      id="fatherName"
                      name="fatherName"
                      placeholder="Father's Name"
                      value={newStudent.fatherName}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="gfName" className="text-sm font-medium">
                      Grandfather's Name
                    </Label>
                    <Input
                      id="gfName"
                      name="gfName"
                      placeholder="Grandfather's Name"
                      value={newStudent.gfName}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="age" className="text-sm font-medium">
                      Age
                    </Label>
                    <Input
                      id="age"
                      name="age"
                      type="number"
                      placeholder="Age"
                      min="5"
                      max="20"
                      value={newStudent.age}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="gender" className="text-sm font-medium">
                      Gender
                    </Label>
                    <Select
                      value={newStudent.gender}
                      onValueChange={(value) => handleSelectChange('gender', value)}
                      required
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select Gender" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Male">Male</SelectItem>
                        <SelectItem value="Female">Female</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => {
                    setOpen(false)
                    setIsEditing(false)
                    setNewStudent({
                      id: '',
                      name: '',
                      fatherName: '',
                      gfName: '',
                      class: '',
                      age: '',
                      gender: ''
                    })
                  }}>
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={createStudentMutation.isPending || updateStudentMutation.isPending}
                  >
                    {createStudentMutation.isPending || updateStudentMutation.isPending ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        {isEditing ? 'Saving...' : 'Adding...'}
                      </>
                    ) : (
                      isEditing ? 'Save Changes' : 'Add Student'
                    )}
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div className="flex flex-col sm:flex-row justify-between items-center mb-6 gap-4">
        <div className="relative w-full sm:w-72">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search here..."
            className="pl-10"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="flex items-center gap-2 w-full sm:w-auto">
          <Button variant="outline" className="flex items-center gap-2">
            <SlidersHorizontal className="h-4 w-4" />
            Filters
          </Button>
          <div className="relative ml-2">
            <select
              className="bg-white border rounded-md py-2 px-4 pr-8 appearance-none text-sm focus:outline-none focus:ring-2 focus:ring-indigo-600"
              value={sortOrder}
              onChange={(e) => setSortOrder(e.target.value as 'newest' | 'oldest' | 'az')}
            >
              <option value="newest">Newest</option>
              <option value="oldest">Oldest</option>
              <option value="az">A-Z</option>
            </select>
            <div className="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <svg className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <input type="checkbox" className="h-4 w-4 rounded border-gray-300" />
              </TableHead>
              <TableHead>SID</TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Father Name</TableHead>
              <TableHead>GF Name</TableHead>
              <TableHead>Class</TableHead>
              <TableHead>Age</TableHead>
              <TableHead>Gender</TableHead>
              <TableHead className="text-right">Action</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {sortedStudents.length > 0 ? (
              sortedStudents.map((student: Student) => (
                <TableRow key={student.id}>
                  <TableCell>
                    <input type="checkbox" className="h-4 w-4 rounded border-gray-300" />
                  </TableCell>
                  <TableCell>
                    <span className="font-mono text-xs font-medium bg-gray-100 px-2 py-1 rounded">
                      {student.sid}
                    </span>
                  </TableCell>
                  <TableCell className="font-medium">
                    <div className="flex items-center gap-3">
                      <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 font-semibold">
                        {student.name.charAt(0)}
                      </div>
                      {student.name}
                    </div>
                  </TableCell>
                  <TableCell>{student.fatherName}</TableCell>
                  <TableCell>{student.gfName}</TableCell>
                  <TableCell>
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      getClassName(student).includes('VII') ? 'bg-indigo-100 text-indigo-800' : 'bg-green-100 text-green-800'
                    }`}>
                      {getClassName(student)}
                    </span>
                  </TableCell>
                  <TableCell>{student.age}</TableCell>
                  <TableCell>{student.gender}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-indigo-600"
                        onClick={() => handleEdit(student)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-red-600"
                        onClick={() => handleDelete(student.id)}
                        disabled={deleteStudentMutation.isPending}
                      >
                        {deleteStudentMutation.isPending ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <Trash2 className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-4">
                  No students found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}


import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'

export async function GET() {
  try {
    // Check if admin user already exists
    const existingAdmin = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
    })

    if (existingAdmin) {
      return NextResponse.json(
        { message: 'Admin user already exists', user: { email: existingAdmin.email, name: existingAdmin.name, role: existingAdmin.role } },
        { status: 200 }
      )
    }

    // Hash the password
    const hashedPassword = await bcrypt.hash('password123', 10)

    // Create admin user
    const admin = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Admin User',
        role: 'ADMIN',
        status: 'active',
      },
    })

    return NextResponse.json(
      { 
        message: 'Admin user created successfully', 
        user: { 
          email: admin.email, 
          name: admin.name, 
          role: admin.role 
        } 
      },
      { status: 201 }
    )
  } catch (error) {
    console.error('Error creating admin user:', error)
    return NextResponse.json(
      { error: 'Failed to create admin user' },
      { status: 500 }
    )
  }
}

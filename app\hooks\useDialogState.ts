'use client';

import { useState, useCallback, useEffect } from 'react';

/**
 * A custom hook to safely manage dialog state and prevent focus recursion issues
 *
 * @param initialState - Initial open state of the dialog
 * @param onOpenChange - Optional callback to be called when the dialog state changes
 * @returns An object with the current state and handlers
 */
export function useDialogState(
  initialState: boolean = false,
  onOpenChange?: (open: boolean) => void
) {
  const [isOpen, setIsOpen] = useState(initialState);

  // Update internal state when external state changes
  useEffect(() => {
    setIsOpen(initialState);
  }, [initialState]);

  // Safely open the dialog
  const open = useCallback(() => {
    // Use requestAnimationFrame to ensure we're not in the middle of a React render cycle
    requestAnimationFrame(() => {
      setIsOpen(true);
      onOpenChange?.(true);
    });
  }, [onOpenChange]);

  // Safely close the dialog with a small delay to prevent focus issues
  const close = useCallback(() => {
    // First update local state immediately
    setIsOpen(false);

    // Then notify parent after a small delay to prevent focus issues
    // This helps break potential focus recursion cycles
    setTimeout(() => {
      onOpenChange?.(false);
    }, 50);
  }, [onOpenChange]);

  // Handle open state changes
  const handleOpenChange = useCallback((open: boolean) => {
    if (open) {
      // For opening, use requestAnimationFrame to avoid React render cycle conflicts
      requestAnimationFrame(() => {
        setIsOpen(true);
        onOpenChange?.(true);
      });
    } else {
      // For closing, update local state immediately
      setIsOpen(false);

      // Then notify parent after a small delay
      setTimeout(() => {
        onOpenChange?.(false);
      }, 50);
    }
  }, [onOpenChange]);

  return {
    isOpen,
    open,
    close,
    handleOpenChange
  };
}

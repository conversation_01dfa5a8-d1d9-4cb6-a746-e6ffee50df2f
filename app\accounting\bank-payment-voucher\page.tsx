'use client'

import React, { useState, useEffect } from 'react'
import './bank-payment-voucher.css'
import Sidebar from '../../components/Sidebar'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'
import { <PERSON><PERSON> } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Label } from '../../components/ui/label'
import { Textarea } from '../../components/ui/textarea'
import {
  CreditCard,
  DollarSign,
  FileText,
  CheckCircle2,
  AlertCircle,
  Loader2,
  Building,
  Landmark,
  FileBarChart,
  Search,
  Download,
  Printer,
  X,
  Hash
} from 'lucide-react'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "../../components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "../../components/ui/table"
import { useToast } from '../../components/ui/use-toast'
import Header from '../../components/Header'

// Define interface for account code data
interface AccountCode {
  id: string;
  code: string;
  description: string;
  isActive: boolean;
}

// Define interface for voucher data
interface Voucher {
  id: string;
  voucherNo: string;
  date: string;
  paidTo: string;
  amount: number;
  paymentMethod: string;
  chequeNo?: string;
  accountNo?: string;
  accountCode?: string;
  purpose: string;
  status: 'pending' | 'approved' | 'rejected';
  createdAt?: string;
  updatedAt?: string;
}

export default function BankPaymentVoucherPage() {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false)
  const [paidTo, setPaidTo] = useState('')
  const [paymentMethod, setPaymentMethod] = useState('cheque')
  const [chequeNo, setChequeNo] = useState('')
  const [accountNo, setAccountNo] = useState('')
  const [accountCode, setAccountCode] = useState('')
  const [accountCodes, setAccountCodes] = useState<AccountCode[]>([])
  const [isLoadingAccountCodes, setIsLoadingAccountCodes] = useState(false)
  const [amount, setAmount] = useState('')
  const [purpose, setPurpose] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isReportOpen, setIsReportOpen] = useState(false)
  const [voucherList, setVoucherList] = useState<Voucher[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()

  // Fetch account codes from the API
  const fetchAccountCodes = async () => {
    setIsLoadingAccountCodes(true)
    try {
      const response = await fetch('/api/accounting/account-codes')

      if (!response.ok) {
        throw new Error('Failed to fetch account codes')
      }

      const data = await response.json()

      // Filter only active account codes
      const activeAccountCodes = data.filter((code: AccountCode) => code.isActive)
      setAccountCodes(activeAccountCodes)
    } catch (error) {
      console.error('Error fetching account codes:', error)
      toast({
        title: "Error",
        description: "Failed to load account codes",
        variant: "destructive",
      })

      // Use mock data as fallback
      setAccountCodes([
        { id: 'acc1', code: '5504', description: 'Tuition Revenue', isActive: true },
        { id: 'acc2', code: '4211', description: 'School Supplies Expense', isActive: true },
        { id: 'acc3', code: '6301', description: 'Salary Expense', isActive: true },
        { id: 'acc4', code: '7102', description: 'Utilities Expense', isActive: true },
        { id: 'acc5', code: '8005', description: 'Office Supplies', isActive: true }
      ])
    } finally {
      setIsLoadingAccountCodes(false)
    }
  }

  // Fetch vouchers from the API
  const fetchVouchers = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/accounting/bank-payment-vouchers')

      // Even if response is not OK, we'll try to parse it as JSON
      // The API is designed to return an empty array in case of errors
      const data = await response.json()

      if (Array.isArray(data)) {
        // Format the data to match our Voucher interface
        const formattedVouchers = data.map((voucher: any) => ({
          ...voucher,
          date: voucher.date ? new Date(voucher.date).toISOString().split('T')[0] : '',
        }))

        setVoucherList(formattedVouchers)
      } else if (data.error) {
        console.error('API returned an error:', data.error)
        toast({
          title: 'Error',
          description: data.error || 'Failed to load vouchers. Please try again.',
          variant: 'destructive',
        })
        // Set empty list to prevent UI from breaking
        setVoucherList([])
      } else {
        // Unexpected response format
        console.error('Unexpected API response format:', data)
        setVoucherList([])
      }
    } catch (error) {
      console.error('Error fetching vouchers:', error)
      toast({
        title: 'Error',
        description: 'Failed to load vouchers. Please try again.',
        variant: 'destructive',
      })
      // Set empty list to prevent UI from breaking
      setVoucherList([])
    } finally {
      setIsLoading(false)
    }
  }

  // Load account codes when the component mounts
  useEffect(() => {
    fetchAccountCodes()
  }, [])

  // Load vouchers when the component mounts or when the report is opened
  useEffect(() => {
    if (isReportOpen) {
      fetchVouchers()
    }
  }, [isReportOpen])

  // Toggle sidebar for desktop
  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  // Toggle sidebar for mobile
  const toggleMobileSidebar = () => {
    setMobileSidebarOpen(!mobileSidebarOpen)
  }

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ET', {
      style: 'currency',
      currency: 'ETB',
      minimumFractionDigits: 2
    }).format(amount)
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validate form
    if (!paidTo || !amount || !purpose) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      })
      return
    }

    if (paymentMethod === 'cheque' && !chequeNo) {
      toast({
        title: "Validation Error",
        description: "Please enter the Cheque Number",
        variant: "destructive",
      })
      return
    }

    if (paymentMethod === 'transfer' && !accountNo) {
      toast({
        title: "Validation Error",
        description: "Please enter the Account Number",
        variant: "destructive",
      })
      return
    }

    // Start submission
    setIsSubmitting(true)

    try {
      // Prepare the data to send to the API
      const voucherData = {
        paidTo: paidTo,
        // Include accountCode for reference, but it won't be stored in the database
        accountCode: accountCode,
        amount: amount,
        paymentMethod: paymentMethod,
        purpose: purpose,
        date: new Date().toISOString().split('T')[0]
      }

      // Log the account code for reference
      console.log('Selected account code (for reference only):', accountCode);

      // Add payment method specific details
      if (paymentMethod === 'cheque') {
        voucherData.chequeNo = chequeNo
      } else {
        voucherData.accountNo = accountNo
      }

      // Send the data to the API
      const response = await fetch('/api/accounting/bank-payment-vouchers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(voucherData),
      })

      // Parse the response
      const responseData = await response.json()

      if (!response.ok) {
        console.error('API error response:', responseData);

        // Create a more detailed error message
        let errorMessage = responseData.error || 'Failed to create voucher';

        if (responseData.details) {
          console.error('Error details:', responseData.details);
        }

        // Create a custom error with the response data
        const error = new Error(errorMessage);
        error.response = responseData;
        throw error;
      }

      // Get the created voucher from the response
      const newVoucher = responseData

      toast({
        title: "Payment Voucher Created",
        description: `Bank Payment Voucher ${newVoucher.voucherNo} has been created successfully${accountCode ? ` (Account Code: ${accountCode})` : ''}`,
        variant: "default",
      })

      // Reset form
      setPaidTo('')
      setPaymentMethod('cheque')
      setChequeNo('')
      setAccountNo('')
      setAccountCode('')
      setAmount('')
      setPurpose('')
    } catch (error) {
      console.error('Error creating payment voucher:', error)

      // Extract detailed error message if available
      let errorMessage = "Failed to create payment voucher. Please try again.";

      if (error instanceof Error) {
        errorMessage = error.message;
      }

      // Check if the error is related to the database schema
      if (errorMessage.includes('Unknown field') || errorMessage.includes('column')) {
        errorMessage = "There's an issue with the database schema. Please contact the administrator.";
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })

      // Log additional details to console for debugging
      if (error.response) {
        try {
          const errorData = error.response;
          console.error('API Error Response:', errorData);
        } catch (e) {
          console.error('Could not parse error response:', e);
        }
      }
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
      <Sidebar
        isCollapsed={sidebarCollapsed}
        toggleSidebar={toggleSidebar}
        isMobileOpen={mobileSidebarOpen}
      />

      <main className={`main-content ${sidebarCollapsed ? 'sidebar-collapsed' : ''} w-full h-screen overflow-y-auto`}>
        <Header toggleMobileSidebar={toggleMobileSidebar} />

        <div className="container mx-auto px-4 py-6">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold text-gray-800 dark:text-white">
              Bank Payment Voucher (BPV)
            </h1>
            <Button
              className="bg-blue-600 hover:bg-blue-700"
              onClick={() => setIsReportOpen(true)}
            >
              <FileBarChart className="h-4 w-4 mr-2" />
              Voucher Report
            </Button>
          </div>

          <Card className="shadow-lg border-0">
            <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border-b">
              <CardTitle className="flex items-center text-xl">
                <CreditCard className="mr-2 h-5 w-5 text-blue-600 dark:text-blue-400" />
                Create Bank Payment Voucher
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="paid-to" className="text-sm font-medium flex items-center">
                      <Landmark className="h-4 w-4 mr-2 text-gray-500" />
                      Paid To
                    </Label>
                    <Input
                      id="paid-to"
                      placeholder="Enter recipient name"
                      value={paidTo}
                      onChange={(e) => setPaidTo(e.target.value.charAt(0).toUpperCase() + e.target.value.slice(1))}
                      className="h-10"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="account-code" className="text-sm font-medium flex items-center">
                      <Hash className="h-4 w-4 mr-2 text-gray-500" />
                      Account Code
                    </Label>
                    <Select
                      value={accountCode}
                      onValueChange={setAccountCode}
                    >
                      <SelectTrigger id="account-code" className={isLoadingAccountCodes ? "opacity-70" : ""}>
                        <SelectValue placeholder="Select account code" />
                      </SelectTrigger>
                      <SelectContent>
                        {isLoadingAccountCodes ? (
                          <div className="flex items-center justify-center p-2">
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            Loading...
                          </div>
                        ) : accountCodes.length === 0 ? (
                          <div className="p-2 text-center text-sm text-gray-500">
                            No account codes found
                          </div>
                        ) : (
                          accountCodes.map((code) => (
                            <SelectItem key={code.id} value={code.code}>
                              {code.code} - {code.description}
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="payment-method" className="text-sm font-medium flex items-center">
                      <CreditCard className="h-4 w-4 mr-2 text-gray-500" />
                      Method of Payment
                    </Label>
                    <Select
                      value={paymentMethod}
                      onValueChange={setPaymentMethod}
                    >
                      <SelectTrigger id="payment-method">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="cheque">Cheque</SelectItem>
                        <SelectItem value="transfer">Transfer</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {paymentMethod === 'cheque' ? (
                    <div className="space-y-2">
                      <Label htmlFor="cheque-no" className="text-sm font-medium flex items-center">
                        <FileText className="h-4 w-4 mr-2 text-gray-500" />
                        Cheque No.
                      </Label>
                      <Input
                        id="cheque-no"
                        placeholder="Enter cheque number"
                        value={chequeNo}
                        onChange={(e) => setChequeNo(e.target.value.replace(/[a-z]/g, match => match.toUpperCase()))}
                        className="h-10"
                        required
                      />
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <Label htmlFor="account-no" className="text-sm font-medium flex items-center">
                        <FileText className="h-4 w-4 mr-2 text-gray-500" />
                        Account No.
                      </Label>
                      <Input
                        id="account-no"
                        placeholder="Enter account number"
                        value={accountNo}
                        onChange={(e) => setAccountNo(e.target.value.replace(/[a-z]/g, match => match.toUpperCase()))}
                        className="h-10"
                        required
                      />
                    </div>
                  )}

                  <div className="space-y-2">
                    <Label htmlFor="amount" className="text-sm font-medium flex items-center">
                      <DollarSign className="h-4 w-4 mr-2 text-gray-500" />
                      Amount
                    </Label>
                    <Input
                      id="amount"
                      type="number"
                      placeholder="Enter amount"
                      value={amount}
                      onChange={(e) => setAmount(e.target.value)}
                      className="h-10"
                      required
                    />
                  </div>

                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="purpose" className="text-sm font-medium flex items-center">
                      <FileText className="h-4 w-4 mr-2 text-gray-500" />
                      Purpose of Payment
                    </Label>
                    <Textarea
                      id="purpose"
                      placeholder="Enter purpose of payment"
                      value={purpose}
                      onChange={(e) => setPurpose(e.target.value.charAt(0).toUpperCase() + e.target.value.slice(1))}
                      rows={3}
                      required
                    />
                  </div>
                </div>

                <div className="flex justify-end space-x-4 pt-4 border-t">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setPaidTo('')
                      setPaymentMethod('cheque')
                      setChequeNo('')
                      setAccountNo('')
                      setAccountCode('')
                      setAmount('')
                      setPurpose('')
                    }}
                    disabled={isSubmitting}
                  >
                    <AlertCircle className="h-4 w-4 mr-2" />
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    className="bg-green-600 hover:bg-green-700"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Processing...
                      </>
                    ) : (
                      <>
                        <CheckCircle2 className="h-4 w-4 mr-2" />
                        Create Voucher
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </main>

      {/* Overlay for mobile menu */}
      {mobileSidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 md:hidden z-40"
          onClick={toggleMobileSidebar}
        ></div>
      )}

      {/* Voucher Report Dialog */}
      <Dialog open={isReportOpen} onOpenChange={setIsReportOpen}>
        <DialogContent className="max-w-6xl w-[90vw] dialog-content">
          <DialogHeader>
            <DialogTitle className="text-xl flex items-center">
              <FileBarChart className="h-5 w-5 mr-2 text-blue-600" />
              Bank Payment Voucher Report
            </DialogTitle>
            <DialogDescription>
              View all bank payment vouchers and their details
            </DialogDescription>
          </DialogHeader>

          {/* Print-only title */}
          <div className="voucher-report-title hidden">
            Bank Payment Voucher Report - {new Date().toLocaleDateString()}
          </div>

          <div className="flex justify-between items-center mb-4 search-container print-export-buttons">
            <div className="relative w-64">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-500" />
              <Input
                placeholder="Search vouchers..."
                className="pl-8"
                onChange={(e) => {
                  const searchTerm = e.target.value.toLowerCase();
                  if (searchTerm) {
                    // First fetch all vouchers, then filter locally
                    setIsLoading(true);
                    fetchVouchers()
                      .then(() => {
                        setVoucherList(prev =>
                          prev.filter(voucher =>
                            (voucher.voucherNo && voucher.voucherNo.toLowerCase().includes(searchTerm)) ||
                            (voucher.paidTo && voucher.paidTo.toLowerCase().includes(searchTerm)) ||
                            (voucher.purpose && voucher.purpose.toLowerCase().includes(searchTerm))
                          )
                        );
                      })
                      .catch(error => {
                        console.error('Error during search:', error);
                        toast({
                          title: 'Search Error',
                          description: 'An error occurred while searching. Please try again.',
                          variant: 'destructive',
                        });
                      })
                      .finally(() => {
                        setIsLoading(false);
                      });
                  } else {
                    // If search term is empty, fetch all vouchers
                    fetchVouchers();
                  }
                }}
              />
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  // Create a custom print function to handle the print layout
                  const printContent = () => {
                    // Open a new window for printing
                    const printWindow = window.open('', '_blank');

                    if (!printWindow) {
                      toast({
                        title: "Print Error",
                        description: "Unable to open print window. Please check your popup blocker settings.",
                        variant: "destructive",
                      });
                      return;
                    }

                    // Generate the HTML content for the print window
                    const printContent = `
                      <!DOCTYPE html>
                      <html>
                        <head>
                          <title>Bank Payment Voucher Report</title>
                          <meta charset="utf-8">
                          <meta name="viewport" content="width=device-width, initial-scale=1">
                          <style>
                            body {
                              font-family: Arial, sans-serif;
                              margin: 0;
                              padding: 20px;
                            }
                            h1 {
                              text-align: center;
                              font-size: 24px;
                              margin-bottom: 20px;
                            }
                            table {
                              width: 100%;
                              border-collapse: collapse;
                              margin-bottom: 20px;
                            }
                            th, td {
                              padding: 10px;
                              text-align: left;
                              border: 1px solid #ddd;
                            }
                            th {
                              background-color: #f3f4f6;
                              font-weight: bold;
                            }
                            .status-badge {
                              padding: 2px 6px;
                              border-radius: 4px;
                              font-size: 12px;
                            }
                            .status-approved {
                              background-color: #dcfce7;
                              color: #166534;
                            }
                            .status-pending {
                              background-color: #fef9c3;
                              color: #854d0e;
                            }
                            .status-rejected {
                              background-color: #fee2e2;
                              color: #991b1b;
                            }
                            .timestamp {
                              text-align: right;
                              margin-top: 20px;
                              font-size: 12px;
                            }
                            .capitalize {
                              text-transform: capitalize;
                            }

                            /* Column widths */
                            th:nth-child(1), td:nth-child(1) { width: 15%; } /* Voucher No. */
                            th:nth-child(2), td:nth-child(2) { width: 12%; } /* Date */
                            th:nth-child(3), td:nth-child(3) { width: 20%; } /* Paid To */
                            th:nth-child(4), td:nth-child(4) { width: 15%; } /* Amount */
                            th:nth-child(5), td:nth-child(5) { width: 12%; } /* Payment Method */
                            th:nth-child(6), td:nth-child(6) { width: 15%; } /* Reference */
                            th:nth-child(7), td:nth-child(7) { width: 11%; } /* Status */
                          </style>
                        </head>
                        <body>
                          <h1>Bank Payment Voucher Report</h1>
                          <table>
                            <thead>
                              <tr>
                                <th>Voucher No.</th>
                                <th>Date</th>
                                <th>Paid To</th>
                                <th>Account Code</th>
                                <th>Amount</th>
                                <th>Payment Method</th>
                                <th>Reference</th>
                                <th>Status</th>
                              </tr>
                            </thead>
                            <tbody>
                              ${voucherList.map(voucher => `
                                <tr>
                                  <td>${voucher.voucherNo}</td>
                                  <td>${new Date(voucher.date).toLocaleDateString()}</td>
                                  <td>${voucher.paidTo}</td>
                                  <td>${voucher.accountCode || '-'}</td>
                                  <td>${formatCurrency(voucher.amount)}</td>
                                  <td class="capitalize">${voucher.paymentMethod}</td>
                                  <td>${voucher.paymentMethod === 'cheque' ? voucher.chequeNo : voucher.accountNo || '-'}</td>
                                  <td>
                                    <span class="status-badge status-${voucher.status}">
                                      ${voucher.status.charAt(0).toUpperCase() + voucher.status.slice(1)}
                                    </span>
                                  </td>
                                </tr>
                              `).join('')}
                            </tbody>
                          </table>
                          <div class="timestamp">
                            <p>Printed on: ${new Date().toLocaleString()}</p>
                          </div>
                          <script>
                            // Auto-print when loaded
                            window.onload = function() {
                              window.print();
                              // Close the window after printing (optional)
                              // setTimeout(function() { window.close(); }, 500);
                            };
                          </script>
                        </body>
                      </html>
                    `;

                    // Write the content to the new window
                    printWindow.document.open();
                    printWindow.document.write(printContent);
                    printWindow.document.close();
                  };

                  // Call the print function
                  printContent();
                }}
              >
                <Printer className="h-4 w-4 mr-1" />
                Print
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  // Enhanced CSV export with proper formatting
                  if (voucherList.length === 0) {
                    toast({
                      title: "No data to export",
                      description: "There are no vouchers to export.",
                      variant: "destructive",
                    });
                    return;
                  }

                  // Headers matching the print view
                  const headers = ["Voucher No.", "Date", "Paid To", "Account Code", "Amount", "Payment Method", "Reference", "Status"];

                  // Format data to match print view
                  const csvData = voucherList.map(v => [
                    `"${v.voucherNo}"`, // Wrap in quotes to handle special characters
                    `"${new Date(v.date).toLocaleDateString()}"`,
                    `"${v.paidTo}"`,
                    `"${v.accountCode || '-'}"`,
                    `"${formatCurrency(v.amount)}"`,
                    `"${v.paymentMethod.charAt(0).toUpperCase() + v.paymentMethod.slice(1)}"`,
                    `"${v.paymentMethod === 'cheque' ? v.chequeNo : v.accountNo || '-'}"`,
                    `"${v.status.charAt(0).toUpperCase() + v.status.slice(1)}"`
                  ]);

                  // Add BOM for proper UTF-8 encoding in Excel
                  const BOM = '\uFEFF';
                  const csvContent = BOM + [
                    headers.join(','),
                    ...csvData.map(row => row.join(','))
                  ].join('\n');

                  // Create and download the file
                  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                  const url = URL.createObjectURL(blob);
                  const link = document.createElement('a');
                  link.setAttribute('href', url);

                  // Include timestamp in filename
                  const timestamp = new Date().toISOString().split('T')[0];
                  link.setAttribute('download', `bank_payment_vouchers_${timestamp}.csv`);

                  link.style.visibility = 'hidden';
                  document.body.appendChild(link);
                  link.click();
                  document.body.removeChild(link);

                  toast({
                    title: "Export Successful",
                    description: "Bank Payment Voucher Report has been exported to CSV with all formatting",
                  });
                }}
              >
                <Download className="h-4 w-4 mr-1" />
                Export
              </Button>
            </div>
          </div>

          <div className="border rounded-md overflow-x-auto">
            {isLoading ? (
              <div className="flex items-center justify-center h-64">
                <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
                <span className="ml-2 text-lg">Loading vouchers...</span>
              </div>
            ) : voucherList.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-64 text-gray-500">
                <FileBarChart className="h-12 w-12 mb-2 opacity-20" />
                <p>No vouchers found</p>
                <p className="text-sm">Create a new voucher to see it here</p>
              </div>
            ) : (
              <div className="min-w-[1000px]">
                <Table className="w-full table-fixed border-separate border-spacing-0">
                  <TableHeader>
                    <TableRow className="bg-gray-50 dark:bg-gray-800">
                      <TableHead className="w-[12%] px-4">Voucher No.</TableHead>
                      <TableHead className="w-[10%] px-4">Date</TableHead>
                      <TableHead className="w-[15%] px-4">Paid To</TableHead>
                      <TableHead className="w-[10%] px-4">Account Code</TableHead>
                      <TableHead className="w-[12%] px-4">Amount</TableHead>
                      <TableHead className="w-[10%] px-4">Payment Method</TableHead>
                      <TableHead className="w-[15%] px-4">Reference</TableHead>
                      <TableHead className="w-[10%] px-4">Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {voucherList.map((voucher) => (
                      <TableRow key={voucher.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                        <TableCell className="font-medium w-[12%] px-4 truncate">{voucher.voucherNo}</TableCell>
                        <TableCell className="w-[10%] px-4">{new Date(voucher.date).toLocaleDateString()}</TableCell>
                        <TableCell className="w-[15%] px-4 truncate">{voucher.paidTo}</TableCell>
                        <TableCell className="w-[10%] px-4">{voucher.accountCode || '-'}</TableCell>
                        <TableCell className="w-[12%] px-4">{formatCurrency(voucher.amount)}</TableCell>
                        <TableCell className="capitalize w-[10%] px-4">{voucher.paymentMethod}</TableCell>
                        <TableCell className="w-[15%] px-4">
                          {voucher.paymentMethod === 'cheque'
                            ? voucher.chequeNo
                            : voucher.accountNo}
                        </TableCell>
                        <TableCell className="w-[10%] px-4">
                          <span className={`status-badge px-2 py-1 rounded-full text-xs font-medium ${
                            voucher.status === 'approved'
                              ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                              : voucher.status === 'pending'
                                ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'
                                : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                          }`}>
                            {voucher.status.charAt(0).toUpperCase() + voucher.status.slice(1)}
                          </span>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsReportOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

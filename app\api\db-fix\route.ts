import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

export async function GET() {
  let prisma: PrismaClient | null = null;

  try {
    console.log('Attempting to fix database schema...');
    
    // Create a new PrismaClient instance specifically for this operation
    prisma = new PrismaClient({
      log: ['query', 'info', 'warn', 'error'],
    });

    // Connect to the database
    await prisma.$connect();
    console.log('Database connection successful');

    // Execute raw SQL to add the accountCode column if it doesn't exist
    const result = await prisma.$executeRaw`
      ALTER TABLE bank_payment_voucher 
      ADD COLUMN IF NOT EXISTS accountCode VARCHAR(255)
    `;

    console.log('Database schema update result:', result);

    return NextResponse.json({
      success: true,
      message: 'Database schema updated successfully',
      result
    });
  } catch (error) {
    console.error('Error fixing database schema:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : null
    }, { status: 500 });
  } finally {
    // Clean up the connection
    if (prisma) {
      await prisma.$disconnect().catch(e => console.error('Error disconnecting from database:', e));
    }
  }
}

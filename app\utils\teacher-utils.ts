// Teacher utility functions for handling data from both Teacher and User tables

export interface UnifiedTeacher {
  id: string
  name: string
  email: string
  subject?: string
  mobile?: string
  fatherName?: string
  gender?: string
  source: 'teacher' | 'user'
  role?: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export interface TeacherFromTable {
  id: string
  name: string
  email: string
  subject: string
  mobile: string
  fatherName: string
  gender: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export interface UserWithTeacherRole {
  id: string
  name: string
  email: string
  role: string
  phone?: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

/**
 * Transform a teacher from the Teacher table to unified format
 */
export function transformTeacherFromTable(teacher: TeacherFromTable): UnifiedTeacher {
  return {
    id: teacher.id,
    name: teacher.name,
    email: teacher.email,
    subject: teacher.subject,
    mobile: teacher.mobile,
    fatherName: teacher.fatherName,
    gender: teacher.gender,
    source: 'teacher',
    createdAt: teacher.createdAt,
    updatedAt: teacher.updatedAt
  }
}

/**
 * Transform a user with teacher role to unified format
 */
export function transformUserToTeacher(user: UserWithTeacherRole): UnifiedTeacher {
  return {
    id: user.id,
    name: user.name,
    email: user.email,
    mobile: user.phone,
    source: 'user',
    role: user.role,
    createdAt: user.createdAt,
    updatedAt: user.updatedAt
  }
}

/**
 * Combine and deduplicate teachers from both sources
 * Prioritizes Teacher table entries over User entries when email matches
 */
export function combineTeachers(
  teachersFromTable: TeacherFromTable[],
  usersWithTeacherRole: UserWithTeacherRole[]
): UnifiedTeacher[] {
  const unifiedTeachers: UnifiedTeacher[] = []
  const emailSet = new Set<string>()

  // First, add all teachers from the Teacher table
  for (const teacher of teachersFromTable) {
    const unified = transformTeacherFromTable(teacher)
    unifiedTeachers.push(unified)
    emailSet.add(teacher.email.toLowerCase())
  }

  // Then, add users with teacher role that don't already exist (by email)
  for (const user of usersWithTeacherRole) {
    if (!emailSet.has(user.email.toLowerCase())) {
      const unified = transformUserToTeacher(user)
      unifiedTeachers.push(unified)
      emailSet.add(user.email.toLowerCase())
    }
  }

  // Sort by name
  return unifiedTeachers.sort((a, b) => a.name.localeCompare(b.name))
}

/**
 * Filter teachers by search term
 */
export function filterTeachers(teachers: UnifiedTeacher[], searchTerm: string): UnifiedTeacher[] {
  if (!searchTerm.trim()) {
    return teachers
  }

  const term = searchTerm.toLowerCase()
  return teachers.filter(teacher => 
    teacher.name.toLowerCase().includes(term) ||
    teacher.email.toLowerCase().includes(term) ||
    (teacher.subject && teacher.subject.toLowerCase().includes(term)) ||
    (teacher.mobile && teacher.mobile.includes(term))
  )
}

/**
 * Get display name for teacher with source indicator
 */
export function getTeacherDisplayName(teacher: UnifiedTeacher): string {
  const sourceIndicator = teacher.source === 'user' ? ' (User)' : ''
  return `${teacher.name}${sourceIndicator}`
}

/**
 * Get teacher description for dropdowns
 */
export function getTeacherDescription(teacher: UnifiedTeacher): string {
  const parts = [teacher.email]
  if (teacher.subject) {
    parts.push(teacher.subject)
  }
  if (teacher.source === 'user') {
    parts.push('(User Account)')
  }
  return parts.join(' • ')
}

import { useState, useCallback } from 'react'

interface UsePermissionAlertResult {
  isPermissionDenied: boolean
  permissionMessage: string
  showPermissionDenied: (message?: string) => void
  hidePermissionDenied: () => void
}

/**
 * Custom hook for handling permission denied alerts
 * @param defaultMessage Default message to show when permission is denied
 * @returns Object with permission denied state and functions to show/hide the alert
 */
export function usePermissionAlert(
  defaultMessage = 'You do not have permission to access this feature.'
): UsePermissionAlertResult {
  const [isPermissionDenied, setIsPermissionDenied] = useState(false)
  const [permissionMessage, setPermissionMessage] = useState(defaultMessage)

  const showPermissionDenied = useCallback((message?: string) => {
    if (message) {
      setPermissionMessage(message)
    } else {
      setPermissionMessage(defaultMessage)
    }
    setIsPermissionDenied(true)
  }, [defaultMessage])

  const hidePermissionDenied = useCallback(() => {
    setIsPermissionDenied(false)
  }, [])

  return {
    isPermissionDenied,
    permissionMessage,
    showPermissionDenied,
    hidePermissionDenied
  }
}

export default usePermissionAlert

// <PERSON>ript to check for specific subject in database
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    // Check for Maths (8E) subject directly
    console.log('Checking for Maths (8E) subject directly...');
    const mathsSubject = await prisma.subject.findFirst({
      where: {
        name: 'Maths (8E)'
      },
      include: {
        class: true
      }
    });
    console.log('Maths (8E) subject:', mathsSubject || 'Not found');

    // Check for any subjects with "Maths" in the name
    console.log('\nChecking for subjects containing "Maths"...');
    const mathsSubjects = await prisma.subject.findMany({
      where: {
        name: {
          contains: 'Maths'
        }
      },
      include: {
        class: true
      }
    });
    console.log(`Found ${mathsSubjects.length} subjects containing "Maths"`);
    mathsSubjects.forEach(subject => {
      console.log(`- ${subject.name} (ID: ${subject.id}, ClassID: ${subject.classId}, ClassName: ${subject.class?.name || 'N/A'})`);
    });

    // Check for subjects associated with Class 8E
    console.log('\nChecking for subjects associated with Class 8E...');
    const class8E = await prisma.class.findFirst({
      where: {
        name: '8E'
      }
    });
    
    if (class8E) {
      const class8ESubjects = await prisma.subject.findMany({
        where: {
          classId: class8E.id
        }
      });
      console.log(`Found ${class8ESubjects.length} subjects for Class 8E`);
      class8ESubjects.forEach(subject => {
        console.log(`- ${subject.name} (ID: ${subject.id})`);
      });
    } else {
      console.log('Class 8E not found');
    }

    // Try fetching all subjects to see if there's a general issue
    console.log('\nFetching all subjects...');
    const allSubjects = await prisma.subject.findMany({
      orderBy: {
        name: 'asc'
      },
      take: 10 // Limit to 10 for brevity
    });
    console.log(`Found ${allSubjects.length} subjects (showing first 10)`);
    allSubjects.forEach(subject => {
      console.log(`- ${subject.name} (ID: ${subject.id}, ClassID: ${subject.classId})`);
    });

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();

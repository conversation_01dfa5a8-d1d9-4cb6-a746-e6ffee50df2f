'use client'

import React, { useState, useEffect } from 'react'
import { useDialogState } from '../../hooks/useDialogState'
import EditPaymentDialog from './EditPaymentDialog'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "../../components/ui/alert-dialog"
import {
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '../../components/ui/dialog'
import { SafeDialog } from '../../components/SafeDialog'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Label } from '../../components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../components/ui/table'
import {
  Calendar,
  Search,
  Download,
  FileText,
  Printer,
  RefreshCw,
  Filter,
  X,
  CheckCircle,
  CreditCard,
  DollarSign,
  User,
  BookOpen,
  Loader2,
  Edit,
  Trash2,
  AlertCircle,
  MoreVertical
} from 'lucide-react'
import { useQuery } from '@tanstack/react-query'
import { useToast } from '../../components/ui/use-toast'
import { format } from 'date-fns'

interface PaymentHistoryDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

interface PaymentRecord {
  id: string
  invoiceNumber: string
  studentId: string
  studentName: string
  className: string
  paymentDate: string
  forMonth?: string | null
  amount: number
  paymentPurpose: string
  paymentMethod: string
  transferId?: string
  status: string
  createdAt: string
  notes?: string | null
}

// Helper function to get month name from month number
const getMonthName = (monthNumber: string) => {
  const months = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];
  const index = parseInt(monthNumber) - 1;
  return months[index] || '';
};

// Helper function to extract month name from date string
const getMonthFromDate = (dateString: string) => {
  try {
    const date = new Date(dateString);
    const monthIndex = date.getMonth();
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return months[monthIndex];
  } catch (error) {
    return '-';
  }
};

export default function PaymentHistoryDialog({ open, onOpenChange }: PaymentHistoryDialogProps) {
  // Use our custom hook to manage dialog state safely
  const dialogState = useDialogState(open, onOpenChange);
  const [filters, setFilters] = useState({
    className: '',
    paymentPurpose: 'all',
    status: 'all',
    month: 'all'
  })
  const [activeFilters, setActiveFilters] = useState({
    className: '',
    paymentPurpose: 'all',
    status: 'all',
    month: 'all'
  })
  const [isFilterExpanded, setIsFilterExpanded] = useState(false)
  const [hasActiveFilters, setHasActiveFilters] = useState(false)
  const [selectedPayment, setSelectedPayment] = useState<PaymentRecord | null>(null)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { toast } = useToast()

  // Check if there are any active filters
  useEffect(() => {
    const hasFilters = Object.entries(activeFilters).some(([key, value]) => {
      // For both paymentPurpose, className, and status, we consider 'all' as not an active filter
      if (key === 'paymentPurpose' || key === 'className' || key === 'status') {
        return value !== '' && value !== 'all'
      }
      return value !== ''
    })
    setHasActiveFilters(hasFilters)
  }, [activeFilters])

  // Fetch payment history
  const {
    data: paymentHistory = [],
    isLoading,
    isError,
    refetch
  } = useQuery({
    queryKey: ['paymentHistory', activeFilters],
    queryFn: async () => {
      // Special handling for unpaid status
      if (activeFilters.status === 'unpaid' &&
          activeFilters.className && activeFilters.className !== 'all' &&
          activeFilters.paymentPurpose && activeFilters.paymentPurpose !== 'all') {

        // For unpaid status, we need both className and paymentPurpose
        const unpaidQueryParams = new URLSearchParams()
        unpaidQueryParams.append('className', activeFilters.className)
        unpaidQueryParams.append('paymentPurpose', activeFilters.paymentPurpose)

        const unpaidUrl = `/api/accounting/unpaid-students?${unpaidQueryParams.toString()}`
        const unpaidResponse = await fetch(unpaidUrl)

        if (!unpaidResponse.ok) {
          throw new Error('Failed to fetch unpaid students')
        }

        return unpaidResponse.json()
      } else {
        // Regular payment history query
        const queryParams = new URLSearchParams()

        if (activeFilters.className && activeFilters.className !== 'all') queryParams.append('className', activeFilters.className)
        if (activeFilters.paymentPurpose && activeFilters.paymentPurpose !== 'all') queryParams.append('paymentPurpose', activeFilters.paymentPurpose)
        if (activeFilters.status && activeFilters.status !== 'all' && activeFilters.status !== 'unpaid') queryParams.append('status', activeFilters.status)

        // Handle month filter
        if (activeFilters.month && activeFilters.month !== 'all') {
          // Add the month filter directly - ensure it's a string
          queryParams.append('forMonth', activeFilters.month.toString())
          console.log(`Filtering by month: ${activeFilters.month} (${getMonthName(activeFilters.month.toString())})`)
        }

        const url = `/api/accounting/payment-history?${queryParams.toString()}`
        console.log('Fetching payment history from:', url)
        const response = await fetch(url)

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}))
          console.error('API error:', errorData)
          throw new Error(`Failed to fetch payment history: ${response.status} ${response.statusText}`)
        }

        const data = await response.json()
        console.log(`Fetched ${data.length} payment records`)
        return data
      }
    },
    enabled: dialogState.isOpen
  })

  // Reset filters
  const resetFilters = () => {
    // Reset both the form filters and active filters
    const emptyFilters = {
      className: '',
      paymentPurpose: 'all',
      status: 'all',
      month: 'all'
    }

    setFilters(emptyFilters)
    setActiveFilters(emptyFilters)

    toast({
      title: 'Filters Reset',
      description: 'All filters have been cleared',
    })
  }

  // Apply filters
  const applyFilters = () => {
    // Update active filters with current form values
    setActiveFilters(filters)

    toast({
      title: 'Filters Applied',
      description: 'Payment history has been filtered',
    })
  }

  // Reset filters when dialog opens
  useEffect(() => {
    if (dialogState.isOpen) {
      setIsFilterExpanded(false)
    }
  }, [dialogState.isOpen])

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ET', {
      style: 'currency',
      currency: 'ETB',
      minimumFractionDigits: 2
    }).format(amount)
  }

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM dd, yyyy')
    } catch (error) {
      return dateString
    }
  }

  // Format month display for both single and multi-month payments
  const formatMonthDisplay = (forMonth: string | null) => {
    if (!forMonth) return '-';

    if (forMonth.includes(',')) {
      // Handle multi-month format
      return forMonth.split(',')
        .map(m => getMonthName(m))
        .join(', ');
    }

    // Handle single month format
    return getMonthName(forMonth);
  }

  // Handle print
  const handlePrint = () => {
    if (!paymentHistory || paymentHistory.length === 0) {
      toast({
        title: 'No Data to Print',
        description: 'There are no payment records to print',
        variant: 'destructive'
      })
      return
    }

    // Create a printable version of the table
    const printWindow = window.open('', '_blank')
    if (!printWindow) {
      toast({
        title: 'Print Failed',
        description: 'Unable to open print window. Please check your browser settings.',
        variant: 'destructive'
      })
      return
    }

    // Generate the HTML content for printing
    const printContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Payment History - ${new Date().toLocaleDateString()}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          h1 { color: #1e40af; font-size: 24px; margin-bottom: 10px; }
          .subtitle { color: #6b7280; margin-bottom: 20px; }
          table { width: 100%; border-collapse: collapse; margin-top: 20px; table-layout: fixed; }
          th { background-color: #f3f4f6; text-align: left; padding: 8px; border-bottom: 1px solid #e5e7eb; font-size: 12px; }
          td { padding: 8px; border-bottom: 1px solid #e5e7eb; font-size: 12px; overflow: hidden; text-overflow: ellipsis; }
          .footer { margin-top: 30px; font-size: 12px; color: #6b7280; text-align: center; }
          .table-container { width: 100%; }

          /* Column widths */
          th:nth-child(1), td:nth-child(1) { width: 10%; } /* Invoice # */
          th:nth-child(2), td:nth-child(2) { width: 18%; } /* Student */
          th:nth-child(3), td:nth-child(3) { width: 7%; }  /* Class */
          th:nth-child(4), td:nth-child(4) { width: 10%; } /* Date */
          th:nth-child(5), td:nth-child(5) { width: 12%; } /* As Of (Month) */
          th:nth-child(6), td:nth-child(6) { width: 15%; } /* Purpose */
          th:nth-child(7), td:nth-child(7) { width: 10%; } /* Amount */
          th:nth-child(8), td:nth-child(8) { width: 12%; } /* Method */
          th:nth-child(9), td:nth-child(9) { width: 6%; } /* Status */

          /* Add spacing and prevent overlap */
          th, td {
            padding: 8px 12px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          /* Allow student name and purpose to wrap if needed */
          td:nth-child(2) div, td:nth-child(6) { white-space: normal; }

          @media print {
            body { margin: 0; padding: 15px; }
            button { display: none; }
            table { page-break-inside: auto; }
            tr { page-break-inside: avoid; page-break-after: auto; }
            td { word-break: break-word; }
          }
        </style>
      </head>
      <body>
        <h1>Payment History</h1>
        <div class="subtitle">Generated on ${new Date().toLocaleString()}</div>

        ${hasActiveFilters ? `
        <div style="margin-bottom: 20px; padding: 10px; background-color: #f3f4f6; border-radius: 5px;">
          <strong>Filters Applied:</strong>
          ${activeFilters.studentId ? `<div>Student ID: ${activeFilters.studentId}</div>` : ''}
          ${activeFilters.className ? `<div>Class: ${activeFilters.className}</div>` : ''}
          ${activeFilters.paymentPurpose && activeFilters.paymentPurpose !== 'all' ? `<div>Purpose: ${activeFilters.paymentPurpose}</div>` : ''}
          ${activeFilters.month && activeFilters.month !== 'all' ? `<div>As Of (Month): ${getMonthName(activeFilters.month)}</div>` : ''}
          ${activeFilters.startDate ? `<div>From Date: ${formatDate(activeFilters.startDate)}</div>` : ''}
          ${activeFilters.endDate ? `<div>To Date: ${formatDate(activeFilters.endDate)}</div>` : ''}
        </div>
        ` : ''}

        <div class="table-container">
          <table>
            <thead>
              <tr>
                <th>Invoice #</th>
                <th>Student</th>
                <th>Class</th>
                <th>Date</th>
                <th>As Of (Month)</th>
                <th>Purpose</th>
                <th>Amount</th>
                <th>Method</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody>
              ${paymentHistory.map(payment => `
                <tr>
                  <td>${payment.invoiceNumber}</td>
                  <td>
                    <div style="font-weight: 500; overflow: hidden; text-overflow: ellipsis;">${payment.studentName}</div>
                    <div style="font-size: 11px; color: #6b7280; overflow: hidden; text-overflow: ellipsis;">${payment.studentId}</div>
                  </td>
                  <td>${payment.className}</td>
                  <td>${formatDate(payment.paymentDate)}</td>
                  <td>${formatMonthDisplay(payment.forMonth)}</td>
                  <td style="word-break: break-word;">${payment.paymentPurpose}</td>
                  <td style="text-align: right;">${formatCurrency(payment.amount)}</td>
                  <td>
                    <div>${payment.paymentMethod}</div>
                    ${payment.transferId ? `<div style="font-size: 11px; color: #6b7280; overflow: hidden; text-overflow: ellipsis;">${payment.transferId}</div>` : ''}
                  </td>
                  <td><span style="color: ${payment.status === 'paid' ? '#15803d' : '#b45309'}">${payment.status === 'paid' ? 'Paid' : 'Unpaid'}</span></td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>

        <div class="footer">
          <p>Total Records: ${paymentHistory.length}</p>
          <p>Alfalah Islamic School - Payment History Report</p>
        </div>

        <div style="text-align: center; margin-top: 20px;">
          <button onclick="window.print()">Print this page</button>
        </div>
      </body>
      </html>
    `

    printWindow.document.open()
    printWindow.document.write(printContent)
    printWindow.document.close()

    // Give some time for the content to load before printing
    setTimeout(() => {
      printWindow.print()
    }, 500)

    toast({
      title: 'Print Initiated',
      description: 'Preparing payment history for printing',
    })
  }

  // Handle export
  const handleExport = () => {
    if (!paymentHistory || paymentHistory.length === 0) {
      toast({
        title: 'No Data to Export',
        description: 'There are no payment records to export',
        variant: 'destructive'
      })
      return
    }

    // Create CSV content with headers matching the print view
    const headers = [
      'Invoice Number',
      'Student ID',
      'Student Name',
      'Class',
      'Payment Date',
      'As Of (Month)', // Added to match print view
      'Payment Purpose',
      'Amount',
      'Payment Method',
      'Transfer ID',
      'Status'
    ]

    // Format the data to match the print view
    const csvContent = [
      headers.join(','),
      ...paymentHistory.map(payment => {
        // Format each field to match the print view
        const formattedDate = formatDate(payment.paymentDate);
        const formattedMonths = formatMonthDisplay(payment.forMonth);
        const formattedAmount = payment.amount.toFixed(2); // Format amount with 2 decimal places

        return [
          `"${payment.invoiceNumber}"`,
          `"${payment.studentId}"`,
          `"${payment.studentName}"`, // Wrap in quotes to handle commas in names
          `"${payment.className}"`,
          `"${formattedDate}"`,
          `"${formattedMonths}"`, // Formatted months
          `"${payment.paymentPurpose}"`,
          formattedAmount,
          `"${payment.paymentMethod}"`,
          payment.transferId ? `"${payment.transferId}"` : '',
          `"${payment.status === 'paid' ? 'Paid' : 'Unpaid'}"`
        ].join(',');
      })
    ].join('\n');

    // Add BOM for proper UTF-8 encoding in Excel
    const BOM = '\uFEFF';
    const csvContentWithBOM = BOM + csvContent;

    // Create a blob and download link
    const blob = new Blob([csvContentWithBOM], { type: 'text/csv;charset=utf-8;' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.setAttribute('href', url)

    // Include filter information in the filename if filters are applied
    let filename = 'payment_history';
    if (hasActiveFilters) {
      if (activeFilters.className && activeFilters.className !== 'all') {
        filename += `_class-${activeFilters.className}`;
      }
      if (activeFilters.paymentPurpose && activeFilters.paymentPurpose !== 'all') {
        filename += `_purpose-${activeFilters.paymentPurpose.replace(/\s+/g, '-').toLowerCase()}`;
      }
      if (activeFilters.month && activeFilters.month !== 'all') {
        filename += `_month-${getMonthName(activeFilters.month).toLowerCase()}`;
      }
      if (activeFilters.status && activeFilters.status !== 'all') {
        filename += `_${activeFilters.status}`;
      }
    }
    filename += `_${new Date().toISOString().split('T')[0]}`;

    link.setAttribute('download', `${filename}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    toast({
      title: 'Export Successful',
      description: 'Payment history has been exported to CSV with all columns and formatting',
    })
  }

  // Handle edit payment
  const handleEditPayment = (payment: PaymentRecord) => {
    setSelectedPayment(payment)
    setIsEditDialogOpen(true)
  }

  // Handle delete payment
  const handleDeletePayment = (payment: PaymentRecord) => {
    setSelectedPayment(payment)
    setIsDeleteDialogOpen(true)
  }

  // Confirm delete payment
  const confirmDeletePayment = async () => {
    if (!selectedPayment) return

    setIsSubmitting(true)

    try {
      const response = await fetch(`/api/accounting/payments/${selectedPayment.id}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error('Failed to delete payment')
      }

      // Refetch payment history
      refetch()

      toast({
        title: 'Payment Deleted',
        description: `Invoice #${selectedPayment.invoiceNumber} has been deleted successfully`,
      })

      // Close the dialog
      setIsDeleteDialogOpen(false)
      setSelectedPayment(null)
    } catch (error) {
      console.error('Error deleting payment:', error)
      toast({
        title: 'Error',
        description: 'Failed to delete payment. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <>
    <SafeDialog
      open={dialogState.isOpen}
      onOpenChange={dialogState.handleOpenChange}
      maxWidth="max-w-[1200px]"
      title={
        <div className="text-xl flex items-center">
          <FileText className="mr-2 h-5 w-5 text-blue-600" />
          Payment History
        </div>
      }
      description="View and filter payment records"
    >
      <div className="w-full max-h-[90vh] overflow-hidden flex flex-col">

        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center">
            <Button
              variant={hasActiveFilters ? "default" : "outline"}
              size="sm"
              onClick={() => setIsFilterExpanded(!isFilterExpanded)}
              className={`text-sm relative ${hasActiveFilters ? "bg-blue-600 text-white hover:bg-blue-700" : ""}`}
            >
              <Filter className="h-4 w-4 mr-2" />
              {isFilterExpanded ? 'Hide Filters' : 'Show Filters'}
              {hasActiveFilters && !isFilterExpanded && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                  {Object.entries(activeFilters).filter(([key, v]) => v !== '' && (key !== 'paymentPurpose' || v !== 'all')).length}
                </span>
              )}
            </Button>

            {hasActiveFilters && (
              <Button
                variant="ghost"
                size="sm"
                onClick={resetFilters}
                className="text-sm ml-2"
              >
                <X className="h-4 w-4 mr-1" />
                Clear
              </Button>
            )}
          </div>

          <div className="flex space-x-2">
            <Button variant="outline" size="sm" onClick={handlePrint} className="text-sm">
              <Printer className="h-4 w-4 mr-2" />
              Print
            </Button>
            <Button variant="outline" size="sm" onClick={handleExport} className="text-sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button variant="outline" size="sm" onClick={() => refetch()} className="text-sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </div>

        {isFilterExpanded && (
          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-md mb-4 border border-gray-200 dark:border-gray-700 shadow-sm">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-sm font-medium flex items-center">
                <Filter className="h-4 w-4 mr-2 text-blue-500" />
                Filter Payment Records
              </h3>
              {hasActiveFilters && (
                <div className="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded-full">
                  {Object.entries(activeFilters).filter(([key, v]) => v !== '' && (key !== 'paymentPurpose' || v !== 'all')).length} active {Object.entries(activeFilters).filter(([key, v]) => v !== '' && (key !== 'paymentPurpose' || v !== 'all')).length === 1 ? 'filter' : 'filters'}
                </div>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="space-y-2">
                <Label
                  htmlFor="filter-class"
                  className={`text-sm flex items-center justify-between ${activeFilters.className ? 'text-blue-600 font-medium' : ''}`}
                >
                  <span className="flex items-center">
                    <BookOpen className="h-3.5 w-3.5 mr-1.5 text-gray-500" />
                    Class
                  </span>
                  {activeFilters.className && (
                    <span className="text-xs bg-blue-100 text-blue-800 px-1.5 py-0.5 rounded">Active</span>
                  )}
                </Label>
                <Select
                  value={filters.className}
                  onValueChange={(value) => setFilters({ ...filters, className: value })}
                >
                  <SelectTrigger
                    id="filter-class"
                    className={`h-9 ${activeFilters.className ? 'border-blue-300 ring-1 ring-blue-200' : ''}`}
                  >
                    <SelectValue placeholder="Select class" />
                  </SelectTrigger>
                  <SelectContent className="max-h-[300px] overflow-y-auto">
                    <SelectItem value="all">All Classes</SelectItem>

                    {/* Grade 1 */}
                    <SelectItem value="1A">Class 1A</SelectItem>
                    <SelectItem value="1B">Class 1B</SelectItem>
                    <SelectItem value="1C">Class 1C</SelectItem>
                    <SelectItem value="1D">Class 1D</SelectItem>

                    {/* Grade 2 */}
                    <SelectItem value="2A">Class 2A</SelectItem>
                    <SelectItem value="2B">Class 2B</SelectItem>
                    <SelectItem value="2C">Class 2C</SelectItem>
                    <SelectItem value="2D">Class 2D</SelectItem>

                    {/* Grade 3 */}
                    <SelectItem value="3A">Class 3A</SelectItem>
                    <SelectItem value="3B">Class 3B</SelectItem>
                    <SelectItem value="3C">Class 3C</SelectItem>
                    <SelectItem value="3D">Class 3D</SelectItem>

                    {/* Grade 4 */}
                    <SelectItem value="4A">Class 4A</SelectItem>
                    <SelectItem value="4B">Class 4B</SelectItem>
                    <SelectItem value="4C">Class 4C</SelectItem>
                    <SelectItem value="4D">Class 4D</SelectItem>

                    {/* Grade 5 */}
                    <SelectItem value="5A">Class 5A</SelectItem>
                    <SelectItem value="5B">Class 5B</SelectItem>
                    <SelectItem value="5C">Class 5C</SelectItem>
                    <SelectItem value="5D">Class 5D</SelectItem>

                    {/* Grade 6 */}
                    <SelectItem value="6A">Class 6A</SelectItem>
                    <SelectItem value="6B">Class 6B</SelectItem>
                    <SelectItem value="6C">Class 6C</SelectItem>
                    <SelectItem value="6D">Class 6D</SelectItem>

                    {/* Grade 7 */}
                    <SelectItem value="7A">Class 7A</SelectItem>
                    <SelectItem value="7B">Class 7B</SelectItem>
                    <SelectItem value="7C">Class 7C</SelectItem>
                    <SelectItem value="7D">Class 7D</SelectItem>

                    {/* Grade 8 */}
                    <SelectItem value="8A">Class 8A</SelectItem>
                    <SelectItem value="8B">Class 8B</SelectItem>
                    <SelectItem value="8C">Class 8C</SelectItem>
                    <SelectItem value="8D">Class 8D</SelectItem>
                    <SelectItem value="8E">Class 8E</SelectItem>

                    {/* Grade 9 */}
                    <SelectItem value="9A">Class 9A</SelectItem>
                    <SelectItem value="9B">Class 9B</SelectItem>
                    <SelectItem value="9C">Class 9C</SelectItem>
                    <SelectItem value="9D">Class 9D</SelectItem>

                    {/* Grade 10 */}
                    <SelectItem value="10A">Class 10A</SelectItem>
                    <SelectItem value="10B">Class 10B</SelectItem>
                    <SelectItem value="10C">Class 10C</SelectItem>
                    <SelectItem value="10D">Class 10D</SelectItem>

                    {/* Grade 11 */}
                    <SelectItem value="11A">Class 11A</SelectItem>
                    <SelectItem value="11B">Class 11B</SelectItem>
                    <SelectItem value="11C">Class 11C</SelectItem>
                    <SelectItem value="11D">Class 11D</SelectItem>

                    {/* Grade 12 */}
                    <SelectItem value="12A">Class 12A</SelectItem>
                    <SelectItem value="12B">Class 12B</SelectItem>
                    <SelectItem value="12C">Class 12C</SelectItem>
                    <SelectItem value="12D">Class 12D</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="filter-purpose"
                  className={`text-sm flex items-center justify-between ${activeFilters.paymentPurpose && activeFilters.paymentPurpose !== 'all' ? 'text-blue-600 font-medium' : ''}`}
                >
                  <span className="flex items-center">
                    <FileText className="h-3.5 w-3.5 mr-1.5 text-gray-500" />
                    Payment Purpose
                  </span>
                  {activeFilters.paymentPurpose && activeFilters.paymentPurpose !== 'all' && (
                    <span className="text-xs bg-blue-100 text-blue-800 px-1.5 py-0.5 rounded">Active</span>
                  )}
                </Label>
                <Select
                  value={filters.paymentPurpose}
                  onValueChange={(value) => setFilters({ ...filters, paymentPurpose: value })}
                >
                  <SelectTrigger
                    id="filter-purpose"
                    className={`h-9 ${activeFilters.paymentPurpose && activeFilters.paymentPurpose !== 'all' ? 'border-blue-300 ring-1 ring-blue-200' : ''}`}
                  >
                    <SelectValue placeholder="Select purpose" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Purposes</SelectItem>
                    <SelectItem value="Tuition Fee (Monthly)">Tuition Fee (Monthly)</SelectItem>
                    <SelectItem value="Registration Fee">Registration Fee</SelectItem>
                    <SelectItem value="Exam Fee">Exam Fee</SelectItem>
                    <SelectItem value="Library Fee">Library Fee</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="filter-month"
                  className={`text-sm flex items-center justify-between ${activeFilters.month && activeFilters.month !== 'all' ? 'text-blue-600 font-medium' : ''}`}
                >
                  <span className="flex items-center">
                    <Calendar className="h-3.5 w-3.5 mr-1.5 text-gray-500" />
                    As Of (Month)
                  </span>
                  {activeFilters.month && activeFilters.month !== 'all' && (
                    <span className="text-xs bg-blue-100 text-blue-800 px-1.5 py-0.5 rounded">Active</span>
                  )}
                </Label>
                <Select
                  value={filters.month}
                  onValueChange={(value) => setFilters({ ...filters, month: value })}
                >
                  <SelectTrigger
                    id="filter-month"
                    className={`h-9 ${activeFilters.month && activeFilters.month !== 'all' ? 'border-blue-300 ring-1 ring-blue-200' : ''}`}
                  >
                    <SelectValue placeholder="Select month" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Months</SelectItem>
                    <SelectItem value="1">January</SelectItem>
                    <SelectItem value="2">February</SelectItem>
                    <SelectItem value="3">March</SelectItem>
                    <SelectItem value="4">April</SelectItem>
                    <SelectItem value="5">May</SelectItem>
                    <SelectItem value="6">June</SelectItem>
                    <SelectItem value="7">July</SelectItem>
                    <SelectItem value="8">August</SelectItem>
                    <SelectItem value="9">September</SelectItem>
                    <SelectItem value="10">October</SelectItem>
                    <SelectItem value="11">November</SelectItem>
                    <SelectItem value="12">December</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="filter-status"
                  className={`text-sm flex items-center justify-between ${activeFilters.status && activeFilters.status !== 'all' ? 'text-blue-600 font-medium' : ''}`}
                >
                  <span className="flex items-center">
                    <CheckCircle className="h-3.5 w-3.5 mr-1.5 text-gray-500" />
                    Status
                  </span>
                  {activeFilters.status && activeFilters.status !== 'all' && (
                    <span className="text-xs bg-blue-100 text-blue-800 px-1.5 py-0.5 rounded">Active</span>
                  )}
                </Label>
                <Select
                  value={filters.status}
                  onValueChange={(value) => setFilters({ ...filters, status: value })}
                >
                  <SelectTrigger
                    id="filter-status"
                    className={`h-9 ${activeFilters.status && activeFilters.status !== 'all' ? 'border-blue-300 ring-1 ring-blue-200' : ''}`}
                  >
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="paid">Paid</SelectItem>
                    <SelectItem value="unpaid">Unpaid</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-end space-x-2">
                <Button
                  onClick={applyFilters}
                  className="h-9 bg-blue-600 hover:bg-blue-700"
                >
                  <Search className="h-4 w-4 mr-2" />
                  Apply Filters
                </Button>
                <Button
                  variant="outline"
                  onClick={resetFilters}
                  className="h-9"
                >
                  <X className="h-4 w-4 mr-2" />
                  Reset
                </Button>
              </div>
            </div>

            {hasActiveFilters && (
              <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
                <div className="text-xs text-gray-500 mb-2">Active filters:</div>
                <div className="flex flex-wrap gap-2">
                  {activeFilters.className && activeFilters.className !== 'all' && (
                    <div className="bg-blue-50 text-blue-700 text-xs px-2 py-1 rounded-full flex items-center">
                      <BookOpen className="h-3 w-3 mr-1" />
                      Class: {activeFilters.className}
                    </div>
                  )}
                  {activeFilters.paymentPurpose && activeFilters.paymentPurpose !== 'all' && (
                    <div className="bg-blue-50 text-blue-700 text-xs px-2 py-1 rounded-full flex items-center">
                      <FileText className="h-3 w-3 mr-1" />
                      Purpose: {activeFilters.paymentPurpose}
                    </div>
                  )}
                  {activeFilters.month && activeFilters.month !== 'all' && (
                    <div className="bg-blue-50 text-blue-700 text-xs px-2 py-1 rounded-full flex items-center">
                      <Calendar className="h-3 w-3 mr-1" />
                      As Of (Month): {getMonthName(activeFilters.month)}
                    </div>
                  )}

                  {activeFilters.status && activeFilters.status !== 'all' && (
                    <div className="bg-blue-50 text-blue-700 text-xs px-2 py-1 rounded-full flex items-center">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Status: {activeFilters.status === 'paid' ? 'Paid' : 'Unpaid'}
                    </div>
                  )}

                  {/* Show a note when unpaid status is selected but missing required filters */}
                  {activeFilters.status === 'unpaid' &&
                   (activeFilters.className === 'all' || activeFilters.paymentPurpose === 'all') && (
                    <div className="bg-amber-50 text-amber-700 text-xs px-2 py-1 rounded-full flex items-center">
                      <AlertCircle className="h-3 w-3 mr-1" />
                      Note: Both Class and Payment Purpose are required for Unpaid filter
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        )}

        <div className="flex-1 overflow-x-auto overflow-y-auto w-full" style={{ minWidth: '1140px' }}>
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
              <span className="ml-2 text-lg">Loading payment history...</span>
            </div>
          ) : isError ? (
            <div className="flex items-center justify-center h-64 text-red-500">
              <p>Error loading payment history. Please try again.</p>
            </div>
          ) : paymentHistory.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-64 text-gray-500">
              <FileText className="h-12 w-12 mb-2 opacity-20" />
              <p>No payment records found</p>
              <p className="text-sm">Try adjusting your filters or add new payments</p>
            </div>
          ) : (
            <Table className="w-full table-fixed border-separate border-spacing-0">
              <TableHeader className="bg-gray-50 dark:bg-gray-800 sticky top-0">
                <TableRow>
                  <TableHead className="w-[120px] px-4">Invoice #</TableHead>
                  <TableHead className="w-[180px] px-4">Student</TableHead>
                  <TableHead className="w-[80px] px-4">Class</TableHead>
                  <TableHead className="w-[100px] px-4">Date</TableHead>
                  <TableHead className="w-[110px] px-4">As Of (Month)</TableHead>
                  <TableHead className="w-[140px] px-4">Purpose</TableHead>
                  <TableHead className="w-[100px] px-4">Amount</TableHead>
                  <TableHead className="w-[120px] px-4">Method</TableHead>
                  <TableHead className="w-[90px] px-4">Status</TableHead>
                  <TableHead className="w-[100px] px-4 text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paymentHistory.map((payment: PaymentRecord) => (
                  <TableRow key={payment.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                    <TableCell className="font-medium w-[120px] px-4 truncate">
                      {payment.invoiceNumber}
                    </TableCell>
                    <TableCell className="w-[180px] px-4">
                      <div className="flex flex-col">
                        <span className="truncate">{payment.studentName}</span>
                        <span className="text-xs text-gray-500 truncate">{payment.studentId}</span>
                      </div>
                    </TableCell>
                    <TableCell className="w-[80px] px-4">{payment.className}</TableCell>
                    <TableCell className="w-[100px] px-4">{formatDate(payment.paymentDate)}</TableCell>
                    <TableCell className="w-[110px] px-4">
                      {formatMonthDisplay(payment.forMonth)}
                    </TableCell>
                    <TableCell className="w-[140px] px-4 truncate">
                      <span className="truncate block">{payment.paymentPurpose}</span>
                    </TableCell>
                    <TableCell className="font-medium w-[100px] px-4">{formatCurrency(payment.amount)}</TableCell>
                    <TableCell className="w-[120px] px-4">
                      <div className="flex items-center">
                        {payment.paymentMethod === 'cash' ? (
                          <DollarSign className="h-4 w-4 mr-1 text-green-500 flex-shrink-0" />
                        ) : (
                          <CreditCard className="h-4 w-4 mr-1 text-blue-500 flex-shrink-0" />
                        )}
                        <span className="capitalize">{payment.paymentMethod}</span>
                        {payment.transferId && (
                          <span className="text-xs text-gray-500 ml-1 truncate">({payment.transferId})</span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="w-[90px] px-4">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        payment.status === 'paid'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-amber-100 text-amber-800'
                      }`}>
                        {payment.status === 'paid' ? (
                          <CheckCircle className="h-3 w-3 mr-1 flex-shrink-0" />
                        ) : (
                          <AlertCircle className="h-3 w-3 mr-1 flex-shrink-0" />
                        )}
                        {payment.status === 'paid' ? 'Paid' : 'Unpaid'}
                      </span>
                    </TableCell>
                    <TableCell className="text-right w-[100px] px-4">
                      <div className="flex justify-end space-x-1">
                        {payment.status === 'paid' ? (
                          <>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                              onClick={() => handleEditPayment(payment)}
                              title="Edit Payment"
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8 text-red-600 hover:text-red-700 hover:bg-red-50"
                              onClick={() => handleDeletePayment(payment)}
                              title="Delete Payment"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </>
                        ) : (
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-8 text-blue-600 hover:text-blue-700 hover:bg-blue-50 text-xs"
                            onClick={() => {
                              // Close this dialog and open the fee invoice page
                              dialogState.close();
                              // You could add navigation to the fee invoice page here if needed
                            }}
                            title="Create Invoice"
                          >
                            Create Invoice
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </div>

        <div className="mt-4">
          <div className="flex justify-between w-full items-center">
            <div className="text-sm text-gray-500">
              {paymentHistory.length} records found
            </div>
            <Button onClick={dialogState.close}>Close</Button>
          </div>
        </div>
      </div>
    </SafeDialog>

    {/* Delete Confirmation Dialog */}
    <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center text-red-600">
            <AlertCircle className="h-5 w-5 mr-2" />
            Confirm Deletion
          </AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to delete the payment record for invoice <strong>{selectedPayment?.invoiceNumber}</strong>?
            <div className="mt-4 p-4 bg-gray-50 rounded-md text-sm">
              <div><strong>Student:</strong> {selectedPayment?.studentName}</div>
              <div><strong>Amount:</strong> {selectedPayment && formatCurrency(selectedPayment.amount)}</div>
              <div><strong>Date:</strong> {selectedPayment && formatDate(selectedPayment.paymentDate)}</div>
            </div>
            <div className="mt-2 text-red-600">This action cannot be undone.</div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isSubmitting}>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={(e) => {
              e.preventDefault();
              confirmDeletePayment();
            }}
            className="bg-red-600 hover:bg-red-700 text-white"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Deleting...
              </>
            ) : (
              <>
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Payment
              </>
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>

    {/* Edit Payment Dialog */}
    <EditPaymentDialog
      open={isEditDialogOpen}
      onOpenChange={setIsEditDialogOpen}
      payment={selectedPayment}
      onSuccess={() => {
        refetch();
        setSelectedPayment(null);
      }}
    />
    </>
  )
}

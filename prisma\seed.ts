import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database...')

  // Create classes
  const class1A = await prisma.class.create({
    data: {
      name: '1A',
      totalStudents: 25,
      totalSubjects: 6,
    },
  })

  const class1B = await prisma.class.create({
    data: {
      name: '1B',
      totalStudents: 22,
      totalSubjects: 6,
    },
  })

  const class2A = await prisma.class.create({
    data: {
      name: '2A',
      totalStudents: 24,
      totalSubjects: 7,
    },
  })

  const class2B = await prisma.class.create({
    data: {
      name: '2B',
      totalStudents: 23,
      totalSubjects: 7,
    },
  })

  console.log('✅ Created classes')

  // Create teachers
  const teacher1 = await prisma.teacher.create({
    data: {
      name: '<PERSON>',
      fatherName: '<PERSON>',
      gender: 'Male',
      email: '<EMAIL>',
      subject: 'Mathematics',
      mobile: '1234567890',
    },
  })

  const teacher2 = await prisma.teacher.create({
    data: {
      name: '<PERSON>',
      father<PERSON>ame: '<PERSON>',
      gender: 'Female',
      email: '<EMAIL>',
      subject: 'English',
      mobile: '2345678901',
    },
  })

  const teacher3 = await prisma.teacher.create({
    data: {
      name: 'Michael Brown',
      fatherName: 'James Brown',
      gender: 'Male',
      email: '<EMAIL>',
      subject: 'Science',
      mobile: '3456789012',
    },
  })

  const teacher4 = await prisma.teacher.create({
    data: {
      name: 'Emily Davis',
      fatherName: 'William Davis',
      gender: 'Female',
      email: '<EMAIL>',
      subject: 'History',
      mobile: '4567890123',
    },
  })

  const teacher5 = await prisma.teacher.create({
    data: {
      name: 'Ahmed Hassan',
      fatherName: 'Omar Hassan',
      gender: 'Male',
      email: '<EMAIL>',
      subject: 'Arabic',
      mobile: '5678901234',
    },
  })

  console.log('✅ Created teachers')

  // Assign HR teachers to some classes
  await prisma.class.update({
    where: { id: class1A.id },
    data: { hrTeacherId: teacher1.id },
  })

  await prisma.class.update({
    where: { id: class1B.id },
    data: { hrTeacherId: teacher2.id },
  })

  await prisma.class.update({
    where: { id: class2A.id },
    data: { hrTeacherId: teacher3.id },
  })

  // Leave class2B without HR teacher for testing

  console.log('✅ Assigned HR teachers')

  // Create users
  await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password: password
      name: 'Admin User',
      role: 'ADMIN',
    },
  })

  await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: '$2a$10$CwTycUXWue0Thq9StjUM0uJ4uZypmHEcxp0E4llvQUbx/A6qdeIWO', // password: admin@123
      name: 'Super Admin',
      role: 'SUPER_ADMIN',
    },
  })

  console.log('✅ Created users')

  // Create some subjects
  await prisma.subject.createMany({
    data: [
      { name: 'Mathematics', classId: class1A.id },
      { name: 'English', classId: class1A.id },
      { name: 'Science', classId: class1A.id },
      { name: 'Arabic', classId: class1B.id },
      { name: 'Quran', classId: class1B.id },
      { name: 'Islamic Education', classId: class2A.id },
      { name: 'Somali', classId: class2A.id },
      { name: 'Amharic', classId: class2B.id },
      { name: 'History', classId: class2B.id },
      { name: 'Geography', classId: class2B.id },
    ],
  })

  console.log('✅ Created subjects')

  console.log('🎉 Database seeded successfully!')
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })

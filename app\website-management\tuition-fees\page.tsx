"use client"

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import dynamic from 'next/dynamic'
import { useToast } from '@/app/components/ui/use-toast'
import { Button } from '@/app/components/ui/button'
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/app/components/ui/tabs'
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/ui/card'
import { Globe, Wallet, CreditCard, Calendar, Receipt, Upload } from 'lucide-react'
import TuitionFeesTab from './components/TuitionFeesTab'
import TuitionRatesTab from './components/TuitionRatesTab'
import PaymentPlansTab from './components/PaymentPlansTab'
import AdditionalFeesTab from './components/AdditionalFeesTab'
import SubmitReceiptTab from './components/SubmitReceiptTab'
import Sidebar from '@/app/components/Sidebar'

// Import Header component with no SSR to avoid hydration issues
const Header = dynamic(() => import('@/app/components/Header'), {
  ssr: false
})

export default function TuitionFeesManagement() {
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState('overview')
  const [isMounted, setIsMounted] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false)

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  const toggleMobileSidebar = () => {
    setMobileSidebarOpen(!mobileSidebarOpen)
  }

  useEffect(() => {
    setIsMounted(true)
    // Simulate loading data
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 500)
    return () => clearTimeout(timer)
  }, [])

  // Only show loading while mounting
  if (!isMounted || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-600"></div>
      </div>
    )
  }

  return (
    <div className="flex h-screen bg-gray-100 dark:bg-gray-900">
      <Sidebar
        isCollapsed={sidebarCollapsed}
        toggleSidebar={toggleSidebar}
        isMobileOpen={mobileSidebarOpen}
      />

      <main className={`main-content ${sidebarCollapsed ? 'sidebar-collapsed' : ''} w-full h-screen overflow-y-auto`}>
        <Header toggleMobileSidebar={toggleMobileSidebar} />

        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-2xl font-semibold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent inline-block">
                Tuition & Fees Management
              </h1>
              <p className="text-gray-600 dark:text-gray-300 mt-1">
                Manage tuition rates, fees, and payment information displayed on the website
              </p>
            </div>
            <div className="flex space-x-2">
              <Link href="/website/admissions/tuition-fees" target="_blank">
                <Button variant="outline" size="sm" className="flex items-center">
                  <Globe className="h-4 w-4 mr-1" />
                  <span>View on Website</span>
                </Button>
              </Link>
            </div>
          </div>

      <Card className="shadow-lg border-0">
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border-b">
          <CardTitle className="flex items-center text-xl">
            <Wallet className="mr-2 h-5 w-5 text-blue-600 dark:text-blue-400" />
            Tuition & Fees Content Management
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="w-full grid grid-cols-5 mb-8 rounded-lg bg-blue-50 dark:bg-gray-800 p-1">
              <TabsTrigger
                value="overview"
                className="rounded-md py-2 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:shadow-sm"
              >
                <Wallet className="h-4 w-4 mr-2 text-blue-600 dark:text-blue-400" />
                Tuition & Fees
              </TabsTrigger>
              <TabsTrigger
                value="tuition-rates"
                className="rounded-md py-2 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:shadow-sm"
              >
                <CreditCard className="h-4 w-4 mr-2 text-blue-600 dark:text-blue-400" />
                Tuition Rates & Fees
              </TabsTrigger>
              <TabsTrigger
                value="payment-plans"
                className="rounded-md py-2 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:shadow-sm"
              >
                <Calendar className="h-4 w-4 mr-2 text-blue-600 dark:text-blue-400" />
                Payment Plans
              </TabsTrigger>
              <TabsTrigger
                value="additional-fees"
                className="rounded-md py-2 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:shadow-sm"
              >
                <Receipt className="h-4 w-4 mr-2 text-blue-600 dark:text-blue-400" />
                Additional Fees
              </TabsTrigger>
              <TabsTrigger
                value="submit-receipt"
                className="rounded-md py-2 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:shadow-sm"
              >
                <Upload className="h-4 w-4 mr-2 text-blue-600 dark:text-blue-400" />
                Submit Payment Receipt
              </TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="mt-6">
              <TuitionFeesTab />
            </TabsContent>

            <TabsContent value="tuition-rates" className="mt-6">
              <TuitionRatesTab />
            </TabsContent>

            <TabsContent value="payment-plans" className="mt-6">
              <PaymentPlansTab />
            </TabsContent>

            <TabsContent value="additional-fees" className="mt-6">
              <AdditionalFeesTab />
            </TabsContent>

            <TabsContent value="submit-receipt" className="mt-6">
              <SubmitReceiptTab />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
        </div>
      </main>

      {/* Overlay for mobile menu */}
      {mobileSidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 md:hidden z-40"
          onClick={toggleMobileSidebar}
        ></div>
      )}
    </div>
  )
}

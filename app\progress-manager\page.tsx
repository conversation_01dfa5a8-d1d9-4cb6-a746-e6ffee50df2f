'use client'

import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import DashboardLayout from '@/app/components/DashboardLayout'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/app/components/ui/tabs'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/app/components/ui/card'
import { Badge } from '@/app/components/ui/badge'
import { CalendarDays, BookOpen, TrendingUp, Users, Clock, CheckCircle, AlertCircle, XCircle } from 'lucide-react'
import AttendanceProgress from '@/app/components/ProgressManager/AttendanceProgress'
import MarkProgress from '@/app/components/ProgressManager/MarkProgress'
import { format, subDays } from 'date-fns'

export default function ProgressManagerPage() {
  const [activeTab, setActiveTab] = useState('attendance')

  // Fetch real-time statistics
  const { data: attendanceStats } = useQuery({
    queryKey: ['attendance-stats'],
    queryFn: async () => {
      const today = format(new Date(), 'yyyy-MM-dd')
      const res = await fetch(`/api/progress/attendance?startDate=${today}&endDate=${today}`)
      if (!res.ok) throw new Error('Failed to fetch attendance stats')
      return res.json()
    }
  })

  const { data: markStats } = useQuery({
    queryKey: ['mark-stats'],
    queryFn: async () => {
      const res = await fetch('/api/progress/marks?className=all&term=First Semester')
      if (!res.ok) throw new Error('Failed to fetch mark stats')
      return res.json()
    }
  })

  const { data: classStats } = useQuery({
    queryKey: ['class-stats'],
    queryFn: async () => {
      const res = await fetch('/api/classes')
      if (!res.ok) throw new Error('Failed to fetch class stats')
      return res.json()
    }
  })

  // Calculate statistics
  const todayAttendanceCompletion = attendanceStats?.data ?
    Math.round((attendanceStats.data.filter((item: any) => item.status === 'completed').length /
    attendanceStats.data.length) * 100) || 0 : 0

  const markCompletion = markStats?.summary?.overallCompletion || 0
  const totalClasses = classStats?.length || 0
  const pendingTasks = markStats?.summary ?
    markStats.summary.notStartedSubjects + markStats.summary.inProgressSubjects : 0

  return (
    <DashboardLayout>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
        <div className="space-y-8 p-6">
          {/* Header */}
          <div className="relative overflow-hidden rounded-2xl bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 p-8 text-white shadow-2xl">
            <div className="absolute inset-0 bg-black/10"></div>
            <div className="relative flex items-center justify-between">
              <div>
                <h1 className="text-4xl font-bold tracking-tight mb-2">Progress Manager</h1>
                <p className="text-blue-100 text-lg">
                  Real-time monitoring of attendance and academic progress across all classes
                </p>
              </div>
              <div className="flex items-center gap-3">
                <Badge variant="secondary" className="bg-white/20 text-white border-white/30 backdrop-blur-sm">
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Live Data
                </Badge>
                <div className="h-12 w-12 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center">
                  <TrendingUp className="w-6 h-6" />
                </div>
              </div>
            </div>
            {/* Decorative elements */}
            <div className="absolute -top-4 -right-4 w-24 h-24 bg-white/10 rounded-full blur-xl"></div>
            <div className="absolute -bottom-8 -left-8 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card className="relative overflow-hidden border-0 shadow-lg bg-gradient-to-br from-emerald-50 to-teal-50 hover:shadow-xl transition-all duration-300">
              <div className="absolute top-0 right-0 w-20 h-20 bg-emerald-500/10 rounded-full -mr-10 -mt-10"></div>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <CardTitle className="text-sm font-semibold text-emerald-700">Today's Attendance</CardTitle>
                <div className="h-10 w-10 rounded-full bg-emerald-500/20 flex items-center justify-center">
                  <CalendarDays className="h-5 w-5 text-emerald-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-emerald-600 mb-1">{todayAttendanceCompletion}%</div>
                <p className="text-xs text-emerald-600/70">
                  {attendanceStats?.data ?
                    `${attendanceStats.data.filter((item: any) => item.status === 'completed').length} of ${attendanceStats.data.length} classes completed` :
                    'Loading...'
                  }
                </p>
              </CardContent>
            </Card>

            <Card className="relative overflow-hidden border-0 shadow-lg bg-gradient-to-br from-blue-50 to-indigo-50 hover:shadow-xl transition-all duration-300">
              <div className="absolute top-0 right-0 w-20 h-20 bg-blue-500/10 rounded-full -mr-10 -mt-10"></div>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <CardTitle className="text-sm font-semibold text-blue-700">Mark Entry Progress</CardTitle>
                <div className="h-10 w-10 rounded-full bg-blue-500/20 flex items-center justify-center">
                  <BookOpen className="h-5 w-5 text-blue-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-blue-600 mb-1">{markCompletion}%</div>
                <p className="text-xs text-blue-600/70">
                  Current semester completion
                </p>
              </CardContent>
            </Card>

            <Card className="relative overflow-hidden border-0 shadow-lg bg-gradient-to-br from-purple-50 to-violet-50 hover:shadow-xl transition-all duration-300">
              <div className="absolute top-0 right-0 w-20 h-20 bg-purple-500/10 rounded-full -mr-10 -mt-10"></div>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <CardTitle className="text-sm font-semibold text-purple-700">Active Classes</CardTitle>
                <div className="h-10 w-10 rounded-full bg-purple-500/20 flex items-center justify-center">
                  <Users className="h-5 w-5 text-purple-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-purple-600 mb-1">{totalClasses}</div>
                <p className="text-xs text-purple-600/70">
                  Across all grades
                </p>
              </CardContent>
            </Card>

            <Card className="relative overflow-hidden border-0 shadow-lg bg-gradient-to-br from-amber-50 to-orange-50 hover:shadow-xl transition-all duration-300">
              <div className="absolute top-0 right-0 w-20 h-20 bg-amber-500/10 rounded-full -mr-10 -mt-10"></div>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <CardTitle className="text-sm font-semibold text-amber-700">Pending Tasks</CardTitle>
                <div className="h-10 w-10 rounded-full bg-amber-500/20 flex items-center justify-center">
                  <Clock className="h-5 w-5 text-amber-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-amber-600 mb-1">{pendingTasks}</div>
                <p className="text-xs text-amber-600/70">
                  Subjects requiring attention
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Main Content Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <div className="flex justify-center">
              <TabsList className="grid w-full max-w-md grid-cols-2 bg-white/80 backdrop-blur-sm border-0 shadow-lg rounded-xl p-1">
                <TabsTrigger
                  value="attendance"
                  className="flex items-center gap-2 rounded-lg data-[state=active]:bg-gradient-to-r data-[state=active]:from-emerald-500 data-[state=active]:to-teal-500 data-[state=active]:text-white transition-all duration-300"
                >
                  <CalendarDays className="w-4 h-4" />
                  Attendance Progress
                </TabsTrigger>
                <TabsTrigger
                  value="marks"
                  className="flex items-center gap-2 rounded-lg data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-indigo-500 data-[state=active]:text-white transition-all duration-300"
                >
                  <BookOpen className="w-4 h-4" />
                  Mark Entry Progress
                </TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="attendance" className="space-y-6">
              <Card className="border-0 shadow-xl bg-white/80 backdrop-blur-sm rounded-2xl overflow-hidden">
                <CardHeader className="bg-gradient-to-r from-emerald-500/10 to-teal-500/10 border-b border-emerald-100">
                  <CardTitle className="flex items-center gap-3 text-emerald-700">
                    <div className="h-10 w-10 rounded-full bg-emerald-500/20 flex items-center justify-center">
                      <CalendarDays className="w-5 h-5 text-emerald-600" />
                    </div>
                    Attendance Progress Tracking
                  </CardTitle>
                  <CardDescription className="text-emerald-600/70">
                    Monitor daily attendance completion across all classes with interactive timeline view
                  </CardDescription>
                </CardHeader>
                <CardContent className="p-6">
                  <AttendanceProgress />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="marks" className="space-y-6">
              <Card className="border-0 shadow-xl bg-white/80 backdrop-blur-sm rounded-2xl overflow-hidden">
                <CardHeader className="bg-gradient-to-r from-blue-500/10 to-indigo-500/10 border-b border-blue-100">
                  <CardTitle className="flex items-center gap-3 text-blue-700">
                    <div className="h-10 w-10 rounded-full bg-blue-500/20 flex items-center justify-center">
                      <BookOpen className="w-5 h-5 text-blue-600" />
                    </div>
                    Mark Entry Progress Tracking
                  </CardTitle>
                  <CardDescription className="text-blue-600/70">
                    Track mark submission progress by class and subject with detailed completion analytics
                  </CardDescription>
                </CardHeader>
                <CardContent className="p-6">
                  <MarkProgress />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Legend */}
          <Card className="border-0 shadow-lg bg-white/60 backdrop-blur-sm rounded-2xl">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg font-semibold text-gray-700 flex items-center gap-2">
                <div className="h-8 w-8 rounded-full bg-gray-500/20 flex items-center justify-center">
                  <AlertCircle className="w-4 h-4 text-gray-600" />
                </div>
                Status Legend
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="flex items-center gap-3 p-3 rounded-lg bg-red-50 border border-red-100">
                  <div className="w-4 h-4 bg-red-500 rounded-full shadow-sm"></div>
                  <span className="text-sm font-medium text-red-700">Not Started</span>
                </div>
                <div className="flex items-center gap-3 p-3 rounded-lg bg-yellow-50 border border-yellow-100">
                  <div className="w-4 h-4 bg-yellow-500 rounded-full shadow-sm"></div>
                  <span className="text-sm font-medium text-yellow-700">In Progress</span>
                </div>
                <div className="flex items-center gap-3 p-3 rounded-lg bg-green-50 border border-green-100">
                  <div className="w-4 h-4 bg-green-500 rounded-full shadow-sm"></div>
                  <span className="text-sm font-medium text-green-700">Completed</span>
                </div>
                <div className="flex items-center gap-3 p-3 rounded-lg bg-gray-50 border border-gray-100">
                  <div className="w-4 h-4 bg-gray-300 rounded-full shadow-sm"></div>
                  <span className="text-sm font-medium text-gray-700">No Data</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  )
}

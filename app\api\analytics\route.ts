import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { prisma } from '@/lib/prisma'
import { verifyJWT } from '@/lib/jwt'

export async function GET(request: NextRequest) {
  try {
    console.log('Analytics API request received')

    // Get the token from cookies
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    if (!token) {
      console.error('No token found in cookies')
      return NextResponse.json(
        { error: 'Unauthorized - No token found' },
        { status: 401 }
      )
    }

    try {
      // Verify the token
      const decoded = await verifyJWT(token)
      console.log('Token verified, user ID:', decoded.id)

      // Check if user is Super Admin
      const user = await prisma.user.findUnique({
        where: { id: decoded.id },
        select: { role: true }
      })

      if (!user || user.role !== 'SUPER_ADMIN') {
        return NextResponse.json(
          { error: 'Forbidden - Super Admin access required' },
          { status: 403 }
        )
      }

      // Get query parameters
      const { searchParams } = new URL(request.url)
      const range = searchParams.get('range') || '30d'
      const selectedClass = searchParams.get('class') || 'all'

      console.log('Analytics parameters:', { range, selectedClass })

      // Calculate date range
      const now = new Date()
      let startDate = new Date()

      switch (range) {
        case '7d':
          startDate.setDate(now.getDate() - 7)
          break
        case '30d':
          startDate.setDate(now.getDate() - 30)
          break
        case '90d':
          startDate.setDate(now.getDate() - 90)
          break
        case '1y':
          startDate.setFullYear(now.getFullYear() - 1)
          break
        default:
          startDate.setDate(now.getDate() - 30)
      }

      // Build class filter
      const classFilter = selectedClass === 'all' ? {} : { className: selectedClass }

      // Fetch overview data
      const [
        totalStudents,
        totalTeachers,
        totalClasses,
        recentAttendance,
        recentMarks,
        totalPayments
      ] = await Promise.all([
        prisma.student.count({ where: classFilter }),
        prisma.teacher.count(),
        prisma.class.count(),
        prisma.attendance.findMany({
          where: {
            ...classFilter,
            createdAt: { gte: startDate }
          }
        }),
        prisma.mark.findMany({
          where: {
            ...classFilter,
            createdAt: { gte: startDate }
          }
        }),
        prisma.payment.aggregate({
          where: {
            createdAt: { gte: startDate }
          },
          _sum: { amount: true }
        })
      ])

      // Calculate attendance rate
      const attendanceStats = recentAttendance.reduce((acc, record) => {
        acc.total++
        if (record.status === 'present') acc.present++
        else if (record.status === 'absent') acc.absent++
        else acc.permission++
        return acc
      }, { total: 0, present: 0, absent: 0, permission: 0 })

      const attendanceRate = attendanceStats.total > 0
        ? Math.round((attendanceStats.present / attendanceStats.total) * 100)
        : 0

      // Calculate average grade
      const averageGrade = recentMarks.length > 0
        ? Math.round(recentMarks.reduce((sum, mark) => sum + mark.marks, 0) / recentMarks.length)
        : 0

      // Get real enrollment trends from database
      const enrollmentTrends = await Promise.all(
        Array.from({ length: 6 }, async (_, i) => {
          const month = new Date()
          month.setMonth(month.getMonth() - (5 - i))
          const monthStart = new Date(month.getFullYear(), month.getMonth(), 1)
          const monthEnd = new Date(month.getFullYear(), month.getMonth() + 1, 0)

          const [studentsCount, teachersCount] = await Promise.all([
            prisma.student.count({
              where: {
                createdAt: { lte: monthEnd },
                ...classFilter
              }
            }),
            prisma.teacher.count({
              where: {
                createdAt: { lte: monthEnd }
              }
            })
          ])

          return {
            month: month.toLocaleDateString('en-US', { month: 'short' }),
            students: studentsCount,
            teachers: teachersCount
          }
        })
      )

      // Get real attendance trends from database
      const attendanceTrends = await Promise.all(
        Array.from({ length: 7 }, async (_, i) => {
          const date = new Date()
          date.setDate(date.getDate() - (6 - i))
          const dateStr = date.toISOString().split('T')[0]

          const dayAttendance = await prisma.attendance.findMany({
            where: {
              date: dateStr,
              ...classFilter
            }
          })

          const present = dayAttendance.filter(a => a.status === 'present').length
          const absent = dayAttendance.filter(a => a.status === 'absent').length
          const total = dayAttendance.length
          const rate = total > 0 ? Math.round((present / total) * 100) : 0

          return {
            date: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
            rate,
            present,
            absent
          }
        })
      )

      // Get subject performance
      const subjectPerformance = await prisma.mark.groupBy({
        by: ['subject'],
        where: {
          ...classFilter,
          createdAt: { gte: startDate }
        },
        _avg: { marks: true },
        _count: { studentId: true }
      })

      const formattedSubjectPerformance = await Promise.all(
        subjectPerformance.map(async (subject) => {
          // Calculate real pass rate (marks >= 60)
          const passingStudents = await prisma.mark.count({
            where: {
              subject: subject.subject,
              marks: { gte: 60 },
              ...classFilter,
              createdAt: { gte: startDate }
            }
          })

          const totalStudents = subject._count.studentId
          const passRate = totalStudents > 0 ? Math.round((passingStudents / totalStudents) * 100) : 0

          return {
            subject: subject.subject,
            average: Math.round(subject._avg.marks || 0),
            students: totalStudents,
            passRate
          }
        })
      )

      // Get real grade distribution from database
      const allStudentAverages = await prisma.mark.groupBy({
        by: ['studentId'],
        where: {
          ...classFilter,
          createdAt: { gte: startDate }
        },
        _avg: { marks: true }
      })

      const gradeRanges = {
        'A (90-100)': 0,
        'B (80-89)': 0,
        'C (70-79)': 0,
        'D (60-69)': 0,
        'F (0-59)': 0
      }

      allStudentAverages.forEach(student => {
        const avg = student._avg.marks || 0
        if (avg >= 90) gradeRanges['A (90-100)']++
        else if (avg >= 80) gradeRanges['B (80-89)']++
        else if (avg >= 70) gradeRanges['C (70-79)']++
        else if (avg >= 60) gradeRanges['D (60-69)']++
        else gradeRanges['F (0-59)']++
      })

      const totalGraded = allStudentAverages.length
      const gradeDistribution = Object.entries(gradeRanges).map(([grade, count]) => ({
        grade,
        count,
        percentage: totalGraded > 0 ? Math.round((count / totalGraded) * 100) : 0
      }))

      // Get real top performers from database
      const topPerformersData = await prisma.mark.groupBy({
        by: ['studentId'],
        where: {
          ...classFilter,
          createdAt: { gte: startDate }
        },
        _avg: { marks: true },
        orderBy: {
          _avg: { marks: 'desc' }
        },
        take: 10
      })

      const topPerformersWithDetails = await Promise.all(
        topPerformersData.map(async (performer) => {
          const student = await prisma.student.findUnique({
            where: { id: performer.studentId },
            select: { name: true, className: true }
          })
          return {
            name: student?.name || 'Unknown',
            average: Math.round(performer._avg.marks || 0),
            class: student?.className || 'Unknown'
          }
        })
      )

      const topPerformers = topPerformersWithDetails

      // Get real demographics from database
      const genderDistribution = await prisma.student.groupBy({
        by: ['gender'],
        where: classFilter,
        _count: { id: true }
      })

      const genderColors = ['#3B82F6', '#EC4899', '#10B981', '#F59E0B']
      const formattedGenderDistribution = genderDistribution.map((item, index) => ({
        name: item.gender,
        value: item._count.id,
        color: genderColors[index % genderColors.length]
      }))

      // Get real age distribution from database
      const allStudents = await prisma.student.findMany({
        where: classFilter,
        select: { age: true }
      })

      const ageRanges = {
        '5-7': 0,
        '8-10': 0,
        '11-13': 0,
        '14-16': 0,
        '17+': 0
      }

      allStudents.forEach(student => {
        const age = student.age
        if (age >= 5 && age <= 7) ageRanges['5-7']++
        else if (age >= 8 && age <= 10) ageRanges['8-10']++
        else if (age >= 11 && age <= 13) ageRanges['11-13']++
        else if (age >= 14 && age <= 16) ageRanges['14-16']++
        else ageRanges['17+']++
      })

      const ageDistribution = Object.entries(ageRanges)
        .filter(([_, count]) => count > 0)
        .map(([age, count]) => ({ age, count }))

      // Class distribution
      const classDistribution = await prisma.class.findMany({
        include: {
          _count: { select: { students: true } }
        }
      })

      const formattedClassDistribution = classDistribution.map(cls => ({
        class: cls.name,
        students: cls._count.students,
        capacity: 30 // Mock capacity
      }))

      // Get real financial data from database
      const feeCollection = await Promise.all(
        Array.from({ length: 6 }, async (_, i) => {
          const month = new Date()
          month.setMonth(month.getMonth() - (5 - i))
          const monthStart = new Date(month.getFullYear(), month.getMonth(), 1)
          const monthEnd = new Date(month.getFullYear(), month.getMonth() + 1, 0)

          const [collectedPayments, allPayments] = await Promise.all([
            prisma.payment.aggregate({
              where: {
                status: 'paid',
                paymentDate: { gte: monthStart, lte: monthEnd }
              },
              _sum: { amount: true }
            }),
            prisma.payment.aggregate({
              where: {
                paymentDate: { gte: monthStart, lte: monthEnd }
              },
              _sum: { amount: true }
            })
          ])

          const collected = collectedPayments._sum.amount || 0
          const total = allPayments._sum.amount || 0
          const pending = total - collected

          return {
            month: month.toLocaleDateString('en-US', { month: 'short' }),
            collected,
            pending,
            total
          }
        })
      )

      // Get real payment methods data from database
      const paymentMethodsData = await prisma.payment.groupBy({
        by: ['paymentMethod'],
        where: {
          status: 'paid',
          createdAt: { gte: startDate }
        },
        _sum: { amount: true },
        _count: { id: true }
      })

      const paymentMethods = paymentMethodsData.map(method => ({
        method: method.paymentMethod,
        amount: method._sum.amount || 0,
        transactions: method._count.id
      }))

      // Get real operational data from database
      const recentLogins = await prisma.user.findMany({
        where: {
          lastLogin: { gte: startDate }
        },
        select: { lastLogin: true }
      })

      // Create hourly usage pattern based on login times
      const hourlyUsage = Array.from({ length: 24 }, (_, hour) => {
        const hourLogins = recentLogins.filter(user => {
          if (!user.lastLogin) return false
          const loginHour = user.lastLogin.getHours()
          return loginHour === hour
        }).length

        return {
          hour: `${hour}:00`,
          users: hourLogins,
          actions: hourLogins * 5 // Estimate actions per user
        }
      })

      // Get real module usage based on database activity
      const [studentsCount, attendanceCount, marksCount, paymentsCount] = await Promise.all([
        prisma.student.count(),
        prisma.attendance.count({ where: { createdAt: { gte: startDate } } }),
        prisma.mark.count({ where: { createdAt: { gte: startDate } } }),
        prisma.payment.count({ where: { createdAt: { gte: startDate } } })
      ])

      const moduleUsage = [
        { module: 'Students', usage: studentsCount, users: Math.ceil(studentsCount / 10) },
        { module: 'Attendance', usage: attendanceCount, users: Math.ceil(attendanceCount / 20) },
        { module: 'Marks', usage: marksCount, users: Math.ceil(marksCount / 15) },
        { module: 'Accounting', usage: paymentsCount, users: Math.ceil(paymentsCount / 5) },
        { module: 'Reports', usage: Math.floor((studentsCount + marksCount) / 2), users: Math.ceil(studentsCount / 20) }
      ].sort((a, b) => b.usage - a.usage)

      // Get real improvement trends from database
      const improvementTrends = await Promise.all(
        Array.from({ length: 6 }, async (_, i) => {
          const month = new Date()
          month.setMonth(month.getMonth() - (5 - i))
          const monthStart = new Date(month.getFullYear(), month.getMonth(), 1)
          const monthEnd = new Date(month.getFullYear(), month.getMonth() + 1, 0)

          const monthMarks = await prisma.mark.aggregate({
            where: {
              createdAt: { gte: monthStart, lte: monthEnd },
              ...classFilter
            },
            _avg: { marks: true }
          })

          const prevMonth = new Date(month)
          prevMonth.setMonth(prevMonth.getMonth() - 1)
          const prevMonthStart = new Date(prevMonth.getFullYear(), prevMonth.getMonth(), 1)
          const prevMonthEnd = new Date(prevMonth.getFullYear(), prevMonth.getMonth() + 1, 0)

          const prevMonthMarks = await prisma.mark.aggregate({
            where: {
              createdAt: { gte: prevMonthStart, lte: prevMonthEnd },
              ...classFilter
            },
            _avg: { marks: true }
          })

          const currentAvg = monthMarks._avg.marks || 0
          const prevAvg = prevMonthMarks._avg.marks || 0
          const improvement = currentAvg - prevAvg

          return {
            month: month.toLocaleDateString('en-US', { month: 'short' }),
            improvement: Math.round(improvement * 10) / 10 // Round to 1 decimal
          }
        })
      )

      // Calculate growth rate from enrollment trends
      const currentStudents = enrollmentTrends[enrollmentTrends.length - 1]?.students || totalStudents
      const previousStudents = enrollmentTrends[enrollmentTrends.length - 2]?.students || totalStudents
      const growthRate = previousStudents > 0
        ? Math.round(((currentStudents - previousStudents) / previousStudents) * 100)
        : 0

      // Calculate active users from recent logins
      const activeUsers = recentLogins.length

      // Get real outstanding fees data
      const outstandingFeesData = await Promise.all(
        formattedClassDistribution.map(async (cls) => {
          const unpaidPayments = await prisma.payment.aggregate({
            where: {
              status: { not: 'paid' },
              student: { className: cls.class }
            },
            _sum: { amount: true },
            _count: { id: true }
          })

          const studentsWithUnpaidFees = await prisma.student.count({
            where: {
              className: cls.class,
              payments: {
                some: {
                  status: { not: 'paid' }
                }
              }
            }
          })

          return {
            class: cls.class,
            amount: unpaidPayments._sum.amount || 0,
            students: studentsWithUnpaidFees
          }
        })
      )

      // Compile analytics data with real database information
      const analyticsData = {
        overview: {
          totalStudents,
          totalTeachers,
          totalClasses,
          totalRevenue: totalPayments._sum.amount || 0,
          attendanceRate,
          averageGrade,
          growthRate,
          activeUsers
        },
        trends: {
          enrollment: enrollmentTrends,
          attendance: attendanceTrends,
          performance: formattedSubjectPerformance,
          revenue: feeCollection
        },
        demographics: {
          genderDistribution: formattedGenderDistribution,
          ageDistribution,
          classDistribution: formattedClassDistribution
        },
        academic: {
          subjectPerformance: formattedSubjectPerformance,
          gradeDistribution,
          topPerformers,
          improvementTrends
        },
        financial: {
          feeCollection,
          paymentMethods,
          outstandingFees: outstandingFeesData
        },
        operational: {
          systemUsage: hourlyUsage,
          moduleUsage,
          errorRates: [] // No error tracking in current schema
        }
      }

      console.log('Analytics data compiled successfully')
      return NextResponse.json(analyticsData)

    } catch (jwtError) {
      console.error('JWT verification error:', jwtError)
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      )
    }
  } catch (error) {
    console.error('Error fetching analytics data:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch analytics data' },
      { status: 500 }
    )
  }
}

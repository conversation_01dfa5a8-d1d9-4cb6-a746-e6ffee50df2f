import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    // First, get all classes with their relations
    const classes = await prisma.class.findMany({
      include: {
        hrTeacher: {
          select: {
            id: true,
            name: true,
            subject: true,
          },
        },
        subjects: true,
        students: true, // Include students to count them
      },
      orderBy: {
        name: 'asc',
      },
    })

    // Map the classes to include the actual student count
    const classesWithCounts = classes.map(cls => ({
      ...cls,
      totalStudents: cls.students.length, // Use the actual count from the relation
      students: undefined // Remove the students array from the response
    }))

    return NextResponse.json(classesWithCounts)
  } catch (error) {
    console.error('Error fetching classes:', error)
    return NextResponse.json(
      { error: 'Failed to fetch classes' },
      { status: 500 }
    )
  }
}

export async function POST(request: Request) {
  try {
    const data = await request.json()
    const newClass = await prisma.class.create({
      data: {
        name: data.name,
        hrTeacherId: data.hrTeacherId === 'none' ? null : data.hrTeacherId,
        totalStudents: data.totalStudents || 0,
        totalSubjects: data.totalSubjects || 0,
      },
      include: {
        hrTeacher: {
          select: {
            id: true,
            name: true,
            subject: true,
          },
        },
      },
    })
    return NextResponse.json(newClass)
  } catch (error) {
    console.error('Error creating class:', error)
    return NextResponse.json(
      { error: 'Failed to create class' },
      { status: 500 }
    )
  }
}

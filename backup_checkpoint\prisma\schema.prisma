generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  id            String    @id @default(cuid())
  email         String    @unique
  password      String
  name          String
  role          String    // 'ADMIN' | 'TEACHER' | 'STAFF'
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  lastLogin     DateTime?
  status        String    @default("active") // 'active' | 'inactive' | 'locked'
}

model Class {
  id            String   @id @default(cuid())
  name          String   @unique
  hrTeacherId   String?
  totalStudents Int      @default(0)
  totalSubjects Int      @default(0)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  students      Student[]
  teachers      ClassTeacher[]
  subjects      Subject[]
  hrTeacher     Teacher?       @relation("HeadTeacher", fields: [hrTeacherId], references: [id])
}

model Teacher {
  id          String   @id @default(cuid())
  name        String
  fatherName  String
  gender      String
  email       String   @unique
  subject     String
  mobile      String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  classes     ClassTeacher[]
  subjects    SubjectTeacher[]
  classHR     Class[]         @relation("HeadTeacher")
}

model Student {
  id         String       @id @default(cuid())
  sid        String       @unique
  name       String
  className  String
  fatherName String
  gfName     String
  age        Int
  gender     String
  createdAt  DateTime    @default(now())
  updatedAt  DateTime    @updatedAt

  // Relations
  attendance Attendance[]
  marks      Mark[]
  class      Class       @relation(fields: [className], references: [name])
}

model Subject {
  id        String   @id @default(cuid())
  name      String   @unique
  classId   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  class     Class    @relation(fields: [classId], references: [id])
  teachers  SubjectTeacher[]
}

model Mark {
  id        String   @id @default(cuid())
  studentId String
  subject   String
  marks     Int
  className String
  term      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  student   Student  @relation(fields: [studentId], references: [id])
}

model ClassTeacher {
  id        String   @id @default(cuid())
  classId   String
  teacherId String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  class     Class    @relation(fields: [classId], references: [id])
  teacher   Teacher  @relation(fields: [teacherId], references: [id])
}

model SubjectTeacher {
  id        String   @id @default(cuid())
  subjectId String
  teacherId String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  subject   Subject  @relation(fields: [subjectId], references: [id])
  teacher   Teacher  @relation(fields: [teacherId], references: [id])
}

model Attendance {
  id        String   @id @default(cuid())
  date      String
  className String
  studentId String
  status    String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  student   Student  @relation(fields: [studentId], references: [id])

  @@index([className, date])
  @@index([studentId])
}

// Additional models for marks, etc.



"use client"

import { useEffect, useRef, useCallback, useState } from 'react'

export function useHorizontalScroll() {
  const scrollRef = useRef<HTMLDivElement>(null)

  const updateScrollShadows = useCallback(() => {
    const container = scrollRef.current
    if (!container) return

    const { scrollLeft, scrollWidth, clientWidth } = container
    const isScrolledLeft = scrollLeft > 0
    const isScrolledRight = scrollLeft < scrollWidth - clientWidth - 1

    // Update CSS classes for scroll shadows
    container.classList.toggle('scrolled-left', isScrolledLeft)
    container.classList.toggle('scrolled-right', !isScrolledRight)
  }, [])

  useEffect(() => {
    const container = scrollRef.current
    if (!container) return

    // Initial check
    updateScrollShadows()

    // Add scroll event listener
    container.addEventListener('scroll', updateScrollShadows, { passive: true })

    // Add resize observer to handle container size changes
    const resizeObserver = new ResizeObserver(updateScrollShadows)
    resizeObserver.observe(container)

    return () => {
      container.removeEventListener('scroll', updateScrollShadows)
      resizeObserver.disconnect()
    }
  }, [updateScrollShadows])

  return { scrollRef, updateScrollShadows }
}

// Enhanced hook for responsive table behavior
export function useResponsiveTable() {
  const [viewMode, setViewMode] = useState<'mobile' | 'tablet' | 'desktop'>('desktop')
  const [isScrolling, setIsScrolling] = useState(false)
  const scrollRef = useRef<HTMLDivElement>(null)

  const updateViewMode = useCallback(() => {
    const width = window.innerWidth
    if (width < 768) {
      setViewMode('mobile')
    } else if (width < 1024) {
      setViewMode('tablet')
    } else {
      setViewMode('desktop')
    }
  }, [])

  const updateScrollShadows = useCallback(() => {
    const container = scrollRef.current
    if (!container) return

    const { scrollLeft, scrollWidth, clientWidth } = container
    const isScrolledLeft = scrollLeft > 0
    const isScrolledRight = scrollLeft < scrollWidth - clientWidth - 1

    container.classList.toggle('scrolled-left', isScrolledLeft)
    container.classList.toggle('scrolled-right', !isScrolledRight)
  }, [])

  useEffect(() => {
    updateViewMode()
    window.addEventListener('resize', updateViewMode, { passive: true })
    return () => window.removeEventListener('resize', updateViewMode)
  }, [updateViewMode])

  useEffect(() => {
    const container = scrollRef.current
    if (!container) return

    let scrollTimeout: NodeJS.Timeout

    const handleScroll = () => {
      setIsScrolling(true)
      updateScrollShadows()

      clearTimeout(scrollTimeout)
      scrollTimeout = setTimeout(() => {
        setIsScrolling(false)
      }, 150)
    }

    updateScrollShadows()
    container.addEventListener('scroll', handleScroll, { passive: true })

    const resizeObserver = new ResizeObserver(updateScrollShadows)
    resizeObserver.observe(container)

    return () => {
      container.removeEventListener('scroll', handleScroll)
      resizeObserver.disconnect()
      clearTimeout(scrollTimeout)
    }
  }, [updateScrollShadows])

  return {
    scrollRef,
    viewMode,
    isScrolling,
    updateScrollShadows
  }
}

export function useTableScroll() {
  const { scrollRef, updateScrollShadows } = useHorizontalScroll()

  const scrollToColumn = useCallback((columnIndex: number) => {
    const container = scrollRef.current
    if (!container) return

    const table = container.querySelector('table')
    if (!table) return

    const cells = table.querySelectorAll('tbody tr:first-child td')
    const targetCell = cells[columnIndex] as HTMLElement
    
    if (targetCell) {
      const containerRect = container.getBoundingClientRect()
      const cellRect = targetCell.getBoundingClientRect()
      
      const scrollLeft = container.scrollLeft + cellRect.left - containerRect.left - 20
      
      container.scrollTo({
        left: Math.max(0, scrollLeft),
        behavior: 'smooth'
      })
    }
  }, [scrollRef])

  const scrollToStart = useCallback(() => {
    const container = scrollRef.current
    if (!container) return

    container.scrollTo({
      left: 0,
      behavior: 'smooth'
    })
  }, [scrollRef])

  const scrollToEnd = useCallback(() => {
    const container = scrollRef.current
    if (!container) return

    container.scrollTo({
      left: container.scrollWidth,
      behavior: 'smooth'
    })
  }, [scrollRef])

  return {
    scrollRef,
    updateScrollShadows,
    scrollToColumn,
    scrollToStart,
    scrollToEnd
  }
}

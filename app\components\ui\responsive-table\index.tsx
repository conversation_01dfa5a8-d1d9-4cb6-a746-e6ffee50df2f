"use client"

import React, { useMemo } from 'react'
import { useResponsiveTable } from '../../../hooks/useHorizontalScroll'
import { MobileCardView } from './MobileCardView'
import { TabletScrollView } from './TabletScrollView'
import { DesktopTableView } from './DesktopTableView'
import { cn } from '../../../lib/utils'

export interface ResponsiveTableColumn<T = any> {
  id: string
  header: string
  accessorKey?: keyof T
  cell?: (row: T) => React.ReactNode
  sortable?: boolean
  filterable?: boolean
  sticky?: boolean
  minWidth?: number
  maxWidth?: number
  priority?: 'high' | 'medium' | 'low' // For mobile column prioritization
  mobileLabel?: string // Custom label for mobile view
  className?: string
}

export interface ResponsiveTableProps<T = any> {
  data: T[]
  columns: ResponsiveTableColumn<T>[]
  isLoading?: boolean
  className?: string
  onRowClick?: (row: T) => void
  onRowEdit?: (row: T) => void
  onRowDelete?: (row: T) => void
  onRowSwipeLeft?: (row: T) => void
  onRowSwipeRight?: (row: T) => void
  searchable?: boolean
  filterable?: boolean
  sortable?: boolean
  pagination?: boolean
  pageSize?: number
  emptyMessage?: string
  loadingMessage?: string
  stickyHeader?: boolean
  virtualScrolling?: boolean
  virtualRowHeight?: number
  virtualContainerHeight?: number
  infiniteScroll?: boolean
  onLoadMore?: () => Promise<void>
  hasMore?: boolean
  onRefresh?: () => void
  rowKey?: keyof T | ((row: T) => string)
  enableSwipeActions?: boolean
  enablePullToRefresh?: boolean
  enableDragScroll?: boolean
}

export function ResponsiveTable<T = any>({
  data,
  columns,
  isLoading = false,
  className,
  onRowClick,
  onRowEdit,
  onRowDelete,
  onRowSwipeLeft,
  onRowSwipeRight,
  searchable = true,
  filterable = false,
  sortable = true,
  pagination = true,
  pageSize = 10,
  emptyMessage = "No data available",
  loadingMessage = "Loading...",
  stickyHeader = true,
  virtualScrolling = false,
  virtualRowHeight = 50,
  virtualContainerHeight = 400,
  infiniteScroll = false,
  onLoadMore,
  hasMore = true,
  onRefresh,
  rowKey = 'id',
  enableSwipeActions = true,
  enablePullToRefresh = true,
  enableDragScroll = true
}: ResponsiveTableProps<T>) {
  const { viewMode, scrollRef, isScrolling } = useResponsiveTable()

  // Prioritize columns for mobile view
  const prioritizedColumns = useMemo(() => {
    return columns.sort((a, b) => {
      const priorityOrder = { high: 0, medium: 1, low: 2 }
      const aPriority = priorityOrder[a.priority || 'medium']
      const bPriority = priorityOrder[b.priority || 'medium']
      return aPriority - bPriority
    })
  }, [columns])

  // Get row key function
  const getRowKey = useMemo(() => {
    if (typeof rowKey === 'function') {
      return rowKey
    }
    return (row: T) => String(row[rowKey as keyof T])
  }, [rowKey])

  const commonProps = {
    data,
    columns: prioritizedColumns,
    isLoading,
    onRowClick,
    onRowEdit,
    onRowDelete,
    onRowSwipeLeft,
    onRowSwipeRight,
    searchable,
    filterable,
    sortable,
    pagination,
    pageSize,
    emptyMessage,
    loadingMessage,
    stickyHeader,
    virtualScrolling,
    virtualRowHeight,
    virtualContainerHeight,
    infiniteScroll,
    onLoadMore,
    hasMore,
    onRefresh,
    getRowKey,
    isScrolling,
    enableSwipeActions,
    enablePullToRefresh,
    enableDragScroll
  }

  const containerClasses = cn(
    "responsive-table-container",
    "transition-all duration-200 ease-in-out",
    {
      "is-scrolling": isScrolling,
    },
    className
  )

  return (
    <div className={containerClasses}>
      {viewMode === 'mobile' && (
        <MobileCardView {...commonProps} />
      )}
      
      {viewMode === 'tablet' && (
        <TabletScrollView {...commonProps} scrollRef={scrollRef} />
      )}
      
      {viewMode === 'desktop' && (
        <DesktopTableView {...commonProps} scrollRef={scrollRef} />
      )}
    </div>
  )
}

// Export sub-components for direct use if needed
export { MobileCardView } from './MobileCardView'
export { TabletScrollView } from './TabletScrollView'
export { DesktopTableView } from './DesktopTableView'

// Export types
export type { ResponsiveTableColumn, ResponsiveTableProps }

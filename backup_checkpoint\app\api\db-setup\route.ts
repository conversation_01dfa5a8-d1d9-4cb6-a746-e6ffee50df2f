import { NextResponse } from 'next/server';
import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

export async function GET() {
  try {
    console.log('Setting up database...');
    
    // Check if the database URL is set
    if (!process.env.DATABASE_URL) {
      return NextResponse.json({
        status: 'error',
        message: 'DATABASE_URL environment variable is not set',
        suggestion: 'Please set the DATABASE_URL environment variable in your .env file'
      }, { status: 500 });
    }
    
    // Check if the prisma directory exists
    const prismaDir = path.join(process.cwd(), 'prisma');
    if (!fs.existsSync(prismaDir)) {
      return NextResponse.json({
        status: 'error',
        message: 'Prisma directory not found',
        suggestion: 'Please make sure the prisma directory exists in your project root'
      }, { status: 500 });
    }
    
    // Check if the schema.prisma file exists
    const schemaPath = path.join(prismaDir, 'schema.prisma');
    if (!fs.existsSync(schemaPath)) {
      return NextResponse.json({
        status: 'error',
        message: 'schema.prisma file not found',
        suggestion: 'Please make sure the schema.prisma file exists in your prisma directory'
      }, { status: 500 });
    }
    
    // Read the schema.prisma file
    const schema = fs.readFileSync(schemaPath, 'utf8');
    
    // Check if the schema contains the required models
    const requiredModels = ['User', 'Class', 'Student', 'Teacher', 'Subject', 'Attendance', 'Mark'];
    const missingModels = requiredModels.filter(model => !schema.includes(`model ${model}`));
    
    if (missingModels.length > 0) {
      return NextResponse.json({
        status: 'error',
        message: `Missing models in schema.prisma: ${missingModels.join(', ')}`,
        suggestion: 'Please make sure your schema.prisma file contains all required models'
      }, { status: 500 });
    }
    
    // Try to run prisma db push
    try {
      console.log('Running prisma db push...');
      const output = execSync('npx prisma db push --force-reset', { encoding: 'utf8' });
      console.log(output);
      
      return NextResponse.json({
        status: 'success',
        message: 'Database schema created successfully',
        output
      });
    } catch (error) {
      console.error('Error running prisma db push:', error);
      
      return NextResponse.json({
        status: 'error',
        message: 'Failed to create database schema',
        error: error instanceof Error ? error.message : 'Unknown error',
        suggestion: 'Please check your database connection string and ensure the database server is running'
      }, { status: 500 });
    }
  } catch (error) {
    console.error('Error setting up database:', error);
    
    return NextResponse.json({
      status: 'error',
      message: 'Failed to set up database',
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}

import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

/**
 * This API route updates the roles in the database to match the required roles:
 * 'Super Admin', 'Admin', 'Supervisor', 'Accountant', 'Teacher'
 * 
 * It will:
 * 1. Delete all existing roles except the ones we want to keep
 * 2. Create the required roles if they don't exist
 * 3. Update existing users to use the new roles
 */
export async function GET() {
  try {
    console.log('Starting role update process')
    
    // Step 1: Get all existing roles
    const existingRoles = await prisma.role.findMany()
    console.log(`Found ${existingRoles.length} existing roles`)
    
    // Step 2: Define the required roles with descriptions
    const requiredRoles = [
      { name: 'Super Admin', description: 'Full system access with all permissions' },
      { name: 'Admin', description: 'Administrative access with most permissions' },
      { name: 'Supervisor', description: 'Supervisory access with oversight permissions' },
      { name: 'Accountant', description: 'Access to financial and accounting features' },
      { name: 'Teacher', description: 'Access to teaching and classroom management features' }
    ]
    
    // Step 3: Delete roles that are not in our required list
    const rolesToKeep = requiredRoles.map(r => r.name.toUpperCase().replace(' ', '_'))
    const rolesToDelete = existingRoles.filter(r => !rolesToKeep.includes(r.name.toUpperCase().replace(' ', '_')))
    
    console.log(`Roles to keep: ${rolesToKeep.join(', ')}`)
    console.log(`Roles to delete: ${rolesToDelete.map(r => r.name).join(', ')}`)
    
    // Delete roles that are not in our required list
    for (const role of rolesToDelete) {
      // First delete all role permissions
      await prisma.rolePermission.deleteMany({
        where: { roleId: role.id }
      })
      
      // Then delete the role
      await prisma.role.delete({
        where: { id: role.id }
      })
      console.log(`Deleted role: ${role.name}`)
    }
    
    // Step 4: Create or update the required roles
    const createdRoles = []
    for (const roleData of requiredRoles) {
      const normalizedName = roleData.name.toUpperCase().replace(' ', '_')
      
      // Check if role already exists
      const existingRole = existingRoles.find(r => 
        r.name.toUpperCase().replace(' ', '_') === normalizedName
      )
      
      if (existingRole) {
        // Update existing role
        const updatedRole = await prisma.role.update({
          where: { id: existingRole.id },
          data: {
            name: normalizedName,
            description: roleData.description,
            systemDefined: true
          }
        })
        createdRoles.push(updatedRole)
        console.log(`Updated role: ${updatedRole.name}`)
      } else {
        // Create new role
        const newRole = await prisma.role.create({
          data: {
            name: normalizedName,
            description: roleData.description,
            systemDefined: true
          }
        })
        createdRoles.push(newRole)
        console.log(`Created role: ${newRole.name}`)
      }
    }
    
    // Step 5: Update existing users to use the new roles
    // Get all users
    const users = await prisma.user.findMany()
    console.log(`Found ${users.length} users`)
    
    // Map old roles to new roles
    const roleMapping = {
      'ADMIN': 'SUPER_ADMIN', // Map existing ADMIN to SUPER_ADMIN
      'USER': 'TEACHER',      // Map existing USER to TEACHER
      'STAFF': 'ACCOUNTANT',  // Map existing STAFF to ACCOUNTANT
      'HR_TEACHER': 'TEACHER', // Map existing HR_TEACHER to TEACHER
      'SUBJECT_TEACHER': 'TEACHER' // Map existing SUBJECT_TEACHER to TEACHER
    }
    
    // Update users with invalid roles
    for (const user of users) {
      const currentRole = user.role
      
      // Check if current role is valid
      if (!rolesToKeep.includes(currentRole)) {
        // Map to new role or default to TEACHER
        const newRole = roleMapping[currentRole] || 'TEACHER'
        
        await prisma.user.update({
          where: { id: user.id },
          data: { role: newRole }
        })
        
        console.log(`Updated user ${user.email} role from ${currentRole} to ${newRole}`)
      }
    }
    
    return NextResponse.json({
      success: true,
      message: 'Roles updated successfully',
      roles: createdRoles,
      usersUpdated: users.length
    })
    
  } catch (error) {
    console.error('Error updating roles:', error)
    return NextResponse.json(
      { error: 'Failed to update roles', details: error.message },
      { status: 500 }
    )
  }
}

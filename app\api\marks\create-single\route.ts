import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const {
      studentId,
      className,
      subject,
      term,
      academicYear,
      marks,
      totalMarks,
      remarks
    } = body

    // Validate required fields
    if (!studentId || !className || !subject || !term || !academicYear) {
      return NextResponse.json(
        { error: 'Student ID, class name, subject, term, and academic year are required' },
        { status: 400 }
      )
    }

    if (typeof marks !== 'number' || typeof totalMarks !== 'number') {
      return NextResponse.json(
        { error: 'Marks and total marks must be numbers' },
        { status: 400 }
      )
    }

    if (marks < 0 || marks > totalMarks) {
      return NextResponse.json(
        { error: `Invalid marks: ${marks}. Must be between 0 and ${totalMarks}` },
        { status: 400 }
      )
    }

    console.log(`Creating mark for student ${studentId} in ${className} - ${subject}`)

    // Check if student exists
    const student = await prisma.student.findUnique({
      where: { id: studentId },
      select: { id: true, name: true, sid: true, className: true }
    })

    if (!student) {
      return NextResponse.json(
        { error: 'Student not found' },
        { status: 404 }
      )
    }

    // Check if mark already exists for this student/subject/term combination
    const existingMark = await prisma.mark.findFirst({
      where: {
        studentId: studentId,
        className: className,
        subject: subject,
        term: term,
        academicYear: academicYear,
      }
    })

    if (existingMark) {
      return NextResponse.json(
        { error: `A mark already exists for ${student.name} in ${subject} for ${term} ${academicYear}` },
        { status: 409 }
      )
    }

    // Create the mark
    const newMark = await prisma.mark.create({
      data: {
        studentId: studentId,
        className: className,
        subject: subject,
        term: term,
        academicYear: academicYear,
        marks: marks,
        totalMarks: totalMarks,
        remarks: remarks || '',
      },
      include: {
        student: {
          select: {
            id: true,
            name: true,
            sid: true,
            className: true,
          },
        },
      },
    })

    console.log(`Successfully created mark with ID: ${newMark.id}`)

    // Return the created mark with formatted data
    const formattedMark = {
      id: newMark.id,
      studentId: newMark.studentId,
      studentName: newMark.student.name,
      studentSid: newMark.student.sid,
      className: newMark.className,
      subject: newMark.subject,
      term: newMark.term,
      academicYear: newMark.academicYear,
      marks: newMark.marks,
      totalMarks: newMark.totalMarks,
      remarks: newMark.remarks,
      createdAt: newMark.createdAt,
      updatedAt: newMark.updatedAt,
    }

    return NextResponse.json({
      message: 'Mark created successfully',
      mark: formattedMark,
    })
  } catch (error) {
    console.error('Error creating mark:', error)
    
    // Handle specific Prisma errors
    if (error instanceof Error) {
      if (error.message.includes('Unique constraint failed')) {
        return NextResponse.json(
          { error: 'A mark already exists for this student in this subject and term' },
          { status: 409 }
        )
      }
      if (error.message.includes('Foreign key constraint failed')) {
        return NextResponse.json(
          { error: 'Invalid student reference. Please check the student ID.' },
          { status: 400 }
        )
      }
    }

    return NextResponse.json(
      { error: 'Failed to create mark' },
      { status: 500 }
    )
  }
}

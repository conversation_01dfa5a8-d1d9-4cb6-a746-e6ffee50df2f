import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { verifyJWT } from '@/lib/jwt'
import { cookies } from 'next/headers'

// GET a single teacher assignment by ID
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    console.log(`Fetching teacher assignment with ID: ${params.id}`)

    // Skip authentication in development mode
    if (process.env.NODE_ENV === 'development') {
      console.log('Development mode - bypassing authentication')

      try {

      // Fetch the teacher assignment
      const assignment = await prisma.teacherAssignment.findUnique({
        where: { id: params.id },
        include: {
          teacher: true,
          class: true,
          subject: true
        }
      })

      if (!assignment) {
        console.error(`Teacher assignment with ID ${params.id} not found`)
        return NextResponse.json(
          { error: 'Teacher assignment not found' },
          { status: 404 }
        )
      }

      // Format the response
      const formattedAssignment = {
        id: assignment.id,
        teacherId: assignment.teacherId,
        teacherName: assignment.teacher.name,
        classId: assignment.classId,
        className: assignment.class?.name,
        subjectId: assignment.subjectId,
        subjectName: assignment.subject?.name,
        isHRTeacher: assignment.isHRTeacher,
        createdAt: assignment.createdAt,
        updatedAt: assignment.updatedAt
      }

        console.log(`Found teacher assignment: ${assignment.id}`)
        return NextResponse.json(formattedAssignment)
      } catch (dbError) {
        console.error('Database error:', dbError)
        return NextResponse.json(
          { error: 'Failed to fetch teacher assignment from database' },
          { status: 500 }
        )
      }
    } else {
      // Production mode - verify authentication
      const cookieStore = cookies()
      const token = cookieStore.get('token')?.value

      if (!token) {
        console.error('No authentication token found')
        return NextResponse.json(
          { error: 'Unauthorized - Authentication required' },
          { status: 401 }
        )
      }

      try {
        // Verify the token
        const decoded = await verifyJWT(token)

        // Fetch the teacher assignment
        const assignment = await prisma.teacherAssignment.findUnique({
          where: { id: params.id },
          include: {
            teacher: true,
            class: true,
            subject: true
          }
        })

        if (!assignment) {
          console.error(`Teacher assignment with ID ${params.id} not found`)
          return NextResponse.json(
            { error: 'Teacher assignment not found' },
            { status: 404 }
          )
        }

        // Format the response
        const formattedAssignment = {
          id: assignment.id,
          teacherId: assignment.teacherId,
          teacherName: assignment.teacher.name,
          classId: assignment.classId,
          className: assignment.class?.name,
          subjectId: assignment.subjectId,
          subjectName: assignment.subject?.name,
          isHRTeacher: assignment.isHRTeacher,
          createdAt: assignment.createdAt,
          updatedAt: assignment.updatedAt
        }

        console.log(`Found teacher assignment: ${assignment.id}`)
        return NextResponse.json(formattedAssignment)
      } catch (jwtError) {
        console.error('JWT verification error:', jwtError)
        return NextResponse.json(
          { error: 'Invalid or expired token' },
          { status: 401 }
        )
      }
    }
  } catch (error) {
    console.error('Error fetching teacher assignment:', error)
    return NextResponse.json(
      { error: 'Failed to fetch teacher assignment' },
      { status: 500 }
    )
  }
}

// DELETE a teacher assignment
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    console.log(`Deleting teacher assignment with ID: ${params.id}`)

    // Skip authentication in development mode
    if (process.env.NODE_ENV === 'development') {
      console.log('Development mode - bypassing authentication')

      try {

      // Check if the assignment exists
      const assignment = await prisma.teacherAssignment.findUnique({
        where: { id: params.id },
        include: {
          class: true
        }
      })

      if (!assignment) {
        console.error(`Teacher assignment with ID ${params.id} not found`)
        return NextResponse.json(
          { error: 'Teacher assignment not found' },
          { status: 404 }
        )
      }

      // If this is an HR teacher assignment, update the class
      if (assignment.isHRTeacher && assignment.classId) {
        await prisma.class.update({
          where: { id: assignment.classId },
          data: { hrTeacherId: null }
        })
      }

      // Delete the teacher assignment
      await prisma.teacherAssignment.delete({
        where: { id: params.id }
      })

        console.log(`Teacher assignment with ID ${params.id} deleted`)
        return NextResponse.json({
          message: 'Teacher assignment deleted successfully'
        })
      } catch (dbError) {
        console.error('Database error:', dbError)
        return NextResponse.json(
          { error: 'Failed to delete teacher assignment from database' },
          { status: 500 }
        )
      }
    } else {
      // Production mode - verify authentication
      const cookieStore = cookies()
      const token = cookieStore.get('token')?.value

      if (!token) {
        console.error('No authentication token found')
        return NextResponse.json(
          { error: 'Unauthorized - Authentication required' },
          { status: 401 }
        )
      }

      try {
        // Verify the token
        const decoded = await verifyJWT(token)

        // Only allow admins to delete teacher assignments
        if (decoded.role !== 'ADMIN') {
          console.error('Unauthorized teacher assignment deletion attempt', {
            requesterId: decoded.id,
            requesterRole: decoded.role
          })
          return NextResponse.json(
            { error: 'Forbidden - Only administrators can delete teacher assignments' },
            { status: 403 }
          )
        }

        // Check if the assignment exists
        const assignment = await prisma.teacherAssignment.findUnique({
          where: { id: params.id },
          include: {
            class: true
          }
        })

        if (!assignment) {
          console.error(`Teacher assignment with ID ${params.id} not found`)
          return NextResponse.json(
            { error: 'Teacher assignment not found' },
            { status: 404 }
          )
        }

        // If this is an HR teacher assignment, update the class
        if (assignment.isHRTeacher && assignment.classId) {
          await prisma.class.update({
            where: { id: assignment.classId },
            data: { hrTeacherId: null }
          })
        }

        // Delete the teacher assignment
        await prisma.teacherAssignment.delete({
          where: { id: params.id }
        })

        console.log(`Teacher assignment with ID ${params.id} deleted`)
        return NextResponse.json({
          message: 'Teacher assignment deleted successfully'
        })
      } catch (jwtError) {
        console.error('JWT verification error:', jwtError)
        return NextResponse.json(
          { error: 'Invalid or expired token' },
          { status: 401 }
        )
      }
    }
  } catch (error) {
    console.error('Error deleting teacher assignment:', error)
    return NextResponse.json(
      { error: 'Failed to delete teacher assignment' },
      { status: 500 }
    )
  }
}

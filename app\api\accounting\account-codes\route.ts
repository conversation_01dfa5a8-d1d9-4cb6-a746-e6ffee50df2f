import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET all account codes
export async function GET() {
  try {
    // Fetch account codes from the database
    const accountCodes = await prisma.accountCode.findMany({
      orderBy: {
        code: 'asc',
      },
    })

    return NextResponse.json(accountCodes)
  } catch (error) {
    console.error('Error fetching account codes:', error)
    return NextResponse.json(
      {
        error: 'Failed to fetch account codes from database',
        details: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      },
      { status: 500 }
    )
  }
}

// CREATE a new account code
export async function POST(request: Request) {
  try {
    const data = await request.json()

    // Validate required fields
    if (!data.code || !data.description) {
      return NextResponse.json(
        { error: 'Code and description are required' },
        { status: 400 }
      )
    }

    // Check if account code already exists
    const existingCode = await prisma.accountCode.findFirst({
      where: { code: data.code },
    })

    if (existingCode) {
      return NextResponse.json(
        { error: 'Account code already exists' },
        { status: 400 }
      )
    }

    // Create new account code
    const accountCode = await prisma.accountCode.create({
      data: {
        code: data.code,
        description: data.description,
        isActive: data.isActive !== undefined ? data.isActive : true,
      },
    })

    return NextResponse.json(accountCode, { status: 201 })
  } catch (error) {
    console.error('Error creating account code:', error)
    return NextResponse.json(
      {
        error: 'Failed to create account code',
        details: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      },
      { status: 500 }
    )
  }
}

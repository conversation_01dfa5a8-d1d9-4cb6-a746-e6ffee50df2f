import { NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'
import { formatStudentName } from '@/lib/utils'

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  let prisma: PrismaClient | null = null;

  try {
    console.log(`Fetching student with ID: ${params.id}`);

    // Create a new PrismaClient instance specifically for this request
    prisma = new PrismaClient({
      log: ['error', 'warn'],
      datasources: {
        db: {
          url: process.env.DATABASE_URL
        }
      }
    });

    // Connect to the database
    await prisma.$connect();
    console.log('Database connection successful');

    const student = await prisma.student.findUnique({
      where: {
        id: params.id,
      },
      include: {
        class: true,
      },
    })

    if (!student) {
      console.log(`Student with ID ${params.id} not found`);
      return NextResponse.json(
        { error: 'Student not found' },
        { status: 404 }
      )
    }

    console.log(`Found student: ${student.name}`);
    return NextResponse.json(student)
  } catch (error) {
    console.error('Error fetching student:', error);
    return NextResponse.json(
      {
        error: 'Failed to fetch student',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  } finally {
    // Clean up the connection
    if (prisma) {
      await prisma.$disconnect().catch(e => console.error('Error disconnecting from database:', e));
    }
  }
}

export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  let prisma: PrismaClient | null = null;

  try {
    console.log(`Updating student with ID: ${params.id}`);
    const data = await request.json()
    console.log('Update data:', data);

    // Create a new PrismaClient instance specifically for this request
    prisma = new PrismaClient({
      log: ['error', 'warn'],
      datasources: {
        db: {
          url: process.env.DATABASE_URL
        }
      }
    });

    // Connect to the database
    await prisma.$connect();
    console.log('Database connection successful');

    // Start a transaction to ensure all operations succeed or fail together
    const result = await prisma.$transaction(async (tx) => {
      // 1. Find the current student to get the current class name
      const currentStudent = await tx.student.findUnique({
        where: { id: params.id },
      })

      if (!currentStudent) {
        throw new Error('Student not found')
      }

      // 2. Update the student
      const updatedStudent = await tx.student.update({
        where: {
          id: params.id,
        },
        data: {
          name: formatStudentName(data.name),
          fatherName: formatStudentName(data.fatherName),
          gfName: formatStudentName(data.gfName),
          className: data.class,
          age: parseInt(data.age),
          gender: data.gender,
        },
      })

      // 3. Handle class changes if the class has changed
      const oldClassName = currentStudent.className
      const newClassName = data.class

      if (oldClassName !== newClassName) {
        // Update the old class totalStudents if it exists
        if (oldClassName) {
          const oldClassRecord = await tx.class.findFirst({
            where: { name: oldClassName },
          })

          if (oldClassRecord) {
            // Count students in the old class
            const oldClassStudentCount = await tx.student.count({
              where: {
                className: oldClassName,
              },
            })

            // Update with the actual count
            await tx.class.update({
              where: { id: oldClassRecord.id },
              data: { totalStudents: oldClassStudentCount },
            })
          }
        }

        // Update the new class totalStudents if it exists
        if (newClassName) {
          const newClassRecord = await tx.class.findFirst({
            where: { name: newClassName },
          })

          if (newClassRecord) {
            // Count students in the new class
            const newClassStudentCount = await tx.student.count({
              where: {
                className: newClassName,
              },
            })

            // Update with the actual count
            await tx.class.update({
              where: { id: newClassRecord.id },
              data: { totalStudents: newClassStudentCount },
            })
          }
        }
      }

      return updatedStudent
    })

    console.log('Student update transaction completed successfully');
    return NextResponse.json(result)
  } catch (error) {
    console.error('Error updating student:', error);
    return NextResponse.json(
      {
        error: 'Failed to update student',
        details: error instanceof Error ? error.message : 'Unknown error',
        suggestion: 'Please check the database connection and ensure the student exists.'
      },
      { status: 500 }
    )
  } finally {
    // Clean up the connection
    if (prisma) {
      await prisma.$disconnect().catch(e => console.error('Error disconnecting from database:', e));
    }
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  let prisma: PrismaClient | null = null;

  try {
    console.log(`Deleting student with ID: ${params.id}`);

    // Create a new PrismaClient instance specifically for this request
    prisma = new PrismaClient({
      log: ['error', 'warn'],
      datasources: {
        db: {
          url: process.env.DATABASE_URL
        }
      }
    });

    // Connect to the database
    await prisma.$connect();
    console.log('Database connection successful');

    // First check if the student exists before starting a transaction
    const existingStudent = await prisma.student.findUnique({
      where: { id: params.id },
    });

    if (!existingStudent) {
      console.log(`Student with ID ${params.id} not found`);
      return NextResponse.json(
        { error: 'Student not found', details: `No student exists with ID: ${params.id}` },
        { status: 404 }
      );
    }

    console.log(`Found student: ${existingStudent.name} in class ${existingStudent.className}`);

    // Start a transaction to ensure both operations succeed or fail together
    const result = await prisma.$transaction(async (tx) => {
      // We already verified the student exists, but we'll get it again within the transaction
      const student = await tx.student.findUnique({
        where: { id: params.id },
      })

      if (!student) {
        console.log(`Student with ID ${params.id} not found in transaction`);
        throw new Error('Student not found in transaction')
      }
      const className = student.className

      // 2. Delete the student
      const deletedStudent = await tx.student.delete({
        where: { id: params.id },
      })
      console.log(`Deleted student with ID: ${params.id}`);

      // 3. Find the class by name and update totalStudents
      if (className) {
        // Find the class by name
        const classRecord = await tx.class.findFirst({
          where: { name: className },
        })

        if (classRecord) {
          // Count remaining students in this class
          const studentCount = await tx.student.count({
            where: {
              className: className,
            },
          })

          console.log(`Updating class ${className} student count to ${studentCount}`);
          // Update with the actual count
          await tx.class.update({
            where: { id: classRecord.id },
            data: { totalStudents: studentCount },
          })
        }
      }

      return { message: 'Student deleted successfully', student: deletedStudent }
    })

    console.log('Student deletion transaction completed successfully');
    return NextResponse.json(result)
  } catch (error) {
    console.error('Error deleting student:', error);
    return NextResponse.json(
      {
        error: 'Failed to delete student',
        details: error instanceof Error ? error.message : 'Unknown error',
        suggestion: 'Please check the database connection and ensure the student exists.'
      },
      { status: 500 }
    )
  } finally {
    // Clean up the connection
    if (prisma) {
      await prisma.$disconnect().catch(e => console.error('Error disconnecting from database:', e));
    }
  }
}

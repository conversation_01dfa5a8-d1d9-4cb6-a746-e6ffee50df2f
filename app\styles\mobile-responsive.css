/* Wave Animation for Header Text */
@keyframes wave {
  0%, 100% {
    transform: scaleX(0.8) translateY(0px);
    color: #4f46e5; /* indigo-600 */
  }
  25% {
    transform: scaleX(0.8) translateY(-8px);
    color: #7c3aed; /* violet-600 */
  }
  50% {
    transform: scaleX(0.8) translateY(-4px);
    color: #db2777; /* pink-600 */
  }
  75% {
    transform: scaleX(0.8) translateY(-12px);
    color: #059669; /* emerald-600 */
  }
}

@keyframes waveGlow {
  0%, 100% {
    text-shadow: 0 0 5px rgba(79, 70, 229, 0.3);
  }
  25% {
    text-shadow: 0 0 10px rgba(124, 58, 237, 0.4);
  }
  50% {
    text-shadow: 0 0 15px rgba(219, 39, 119, 0.4);
  }
  75% {
    text-shadow: 0 0 12px rgba(5, 150, 105, 0.4);
  }
}

.wave-char {
  animation: wave 3s ease-in-out infinite, waveGlow 3s ease-in-out infinite;
  font-weight: 700;
  transition: all 0.3s ease;
  transform: scaleX(0.8);
  transform-origin: center;
}

.wave-space {
  display: inline-block;
  width: 0.9em;
  height: 1em;
}

.wave-char:hover {
  transform: scaleX(0.88) scaleY(1.1) translateY(-2px);
  text-shadow: 0 0 20px currentColor;
}

/* Dark mode wave colors */
.dark .wave-char {
  animation: waveDark 3s ease-in-out infinite, waveGlowDark 3s ease-in-out infinite;
}

@keyframes waveDark {
  0%, 100% {
    transform: scaleX(0.8) translateY(0px);
    color: #818cf8; /* indigo-400 */
  }
  25% {
    transform: scaleX(0.8) translateY(-8px);
    color: #a78bfa; /* violet-400 */
  }
  50% {
    transform: scaleX(0.8) translateY(-4px);
    color: #f472b6; /* pink-400 */
  }
  75% {
    transform: scaleX(0.8) translateY(-12px);
    color: #34d399; /* emerald-400 */
  }
}

@keyframes waveGlowDark {
  0%, 100% {
    text-shadow: 0 0 8px rgba(129, 140, 248, 0.4);
  }
  25% {
    text-shadow: 0 0 12px rgba(167, 139, 250, 0.5);
  }
  50% {
    text-shadow: 0 0 16px rgba(244, 114, 182, 0.5);
  }
  75% {
    text-shadow: 0 0 14px rgba(52, 211, 153, 0.5);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .wave-char {
    animation: none;
    color: #4f46e5;
    text-shadow: none;
    transform: scaleX(0.8);
  }

  .dark .wave-char {
    animation: none;
    color: #818cf8;
    text-shadow: none;
    transform: scaleX(0.8);
  }
}

/* Performance optimization */
.wave-char {
  will-change: transform, color, text-shadow;
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Mobile-First Responsive Utilities */

/* Touch-friendly interactions */
@media (max-width: 768px) {
  /* Ensure all interactive elements meet minimum touch target size */
  button, 
  [role="button"], 
  input[type="button"], 
  input[type="submit"], 
  input[type="reset"],
  .btn {
    min-height: 44px;
    min-width: 44px;
    padding: 12px 16px;
  }

  /* Improve form controls for mobile */
  input, 
  select, 
  textarea {
    min-height: 48px;
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 12px 16px;
  }

  /* Better spacing for mobile layouts */
  .mobile-spacing {
    padding: 16px;
  }

  .mobile-spacing-sm {
    padding: 12px;
  }

  .mobile-spacing-lg {
    padding: 24px;
  }

  /* Responsive text sizes */
  .mobile-text-sm {
    font-size: 14px;
    line-height: 20px;
  }

  .mobile-text-base {
    font-size: 16px;
    line-height: 24px;
  }

  .mobile-text-lg {
    font-size: 18px;
    line-height: 28px;
  }

  /* Card improvements for mobile */
  .mobile-card {
    border-radius: 12px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    margin-bottom: 16px;
  }

  /* Enhanced Table improvements with horizontal scroll */
  .mobile-table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    position: relative;
  }

  .mobile-table {
    min-width: 800px; /* Ensure table doesn't get too cramped */
  }

  /* Custom scrollbar for horizontal scroll */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: #d1d5db #f3f4f6;
  }

  .scrollbar-thin::-webkit-scrollbar {
    height: 8px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: #f3f4f6;
    border-radius: 4px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 4px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
  }

  /* Horizontal scroll indicators */
  .scroll-indicator {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.1);
    color: white;
    padding: 8px;
    border-radius: 50%;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .scroll-indicator.left {
    left: 8px;
  }

  .scroll-indicator.right {
    right: 8px;
  }

  .mobile-table-container:hover .scroll-indicator {
    opacity: 1;
  }

  /* Table cell improvements for horizontal scroll */
  .table-cell-fixed {
    min-width: 120px;
    max-width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .table-cell-name {
    min-width: 150px;
    max-width: 200px;
    font-weight: 500;
  }

  .table-cell-actions {
    min-width: 100px;
    width: 100px;
    position: sticky;
    right: 0;
    background: white;
    border-left: 1px solid #e5e7eb;
  }

  /* Scroll shadows for better UX */
  .scroll-container {
    position: relative;
  }

  .scroll-container::before,
  .scroll-container::after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    width: 20px;
    pointer-events: none;
    z-index: 5;
    transition: opacity 0.3s ease;
  }

  .scroll-container::before {
    left: 0;
    background: linear-gradient(to right, rgba(255,255,255,1), rgba(255,255,255,0));
    opacity: 0;
  }

  .scroll-container::after {
    right: 0;
    background: linear-gradient(to left, rgba(255,255,255,1), rgba(255,255,255,0));
    opacity: 1;
  }

  .scroll-container.scrolled-left::before {
    opacity: 1;
  }

  .scroll-container.scrolled-right::after {
    opacity: 0;
  }

  /* Enhanced mobile table styling */
  .mobile-table-enhanced {
    border-collapse: separate;
    border-spacing: 0;
  }

  .mobile-table-enhanced th,
  .mobile-table-enhanced td {
    border-right: 1px solid #e5e7eb;
  }

  .mobile-table-enhanced th:last-child,
  .mobile-table-enhanced td:last-child {
    border-right: none;
  }

  /* Progress bar improvements */
  .progress-bar {
    transition: width 0.3s ease-in-out;
    position: relative;
    overflow: hidden;
  }

  .progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.3) 50%, transparent 70%);
    animation: shimmer 2s infinite;
  }

  @keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
  }

  /* Button groups for mobile */
  .mobile-button-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
    width: 100%;
  }

  .mobile-button-group button {
    width: 100%;
    justify-content: center;
  }

  /* Responsive grid improvements */
  .mobile-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .mobile-grid-2 {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
  }

  /* Header improvements */
  .mobile-header {
    padding: 16px;
    border-bottom: 1px solid #e5e7eb;
  }

  .mobile-header h1 {
    font-size: 24px;
    line-height: 32px;
    margin-bottom: 4px;
  }

  .mobile-header p {
    font-size: 14px;
    color: #6b7280;
  }

  /* Navigation improvements */
  .mobile-nav-item {
    padding: 16px;
    border-bottom: 1px solid #f3f4f6;
    display: flex;
    align-items: center;
    min-height: 56px;
  }

  /* Modal/Dialog improvements */
  .mobile-dialog {
    margin: 16px;
    max-height: calc(100vh - 32px);
    overflow-y: auto;
  }

  /* Search improvements */
  .mobile-search {
    position: relative;
    margin-bottom: 16px;
  }

  .mobile-search input {
    width: 100%;
    padding-left: 48px;
    border-radius: 12px;
    border: 2px solid #e5e7eb;
    background-color: #f9fafb;
  }

  .mobile-search input:focus {
    border-color: #3b82f6;
    background-color: white;
  }

  /* Filter improvements */
  .mobile-filters {
    display: grid;
    grid-template-columns: 1fr;
    gap: 12px;
    margin-bottom: 20px;
  }

  .mobile-filters select {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    background-color: white;
  }

  /* Action button improvements */
  .mobile-actions {
    position: sticky;
    bottom: 0;
    background: white;
    padding: 16px;
    border-top: 1px solid #e5e7eb;
    box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  /* Loading states */
  .mobile-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 48px 16px;
    text-align: center;
  }

  /* Empty states */
  .mobile-empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 48px 16px;
    text-align: center;
  }

  .mobile-empty-state h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #111827;
  }

  .mobile-empty-state p {
    font-size: 14px;
    color: #6b7280;
    margin-bottom: 24px;
  }

  /* Improved scrolling */
  .mobile-scroll-container {
    -webkit-overflow-scrolling: touch;
    overflow-y: auto;
    max-height: calc(100vh - 200px);
  }

  /* Better focus states for accessibility */
  button:focus,
  input:focus,
  select:focus,
  textarea:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
  }

  /* Improved hover states for touch devices */
  @media (hover: none) {
    button:hover,
    .btn:hover {
      background-color: initial;
    }
  }
}

/* Tablet-specific improvements */
@media (min-width: 768px) and (max-width: 1024px) {
  .tablet-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
  }

  .tablet-button-group {
    display: flex;
    flex-direction: row;
    gap: 12px;
    flex-wrap: wrap;
  }

  .tablet-button-group button {
    flex: 1;
    min-width: 120px;
  }
}

/* High DPI display improvements */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .high-dpi-border {
    border-width: 0.5px;
  }
}

/* Landscape orientation improvements */
@media (orientation: landscape) and (max-height: 500px) {
  .landscape-compact {
    padding: 8px 16px;
  }
  
  .landscape-compact h1 {
    font-size: 20px;
    line-height: 28px;
  }
}

/* Dark mode mobile improvements */
@media (prefers-color-scheme: dark) {
  .mobile-card {
    background-color: #1f2937;
    border-color: #374151;
  }

  .mobile-search input {
    background-color: #374151;
    border-color: #4b5563;
    color: white;
  }

  .mobile-search input::placeholder {
    color: #9ca3af;
  }
}

/* Enhanced Responsive Table Styles */
.responsive-table-container {
  position: relative;
  width: 100%;
}

.responsive-table-container.is-scrolling {
  user-select: none;
}

/* Mobile card view styles */
.mobile-card-view .mobile-card {
  transition: all 0.2s ease-in-out;
  touch-action: pan-y;
}

.mobile-card-view .mobile-card:active {
  transform: scale(0.98);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Tablet scroll view enhancements */
.tablet-scroll-view .scrollbar-thin::-webkit-scrollbar {
  height: 8px;
}

.tablet-scroll-view .scrollbar-thin::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.tablet-scroll-view .scrollbar-thin::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.tablet-scroll-view .scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Touch-friendly scroll indicators */
.scroll-shadow-left::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 20px;
  background: linear-gradient(to right, rgba(255,255,255,0.8), transparent);
  pointer-events: none;
  z-index: 10;
}

.scroll-shadow-right::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 20px;
  background: linear-gradient(to left, rgba(255,255,255,0.8), transparent);
  pointer-events: none;
  z-index: 10;
}

/* Enhanced touch interactions */
.touch-pan-x {
  touch-action: pan-x;
  -webkit-overflow-scrolling: touch;
}

.touch-pan-y {
  touch-action: pan-y;
  -webkit-overflow-scrolling: touch;
}

/* Pull to refresh indicator */
.pull-to-refresh-indicator {
  position: absolute;
  top: -60px;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  border-radius: 50%;
  padding: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease-out;
}

/* Drag scroll cursor states */
.cursor-grab {
  cursor: grab;
}

.cursor-grabbing {
  cursor: grabbing;
}

/* Loading skeleton animations */
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.5s infinite;
}

/* Sticky column enhancements */
.sticky-column {
  position: sticky;
  right: 0;
  background: white;
  border-left: 1px solid #e5e7eb;
  z-index: 5;
}

.sticky-column::before {
  content: '';
  position: absolute;
  top: 0;
  left: -10px;
  bottom: 0;
  width: 10px;
  background: linear-gradient(to right, transparent, rgba(0,0,0,0.05));
  pointer-events: none;
}

import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { cookies } from 'next/headers'
import { verifyJWT } from '@/lib/jwt'

// GET children data for a parent
export async function GET(request: Request) {
  try {
    // Verify authentication
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    }

    const decoded = await verifyJWT(token)

    // Only PARENT role can access this endpoint
    if (decoded.role !== 'PARENT') {
      return NextResponse.json(
        { error: 'Forbidden - Only parents can access this endpoint' },
        { status: 403 }
      )
    }

    const parentId = decoded.id

    // Get all children linked to this parent
    const parentStudentRelationships = await prisma.parentStudent.findMany({
      where: { parentId },
      include: {
        student: {
          select: {
            id: true,
            sid: true,
            name: true,
            className: true,
            fatherName: true,
            gfName: true,
            age: true,
            gender: true,
            academicYear: true,
            photoUrl: true,
            class: {
              select: {
                name: true,
                totalStudents: true
              }
            }
          }
        }
      },
      orderBy: {
        student: {
          className: 'asc'
        }
      }
    })

    // Extract student data
    const children = parentStudentRelationships.map(rel => rel.student)

    // Get basic statistics for each child
    const childrenWithStats = await Promise.all(
      children.map(async (child) => {
        // Get attendance statistics for current month
        const currentDate = new Date()
        const currentMonth = currentDate.getMonth() + 1
        const currentYear = currentDate.getFullYear()
        
        const attendanceStats = await prisma.attendance.groupBy({
          by: ['status'],
          where: {
            studentId: child.id,
            date: {
              contains: `${currentYear}-${currentMonth.toString().padStart(2, '0')}`
            }
          },
          _count: {
            status: true
          }
        })

        // Calculate attendance percentages
        const totalDays = attendanceStats.reduce((sum, stat) => sum + stat._count.status, 0)
        const presentDays = attendanceStats.find(stat => stat.status === 'present')?._count.status || 0
        const attendancePercentage = totalDays > 0 ? Math.round((presentDays / totalDays) * 100) : 0

        // Get recent marks count
        const recentMarksCount = await prisma.mark.count({
          where: {
            studentId: child.id,
            createdAt: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
            }
          }
        })

        return {
          ...child,
          stats: {
            attendancePercentage,
            totalAttendanceDays: totalDays,
            presentDays,
            recentMarksCount
          }
        }
      })
    )

    return NextResponse.json({
      children: childrenWithStats,
      totalChildren: children.length,
      message: 'Children data retrieved successfully'
    })
  } catch (error) {
    console.error('Error fetching children data:', error)
    return NextResponse.json(
      { error: 'Failed to fetch children data' },
      { status: 500 }
    )
  }
}

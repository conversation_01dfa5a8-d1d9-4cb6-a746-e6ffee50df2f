"use client"

import React, { useState, useEffect } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { format, startOfMonth, endOfMonth, eachDayOfInterval, startOfWeek, endOfWeek, subWeeks } from 'date-fns'
import { Button } from './ui/button'
import { fallbackClasses, fallbackStudents, fallbackAttendance, getStudentsForClass, getAttendanceForClassAndDate, generateAttendanceReport } from '../lib/fallback-data'
import { Calendar, Clock, User, UserCheck, UserX, CalendarDays, FileText, CheckCircle, XCircle, AlertCircle, AlertTriangle, Users, Upload, Bookmark, Trash2, Loader2 } from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from './ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from './ui/select'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "./ui/popover"
import { Calendar as CalendarComponent } from "./ui/calendar"
import { AttendanceRecord, AttendanceStatus, Student, WeekRange, AttendanceStats } from '../types/attendance'
import { toast } from "./ui/use-toast"

export function Attendance() {
  const queryClient = useQueryClient()
  const [date, setDate] = useState<Date>(new Date())
  const [selectedClass, setSelectedClass] = useState<string>('')
  const [activeTab, setActiveTab] = useState('daily')
  const [takeAttendanceOpen, setTakeAttendanceOpen] = useState(false)
  const [tempAttendance, setTempAttendance] = useState<{ [key: string]: AttendanceStatus }>({})
  const [selectedWeek, setSelectedWeek] = useState<string>('')
  const [selectedMonth, setSelectedMonth] = useState<string>(format(new Date(), 'yyyy-MM'))
  const [selectedSemester, setSelectedSemester] = useState<string>('first')
  const [reportData, setReportData] = useState<any>(null)
  const [isGeneratingReport, setIsGeneratingReport] = useState(false)

  // Format date for display and API
  const formattedDate = format(date, 'MMMM do, yyyy')
  const dateKey = format(date, 'yyyy-MM-dd')

  // Fetch classes with fallback
  const { data: classes, isLoading: isLoadingClasses, error: classesError, isError: isClassesError } = useQuery({
    queryKey: ['classes'],
    queryFn: async () => {
      try {
        const res = await fetch('/api/classes')
        if (!res.ok) throw new Error('Failed to fetch classes')
        return res.json()
      } catch (error) {
        console.warn('Using fallback class data due to error:', error)
        return fallbackClasses
      }
    }
  })

  // Fetch students for selected class with fallback
  const { data: students, isLoading: isLoadingStudents, error: studentsError } = useQuery({
    queryKey: ['students', selectedClass],
    queryFn: async () => {
      try {
        const res = await fetch(`/api/students?class=${selectedClass}`)
        if (!res.ok) throw new Error('Failed to fetch students')
        return res.json()
      } catch (error) {
        console.warn(`Using fallback student data for class ${selectedClass} due to error:`, error)
        return getStudentsForClass(selectedClass)
      }
    },
    enabled: !!selectedClass
  })

  // Fetch attendance records with fallback
  const { data: attendanceRecords, isLoading: isLoadingAttendance } = useQuery({
    queryKey: ['attendance', selectedClass, dateKey],
    queryFn: async () => {
      try {
        const res = await fetch(`/api/attendance?class=${selectedClass}&date=${dateKey}`)
        if (!res.ok) throw new Error('Failed to fetch attendance')
        return res.json()
      } catch (error) {
        console.warn(`Using fallback attendance data for class ${selectedClass} and date ${dateKey} due to error:`, error)
        return getAttendanceForClassAndDate(selectedClass, dateKey)
      }
    },
    enabled: !!selectedClass
  })

  // Create/Update attendance mutation with fallback
  const attendanceMutation = useMutation({
    mutationFn: async (data: {
      date: string,
      className: string,
      records: { studentId: string, status: AttendanceStatus }[]
    }) => {
      try {
        console.log('Saving attendance data:', data);
        const res = await fetch('/api/attendance', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(data),
        })

        if (!res.ok) {
          // Try to get error details from response
          let errorMessage = 'Failed to save attendance';
          try {
            const errorData = await res.json();
            if (errorData.error) {
              errorMessage = errorData.error;
            }
          } catch (e) {
            // If we can't parse the error response, use the default message
          }
          throw new Error(errorMessage);
        }

        return res.json();
      } catch (error) {
        console.error('Error saving attendance:', error);

        // In development or when using mock data, simulate a successful save
        if (process.env.NODE_ENV !== 'production' || error.message.includes('mock')) {
          console.warn('Using mock attendance save response');

          // Return a mock response that looks like what the API would return
          return data.records.map(record => ({
            id: `mock-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
            date: data.date,
            className: data.className,
            studentId: record.studentId,
            status: record.status,
            createdAt: new Date(),
            updatedAt: new Date()
          }));
        }

        // If not in development, rethrow the error
        throw error;
      }
    },
    onSuccess: (data) => {
      console.log('Attendance saved successfully:', data);
      // Invalidate the attendance query to refetch the data
      queryClient.invalidateQueries({ queryKey: ['attendance'] })
      // Close the dialog
      setTakeAttendanceOpen(false)
      // Show success toast
      toast({
        title: "Success",
        description: "Attendance has been saved successfully.",
      })
    },
    onError: (error: Error) => {
      console.error('Error in attendance mutation:', error);
      // Show error toast with the specific error message
      toast({
        title: "Error",
        description: error.message || "Failed to save attendance. Please try again.",
        variant: "destructive",
      })
    }
  })

  // Initialize temp attendance when dialog opens
  useEffect(() => {
    if (takeAttendanceOpen && selectedClass && students) {
      const initialAttendance: { [key: string]: AttendanceStatus } = {}
      students.forEach((student: Student) => {
        const existingRecord = attendanceRecords?.find(
          (record: AttendanceRecord) => record.studentId === student.id
        )
        initialAttendance[student.id] = existingRecord?.status || 'present'
      })
      setTempAttendance(initialAttendance)
    }
  }, [takeAttendanceOpen, selectedClass, students, attendanceRecords])

  // Handle save attendance with validation
  const handleSaveAttendance = () => {
    if (!selectedClass) {
      toast({
        title: "Error",
        description: "Please select a class before saving attendance.",
        variant: "destructive",
      })
      return
    }

    if (Object.keys(tempAttendance).length === 0) {
      toast({
        title: "Error",
        description: "No attendance records to save. Please mark attendance for at least one student.",
        variant: "destructive",
      })
      return
    }

    try {
      // Convert the attendance data to the format expected by the API
      const records = Object.entries(tempAttendance).map(([studentId, status]) => ({
        studentId,
        status,
      }))

      console.log('Saving attendance records:', records.length)

      // Call the mutation
      attendanceMutation.mutate({
        date: dateKey,
        className: selectedClass,
        records,
      })
    } catch (error) {
      console.error('Error preparing attendance data:', error)
      toast({
        title: "Error",
        description: "Failed to prepare attendance data. Please try again.",
        variant: "destructive",
      })
    }
  }

  // Calculate attendance statistics
  const calculateStats = (records: AttendanceRecord[]): AttendanceStats => {
    const total = records.length
    const present = records.filter(r => r.status === 'present').length
    const absent = records.filter(r => r.status === 'absent').length
    const permission = records.filter(r => r.status === 'permission').length

    return {
      total,
      present,
      absent,
      permission,
      presentPercentage: total > 0 ? Math.round((present / total) * 100) : 0,
      absentPercentage: total > 0 ? Math.round((absent / total) * 100) : 0,
      permissionPercentage: total > 0 ? Math.round((permission / total) * 100) : 0
    }
  }

  // Set status for a student
  const setStatus = (studentId: string, status: AttendanceStatus) => {
    setTempAttendance(prev => ({
      ...prev,
      [studentId]: status
    }))
  }

  // Get week ranges for the dropdown
  interface WeekRange {
    startDate: Date
    endDate: Date
    label: string
  }

  const getWeekRanges = (): WeekRange[] => {
    const weeks: WeekRange[] = []
    const today = new Date()

    // Generate the last 10 weeks
    for (let i = 0; i < 10; i++) {
      const startDate = startOfWeek(subWeeks(today, i))
      const endDate = endOfWeek(subWeeks(today, i))

      weeks.push({
        startDate,
        endDate,
        label: `${format(startDate, 'MMM d')} - ${format(endDate, 'MMM d, yyyy')}`
      })
    }

    return weeks
  }

  // Handle generating reports
  const handleGenerateReport = async () => {
    if (!selectedClass) {
      toast({
        title: 'Error',
        description: 'Please select a class',
        variant: 'destructive',
      })
      return
    }

    // Validate parameters based on the active tab
    if (activeTab === 'weekly' && !selectedWeek) {
      toast({
        title: 'Error',
        description: 'Please select a week',
        variant: 'destructive',
      })
      return
    }

    if (activeTab === 'monthly') {
      if (!selectedMonth) {
        toast({
          title: 'Error',
          description: 'Please select a month',
          variant: 'destructive',
        })
        return
      }

      // Validate month format (YYYY-MM)
      if (!/^\d{4}-\d{2}$/.test(selectedMonth)) {
        toast({
          title: 'Error',
          description: 'Invalid month format. Expected format: YYYY-MM',
          variant: 'destructive',
        })
        return
      }
    }

    if (activeTab === 'semester' && !selectedSemester) {
      toast({
        title: 'Error',
        description: 'Please select a semester',
        variant: 'destructive',
      })
      return
    }

    setIsGeneratingReport(true)
    setReportData(null)

    try {
      let url = `/api/attendance/report?class=${selectedClass}&type=${activeTab}`
      let params = {};

      // Add parameters based on the active tab
      switch (activeTab) {
        case 'daily':
          url += `&date=${format(date, 'yyyy-MM-dd')}`
          params = { date: format(date, 'yyyy-MM-dd') };
          break
        case 'weekly':
          url += `&week=${selectedWeek}`
          params = { week: selectedWeek };
          break
        case 'monthly':
          url += `&month=${selectedMonth}`
          params = { month: selectedMonth };
          break
        case 'semester':
          url += `&semester=${selectedSemester}`
          params = { semester: selectedSemester };
          break
      }

      console.log('Fetching report from:', url)

      try {
        const res = await fetch(url)

        if (!res.ok) {
          const errorData = await res.json()
          throw new Error(errorData.error || 'Failed to generate report')
        }

        const data = await res.json()
        console.log('Report data:', data)
        setReportData(data)
      } catch (error) {
        console.warn(`Using fallback report data for class ${selectedClass} due to error:`, error)
        // Use fallback report data
        const fallbackReport = generateAttendanceReport(selectedClass, activeTab, params)
        console.log('Fallback report data:', fallbackReport)
        setReportData(fallbackReport)
      }
    } catch (error) {
      console.error('Error generating report:', error)
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to generate attendance report',
        variant: 'destructive',
      })
    } finally {
      setIsGeneratingReport(false)
    }
  }

  // State to track active tab
  const [activeMainTab, setActiveMainTab] = useState('take-attendance')

  return (
    <div className="space-y-6">
      {/* Main Tabs */}
      <div className="w-full">
        <div className="grid grid-cols-2 mb-6 bg-gray-100 p-1 rounded-lg">
          <button
            onClick={() => setActiveMainTab('take-attendance')}
            className={`flex items-center justify-center gap-2 py-3 px-5 rounded-md transition-all ${activeMainTab === 'take-attendance'
              ? 'bg-white text-indigo-600 shadow-sm font-medium'
              : 'text-gray-500 hover:text-gray-700'}`}
          >
            <UserCheck className="h-4 w-4" />
            Take Attendance
          </button>
          <button
            onClick={() => setActiveMainTab('attendance-report')}
            className={`flex items-center justify-center gap-2 py-3 px-5 rounded-md transition-all ${activeMainTab === 'attendance-report'
              ? 'bg-white text-indigo-600 shadow-sm font-medium'
              : 'text-gray-500 hover:text-gray-700'}`}
          >
            <FileText className="h-4 w-4" />
            Attendance Report
          </button>
        </div>

        {/* Take Attendance Tab Content */}
        {activeMainTab === 'take-attendance' && (
          <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h2 className="text-xl font-semibold">Take Attendance</h2>
              <p className="text-gray-600 text-sm mt-1">Record daily attendance for classes</p>
            </div>

          <Dialog open={takeAttendanceOpen} onOpenChange={setTakeAttendanceOpen}>
            <DialogTrigger asChild>
              <Button>
                <UserCheck className="mr-2 h-4 w-4" />
                Take Attendance
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-3xl">
              <DialogHeader>
                <DialogTitle>Take Attendance</DialogTitle>
                <DialogDescription>
                  Select a class and date to record attendance
                </DialogDescription>
              </DialogHeader>

              <div className="grid gap-6 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">Select Class</label>
                    <Select value={selectedClass} onValueChange={setSelectedClass}>
                      <SelectTrigger className={classesError ? 'border-red-500' : ''} disabled={isLoadingClasses}>
                        {isLoadingClasses ? (
                          <div className="flex items-center gap-2">
                            <div className="h-4 w-4 animate-spin rounded-full border-2 border-indigo-600 border-t-transparent"></div>
                            <span>Loading classes...</span>
                          </div>
                        ) : (
                          <SelectValue placeholder="Select class" />
                        )}
                      </SelectTrigger>
                      <SelectContent>
                        {classes && classes.length > 0 ? (
                          classes.map((cls: { id: string, name: string }) => (
                            <SelectItem key={cls.id} value={cls.name}>
                              Class {cls.name}
                            </SelectItem>
                          ))
                        ) : (
                          <div className="p-2 text-center text-sm text-gray-500">
                            {classesError ? 'Error loading classes' : 'No classes found'}
                          </div>
                        )}
                      </SelectContent>
                    </Select>
                    {classesError && (
                      <p className="mt-1 text-xs text-red-500">Failed to load classes. Please try again.</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">Select Date</label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button variant="outline" className="w-full justify-start text-left">
                          <Calendar className="mr-2 h-4 w-4" />
                          {formattedDate}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <CalendarComponent
                          mode="single"
                          selected={date}
                          onSelect={(date) => date && setDate(date)}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>

                {selectedClass && students && (
                  <div className="border rounded-md">
                    <div className="bg-gray-50 px-4 py-3 border-b">
                      <div className="grid grid-cols-12 text-sm font-medium text-gray-700">
                        <div className="col-span-1">#</div>
                        <div className="col-span-5">Student Name</div>
                        <div className="col-span-6">Status</div>
                      </div>
                    </div>
                    <div className="divide-y">
                      {students.map((student: Student, index: number) => (
                        <div key={student.id} className="px-4 py-3">
                          <div className="grid grid-cols-12 items-center">
                            <div className="col-span-1 text-sm text-gray-600">{index + 1}</div>
                            <div className="col-span-5 font-medium">{student.name}</div>
                            <div className="col-span-6">
                              <div className="flex items-center space-x-2">
                                {['present', 'absent', 'permission'].map((status) => (
                                  <button
                                    key={status}
                                    type="button"
                                    onClick={() => setStatus(student.id, status as AttendanceStatus)}
                                    className={`flex items-center px-3 py-1 rounded-md text-sm ${
                                      tempAttendance[student.id] === status
                                        ? `bg-${status === 'present' ? 'green' : status === 'absent' ? 'red' : 'blue'}-100
                                           text-${status === 'present' ? 'green' : status === 'absent' ? 'red' : 'blue'}-800
                                           font-medium`
                                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                    }`}
                                  >
                                    {status === 'present' && <CheckCircle className="mr-1 h-4 w-4" />}
                                    {status === 'absent' && <XCircle className="mr-1 h-4 w-4" />}
                                    {status === 'permission' && <AlertCircle className="mr-1 h-4 w-4" />}
                                    {status.charAt(0).toUpperCase() + status.slice(1)}
                                  </button>
                                ))}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              <DialogFooter>
                <Button variant="outline" onClick={() => setTakeAttendanceOpen(false)}>
                  Cancel
                </Button>
                <Button
                  onClick={handleSaveAttendance}
                  disabled={!selectedClass || attendanceMutation.isPending}
                >
                  {attendanceMutation.isPending ? 'Saving...' : 'Save Attendance'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        {/* Class selection for attendance view */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div>
            <label className="block text-sm font-medium mb-2">Select Class</label>
            <Select value={selectedClass} onValueChange={setSelectedClass}>
              <SelectTrigger className={classesError ? 'border-red-500' : ''} disabled={isLoadingClasses}>
                {isLoadingClasses ? (
                  <div className="flex items-center gap-2">
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-indigo-600 border-t-transparent"></div>
                    <span>Loading classes...</span>
                  </div>
                ) : (
                  <SelectValue placeholder="Select class" />
                )}
              </SelectTrigger>
              <SelectContent>
                {classes && classes.length > 0 ? (
                  classes.map((cls: { id: string, name: string }) => (
                    <SelectItem key={cls.id} value={cls.name}>
                      Class {cls.name}
                    </SelectItem>
                  ))
                ) : (
                  <div className="p-2 text-center text-sm text-gray-500">
                    {classesError ? 'Error loading classes' : 'No classes found'}
                  </div>
                )}
              </SelectContent>
            </Select>
            {classesError && (
              <p className="mt-1 text-xs text-red-500">Failed to load classes. Please try again.</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Select Date</label>
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="w-full justify-start text-left">
                  <Calendar className="mr-2 h-4 w-4" />
                  {formattedDate}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <CalendarComponent
                  mode="single"
                  selected={date}
                  onSelect={(date) => date && setDate(date)}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>

        {/* Attendance records display */}
        {selectedClass && attendanceRecords && (
          <div className="border rounded-lg overflow-hidden">
            <div className="bg-gray-50 px-4 py-3 border-b">
              <div className="grid grid-cols-12 text-sm font-medium text-gray-700">
                <div className="col-span-1">#</div>
                <div className="col-span-5">Student Name</div>
                <div className="col-span-6">Status</div>
              </div>
            </div>

            <div className="divide-y">
              {isLoadingStudents ? (
                <div className="px-4 py-6 text-center">
                  <div className="inline-flex items-center gap-2">
                    <div className="h-5 w-5 animate-spin rounded-full border-2 border-indigo-600 border-t-transparent"></div>
                    <span>Loading students...</span>
                  </div>
                </div>
              ) : studentsError ? (
                <div className="px-4 py-6 text-center text-red-500">
                  <AlertTriangle className="h-5 w-5 mx-auto mb-2" />
                  Failed to load students. Please try again.
                </div>
              ) : students && students.length > 0 ? (
                students.map((student: Student, index: number) => {
                  const record = attendanceRecords.find(
                    (r: AttendanceRecord) => r.studentId === student.id
                  )

                  return (
                    <div key={student.id} className="px-4 py-3">
                      <div className="grid grid-cols-12 items-center">
                        <div className="col-span-1 text-sm text-gray-600">{index + 1}</div>
                        <div className="col-span-5 font-medium">{student.name}</div>
                        <div className="col-span-6">
                          {record ? (
                            <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                              ${record.status === 'present' ? 'bg-green-100 text-green-800' :
                                record.status === 'absent' ? 'bg-red-100 text-red-800' :
                                'bg-blue-100 text-blue-800'}`}>
                              {record.status === 'present' && <CheckCircle className="mr-1 h-4 w-4" />}
                              {record.status === 'absent' && <XCircle className="mr-1 h-4 w-4" />}
                              {record.status === 'permission' && <AlertCircle className="mr-1 h-4 w-4" />}
                              {record.status.charAt(0).toUpperCase() + record.status.slice(1)}
                            </div>
                          ) : (
                            <span className="text-gray-400 text-sm">Not recorded</span>
                          )}
                        </div>
                      </div>
                    </div>
                  )
                })
              ) : (
                <div className="px-4 py-6 text-center text-gray-500">
                  No students found in this class
                </div>
              )}
            </div>
          </div>
        )}

        {selectedClass && !attendanceRecords && isLoadingAttendance && (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
          </div>
        )}

        {selectedClass && !isLoadingAttendance && attendanceRecords?.length === 0 && (
          <div className="text-center py-12 border rounded-lg">
            <UserX className="h-12 w-12 mx-auto text-gray-400 mb-3" />
            <h3 className="text-lg font-medium text-gray-900">No attendance records</h3>
            <p className="mt-1 text-sm text-gray-500">
              No attendance has been recorded for this class on {formattedDate}.
            </p>
            <div className="mt-6">
              <Button onClick={() => setTakeAttendanceOpen(true)}>
                Take Attendance Now
              </Button>
            </div>
          </div>
        )}
          </div>
        )}

        {/* Attendance Reports Tab Content */}
        {activeMainTab === 'attendance-report' && (
          <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h2 className="text-xl font-semibold">Attendance Reports</h2>
              <p className="text-gray-600 text-sm mt-1">View and analyze attendance data</p>
            </div>
          </div>

        {/* Report Type Tabs */}
        <div className="mb-6">
          <div className="bg-gray-100 p-1 rounded-lg grid grid-cols-4 gap-1">
            {[{ id: 'daily', icon: <Calendar className="h-4 w-4" /> },
              { id: 'weekly', icon: <CalendarDays className="h-4 w-4" /> },
              { id: 'monthly', icon: <Clock className="h-4 w-4" /> },
              { id: 'semester', icon: <Bookmark className="h-4 w-4" /> }].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center justify-center gap-2 py-3 px-2 rounded-md transition-all ${activeTab === tab.id
                  ? 'bg-white text-indigo-600 shadow-sm font-medium'
                  : 'text-gray-500 hover:text-gray-700'}`}
              >
                {tab.icon}
                {tab.id.charAt(0).toUpperCase() + tab.id.slice(1)}
              </button>
            ))}
          </div>
        </div>

        {/* Report Filters */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div>
            <label className="block text-sm font-medium mb-2">Select Class</label>
            <Select value={selectedClass} onValueChange={setSelectedClass}>
              <SelectTrigger className={classesError ? 'border-red-500' : ''} disabled={isLoadingClasses}>
                {isLoadingClasses ? (
                  <div className="flex items-center gap-2">
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-indigo-600 border-t-transparent"></div>
                    <span>Loading classes...</span>
                  </div>
                ) : (
                  <SelectValue placeholder="Select class" />
                )}
              </SelectTrigger>
              <SelectContent>
                {classes && classes.length > 0 ? (
                  classes.map((cls: { id: string, name: string }) => (
                    <SelectItem key={cls.id} value={cls.name}>
                      Class {cls.name}
                    </SelectItem>
                  ))
                ) : (
                  <div className="p-2 text-center text-sm text-gray-500">
                    {classesError ? 'Error loading classes' : 'No classes found'}
                  </div>
                )}
              </SelectContent>
            </Select>
            {classesError && (
              <p className="mt-1 text-xs text-red-500">Failed to load classes. Please try again.</p>
            )}
          </div>

          {activeTab === 'daily' && (
            <div>
              <label className="block text-sm font-medium mb-2">Select Date</label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="w-full justify-start text-left">
                    <Calendar className="mr-2 h-4 w-4" />
                    {formattedDate}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <CalendarComponent
                    mode="single"
                    selected={date}
                    onSelect={(date) => date && setDate(date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          )}

          {activeTab === 'weekly' && (
            <div>
              <label className="block text-sm font-medium mb-2">Select Week</label>
              <Select value={selectedWeek} onValueChange={setSelectedWeek}>
                <SelectTrigger>
                  <SelectValue placeholder="Select week" />
                </SelectTrigger>
                <SelectContent>
                  {getWeekRanges().map((week: WeekRange) => (
                    <SelectItem key={week.label} value={`${format(week.startDate, 'yyyy-MM-dd')}_${format(week.endDate, 'yyyy-MM-dd')}`}>
                      {week.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {activeTab === 'monthly' && (
            <div>
              <label className="block text-sm font-medium mb-2">Select Month</label>
              <div className="grid grid-cols-2 gap-4">
                <Select
                  value={selectedMonth.split('-')[0] || new Date().getFullYear().toString()}
                  onValueChange={(year) => {
                    const currentMonth = selectedMonth.split('-')[1] || format(new Date(), 'MM');
                    setSelectedMonth(`${year}-${currentMonth}`);
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select year" />
                  </SelectTrigger>
                  <SelectContent>
                    {Array.from({ length: 5 }, (_, i) => new Date().getFullYear() - 2 + i).map((year) => (
                      <SelectItem key={year} value={year.toString()}>
                        {year}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select
                  value={selectedMonth.split('-')[1] || format(new Date(), 'MM')}
                  onValueChange={(month) => {
                    const currentYear = selectedMonth.split('-')[0] || new Date().getFullYear().toString();
                    setSelectedMonth(`${currentYear}-${month}`);
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select month" />
                  </SelectTrigger>
                  <SelectContent>
                    {[
                      { name: 'January', value: '01' },
                      { name: 'February', value: '02' },
                      { name: 'March', value: '03' },
                      { name: 'April', value: '04' },
                      { name: 'May', value: '05' },
                      { name: 'June', value: '06' },
                      { name: 'July', value: '07' },
                      { name: 'August', value: '08' },
                      { name: 'September', value: '09' },
                      { name: 'October', value: '10' },
                      { name: 'November', value: '11' },
                      { name: 'December', value: '12' },
                    ].map((month) => (
                      <SelectItem key={month.value} value={month.value}>
                        {month.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}

          {activeTab === 'semester' && (
            <div>
              <label className="block text-sm font-medium mb-2">Select Semester</label>
              <Select value={selectedSemester} onValueChange={setSelectedSemester}>
                <SelectTrigger>
                  <SelectValue placeholder="Select semester" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="first">First Semester</SelectItem>
                  <SelectItem value="second">Second Semester</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}
        </div>

        {/* Generate Report Button */}
        <div className="mb-6">
          <Button onClick={handleGenerateReport} disabled={!selectedClass || isGeneratingReport}>
            {isGeneratingReport ? (
              <>
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                Generating Report...
              </>
            ) : (
              <>
                <FileText className="mr-2 h-4 w-4" />
                Generate Report
              </>
            )}
          </Button>
        </div>

        {/* Report Results */}
        {reportData && (
          <div className="space-y-6">
            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Total Students</p>
                    <p className="text-2xl font-bold">{reportData.summary.totalStudents}</p>
                  </div>
                  <div className="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center text-blue-600">
                    <Users className="h-6 w-6" />
                  </div>
                </div>
              </div>

              <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Present</p>
                    <p className="text-2xl font-bold text-green-600">{reportData.summary.presentCount}</p>
                    <p className="text-xs text-gray-500">{reportData.summary.attendanceRate}% Attendance Rate</p>
                  </div>
                  <div className="h-12 w-12 bg-green-100 rounded-full flex items-center justify-center text-green-600">
                    <CheckCircle className="h-6 w-6" />
                  </div>
                </div>
              </div>

              <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Absent</p>
                    <p className="text-2xl font-bold text-red-600">{reportData.summary.absentCount}</p>
                    <p className="text-xs text-gray-500">
                      {reportData.summary.totalRecords > 0
                        ? Math.round((reportData.summary.absentCount / reportData.summary.totalRecords) * 100)
                        : 0}% Absence Rate
                    </p>
                  </div>
                  <div className="h-12 w-12 bg-red-100 rounded-full flex items-center justify-center text-red-600">
                    <XCircle className="h-6 w-6" />
                  </div>
                </div>
              </div>

              <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Permission</p>
                    <p className="text-2xl font-bold text-blue-600">{reportData.summary.permissionCount}</p>
                    <p className="text-xs text-gray-500">
                      {reportData.summary.totalRecords > 0
                        ? Math.round((reportData.summary.permissionCount / reportData.summary.totalRecords) * 100)
                        : 0}% Permission Rate
                    </p>
                  </div>
                  <div className="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center text-blue-600">
                    <AlertCircle className="h-6 w-6" />
                  </div>
                </div>
              </div>
            </div>

            {/* Attendance Chart */}
            <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
              <h3 className="text-lg font-medium mb-4">Attendance Overview</h3>
              <div className="h-64">
                {/* Chart would go here - using a placeholder for now */}
                <div className="h-full flex items-center justify-center bg-gray-50 rounded-lg">
                  <div className="text-center">
                    <div className="flex justify-center space-x-4 mb-4">
                      <div className="flex items-center">
                        <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                        <span className="text-sm">Present</span>
                      </div>
                      <div className="flex items-center">
                        <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                        <span className="text-sm">Absent</span>
                      </div>
                      <div className="flex items-center">
                        <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                        <span className="text-sm">Permission</span>
                      </div>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-4 mb-4">
                      <div className="flex h-4 rounded-full overflow-hidden">
                        <div
                          className="bg-green-500"
                          style={{ width: `${reportData.summary.attendanceRate}%` }}
                        ></div>
                        <div
                          className="bg-red-500"
                          style={{ width: `${reportData.summary.totalRecords > 0
                            ? Math.round((reportData.summary.absentCount / reportData.summary.totalRecords) * 100)
                            : 0}%` }}
                        ></div>
                        <div
                          className="bg-blue-500"
                          style={{ width: `${reportData.summary.totalRecords > 0
                            ? Math.round((reportData.summary.permissionCount / reportData.summary.totalRecords) * 100)
                            : 0}%` }}
                        ></div>
                      </div>
                    </div>
                    <p className="text-sm text-gray-500">
                      Report Period: {format(new Date(reportData.dateRange.start), 'MMM d, yyyy')} - {format(new Date(reportData.dateRange.end), 'MMM d, yyyy')}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Daily Breakdown */}
            <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
              <h3 className="text-lg font-medium mb-4">Daily Breakdown</h3>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Present</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Absent</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Permission</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Attendance Rate</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {reportData.dailyBreakdown.map((day: any) => (
                      <tr key={day.date}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {format(new Date(day.date), 'MMM d, yyyy')}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{day.total}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{day.present}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{day.absent}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{day.permission}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {day.total > 0 ? Math.round((day.present / day.total) * 100) : 0}%
                        </td>
                      </tr>
                    ))}

                    {reportData.dailyBreakdown.length === 0 && (
                      <tr>
                        <td colSpan={6} className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                          No attendance data available for this period
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Student Breakdown */}
            <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
              <h3 className="text-lg font-medium mb-4">Student Breakdown</h3>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Present</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Absent</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Permission</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Attendance Rate</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {reportData.studentBreakdown.map((student: any) => (
                      <tr key={student.studentId}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {student.studentName}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{student.present}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{student.absent}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{student.permission}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {student.total > 0 ? Math.round((student.present / student.total) * 100) : 0}%
                        </td>
                      </tr>
                    ))}

                    {reportData.studentBreakdown.length === 0 && (
                      <tr>
                        <td colSpan={5} className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                          No student attendance data available
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {!reportData && selectedClass && (
          <div className="text-center py-12 border rounded-lg">
            <FileText className="h-12 w-12 mx-auto text-gray-400 mb-3" />
            <h3 className="text-lg font-medium text-gray-900">No Report Generated</h3>
            <p className="mt-1 text-sm text-gray-500">
              Select the report parameters and click "Generate Report" to view attendance data.
            </p>
          </div>
        )}
          </div>
        )}
      </div>
    </div>
  )
}

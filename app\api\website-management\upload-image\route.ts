import { NextResponse } from 'next/server'
import { writeFile } from 'fs/promises'
import { join } from 'path'
import crypto from 'crypto'

// POST to upload an image
export async function POST(request: Request) {
  try {
    const formData = await request.formData()
    const file = formData.get('file') as File

    if (!file) {
      return NextResponse.json({
        error: 'No file provided'
      }, { status: 400 })
    }

    // Validate file type
    const validImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
    const validPdfType = 'application/pdf'
    const isValidType = validImageTypes.includes(file.type) || file.type === validPdfType

    if (!isValidType) {
      return NextResponse.json({
        error: 'File must be an image (JPEG, PNG, GIF, WebP) or PDF'
      }, { status: 400 })
    }

    // Get file extension
    const fileExtension = file.name.split('.').pop()?.toLowerCase() || ''
    const validExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'pdf']

    if (!validExtensions.includes(fileExtension)) {
      return NextResponse.json({
        error: 'Invalid file extension. Allowed: jpg, jpeg, png, gif, webp, pdf'
      }, { status: 400 })
    }

    // Limit file size (10MB)
    const MAX_SIZE = 10 * 1024 * 1024 // 10MB
    if (file.size > MAX_SIZE) {
      return NextResponse.json({
        error: 'File size exceeds 5MB limit'
      }, { status: 400 })
    }

    // Generate a unique filename using crypto
    const uniqueId = crypto.randomBytes(16).toString('hex')
    const uniqueFilename = `${uniqueId}.${fileExtension}`

    // Define the upload directory and path
    // Check if it's a payment receipt upload
    const isReceipt = formData.get('type') === 'receipt'
    const uploadSubDir = isReceipt ? 'receipts' : ''
    const uploadDir = join(process.cwd(), 'public', 'uploads', uploadSubDir)
    const filePath = join(uploadDir, uniqueFilename)

    // Convert the file to an ArrayBuffer
    const buffer = await file.arrayBuffer()

    try {
      // Write the file to the filesystem
      await writeFile(filePath, Buffer.from(buffer))
    } catch (error) {
      console.error('Error writing file:', error)
      // Create the directory if it doesn't exist and try again
      const { mkdir } = require('fs/promises')
      try {
        await mkdir(uploadDir, { recursive: true })
        await writeFile(filePath, Buffer.from(buffer))
      } catch (retryError) {
        console.error('Failed to create directory or write file:', retryError)
        throw retryError
      }
    }

    // Return the URL to the uploaded file
    const fileUrl = isReceipt ? `/uploads/receipts/${uniqueFilename}` : `/uploads/${uniqueFilename}`

    return NextResponse.json({
      url: fileUrl,
      filename: uniqueFilename,
      success: true
    })
  } catch (error) {
    console.error('Error uploading image:', error)
    return NextResponse.json({
      error: error.message || 'Failed to upload image'
    }, { status: 500 })
  }
}

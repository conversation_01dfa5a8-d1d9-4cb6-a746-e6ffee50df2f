'use client'

import React from 'react'
import { Card, CardContent } from '../ui/card'
import { Users, UserCheck, UserX, Award, Calendar } from 'lucide-react'
import { Progress } from '../ui/progress'
import { cn } from '@/app/lib/utils'

interface OverviewCardsProps {
  totalStudents: number
  presentStudents: number
  absentStudents: number
  permissionStudents: number
  hasRealData: boolean
  averageScore: number
}

export function OverviewCards({
  totalStudents,
  presentStudents,
  absentStudents,
  permissionStudents,
  hasRealData,
  averageScore
}: OverviewCardsProps) {
  // Calculate attendance percentage
  const attendancePercentage = totalStudents > 0 
    ? Math.round((presentStudents / totalStudents) * 100) 
    : 0

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
      {/* Total Students Card */}
      <Card className="overflow-hidden border-0 shadow-lg hover:shadow-xl transition-all duration-300 dark:bg-gray-800 dark:border-gray-700">
        <CardContent className="p-0">
          <div className="relative">
            <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-400 to-blue-600"></div>
            <div className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Total Students</p>
                  <h3 className="text-3xl font-bold text-gray-900 dark:text-white animate-fadeIn">
                    {totalStudents}
                  </h3>
                  <p className="text-xs text-blue-500 mt-2 flex items-center">
                    <span className="flex items-center">
                      <Users className="h-3 w-3 mr-1" />
                      Enrolled students
                    </span>
                  </p>
                </div>
                <div className="p-4 rounded-lg bg-blue-50 dark:bg-blue-900/30">
                  <Users size={28} className="text-blue-500 dark:text-blue-400" />
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Present Students Card */}
      <Card className="overflow-hidden border-0 shadow-lg hover:shadow-xl transition-all duration-300 dark:bg-gray-800 dark:border-gray-700">
        <CardContent className="p-0">
          <div className="relative">
            <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-green-400 to-green-600"></div>
            <div className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Present Today</p>
                  <h3 className="text-3xl font-bold text-gray-900 dark:text-white animate-fadeIn">
                    {presentStudents}
                  </h3>
                  <div className="flex items-center mt-2">
                    <Progress 
                      value={attendancePercentage} 
                      className="h-1.5 w-24" 
                    />
                    <span className="text-xs text-green-500 ml-2">{attendancePercentage}%</span>
                  </div>
                  {!hasRealData && (
                    <p className="text-xs text-gray-400 mt-1">Estimated</p>
                  )}
                </div>
                <div className="p-4 rounded-lg bg-green-50 dark:bg-green-900/30">
                  <UserCheck size={28} className="text-green-500 dark:text-green-400" />
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Absent Students Card */}
      <Card className="overflow-hidden border-0 shadow-lg hover:shadow-xl transition-all duration-300 dark:bg-gray-800 dark:border-gray-700">
        <CardContent className="p-0">
          <div className="relative">
            <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-red-400 to-red-600"></div>
            <div className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Absent Today</p>
                  <h3 className="text-3xl font-bold text-gray-900 dark:text-white animate-fadeIn">
                    {absentStudents}
                  </h3>
                  <div className="flex items-center mt-2">
                    <Progress 
                      value={totalStudents > 0 ? Math.round((absentStudents / totalStudents) * 100) : 0} 
                      className="h-1.5 w-24 bg-gray-200 dark:bg-gray-700" 
                      indicatorClassName="bg-red-500"
                    />
                    <span className="text-xs text-red-500 ml-2">
                      {totalStudents > 0 ? Math.round((absentStudents / totalStudents) * 100) : 0}%
                    </span>
                  </div>
                  {!hasRealData && (
                    <p className="text-xs text-gray-400 mt-1">Estimated</p>
                  )}
                </div>
                <div className="p-4 rounded-lg bg-red-50 dark:bg-red-900/30">
                  <UserX size={28} className="text-red-500 dark:text-red-400" />
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Performance Card */}
      <Card className="overflow-hidden border-0 shadow-lg hover:shadow-xl transition-all duration-300 dark:bg-gray-800 dark:border-gray-700">
        <CardContent className="p-0">
          <div className="relative">
            <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-purple-400 to-purple-600"></div>
            <div className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Average Score</p>
                  <h3 className="text-3xl font-bold text-gray-900 dark:text-white animate-fadeIn">
                    {averageScore}%
                  </h3>
                  <div className="flex items-center mt-2">
                    <Progress 
                      value={averageScore} 
                      className="h-1.5 w-24" 
                      indicatorClassName={cn(
                        averageScore >= 80 ? "bg-green-500" : 
                        averageScore >= 60 ? "bg-yellow-500" : 
                        "bg-red-500"
                      )}
                    />
                    <span className={cn(
                      "text-xs ml-2",
                      averageScore >= 80 ? "text-green-500" : 
                      averageScore >= 60 ? "text-yellow-500" : 
                      "text-red-500"
                    )}>
                      {averageScore >= 80 ? "Excellent" : 
                       averageScore >= 60 ? "Good" : 
                       "Needs Improvement"}
                    </span>
                  </div>
                </div>
                <div className="p-4 rounded-lg bg-purple-50 dark:bg-purple-900/30">
                  <Award size={28} className="text-purple-500 dark:text-purple-400" />
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

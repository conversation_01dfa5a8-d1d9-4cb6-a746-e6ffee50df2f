import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import bcrypt from 'bcryptjs';

export async function GET() {
  return POST();
}

export async function POST() {
  try {
    console.log('Creating specific admin user...');

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (existingUser) {
      console.log('User already exists, updating password...');
      
      // Update the existing user's password
      const hashedPassword = await bcrypt.hash('admin@123', 10);
      const updatedUser = await prisma.user.update({
        where: { email: '<EMAIL>' },
        data: {
          password: hashedPassword,
          role: 'SUPER_ADMIN',
          status: 'active'
        }
      });

      return NextResponse.json({
        status: 'success',
        message: 'Admin user updated successfully',
        user: {
          id: updatedUser.id,
          email: updatedUser.email,
          name: updatedUser.name,
          role: updatedUser.role
        }
      });
    } else {
      console.log('Creating new admin user...');
      
      // Create new admin user
      const hashedPassword = await bcrypt.hash('admin@123', 10);
      const newUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: hashedPassword,
          name: 'Israel Admin',
          role: 'SUPER_ADMIN',
          status: 'active'
        }
      });

      return NextResponse.json({
        status: 'success',
        message: 'Admin user created successfully',
        user: {
          id: newUser.id,
          email: newUser.email,
          name: newUser.name,
          role: newUser.role
        }
      });
    }
  } catch (error) {
    console.error('Error creating admin user:', error);
    
    return NextResponse.json({
      status: 'error',
      message: 'Failed to create admin user',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

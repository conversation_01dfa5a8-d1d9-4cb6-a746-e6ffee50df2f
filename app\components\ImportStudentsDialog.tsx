'use client'

import { useState } from 'react'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from './ui/use-toast'
import { SimpleDialog } from './SimpleDialog'
import { Button } from './ui/button'
import {
  Loader2,
  Upload,
  FileText,
  CheckCircle2,
  XCircle,
  AlertCircle,
  Download
} from 'lucide-react'
import { Progress } from './ui/progress'
import { Alert, AlertDescription, AlertTitle } from "./ui/alert"

interface ImportStudentsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

interface ImportResults {
  total: number
  success: number
  failed: number
  errors: string[]
}

export function ImportStudentsDialog({ open, onOpenChange }: ImportStudentsDialogProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [importResults, setImportResults] = useState<ImportResults | null>(null)
  const queryClient = useQueryClient()

  // Import students mutation
  const importMutation = useMutation({
    mutationFn: async (formData: FormData) => {
      const res = await fetch('/api/students/bulk-import', {
        method: 'POST',
        body: formData,
      })

      if (!res.ok) {
        const errorData = await res.json()
        throw new Error(errorData.error || 'Failed to import students')
      }

      return res.json()
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['students'] })
      queryClient.invalidateQueries({ queryKey: ['classes'] })

      setImportResults({
        total: data.total,
        success: data.success,
        failed: data.failed,
        errors: data.errors || [],
      })

      toast({
        title: 'Import Complete',
        description: `Successfully imported ${data.success} out of ${data.total} students`,
      })
    },
    onError: (error) => {
      console.error('Failed to import students:', error)
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to import students',
        variant: 'destructive',
      })
    },
  })

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0]
      if (file.type !== 'text/csv' && !file.name.endsWith('.csv')) {
        toast({
          title: 'Invalid File',
          description: 'Please upload a CSV file',
          variant: 'destructive',
        })
        return
      }
      setSelectedFile(file)
      setImportResults(null) // Reset results when a new file is selected
    }
  }

  const handleImport = () => {
    if (!selectedFile) return

    const formData = new FormData()
    formData.append('file', selectedFile)

    importMutation.mutate(formData)
  }

  const handleDownloadTemplate = () => {
    // Create CSV content with photoUrl as optional column
    const headers = ['name', 'fatherName', 'gfName', 'className', 'age', 'gender', 'academicYear', 'photoUrl']
    const sampleData = [
      ['John Doe', 'Richard Doe', 'William Doe', '1A', '12', 'Male', '2023-2024', 'https://example.com/photo1.jpg'],
      ['Jane Smith', 'Robert Smith', 'James Smith', '2B', '13', 'Female', '2023-2024', ''],
      ['Alex Johnson', 'Michael Johnson', 'David Johnson', '8E', '14', 'Male', '2025-2026', ''],
    ]

    const csvContent = [
      headers.join(','),
      ...sampleData.map(row => row.join(','))
    ].join('\n')

    // Create a blob and download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', 'students_template.csv')
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const handleClose = () => {
    if (!importMutation.isPending) {
      setSelectedFile(null)
      setImportResults(null)
      onOpenChange(false)
    }
  }

  return (
    <SimpleDialog
      open={open}
      onOpenChange={handleClose}
      title="Import Students"
      description="Upload a CSV file with student information to add multiple students at once."
      maxWidth="max-w-xl"
    >

        <div className="space-y-4 py-4">
          {!importResults ? (
            <>
              <div className="flex items-center justify-center w-full">
                <label
                  htmlFor="csv-file"
                  className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-md cursor-pointer bg-muted/50 hover:bg-muted transition-colors"
                >
                  <div className="flex flex-col items-center justify-center pt-5 pb-6">
                    <Upload className="w-8 h-8 mb-3 text-muted-foreground" />
                    <p className="mb-2 text-sm text-muted-foreground">
                      <span className="font-semibold">Click to upload</span> or drag and drop
                    </p>
                    <p className="text-xs text-muted-foreground">CSV file only (MAX. 500 students)</p>
                  </div>
                  <input
                    id="csv-file"
                    type="file"
                    accept=".csv"
                    className="hidden"
                    onChange={handleFileChange}
                    disabled={importMutation.isPending}
                  />
                </label>
              </div>

              {selectedFile && (
                <div className="flex items-center gap-2 p-2 bg-muted/50 rounded-md">
                  <FileText className="h-5 w-5 text-muted-foreground" />
                  <div className="flex-1 truncate text-sm">{selectedFile.name}</div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedFile(null)}
                    disabled={importMutation.isPending}
                    className="h-8 w-8 p-0"
                  >
                    <XCircle className="h-4 w-4" />
                  </Button>
                </div>
              )}

              <div className="text-sm text-muted-foreground space-y-2">
                <p>CSV file should have the following <span className="font-semibold">required</span> columns:</p>
                <div className="bg-muted p-2 rounded-md font-mono text-xs">
                  name, fatherName, gfName, className, age, gender, academicYear
                </div>
                <p className="text-xs mt-1">
                  <span className="font-semibold">Optional</span> columns:
                </p>
                <div className="bg-muted p-2 rounded-md font-mono text-xs">
                  photoUrl
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  Note: Students can be imported without photos. Photos can be added later.
                </p>
                <div className="pt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleDownloadTemplate}
                    className="h-8"
                  >
                    <Download className="h-3.5 w-3.5 mr-2" />
                    Download template
                  </Button>
                </div>
              </div>
            </>
          ) : (
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Import Results</span>
                  <span className="text-sm text-muted-foreground">
                    {importResults.success} of {importResults.total} successful
                  </span>
                </div>
                <Progress value={(importResults.success / importResults.total) * 100} />
              </div>

              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center gap-1.5">
                  <CheckCircle2 className="h-4 w-4 text-green-500" />
                  <span>{importResults.success} successful</span>
                </div>
                <div className="flex items-center gap-1.5">
                  <XCircle className="h-4 w-4 text-destructive" />
                  <span>{importResults.failed} failed</span>
                </div>
              </div>

              {importResults.errors.length > 0 && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Import Errors</AlertTitle>
                  <AlertDescription>
                    <div className="max-h-32 overflow-y-auto text-xs mt-2">
                      <ul className="list-disc pl-4 space-y-1">
                        {importResults.errors.map((error, index) => (
                          <li key={index}>{error}</li>
                        ))}
                      </ul>
                    </div>
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}
        </div>

        <div className="flex justify-end gap-2 mt-4">
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={importMutation.isPending}
          >
            {importResults ? 'Close' : 'Cancel'}
          </Button>

          {!importResults && (
            <Button
              onClick={handleImport}
              disabled={!selectedFile || importMutation.isPending}
            >
              {importMutation.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Importing...
                </>
              ) : (
                'Import Students'
              )}
            </Button>
          )}
        </div>
    </SimpleDialog>
  )
}

const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function createSampleData() {
  try {
    console.log('Connecting to database...')
    
    // Create sample teachers
    console.log('Creating sample teachers...')
    const teachers = [
      {
        name: '<PERSON>',
        fatherName: '<PERSON>',
        gender: 'Male',
        email: '<EMAIL>',
        subject: 'Mathematics',
        mobile: '+251911234567'
      },
      {
        name: '<PERSON><PERSON>',
        fatherName: '<PERSON>',
        gender: 'Female',
        email: '<EMAIL>',
        subject: 'English',
        mobile: '+251911234568'
      },
      {
        name: '<PERSON>',
        fatherName: '<PERSON>',
        gender: 'Male',
        email: '<EMAIL>',
        subject: 'Arabic',
        mobile: '+251911234569'
      },
      {
        name: '<PERSON><PERSON>',
        fatherName: '<PERSON>',
        gender: 'Female',
        email: '<EMAIL>',
        subject: 'Islamic Education',
        mobile: '+251911234570'
      },
      {
        name: '<PERSON>',
        father<PERSON>ame: '<PERSON><PERSON>',
        gender: 'Male',
        email: 'ibra<PERSON>.<EMAIL>',
        subject: 'Science',
        mobile: '+251911234571'
      }
    ]

    // Check if teachers already exist
    const existingTeachers = await prisma.teacher.findMany()
    if (existingTeachers.length === 0) {
      await prisma.teacher.createMany({
        data: teachers
      })
      console.log(`Created ${teachers.length} sample teachers`)
    } else {
      console.log(`Found ${existingTeachers.length} existing teachers`)
    }

    // Create sample classes
    console.log('Creating sample classes...')
    const classes = [
      { name: 'Grade 1A' },
      { name: 'Grade 1B' },
      { name: 'Grade 2A' },
      { name: 'Grade 2B' },
      { name: 'Grade 3A' },
      { name: 'Grade 3B' },
      { name: 'Grade 4A' },
      { name: 'Grade 4B' },
      { name: 'Grade 5A' },
      { name: 'Grade 5B' }
    ]

    // Check if classes already exist
    const existingClasses = await prisma.class.findMany()
    if (existingClasses.length === 0) {
      await prisma.class.createMany({
        data: classes
      })
      console.log(`Created ${classes.length} sample classes`)
    } else {
      console.log(`Found ${existingClasses.length} existing classes`)
    }

    // Display summary
    const totalTeachers = await prisma.teacher.count()
    const totalClasses = await prisma.class.count()
    const totalUsers = await prisma.user.count()

    console.log('\n=== Database Summary ===')
    console.log(`Total Teachers: ${totalTeachers}`)
    console.log(`Total Classes: ${totalClasses}`)
    console.log(`Total Users: ${totalUsers}`)
    
    console.log('\n=== Teachers ===')
    const allTeachers = await prisma.teacher.findMany({
      select: {
        name: true,
        email: true,
        subject: true
      }
    })
    allTeachers.forEach(teacher => {
      console.log(`- ${teacher.name} (${teacher.email}) - ${teacher.subject}`)
    })

    console.log('\n=== Classes ===')
    const allClasses = await prisma.class.findMany({
      select: {
        name: true,
        hrTeacherId: true
      }
    })
    allClasses.forEach(cls => {
      console.log(`- ${cls.name} - HR Teacher: ${cls.hrTeacherId ? 'Assigned' : 'Not assigned'}`)
    })

  } catch (error) {
    console.error('Error creating sample data:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createSampleData()

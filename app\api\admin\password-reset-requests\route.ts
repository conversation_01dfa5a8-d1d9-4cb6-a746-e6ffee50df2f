import { NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { prisma } from '@/lib/prisma'
import { verifyJWT } from '@/lib/jwt'

// GET password reset requests
export async function GET() {
  try {
    console.log('Fetching password reset requests')

    // Verify authentication
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    if (!token) {
      console.error('No authentication token found')
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    }

    try {
      // Verify the token
      const decoded = await verifyJWT(token)
      
      // Only allow SUPER_ADMIN to view reset requests
      if (decoded.role !== 'SUPER_ADMIN') {
        console.error('Unauthorized access attempt', { 
          requesterId: decoded.id, 
          requesterRole: decoded.role 
        })
        return NextResponse.json(
          { error: 'Forbidden - Only Super Admins can view reset requests' },
          { status: 403 }
        )
      }

      // For now, return mock data since we don't have a password reset requests table
      // In a real implementation, you would query the database
      const mockResetRequests = [
        {
          id: '1',
          userId: 'user1',
          user: {
            id: 'user1',
            name: '<PERSON> Doe',
            email: '<EMAIL>',
            role: 'TEACHER'
          },
          status: 'completed',
          requestedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
          completedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000 + 30 * 60 * 1000).toISOString(), // 30 min later
          requestedBy: 'Super Admin',
          reason: 'User forgot password',
          method: 'email'
        },
        {
          id: '2',
          userId: 'user2',
          user: {
            id: 'user2',
            name: 'Jane Smith',
            email: '<EMAIL>',
            role: 'ADMIN'
          },
          status: 'pending',
          requestedAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(), // 1 hour ago
          requestedBy: 'Super Admin',
          reason: 'Security concern - suspicious activity',
          method: 'temporary_password'
        },
        {
          id: '3',
          userId: 'user3',
          user: {
            id: 'user3',
            name: 'Bob Wilson',
            email: '<EMAIL>',
            role: 'SUPERVISOR'
          },
          status: 'expired',
          requestedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days ago
          requestedBy: 'Super Admin',
          reason: 'Account locked due to multiple failed attempts',
          method: 'email'
        }
      ]

      console.log(`Returning ${mockResetRequests.length} reset requests`)
      return NextResponse.json(mockResetRequests)

    } catch (jwtError) {
      console.error('JWT verification failed:', jwtError)
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      )
    }

  } catch (error) {
    console.error('Error fetching password reset requests:', error)
    return NextResponse.json(
      { error: 'Failed to fetch password reset requests' },
      { status: 500 }
    )
  }
}

// POST to create a new password reset request
export async function POST(request: Request) {
  try {
    console.log('Creating new password reset request')

    // Verify authentication
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    if (!token) {
      console.error('No authentication token found')
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    }

    try {
      // Verify the token
      const decoded = await verifyJWT(token)
      
      // Only allow SUPER_ADMIN to create reset requests
      if (decoded.role !== 'SUPER_ADMIN') {
        console.error('Unauthorized reset request creation attempt', { 
          requesterId: decoded.id, 
          requesterRole: decoded.role 
        })
        return NextResponse.json(
          { error: 'Forbidden - Only Super Admins can create reset requests' },
          { status: 403 }
        )
      }

      const data = await request.json()
      const { userId, method, reason } = data

      // Validate input
      if (!userId || !method || !reason) {
        return NextResponse.json(
          { error: 'Missing required fields: userId, method, reason' },
          { status: 400 }
        )
      }

      if (!['email', 'temporary_password'].includes(method)) {
        return NextResponse.json(
          { error: 'Invalid method. Must be "email" or "temporary_password"' },
          { status: 400 }
        )
      }

      // Verify the target user exists
      const targetUser = await prisma.user.findUnique({
        where: { id: userId }
      })

      if (!targetUser) {
        return NextResponse.json(
          { error: 'Target user not found' },
          { status: 404 }
        )
      }

      // In a real implementation, you would:
      // 1. Create a password reset request record in the database
      // 2. Generate a secure reset token
      // 3. Send email or generate temporary password
      // 4. Log the action for audit purposes

      console.log(`Password reset request created for user ${targetUser.email} by ${decoded.email}`)

      // For now, return a success response
      return NextResponse.json({
        message: 'Password reset request created successfully',
        requestId: `req_${Date.now()}`,
        method,
        targetUser: {
          id: targetUser.id,
          name: targetUser.name,
          email: targetUser.email
        },
        requestedBy: decoded.email,
        reason
      })

    } catch (jwtError) {
      console.error('JWT verification failed:', jwtError)
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      )
    }

  } catch (error) {
    console.error('Error creating password reset request:', error)
    return NextResponse.json(
      { error: 'Failed to create password reset request' },
      { status: 500 }
    )
  }
}

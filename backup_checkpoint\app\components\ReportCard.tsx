"use client"

import React, { useState, useRef, useEffect } from 'react'
import { But<PERSON> } from './ui/button'
import { Input } from './ui/input'
import {
  FileText,
  Calendar,
  Printer,
  Download,
  CheckCircle,
  Search,
  User,
  ChevronDown
} from 'lucide-react'
import { useReactToPrint } from 'react-to-print'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu"

interface SubjectGrade {
  name: string
  marks: number
  totalMarks: number
  grade: string
  remarks: string
}

interface AttendanceData {
  present: number
  absent: number
  totalDays: number
  percentage: number
}

// Sample data for students
const sampleStudents = [
  { id: '1A1', name: '<PERSON>', class: '1A' },
  { id: '1A2', name: '<PERSON>', class: '1A' },
  { id: '2B1', name: '<PERSON>', class: '2B' },
  { id: '2B2', name: '<PERSON>', class: '2B' },
  { id: '3C1', name: '<PERSON><PERSON><PERSON>', class: '3C' },
  { id: '7A1', name: '<PERSON>', class: '7A' },
  { id: '7A2', name: '<PERSON>', class: '7A' },
  { id: '9C1', name: 'Omar Hassan', class: '9C' },
]

export function ReportCard() {
  const [step, setStep] = useState<'form' | 'preview'>('form')
  const [isGenerating, setIsGenerating] = useState(false)
  const [isGenerated, setIsGenerated] = useState(false)
  const printRef = useRef<HTMLDivElement>(null)

  // Get current academic year (e.g., 2023-2024)
  const getCurrentAcademicYear = () => {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth();

    // If we're in the second half of the year (July-December), academic year is current-next
    // Otherwise it's previous-current
    if (month >= 6) { // July or later
      return `${year}-${year + 1}`;
    } else {
      return `${year - 1}-${year}`;
    }
  };

  // Form state
  const [formData, setFormData] = useState({
    studentId: '',
    studentName: '',
    class: '',
    academicYear: getCurrentAcademicYear(),
    semester: 'First Semester'
  })

  // Add state for dropdown
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Filter students based on search
  const filteredStudents = searchQuery.trim() === ''
    ? sampleStudents
    : sampleStudents.filter(student =>
        student.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
        student.name.toLowerCase().includes(searchQuery.toLowerCase())
      );

  // Effect to automatically populate student name and class based on ID
  useEffect(() => {
    if (formData.studentId) {
      const student = sampleStudents.find(s => s.id === formData.studentId);
      if (student) {
        setFormData(prev => ({
          ...prev,
          studentName: student.name,
          class: student.class
        }));
      } else {
        // Clear name and class if student ID doesn't match
        setFormData(prev => ({
          ...prev,
          studentName: '',
          class: ''
        }));
      }
    }
  }, [formData.studentId]);

  // List of classes
  const classesList = [
    '1A', '1B', '1C', '1D', '2A', '2B', '2C', '2D', '3A', '3B', '3C', '3D',
    '4A', '4B', '4C', '4D', '5A', '5B', '5C', '5D', '6A', '6B', '6C', '6D',
    '7A', '7B', '7C', '7D', '8A', '8B', '8C', '8D', '9A', '9B', '9C', '9D',
    '10A', '10B', '10C', '10D', '11A', '11B', '11C', '11D', '12A', '12B', '12C', '12D'
  ]

  // Sample report card data - in a real app, this would be fetched from an API
  const [reportData, setReportData] = useState({
    subjects: [
      { name: "Mathematics", marks: 85, totalMarks: 100, grade: "A", remarks: "Excellent problem-solving skills" },
      { name: "Science", marks: 78, totalMarks: 100, grade: "B+", remarks: "Good understanding of concepts" },
      { name: "English", marks: 92, totalMarks: 100, grade: "A+", remarks: "Outstanding communication skills" },
      { name: "Social Studies", marks: 80, totalMarks: 100, grade: "A-", remarks: "Strong analytical thinking" },
      { name: "Computer Science", marks: 95, totalMarks: 100, grade: "A+", remarks: "Exceptional programming abilities" },
      { name: "Physical Education", marks: 88, totalMarks: 100, grade: "A", remarks: "Great team player" },
      { name: "Art", marks: 83, totalMarks: 100, grade: "A-", remarks: "Creative and innovative" }
    ],
    attendance: {
      present: 85,
      absent: 5,
      totalDays: 90,
      percentage: 94.4
    },
    teacherRemarks: "A diligent student who consistently shows dedication to learning. Participates actively in class discussions and demonstrates leadership qualities.",
    principalRemarks: "Commendable performance. Keep up the good work and continue to excel in all areas of academic and personal development.",
    classTeacher: "Ms. Sarah Johnson",
    principal: "Dr. Robert Williams",
    gradingSystem: [
      { grade: "A+", range: "95-100", description: "Outstanding" },
      { grade: "A", range: "85-94", description: "Excellent" },
      { grade: "B+", range: "80-84", description: "Very Good" },
      { grade: "B", range: "75-79", description: "Good" },
      { grade: "C+", range: "70-74", description: "Above Average" },
      { grade: "C", range: "65-69", description: "Average" },
      { grade: "D", range: "60-64", description: "Below Average" },
      { grade: "F", range: "Below 60", description: "Needs Improvement" }
    ],
    behavior: {
      discipline: "Excellent",
      cooperation: "Excellent",
      punctuality: "Very Good",
      neatness: "Good"
    }
  })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData({
      ...formData,
      [name]: value
    })
  }

  const handleGenerateReportCard = () => {
    setIsGenerating(true)

    // Simulate API call delay
    setTimeout(() => {
      setIsGenerating(false)
      setIsGenerated(true)
      setStep('preview')
    }, 1500)
  }

  // Define a custom print handler to fix type issues
  const handlePrint = () => {
    if (printRef.current) {
      // Use the print function from the hook
      const printFunction = useReactToPrint({
        documentTitle: `Report Card - ${formData.studentName}`,
        // @ts-ignore - The 'content' property is actually supported but not in types
        content: () => printRef.current,
      });

      // Execute the print function
      printFunction();
    }
  };

  const calculateTotalMarks = () => {
    let total = 0
    let obtained = 0

    reportData.subjects.forEach(subject => {
      total += subject.totalMarks
      obtained += subject.marks
    })

    return {
      total,
      obtained,
      percentage: ((obtained / total) * 100).toFixed(2)
    }
  }

  const getGradeColor = (grade: string) => {
    if (grade.startsWith('A')) return 'text-green-600'
    if (grade.startsWith('B')) return 'text-blue-600'
    if (grade.startsWith('C')) return 'text-yellow-600'
    if (grade.startsWith('D')) return 'text-orange-600'
    if (grade.startsWith('F')) return 'text-red-600'
    return 'text-gray-600'
  }

  const getOverallGrade = (percentage: number) => {
    if (percentage >= 95) return 'A+'
    if (percentage >= 85) return 'A'
    if (percentage >= 80) return 'B+'
    if (percentage >= 75) return 'B'
    if (percentage >= 70) return 'C+'
    if (percentage >= 65) return 'C'
    if (percentage >= 60) return 'D'
    return 'F'
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Student Report Card</h1>
          <p className="text-gray-600 mt-1">Generate and view student performance reports</p>
        </div>
        {isGenerated && (
          <Button
            variant="outline"
            onClick={() => setStep('form')}
            className="flex items-center gap-2"
          >
            <Search className="h-4 w-4" />
            Generate New
          </Button>
        )}
      </div>

      {step === 'form' ? (
        <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
          <div className="p-4 border-b border-gray-100 bg-gray-50/50">
            <h2 className="text-lg font-semibold text-gray-800">Generate Report Card</h2>
            <p className="text-sm text-gray-500">Fill in the details to generate a student report card</p>
          </div>

          <div className="p-5">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-5">
                <div className="bg-blue-50 p-3 rounded-lg flex items-start space-x-3">
                  <div className="mt-0.5 bg-blue-100 p-1.5 rounded-md text-blue-700">
                    <User className="h-4 w-4" />
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-blue-800">Student Information</h3>
                    <p className="text-xs text-blue-600 mt-0.5">Enter the student ID to automatically fetch student details</p>
                  </div>
                </div>

                <div className="grid gap-4">
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1" htmlFor="studentId">Student ID</label>
                    <div className="flex">
                      <div className="relative flex-grow">
                        <Input
                          id="studentId"
                          name="studentId"
                          placeholder="e.g., 1A1, 2B2, etc."
                          value={formData.studentId}
                          onChange={(e) => {
                            handleInputChange(e);
                            setSearchQuery(e.target.value);
                          }}
                          className="h-9 pr-10"
                          required
                        />
                        <DropdownMenu open={isSearchOpen} onOpenChange={setIsSearchOpen}>
                          <DropdownMenuTrigger asChild>
                            <button
                              type="button"
                              className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
                              onClick={() => {
                                setSearchQuery(formData.studentId);
                                setIsSearchOpen(true);
                              }}
                            >
                              <ChevronDown className="h-4 w-4" />
                            </button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end" className="w-[300px] max-h-[300px] overflow-auto p-0">
                            <div className="p-2 border-b">
                              <Input
                                placeholder="Search by ID or name..."
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                                className="h-8"
                              />
                            </div>
                            {filteredStudents.length > 0 ? (
                              filteredStudents.map((student) => (
                                <DropdownMenuItem
                                  key={student.id}
                                  className="px-3 py-2 cursor-pointer"
                                  onClick={() => {
                                    setFormData(prev => ({
                                      ...prev,
                                      studentId: student.id,
                                      studentName: student.name,
                                      class: student.class
                                    }));
                                    setIsSearchOpen(false);
                                  }}
                                >
                                  <div className="flex flex-col">
                                    <span className="font-medium">{student.id}</span>
                                    <span className="text-sm text-gray-500">{student.name} ({student.class.replace(/^Class\s+/i, '').trim()})</span>
                                  </div>
                                </DropdownMenuItem>
                              ))
                            ) : (
                              <div className="px-3 py-2 text-sm text-gray-500">No students found</div>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  </div>

                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1" htmlFor="studentName">Full Name</label>
                    <Input
                      id="studentName"
                      name="studentName"
                      placeholder="Auto-populated from Student ID"
                      value={formData.studentName}
                      className="h-9 bg-gray-50"
                      readOnly
                    />
                  </div>
                </div>

                <div className="mt-4 grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1" htmlFor="class">Class</label>
                    <Input
                      id="class"
                      name="class"
                      placeholder="Auto-populated from Student ID"
                      value={formData.class ? formData.class.replace(/^Class\s+/i, '').trim() : ''}
                      className="h-9 bg-gray-50"
                      readOnly
                    />
                  </div>

                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1" htmlFor="academicYear">Academic Year</label>
                    <select
                      id="academicYear"
                      name="academicYear"
                      value={formData.academicYear}
                      onChange={handleInputChange}
                      className="h-9 w-full rounded-md border border-gray-300 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    >
                      <option value="2024-2025">2024-2025</option>
                      <option value="2023-2024">2023-2024</option>
                      <option value="2022-2023">2022-2023</option>
                      <option value="2021-2022">2021-2022</option>
                      <option value="2020-2021">2020-2021</option>
                      <option value="2019-2020">2019-2020</option>
                    </select>
                  </div>
                </div>

                <div className="mt-4">
                  <label className="block text-xs font-medium text-gray-700 mb-1" htmlFor="semester">Semester</label>
                  <div className="flex space-x-4">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="semester"
                        value="First Semester"
                        checked={formData.semester === 'First Semester'}
                        onChange={handleInputChange}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="ml-2 text-sm text-gray-700">First Semester</span>
                    </label>

                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="semester"
                        value="Second Semester"
                        checked={formData.semester === 'Second Semester'}
                        onChange={handleInputChange}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="ml-2 text-sm text-gray-700">Second Semester</span>
                    </label>
                  </div>
                </div>
              </div>

              <div className="space-y-5 flex items-center justify-center">
                <Button
                  className="w-full bg-blue-600 hover:bg-blue-700 h-10"
                  onClick={handleGenerateReportCard}
                  disabled={
                    !formData.studentId ||
                    !formData.studentName ||
                    !formData.class ||
                    !formData.academicYear
                  }
                >
                  {isGenerating ? (
                    <div className="flex items-center justify-center">
                      <div className="animate-spin mr-2">
                        <Calendar className="h-4 w-4" />
                      </div>
                      <span>Generating...</span>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center">
                      {isGenerated ? (
                        <>
                          <CheckCircle className="h-4 w-4 mr-2" />
                          <span>View Report Card</span>
                        </>
                      ) : (
                        <>
                          <FileText className="h-4 w-4 mr-2" />
                          <span>Generate Report Card</span>
                        </>
                      )}
                    </div>
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="flex justify-between items-center bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center space-x-3">
              <Button
                variant="outline"
                onClick={() => setStep('form')}
              >
                Back to Form
              </Button>
              <span className="text-sm text-gray-500">
                Report Card for: <span className="font-medium text-gray-900">{formData.studentName}</span>
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                className="flex items-center space-x-2"
                onClick={handlePrint}
              >
                <Printer className="h-4 w-4" />
                <span>Print</span>
              </Button>
              <Button
                className="bg-indigo-600 hover:bg-indigo-700 flex items-center space-x-2"
                onClick={handlePrint}
              >
                <Download className="h-4 w-4" />
                <span>Download PDF</span>
              </Button>
            </div>
          </div>

          {/* A4 Size Report Card Preview */}
          <div className="bg-white border border-gray-200 rounded-lg mx-auto shadow-md overflow-hidden">
            <div
              ref={printRef}
              className="w-full p-8"
              style={{ maxWidth: '794px', margin: '0 auto' }}
            >
              {/* School Logo and Header */}
              <div className="text-center mb-6">
                <img
                  src="/school-logo.png"
                  alt="School Logo"
                  className="mx-auto w-24 h-24 mb-4"
                />
                <h1 className="text-xl font-bold uppercase">ALFALAH ELEMENTARY, JUNIOR AND SECONDARY</h1>
                <h2 className="text-lg">ISLAMIC SCHOOL Dire Dawa</h2>
                <h3 className="text-xl font-bold mt-4">STUDENT REPORT CARD</h3>
                <div className="flex justify-between mt-4">
                  <span className="font-semibold">1<sup>st</sup> Semester</span>
                  <span>Academic Year: {formData.academicYear}</span>
                </div>
              </div>

              {/* Student Information */}
              <div className="grid grid-cols-2 gap-8 mb-6 text-left">
                <div>Student Name: {formData.studentName}</div>
                <div>Gender: Male</div>
                <div>Class: {formData.class ? formData.class.replace(/^Class\s+/i, '').trim() : formData.class}</div>
                <div></div>
              </div>

              {/* Subjects Table */}
              <table className="w-full border-collapse border border-gray-300 mb-6">
                <thead>
                  <tr>
                    <th className="border border-gray-300 p-2 text-left">Subject</th>
                    <th className="border border-gray-300 p-2">Mark</th>
                  </tr>
                </thead>
                <tbody>
                  {[
                    "Islamic. Edu.",
                    "Arabic",
                    "Somali",
                    "Amharic",
                    "English",
                    "Maths",
                    "Social Studies",
                    "General Science",
                    "Somali",
                    "Citizen. Studies",
                    "HPE",
                    "ICT",
                    "CTE",
                    "PVT",
                    "Geography",
                    "Total",
                    "Average",
                    "Grade"
                  ].map((subject, index) => (
                    <tr key={index}>
                      <td className="border border-gray-300 p-2">{subject}</td>
                      <td className="border border-gray-300 p-2 text-center">
                        {index === 15 ? calculateTotalMarks().total :
                         index === 16 ? (calculateTotalMarks().total / 15).toFixed(2) :
                         index === 17 ? getOverallGrade(parseFloat(calculateTotalMarks().percentage)) :
                         ""}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>

              {/* Grading Scale */}
              <div className="mb-6">
                <div className="flex">
                  <div className="mr-4">Grading</div>
                  <div>Scale</div>
                </div>
                <div className="flex">
                  <div className="w-8">A</div>
                  <div>90 – 100 Excellent</div>
                </div>
                <div className="flex">
                  <div className="w-8">B</div>
                  <div>80 - 89   Very Good</div>
                </div>
                <div className="flex">
                  <div className="w-8">C</div>
                  <div>70 - 79   Good</div>
                </div>
                <div className="flex">
                  <div className="w-8">B</div>
                  <div>60 - 69   Fair</div>
                </div>
                <div className="flex">
                  <div className="w-8">C</div>
                  <div>50 - 59   Enough</div>
                </div>
                <div className="flex">
                  <div className="w-8">D</div>
                  <div>Below 50  Poor</div>
                </div>
              </div>

              {/* Signatures */}
              <div className="flex justify-between mt-8">
                <div>
                  <p>Home Room Teacher:</p>
                  <p>Signature   : _________________</p>
                </div>
                <div>
                  <p>Director:</p>
                  <p>Signature: _________________</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

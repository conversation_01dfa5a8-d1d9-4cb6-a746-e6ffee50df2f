{"name": "akademi-dashboard", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@prisma/client": "^6.5.0", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-radio-group": "^1.3.4", "@radix-ui/react-roving-focus": "^1.1.3", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.2.4", "@tanstack/react-query": "^5.74.4", "@tanstack/react-table": "^8.21.3", "@types/papaparse": "^5.3.15", "@types/uuid": "^10.0.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "csv-parse": "^5.6.0", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "framer-motion": "^12.10.5", "html2canvas": "^1.4.1", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "0.263.1", "mysql2": "^3.14.1", "next": "^14.1.0", "next-themes": "^0.4.6", "node-fetch": "^2.7.0", "papaparse": "^5.5.2", "prisma": "^6.5.0", "react": "^18.2.0", "react-day-picker": "^9.6.4", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-to-print": "^3.0.5", "recharts": "^2.15.3", "tailwind-merge": "^1.14.0", "uuid": "^11.1.0"}, "devDependencies": {"@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.14.1", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "tailwindcss-animate": "1.0.7", "typescript": "5.2.2"}, "resolutions": {"@radix-ui/react-focus-scope": "1.1.6", "@radix-ui/react-dialog": "1.1.13"}}
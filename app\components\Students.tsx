"use client"

import { useState, useMemo } from 'react'
import { But<PERSON> } from './ui/button'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/table'
import { Input } from './ui/input'
import {
  Search, Edit, Trash2, SlidersHorizontal, Plus, Upload, Loader2,
  ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight,
  X, User
} from 'lucide-react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from './ui/use-toast'
import { formatStudentName } from '@/app/utils/formatters'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select"

import { Label } from "./ui/label"
import { ImportStudentsDialog } from './ImportStudentsDialog'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON><PERSON>eader,
  DialogTitle,
} from "./ui/dialog"
import { useAuth } from '../contexts/auth-context'
import { usePermissionDeniedDialog } from './PermissionDeniedDialog'



interface Student {
  id: string
  sid: string
  name: string
  fatherName: string
  gfName: string
  classId: string
  className: string
  age: number
  gender: string
  academicYear: string
}

// We'll fetch all data from the database with no hardcoded sample data


export function Students() {
  const queryClient = useQueryClient()
  const { user } = useAuth()
  const { showDialog, PermissionDialog } = usePermissionDeniedDialog()
  const [sortOrder, setSortOrder] = useState<'newest' | 'oldest' | 'az'>('newest')
  const [searchTerm, setSearchTerm] = useState('')
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false)
  const [editingStudent, setEditingStudent] = useState<Student | null>(null)
  const [formData, setFormData] = useState({
    name: '',
    fatherName: '',
    gfName: '',
    className: '',
    age: '',
    gender: 'male',
    academicYear: '2023-2024'
  })

  // Filter state
  const [classFilter, setClassFilter] = useState<string>('all')
  const [academicYearFilter, setAcademicYearFilter] = useState<string>('all')
  const [showFilters, setShowFilters] = useState(false)

  // Pagination state
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  })

  // Fetch classes
  const { data: classes = [], isLoading: isLoadingClasses } = useQuery({
    queryKey: ['classes'],
    queryFn: async () => {
      const currentYear = new Date().getFullYear()
      const academicYear = `${currentYear}-${currentYear + 1}`
      const res = await fetch(`/api/classes?year=${academicYear}`)
      if (!res.ok) throw new Error('Failed to fetch classes')
      return res.json()
    }
  })

  // Fetch students
  const { data: students = [], isLoading: isLoadingStudents } = useQuery({
    queryKey: ['students'],
    queryFn: async () => {
      const res = await fetch('/api/students')
      if (!res.ok) throw new Error('Failed to fetch students')
      return res.json()
    }
  })



  // Simple filtered students logic
  const filteredStudents = useMemo(() => {
    let filtered = students.filter((student: Student) =>
      student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.fatherName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.className.toLowerCase().includes(searchTerm.toLowerCase())
    );

    if (classFilter && classFilter !== 'all') {
      filtered = filtered.filter((student: Student) => student.className === classFilter);
    }

    if (academicYearFilter && academicYearFilter !== 'all') {
      filtered = filtered.filter((student: Student) => student.academicYear === academicYearFilter);
    }

    return [...filtered].sort((a, b) => {
      switch (sortOrder) {
        case 'newest':
          return new Date(b.createdAt || Date.now()).getTime() - new Date(a.createdAt || Date.now()).getTime()
        case 'oldest':
          return new Date(a.createdAt || Date.now()).getTime() - new Date(b.createdAt || Date.now()).getTime()
        case 'az':
          return a.name.localeCompare(b.name)
        default:
          return 0
      }
    });
  }, [students, searchTerm, sortOrder, classFilter, academicYearFilter]);

  // Create student mutation
  const createStudentMutation = useMutation({
    mutationFn: async (data: typeof formData) => {
      const res = await fetch('/api/students', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })
      if (!res.ok) {
        const error = await res.json()
        throw new Error(error.message || 'Failed to create student')
      }
      return res.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['students'] })
      setIsAddDialogOpen(false)
      setFormData({
        name: '',
        fatherName: '',
        gfName: '',
        className: '',
        age: '',
        gender: 'male',
        academicYear: '2023-2024'
      })
      toast({ title: 'Success', description: 'Student added successfully' })
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive'
      })
    }
  })

  // Update student mutation
  const updateStudentMutation = useMutation({
    mutationFn: async (data: {
      id: string;
      name: string;
      fatherName: string;
      gfName: string;
      class: string; // API expects 'class' not 'className'
      age: string;
      gender: string;
      academicYear: string;
    }) => {
      const res = await fetch(`/api/students/${data.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })
      if (!res.ok) {
        const error = await res.json()
        throw new Error(error.message || 'Failed to update student')
      }
      return res.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['students'] })
      setEditingStudent(null)
      setIsAddDialogOpen(false)
      toast({ title: 'Success', description: 'Student updated successfully' })
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive'
      })
    }
  })

  // Delete student mutation
  const deleteStudentMutation = useMutation({
    mutationFn: async (id: string) => {
      const res = await fetch(`/api/students/${id}`, { method: 'DELETE' })
      if (!res.ok) {
        const error = await res.json()
        throw new Error(error.message || 'Failed to delete student')
      }
      return res.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['students'] })
      toast({ title: 'Success', description: 'Student deleted successfully' })
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive'
      })
    }
  })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: name.includes('Name') || name === 'name' ? formatStudentName(value) : value
    }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (editingStudent) {
      // Transform formData to match the API's expected format
      const updateData = {
        id: editingStudent.id,
        name: formData.name,
        fatherName: formData.fatherName,
        gfName: formData.gfName,
        class: formData.className, // API expects 'class' not 'className'
        age: formData.age,
        gender: formData.gender,
        academicYear: formData.academicYear
      }
      updateStudentMutation.mutate(updateData)
    } else {
      createStudentMutation.mutate(formData)
    }
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Students</h2>
        <div className="flex gap-2">
          <Button
            onClick={() => {
              if (!user || !['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'DATA_ENCODER'].includes(user.role)) {
                showDialog(
                  'You do not have permission to add students',
                  'Only Super Admin, Admin, Supervisor, and Data Encoder roles can add students.'
                );
                return;
              }
              setIsAddDialogOpen(true);
            }}
            variant="default"
            className="bg-emerald-600 hover:bg-emerald-700"
          >
            <Plus className="mr-2 h-4 w-4" /> New Student
          </Button>
          <Button
            variant="outline"
            onClick={() => {
              if (!user || !['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'DATA_ENCODER'].includes(user.role)) {
                showDialog(
                  'You do not have permission to import students',
                  'Only Super Admin, Admin, Supervisor, and Data Encoder roles can import students.'
                );
                return;
              }
              setIsImportDialogOpen(true);
            }}
          >
            <Upload className="h-4 w-4 mr-2" />
            Import CSV
          </Button>
        </div>
      </div>

      <div className="flex flex-col sm:flex-row justify-between items-center mb-6 gap-4">
        <div className="relative w-full sm:w-72">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search here..."
            className="pl-10"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="flex items-center gap-2 w-full sm:w-auto">
          <Button
            variant="outline"
            className="flex items-center gap-2"
            onClick={() => setShowFilters(!showFilters)}
          >
            <SlidersHorizontal className="h-4 w-4" />
            Filters
            {((classFilter && classFilter !== 'all') || (academicYearFilter && academicYearFilter !== 'all')) && (
              <span className="ml-1 w-5 h-5 bg-emerald-500 text-white rounded-full text-xs flex items-center justify-center">
                {(classFilter && classFilter !== 'all' ? 1 : 0) + (academicYearFilter && academicYearFilter !== 'all' ? 1 : 0)}
              </span>
            )}
          </Button>
          <div className="relative ml-2">
            <select
              className="bg-white border rounded-md py-2 px-4 pr-8 appearance-none text-sm focus:outline-none focus:ring-2 focus:ring-emerald-600"
              value={sortOrder}
              onChange={(e) => setSortOrder(e.target.value as 'newest' | 'oldest' | 'az')}
            >
              <option value="newest">Newest</option>
              <option value="oldest">Oldest</option>
              <option value="az">A-Z</option>
            </select>
            <div className="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <svg className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Advanced Filters */}
      {showFilters && (
        <div className="p-4 border rounded-lg bg-gray-50 mt-2 grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="classFilter" className="mb-1 block">Filter by Class</Label>
            <Select
              value={classFilter}
              onValueChange={setClassFilter}
            >
              <SelectTrigger>
                <SelectValue placeholder="All Classes" />
              </SelectTrigger>
              <SelectContent className="max-h-[200px] overflow-y-auto">
                <SelectItem value="all">All Classes</SelectItem>
                {classes.map((cls: any) => (
                  <SelectItem key={cls.id || cls.name} value={cls.name}>
                    {cls.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="academicYearFilter" className="mb-1 block">Filter by Academic Year</Label>
            <Select
              value={academicYearFilter}
              onValueChange={setAcademicYearFilter}
            >
              <SelectTrigger>
                <SelectValue placeholder="All Years" />
              </SelectTrigger>
              <SelectContent className="max-h-[200px] overflow-y-auto">
                <SelectItem value="all">All Years</SelectItem>
                <SelectItem value="2020-2021">2020-2021</SelectItem>
                <SelectItem value="2021-2022">2021-2022</SelectItem>
                <SelectItem value="2022-2023">2022-2023</SelectItem>
                <SelectItem value="2023-2024">2023-2024</SelectItem>
                <SelectItem value="2024-2025">2024-2025</SelectItem>
                <SelectItem value="2025-2026">2025-2026</SelectItem>
                <SelectItem value="2026-2027">2026-2027</SelectItem>
                <SelectItem value="2027-2028">2027-2028</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {((classFilter && classFilter !== 'all') || (academicYearFilter && academicYearFilter !== 'all')) && (
            <div className="md:col-span-2 flex justify-end">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setClassFilter('all');
                  setAcademicYearFilter('all');
                }}
              >
                <X className="h-3 w-3 mr-1" />
                Clear Filters
              </Button>
            </div>
          )}
        </div>
      )}

      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <input type="checkbox" className="h-4 w-4 rounded border-gray-300" />
              </TableHead>
              <TableHead>SID</TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Father Name</TableHead>
              <TableHead>Class</TableHead>
              <TableHead>Age</TableHead>
              <TableHead>Gender</TableHead>
              <TableHead>Academic Year</TableHead>
              <TableHead className="text-right">Action</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoadingStudents ? (
              <TableRow>
                <TableCell colSpan={9} className="text-center py-4">
                  <Loader2 className="h-8 w-8 animate-spin mx-auto" />
                </TableCell>
              </TableRow>
            ) : filteredStudents.length > 0 ? (
              filteredStudents.slice(
                pagination.pageIndex * pagination.pageSize,
                (pagination.pageIndex + 1) * pagination.pageSize
              ).map((student: Student) => (
                <TableRow key={student.id}>
                  <TableCell>
                    <input type="checkbox" className="h-4 w-4 rounded border-gray-300" />
                  </TableCell>
                  <TableCell className="font-medium">
                    <span className="px-2 py-1 rounded text-xs font-medium bg-emerald-100 text-emerald-800">
                      {student.sid}
                    </span>
                  </TableCell>
                  <TableCell className="font-medium">
                    <div className="flex items-center gap-3">
                      <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 font-semibold">
                        {student.name.charAt(0)}
                      </div>
                      {student.name}
                    </div>
                  </TableCell>
                  <TableCell>{student.fatherName}</TableCell>
                  <TableCell>
                    <span className="px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                      {student.className}
                    </span>
                  </TableCell>
                  <TableCell>{student.age}</TableCell>
                  <TableCell>{student.gender}</TableCell>
                  <TableCell>{student.academicYear}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-emerald-600"
                        onClick={() => {
                          if (!user || !['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'DATA_ENCODER'].includes(user.role)) {
                            showDialog(
                              'You do not have permission to edit students',
                              'Only Super Admin, Admin, Supervisor, and Data Encoder roles can edit students.'
                            );
                            return;
                          }
                          setEditingStudent(student)
                          setFormData({
                            name: student.name,
                            fatherName: student.fatherName,
                            gfName: student.gfName,
                            className: student.className,
                            age: String(student.age),
                            gender: student.gender,
                            academicYear: student.academicYear || '2023-2024'
                          })
                          setIsAddDialogOpen(true)
                        }}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-red-600"
                        onClick={() => {
                          if (!user || !['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'DATA_ENCODER'].includes(user.role)) {
                            showDialog(
                              'You do not have permission to delete students',
                              'Only Super Admin, Admin, Supervisor, and Data Encoder roles can delete students.'
                            );
                            return;
                          }
                          deleteStudentMutation.mutate(student.id)
                        }}
                        disabled={deleteStudentMutation.isPending}
                      >
                        {deleteStudentMutation.isPending ? (
                          <div className="animate-spin">...</div>
                        ) : (
                          <Trash2 className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={9} className="text-center py-4 text-gray-500">
                  No students found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination - Optimized for performance */}
      {students.length > 0 && (
        <div className="flex items-center justify-between mt-4 border-t pt-4">
          <div className="flex-1 text-sm text-muted-foreground">
            {/* Use memoized count to avoid expensive calculations */}
            <span>{filteredStudents.length} student(s) found</span>
          </div>
          <div className="flex items-center space-x-6 lg:space-x-8">
            <div className="flex items-center space-x-2">
              <p className="text-sm font-medium">Rows per page</p>
              {/* Optimized Select component with direct state management */}
              <div className="relative inline-block">
                <select
                  className="h-8 w-[70px] rounded-md border border-input bg-background px-3 py-1 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                  value={pagination.pageSize}
                  onChange={(e) => {
                    const newSize = Number(e.target.value);
                    setPagination(prev => ({
                      ...prev,
                      pageSize: newSize,
                      pageIndex: 0, // Reset to first page when changing page size
                    }));
                  }}
                >
                  {[10, 20, 30, 40, 50].map((size) => (
                    <option key={size} value={size}>
                      {size}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            <div className="flex w-[100px] items-center justify-center text-sm font-medium">
              Page {pagination.pageIndex + 1} of{" "}
              {Math.max(1, Math.ceil(filteredStudents.length / pagination.pageSize))}
            </div>
            <div className="flex items-center space-x-2">
              {/* Optimized pagination buttons with direct state management */}
              <Button
                variant="outline"
                className="hidden h-8 w-8 p-0 lg:flex"
                onClick={() => setPagination(prev => ({ ...prev, pageIndex: 0 }))}
                disabled={pagination.pageIndex === 0}
              >
                <span className="sr-only">Go to first page</span>
                <ChevronsLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={() => setPagination(prev => ({ ...prev, pageIndex: Math.max(0, prev.pageIndex - 1) }))}
                disabled={pagination.pageIndex === 0}
              >
                <span className="sr-only">Go to previous page</span>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={() => {
                  const maxPage = Math.ceil(filteredStudents.length / pagination.pageSize) - 1;
                  setPagination(prev => ({
                    ...prev,
                    pageIndex: Math.min(maxPage, prev.pageIndex + 1)
                  }));
                }}
                disabled={pagination.pageIndex >= Math.ceil(filteredStudents.length / pagination.pageSize) - 1}
              >
                <span className="sr-only">Go to next page</span>
                <ChevronRight className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="hidden h-8 w-8 p-0 lg:flex"
                onClick={() => {
                  const maxPage = Math.max(0, Math.ceil(filteredStudents.length / pagination.pageSize) - 1);
                  setPagination(prev => ({ ...prev, pageIndex: maxPage }));
                }}
                disabled={pagination.pageIndex >= Math.ceil(filteredStudents.length / pagination.pageSize) - 1}
              >
                <span className="sr-only">Go to last page</span>
                <ChevronsRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      )}

      {(createStudentMutation.isError || updateStudentMutation.isError || deleteStudentMutation.isError) && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
          <span className="block sm:inline">Failed to perform the requested action. Please try again.</span>
        </div>
      )}

      {/* Add/Edit Student Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={(open) => {
        setIsAddDialogOpen(open)
        if (!open) {
          setEditingStudent(null)
          setFormData({
            name: '',
            fatherName: '',
            gfName: '',
            className: '',
            age: '',
            gender: 'male',
            academicYear: '2023-2024'
          })
        }
      }}>
        <DialogContent className="sm:max-w-[550px]">
          <DialogHeader>
            <DialogTitle>{editingStudent ? 'Edit Student' : 'Add New Student'}</DialogTitle>
            <DialogDescription>
              {editingStudent
                ? 'Edit the student details and click save when you\'re done.'
                : 'Fill in the student details and click save when you\'re done.'
              }
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit} className="grid gap-6 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="name" className="text-sm font-medium">
                  Student Name
                </Label>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="name"
                    name="name"
                    placeholder="Full Name"
                    className="pl-9"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                  />
                </div>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="className" className="text-sm font-medium">
                  Class
                </Label>
                {isLoadingClasses ? (
                  <Select disabled>
                    <SelectTrigger>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      <SelectValue>Loading classes...</SelectValue>
                    </SelectTrigger>
                  </Select>
                ) : (
                  <Select
                    value={formData.className}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, className: value }))}
                    required
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select Class" />
                    </SelectTrigger>
                    <SelectContent className="max-h-[200px] overflow-y-auto">
                      {classes.map((cls: any) => (
                        <SelectItem key={cls.id || cls.name} value={cls.name}>
                          {cls.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="fatherName" className="text-sm font-medium">
                  Father's Name
                </Label>
                <Input
                  id="fatherName"
                  name="fatherName"
                  placeholder="Father's Name"
                  value={formData.fatherName}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="gfName" className="text-sm font-medium">
                  Grandfather's Name
                </Label>
                <Input
                  id="gfName"
                  name="gfName"
                  placeholder="Grandfather's Name"
                  value={formData.gfName}
                  onChange={handleInputChange}
                  required
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="age" className="text-sm font-medium">
                  Age
                </Label>
                <Input
                  id="age"
                  name="age"
                  type="number"
                  placeholder="Age"
                  value={formData.age}
                  onChange={handleInputChange}
                  required
                  min="5"
                  max="20"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="gender" className="text-sm font-medium">
                  Gender
                </Label>
                <Select
                  value={formData.gender}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, gender: value }))}
                  required
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select Gender" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="male">Male</SelectItem>
                    <SelectItem value="female">Female</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="academicYear" className="text-sm font-medium">
                Academic Year
              </Label>
              <Select
                value={formData.academicYear}
                onValueChange={(value) => setFormData(prev => ({ ...prev, academicYear: value }))}
                required
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select Academic Year" />
                </SelectTrigger>
                <SelectContent className="max-h-[200px] overflow-y-auto">
                  <SelectItem value="2020-2021">2020-2021</SelectItem>
                  <SelectItem value="2021-2022">2021-2022</SelectItem>
                  <SelectItem value="2022-2023">2022-2023</SelectItem>
                  <SelectItem value="2023-2024">2023-2024</SelectItem>
                  <SelectItem value="2024-2025">2024-2025</SelectItem>
                  <SelectItem value="2025-2026">2025-2026</SelectItem>
                  <SelectItem value="2026-2027">2026-2027</SelectItem>
                  <SelectItem value="2027-2028">2027-2028</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => {
                setIsAddDialogOpen(false)
                setEditingStudent(null)
                setFormData({
                  name: '',
                  fatherName: '',
                  gfName: '',
                  className: '',
                  age: '',
                  gender: 'male',
                  academicYear: '2023-2024'
                })
              }}>
                Cancel
              </Button>
              <Button type="submit" disabled={createStudentMutation.isPending || updateStudentMutation.isPending}>
                {(createStudentMutation.isPending || updateStudentMutation.isPending) && (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                )}
                {editingStudent ? 'Save Changes' : 'Add Student'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Import Students Dialog */}
      <ImportStudentsDialog
        open={isImportDialogOpen}
        onOpenChange={setIsImportDialogOpen}
      />

      {/* Permission Denied Dialog */}
      <PermissionDialog />
    </div>
  )
}


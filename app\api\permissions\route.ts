import { NextResponse } from 'next/server'
import { verifyJWT } from '@/lib/jwt'
import { cookies } from 'next/headers'
import { prisma } from '@/lib/prisma'

// Helper function to create default permissions
async function createDefaultPermissions() {
  try {
    const permissionCategories = {
      students: [
        { name: 'View Students', description: 'View student information' },
        { name: 'Add Students', description: 'Add new students' },
        { name: 'Edit Students', description: 'Edit student information' },
        { name: 'Delete Students', description: 'Delete students' },
        { name: 'Import Students', description: 'Import students from CSV' }
      ],
      classes: [
        { name: 'View Classes', description: 'View class information' },
        { name: 'Add Classes', description: 'Add new classes' },
        { name: 'Edit Classes', description: 'Edit class information' },
        { name: 'Delete Classes', description: 'Delete classes' }
      ],
      teachers: [
        { name: 'View Teachers', description: 'View teacher information' },
        { name: 'Add Teachers', description: 'Add new teachers' },
        { name: 'Edit Teachers', description: 'Edit teacher information' },
        { name: 'Delete Teachers', description: 'Delete teachers' },
        { name: 'Assign Teachers', description: 'Assign teachers to classes and subjects' }
      ],
      marks: [
        { name: 'View Marks', description: 'View student marks' },
        { name: 'Add Marks', description: 'Add new marks' },
        { name: 'Edit Marks', description: 'Edit marks' },
        { name: 'Delete Marks', description: 'Delete marks' },
        { name: 'Import Marks', description: 'Import marks from CSV' }
      ],
      attendance: [
        { name: 'View Attendance', description: 'View attendance records' },
        { name: 'Add Attendance', description: 'Add attendance records' },
        { name: 'Edit Attendance', description: 'Edit attendance records' }
      ],
      reports: [
        { name: 'Generate Reports', description: 'Generate reports' },
        { name: 'View Reports', description: 'View generated reports' },
        { name: 'Export Reports', description: 'Export reports to PDF/Excel' }
      ],
      system: [
        { name: 'System Settings', description: 'Manage system settings' },
        { name: 'Manage Users', description: 'Manage user accounts' },
        { name: 'Manage Roles', description: 'Manage roles and permissions' }
      ]
    }

    // Create all permissions
    for (const category in permissionCategories) {
      await Promise.all(
        permissionCategories[category].map(permission =>
          prisma.permission.create({
            data: {
              name: permission.name,
              description: permission.description,
              category: category
            }
          })
        )
      )
    }

    console.log('Default permissions created successfully')
  } catch (error) {
    console.error('Error creating default permissions:', error)
    throw error
  }
}

// GET all available permissions
export async function GET() {
  try {
    console.log('Fetching all available permissions')

    // Skip authentication in development mode
    if (process.env.NODE_ENV === 'development') {
      console.log('Development mode - bypassing authentication')

      // Check if we have any permissions in the database
      const permissionCount = await prisma.permission.count()

      // If no permissions exist, create the default permissions
      if (permissionCount === 0) {
        console.log('No permissions found, creating default permissions')
        await createDefaultPermissions()
      }

      // Fetch all permissions
      const permissions = await prisma.permission.findMany({
        orderBy: [
          { category: 'asc' },
          { name: 'asc' }
        ]
      })

      console.log(`Returning ${permissions.length} permissions`)
      return NextResponse.json(permissions)
    } else {
      // Verify authentication in production mode
      const cookieStore = cookies()
      const token = cookieStore.get('token')?.value

      if (!token) {
        console.error('No authentication token found')
        return NextResponse.json(
          { error: 'Unauthorized - Authentication required' },
          { status: 401 }
        )
      }

      try {
        // Verify the token
        const decoded = await verifyJWT(token)

        // Only allow admins to view all permissions
        if (decoded.role !== 'ADMIN') {
          console.error('Unauthorized permissions access attempt', {
            requesterId: decoded.id,
            requesterRole: decoded.role
          })
          return NextResponse.json(
            { error: 'Forbidden - Only administrators can view all permissions' },
            { status: 403 }
          )
        }
      } catch (jwtError) {
        console.error('JWT verification error:', jwtError)
        return NextResponse.json(
          { error: 'Invalid or expired token' },
          { status: 401 }
        )
      }
    }
  } catch (error) {
    console.error('Error fetching permissions:', error)
    return NextResponse.json(
      { error: 'Failed to fetch permissions' },
      { status: 500 }
    )
  }
}

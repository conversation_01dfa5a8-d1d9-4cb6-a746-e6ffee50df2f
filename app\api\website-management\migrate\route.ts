import { NextResponse } from 'next/server';
import { prisma, isUsingMockPrisma } from '@/lib/prisma';
import { migrateWebsiteContentModels, seedWebsiteContent } from '@/lib/db-utils';

export async function GET() {
  try {
    // Check if we're using the mock client
    const usingMock = isUsingMockPrisma();

    if (usingMock) {
      return NextResponse.json({
        status: 'warning',
        message: 'Using mock Prisma client. No migration needed.',
        usingMockClient: true
      });
    }

    // Run Prisma migration for website content models
    const migrationResult = await migrateWebsiteContentModels();

    if (!migrationResult.success) {
      return NextResponse.json({
        status: 'error',
        message: migrationResult.message,
        suggestion: 'Please run the migration manually using "npx prisma db push"'
      }, { status: 500 });
    }

    // Seed initial website content
    const seedResult = await seedWebsiteContent();

    // Return the result
    return NextResponse.json({
      status: 'success',
      message: 'Database migration and seeding completed successfully',
      migrationResult: migrationResult.message,
      seedResult: seedResult.message
    });
  } catch (error) {
    console.error('Migration error:', error);

    return NextResponse.json({
      status: 'error',
      message: 'Migration failed',
      error: error instanceof Error ? error.message : 'Unknown error',
      suggestion: 'Please run the migration manually using "npx prisma db push"'
    }, { status: 500 });
  }
}

'use client'

import React, { useState, useEffect } from 'react'
import Sidebar from '../../components/Sidebar'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Label } from '../../components/ui/label'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs'
import {
  Receipt,
  Calendar,
  User,
  DollarSign,
  CreditCard,
  BookOpen,
  GraduationCap,
  Library,
  FileText,
  CheckCircle2,
  AlertCircle,
  Loader2,
  History,
  CalendarRange
} from 'lucide-react'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../components/ui/select"
import { Checkbox } from "../../components/ui/checkbox"
import { useQuery } from '@tanstack/react-query'
import { useToast } from '../../components/ui/use-toast'
import dynamic from 'next/dynamic'
import PaymentHistoryDialog from '../components/PaymentHistoryDialog'

// Import Header component with no SSR to avoid hydration issues
const Header = dynamic(() => import('../../components/Header'), {
  ssr: false
})

// Define interfaces
interface Student {
  id: string
  sid: string
  name: string
  className: string
}

interface Class {
  id: string
  name: string
}

export default function FeeInvoicePage() {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false)
  const [activeTab, setActiveTab] = useState('tuition-fee')
  const [selectedClass, setSelectedClass] = useState('')
  const [selectedStudent, setSelectedStudent] = useState('')
  const [paymentMethod, setPaymentMethod] = useState('cash')
  const [transferId, setTransferId] = useState('')
  const [amount, setAmount] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [paymentDate, setPaymentDate] = useState(new Date().toISOString().split('T')[0])
  const [paymentPurpose, setPaymentPurpose] = useState('Tuition Fee (Monthly)')
  const [isPaymentHistoryOpen, setIsPaymentHistoryOpen] = useState(false)
  const [selectedFeeType, setSelectedFeeType] = useState('')
  const [isMultiMonth, setIsMultiMonth] = useState(false)
  const [selectedMonths, setSelectedMonths] = useState<string[]>([])
  const [selectedSingleMonth, setSelectedSingleMonth] = useState((new Date().getMonth() + 1).toString())
  const [baseMonthlyAmount, setBaseMonthlyAmount] = useState('')
  const [tuitionFeeAmount, setTuitionFeeAmount] = useState('')
  const { toast } = useToast()

  // Month names for display
  const monthNames = [
    { name: 'January', value: '1' },
    { name: 'February', value: '2' },
    { name: 'March', value: '3' },
    { name: 'April', value: '4' },
    { name: 'May', value: '5' },
    { name: 'June', value: '6' },
    { name: 'July', value: '7' },
    { name: 'August', value: '8' },
    { name: 'September', value: '9' },
    { name: 'October', value: '10' },
    { name: 'November', value: '11' },
    { name: 'December', value: '12' }
  ]

  // Fetch tuition fee amount on component mount
  useEffect(() => {
    // If we're on the tuition fee tab, fetch the amount
    if (activeTab === 'tuition-fee') {
      fetchTuitionFeeAmount()
    }
  }, [])

  // Handle fee type selection for the "Other" tab
  const handleFeeTypeChange = (feeTypeId: string) => {
    setSelectedFeeType(feeTypeId)

    // Find the selected fee type
    const feeType = feeTypes.find((ft: any) => ft.id === feeTypeId)
    if (feeType) {
      // Update the payment purpose and amount
      setPaymentPurpose(feeType.name)
      setAmount(feeType.amount.toString())
    }
  }

  // Handle payment purpose change for other tabs
  const handlePaymentPurposeChange = (purposeName: string) => {
    setPaymentPurpose(purposeName)

    // Find if this purpose matches a fee type and update the amount
    const matchingFeeType = feeTypes.find((ft: any) => ft.name === purposeName)
    if (matchingFeeType) {
      setAmount(matchingFeeType.amount.toString())
    } else {
      // Handle default fee types
      switch (purposeName) {
        case 'Tuition Fee (Monthly)':
          if (tuitionFeeAmount) {
            setAmount(tuitionFeeAmount)
          }
          break
        case 'Registration Fee':
        case 'Exam Fee':
        case 'Library Fee':
          // These will be handled by their respective tabs
          break
      }
    }
  }

  // Handle month selection for multi-month payments
  const handleMonthSelection = (month: string) => {
    setSelectedMonths(prev => {
      // If month is already selected, remove it
      if (prev.includes(month)) {
        const newSelection = prev.filter(m => m !== month)
        updateTotalAmount(newSelection)
        return newSelection
      }
      // Otherwise add it
      else {
        const newSelection = [...prev, month]
        updateTotalAmount(newSelection)
        return newSelection
      }
    })
  }

  // Update the total amount based on selected months
  const updateTotalAmount = (months: string[]) => {
    // Use tuition fee amount from Fee Types if available, otherwise use baseMonthlyAmount
    const amountToUse = tuitionFeeAmount || baseMonthlyAmount

    if (!amountToUse || isNaN(parseFloat(amountToUse))) return

    const monthlyFee = parseFloat(amountToUse)
    const totalAmount = monthlyFee * months.length

    setAmount(totalAmount.toString())
  }

  // Get month name from month number
  const getMonthName = (monthNumber: string) => {
    return monthNames.find(m => m.value === monthNumber)?.name || ''
  }

  // Toggle multi-month mode
  const toggleMultiMonth = (enabled: boolean) => {
    setIsMultiMonth(enabled)

    if (enabled) {
      // Use tuition fee amount from Fee Types if available, otherwise use current amount
      const monthlyAmount = tuitionFeeAmount || amount
      setBaseMonthlyAmount(monthlyAmount)

      // Initialize with current month if no months are selected
      if (selectedMonths.length === 0) {
        const currentMonth = (new Date().getMonth() + 1).toString()
        setSelectedMonths([currentMonth])

        // Set amount to monthly fee (for a single month initially)
        if (monthlyAmount) {
          setAmount(monthlyAmount)
        }
      }
    } else {
      // Reset to single month mode
      setSelectedMonths([])

      // Restore the base amount (single month fee)
      if (tuitionFeeAmount) {
        setAmount(tuitionFeeAmount)
      } else if (baseMonthlyAmount) {
        setAmount(baseMonthlyAmount)
      }
    }
  }

  // Fetch classes
  const { data: classes = [], isLoading: isLoadingClasses } = useQuery({
    queryKey: ['classes'],
    queryFn: async () => {
      const res = await fetch('/api/classes')
      if (!res.ok) throw new Error('Failed to fetch classes')
      return res.json()
    }
  })

  // Fetch students based on selected class
  const { data: students = [], isLoading: isLoadingStudents } = useQuery({
    queryKey: ['students', selectedClass],
    queryFn: async () => {
      if (!selectedClass) return []
      const res = await fetch(`/api/students?class=${selectedClass}`)
      if (!res.ok) throw new Error('Failed to fetch students')
      return res.json()
    },
    enabled: !!selectedClass
  })

  // Fetch fee types for the "Other" tab
  const { data: feeTypes = [], isLoading: isLoadingFeeTypes } = useQuery({
    queryKey: ['feeTypes'],
    queryFn: async () => {
      const res = await fetch('/api/accounting/fee-types')
      if (!res.ok) throw new Error('Failed to fetch fee types')
      return res.json()
    }
  })

  // Function to fetch and set the Tuition Fee amount
  const fetchTuitionFeeAmount = async () => {
    try {
      const response = await fetch('/api/accounting/fee-types')
      if (!response.ok) {
        throw new Error('Failed to fetch fee types')
      }

      const feeTypes = await response.json()
      const tuitionFee = feeTypes.find((ft: any) => ft.name === 'Tuition Fee (Monthly)')

      if (tuitionFee) {
        setTuitionFeeAmount(tuitionFee.amount.toString())

        // If we're on the tuition fee tab and not in multi-month mode, set the amount
        if (activeTab === 'tuition-fee' && !isMultiMonth) {
          setAmount(tuitionFee.amount.toString())
        }
        // If we're in multi-month mode, update the base amount and recalculate
        else if (activeTab === 'tuition-fee' && isMultiMonth) {
          setBaseMonthlyAmount(tuitionFee.amount.toString())
          updateTotalAmount(selectedMonths)
        }
      }
    } catch (error) {
      console.error('Error fetching tuition fee amount:', error)
    }
  }

  // Update payment purpose based on active tab
  useEffect(() => {
    switch (activeTab) {
      case 'tuition-fee':
        setPaymentPurpose('Tuition Fee (Monthly)')
        // Fetch and set the tuition fee amount from fee types
        fetchTuitionFeeAmount()
        break
      case 'registration-fee':
        setPaymentPurpose('Registration Fee')
        break
      case 'exam-fee':
        setPaymentPurpose('Exam Fee')
        break
      case 'library-fee':
        setPaymentPurpose('Library Fee')
        break
      case 'other':
        // For the "Other" tab, we'll let the user select the purpose
        setPaymentPurpose('')
        break
    }
  }, [activeTab, isMultiMonth, selectedMonths])

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  const toggleMobileSidebar = () => {
    setMobileSidebarOpen(!mobileSidebarOpen)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validate form
    if (!selectedClass || !selectedStudent || !amount || !paymentDate) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      })
      return
    }

    if (paymentMethod === 'transfer' && !transferId) {
      toast({
        title: "Validation Error",
        description: "Please enter the Transfer ID",
        variant: "destructive",
      })
      return
    }

    // For multi-month tuition fee payments, validate that at least one month is selected
    if (activeTab === 'tuition-fee' && isMultiMonth && selectedMonths.length === 0) {
      toast({
        title: "Validation Error",
        description: "Please select at least one month to pay for",
        variant: "destructive",
      })
      return
    }

    // Start submission
    setIsSubmitting(true)

    try {
      // Get the fee type ID based on the payment purpose
      const feeTypesResponse = await fetch('/api/accounting/fee-types')
      if (!feeTypesResponse.ok) {
        throw new Error('Failed to fetch fee types')
      }

      const feeTypes = await feeTypesResponse.json()
      let selectedFeeType = feeTypes.find((ft: any) => ft.name === paymentPurpose)

      if (!selectedFeeType) {
        // If fee type doesn't exist, create it
        const newFeeType = {
          name: paymentPurpose,
          description: `Fee for ${paymentPurpose}`,
          amount: parseFloat(isMultiMonth ? baseMonthlyAmount : amount),
          frequency: paymentPurpose === 'Tuition Fee (Monthly)' ? 'monthly' : 'one-time'
        }

        const createResponse = await fetch('/api/accounting/fee-types', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(newFeeType),
        })

        if (!createResponse.ok) {
          const errorData = await createResponse.json()
          throw new Error(errorData.error || 'Failed to create fee type')
        }

        selectedFeeType = await createResponse.json()
      }

      // Handle multi-month tuition fee payments
      if (activeTab === 'tuition-fee' && isMultiMonth && selectedMonths.length > 0) {
        // Use tuition fee amount from Fee Types if available, otherwise use baseMonthlyAmount
        const monthlyAmount = parseFloat(tuitionFeeAmount || baseMonthlyAmount)
        const monthsText = selectedMonths.map(m => monthNames.find(mn => mn.value === m)?.name).join(', ')

        // Sort months numerically for better display
        const sortedMonths = [...selectedMonths].sort((a, b) => parseInt(a) - parseInt(b))

        // Create a single multi-month payment
        const multiMonthPaymentData = {
          studentId: selectedStudent,
          feeTypeId: selectedFeeType.id,
          amount: parseFloat(amount), // This is the total amount for all months
          paymentDate,
          months: sortedMonths, // All selected months
          paymentMethod,
          transferId: transferId || undefined,
          status: 'paid',
          notes: `Payment for ${paymentPurpose} - ${monthsText}`
        }

        const response = await fetch('/api/accounting/payments/multi-month', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(multiMonthPaymentData),
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Failed to record multi-month payment')
        }

        const result = await response.json()

        toast({
          title: "Multi-Month Payment Recorded",
          description: `Invoice created for ${monthsText} with total amount ${new Intl.NumberFormat('en-ET', {
            style: 'currency',
            currency: 'ETB'
          }).format(parseFloat(amount))}`,
          variant: "default",
        })
      } else {
        // Create a single payment (original logic)
        const paymentData = {
          studentId: selectedStudent,
          feeTypeId: selectedFeeType.id,
          amount: parseFloat(amount),
          paymentDate,
          // Store the month information for monthly payments
          forMonth: paymentPurpose === 'Tuition Fee (Monthly)' ?
            selectedSingleMonth : null,
          paymentMethod,
          transferId: transferId || undefined,
          status: 'paid',
          notes: `Payment for ${paymentPurpose}`
        }

        const response = await fetch('/api/accounting/payments', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(paymentData),
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Failed to record payment')
        }

        const payment = await response.json()

        toast({
          title: "Payment Recorded",
          description: `Invoice #${payment.invoiceNumber} has been created successfully`,
          variant: "default",
        })
      }

      // Reset form
      setSelectedStudent('')
      setAmount('')
      setTransferId('')

      // Reset multi-month related state
      setIsMultiMonth(false)
      setSelectedMonths([])
      setSelectedSingleMonth((new Date().getMonth() + 1).toString())
      setBaseMonthlyAmount('')

      // Reset the active tab to ensure UI is in a clean state
      setActiveTab('tuition-fee')
    } catch (error) {
      console.error('Error recording payment:', error)
      toast({
        title: "Error",
        description: error.message || "Failed to record payment",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="flex h-screen bg-gray-100 dark:bg-gray-900">
      <Sidebar
        isCollapsed={sidebarCollapsed}
        toggleSidebar={toggleSidebar}
        isMobileOpen={mobileSidebarOpen}
      />

      <main className={`main-content ${sidebarCollapsed ? 'sidebar-collapsed' : ''} w-full h-screen overflow-y-auto`}>
        <Header toggleMobileSidebar={toggleMobileSidebar} />

        <div className="container mx-auto px-4 py-6">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold text-gray-800 dark:text-white">
              Fee Invoice
            </h1>
            <Button
              className="bg-green-600 hover:bg-green-700"
              onClick={() => setIsPaymentHistoryOpen(true)}
            >
              <History className="h-4 w-4 mr-2" />
              View Payment History
            </Button>
          </div>

          <Card className="shadow-lg border-0">
            <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border-b">
              <CardTitle className="flex items-center text-xl">
                <Receipt className="mr-2 h-5 w-5 text-blue-600 dark:text-blue-400" />
                Create New Fee Invoice
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                <TabsList className="w-full grid grid-cols-5 mb-8 rounded-lg bg-blue-50 dark:bg-gray-800 p-1">
                  <TabsTrigger
                    value="tuition-fee"
                    className="rounded-md py-2 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:shadow-sm"
                  >
                    <DollarSign className="h-4 w-4 mr-2 text-blue-600 dark:text-blue-400" />
                    Tuition Fee (Monthly)
                  </TabsTrigger>
                  <TabsTrigger
                    value="registration-fee"
                    className="rounded-md py-2 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:shadow-sm"
                  >
                    <GraduationCap className="h-4 w-4 mr-2 text-green-600 dark:text-green-400" />
                    Registration Fee
                  </TabsTrigger>
                  <TabsTrigger
                    value="exam-fee"
                    className="rounded-md py-2 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:shadow-sm"
                  >
                    <BookOpen className="h-4 w-4 mr-2 text-amber-600 dark:text-amber-400" />
                    Exam Fee
                  </TabsTrigger>
                  <TabsTrigger
                    value="library-fee"
                    className="rounded-md py-2 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:shadow-sm"
                  >
                    <Library className="h-4 w-4 mr-2 text-purple-600 dark:text-purple-400" />
                    Library Fee
                  </TabsTrigger>
                  <TabsTrigger
                    value="other"
                    className="rounded-md py-2 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:shadow-sm"
                  >
                    <FileText className="h-4 w-4 mr-2 text-orange-600 dark:text-orange-400" />
                    Other
                  </TabsTrigger>
                </TabsList>

                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="payment-date" className="text-sm font-medium flex items-center">
                        <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                        Payment Date
                      </Label>
                      <Input
                        id="payment-date"
                        type="date"
                        value={paymentDate}
                        onChange={(e) => setPaymentDate(e.target.value)}
                        className="h-10"
                        required
                      />
                    </div>

                    {activeTab === 'tuition-fee' ? (
                      <div className="space-y-4">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="multi-month"
                            checked={isMultiMonth}
                            onCheckedChange={(checked) => toggleMultiMonth(checked === true)}
                          />
                          <Label
                            htmlFor="multi-month"
                            className="text-sm font-medium cursor-pointer flex items-center"
                          >
                            <CalendarRange className="h-4 w-4 mr-2 text-blue-500" />
                            Pay for multiple months
                          </Label>
                        </div>

                        {isMultiMonth ? (
                          <div className="space-y-2 border rounded-md p-3 bg-gray-50 dark:bg-gray-800">
                            <Label className="text-sm font-medium flex items-center mb-2">
                              <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                              Select Months to Pay
                            </Label>
                            <div className="grid grid-cols-3 gap-2">
                              {monthNames.map((month) => (
                                <div key={month.value} className="flex items-center space-x-2">
                                  <Checkbox
                                    id={`month-${month.value}`}
                                    checked={selectedMonths.includes(month.value)}
                                    onCheckedChange={() => handleMonthSelection(month.value)}
                                  />
                                  <Label
                                    htmlFor={`month-${month.value}`}
                                    className="text-sm cursor-pointer"
                                  >
                                    {month.name}
                                  </Label>
                                </div>
                              ))}
                            </div>
                            {selectedMonths.length > 0 && (
                              <div className="mt-2 text-sm text-blue-600 dark:text-blue-400">
                                {selectedMonths.length} month{selectedMonths.length > 1 ? 's' : ''} selected
                              </div>
                            )}
                          </div>
                        ) : (
                          <div className="space-y-2">
                            <Label htmlFor="as-of-month" className="text-sm font-medium flex items-center">
                              <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                              As Of (Month)
                            </Label>
                            <Select
                              value={selectedSingleMonth}
                              onValueChange={(value) => {
                                setSelectedSingleMonth(value);
                              }}
                            >
                              <SelectTrigger id="as-of-month" className="h-10">
                                <SelectValue placeholder="Select Month" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="1">January</SelectItem>
                                <SelectItem value="2">February</SelectItem>
                                <SelectItem value="3">March</SelectItem>
                                <SelectItem value="4">April</SelectItem>
                                <SelectItem value="5">May</SelectItem>
                                <SelectItem value="6">June</SelectItem>
                                <SelectItem value="7">July</SelectItem>
                                <SelectItem value="8">August</SelectItem>
                                <SelectItem value="9">September</SelectItem>
                                <SelectItem value="10">October</SelectItem>
                                <SelectItem value="11">November</SelectItem>
                                <SelectItem value="12">December</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        )}
                      </div>
                    ) : activeTab !== 'other' && (
                      <div className="space-y-2">
                        <Label htmlFor="as-of-month" className="text-sm font-medium flex items-center">
                          <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                          As Of (Month)
                        </Label>
                        <Select
                          value={selectedSingleMonth}
                          onValueChange={(value) => {
                            setSelectedSingleMonth(value);
                          }}
                        >
                          <SelectTrigger id="as-of-month" className="h-10">
                            <SelectValue placeholder="Select Month" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="1">January</SelectItem>
                            <SelectItem value="2">February</SelectItem>
                            <SelectItem value="3">March</SelectItem>
                            <SelectItem value="4">April</SelectItem>
                            <SelectItem value="5">May</SelectItem>
                            <SelectItem value="6">June</SelectItem>
                            <SelectItem value="7">July</SelectItem>
                            <SelectItem value="8">August</SelectItem>
                            <SelectItem value="9">September</SelectItem>
                            <SelectItem value="10">October</SelectItem>
                            <SelectItem value="11">November</SelectItem>
                            <SelectItem value="12">December</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    )}

                    <div className="space-y-2">
                      <Label htmlFor="class" className="text-sm font-medium flex items-center">
                        <BookOpen className="h-4 w-4 mr-2 text-gray-500" />
                        Class
                      </Label>
                      <Select
                        value={selectedClass}
                        onValueChange={setSelectedClass}
                      >
                        <SelectTrigger id="class" className={isLoadingClasses ? 'opacity-70' : ''}>
                          {isLoadingClasses ? (
                            <div className="flex items-center">
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                              <span>Loading classes...</span>
                            </div>
                          ) : (
                            <SelectValue placeholder="Select Class" />
                          )}
                        </SelectTrigger>
                        <SelectContent className="max-h-[300px] overflow-y-auto">
                          {classes.length > 0 ? (
                            classes.map((cls: Class) => (
                              <SelectItem key={cls.id} value={cls.name}>
                                {cls.name}
                              </SelectItem>
                            ))
                          ) : (
                            <>
                              {/* Fallback class options if API doesn't return classes */}
                              <SelectItem value="1A">Class 1A</SelectItem>
                              <SelectItem value="1B">Class 1B</SelectItem>
                              <SelectItem value="2A">Class 2A</SelectItem>
                              <SelectItem value="2B">Class 2B</SelectItem>
                              <SelectItem value="3A">Class 3A</SelectItem>
                              <SelectItem value="3B">Class 3B</SelectItem>
                              <SelectItem value="4A">Class 4A</SelectItem>
                              <SelectItem value="4B">Class 4B</SelectItem>
                              <SelectItem value="5A">Class 5A</SelectItem>
                              <SelectItem value="5B">Class 5B</SelectItem>
                              <SelectItem value="6A">Class 6A</SelectItem>
                              <SelectItem value="6B">Class 6B</SelectItem>
                              <SelectItem value="7A">Class 7A</SelectItem>
                              <SelectItem value="7B">Class 7B</SelectItem>
                              <SelectItem value="8A">Class 8A</SelectItem>
                              <SelectItem value="8B">Class 8B</SelectItem>
                              <SelectItem value="9A">Class 9A</SelectItem>
                              <SelectItem value="9B">Class 9B</SelectItem>
                              <SelectItem value="10A">Class 10A</SelectItem>
                              <SelectItem value="10B">Class 10B</SelectItem>
                              <SelectItem value="11A">Class 11A</SelectItem>
                              <SelectItem value="11B">Class 11B</SelectItem>
                              <SelectItem value="12A">Class 12A</SelectItem>
                              <SelectItem value="12B">Class 12B</SelectItem>
                            </>
                          )}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="student" className="text-sm font-medium flex items-center">
                        <User className="h-4 w-4 mr-2 text-gray-500" />
                        Student Name
                      </Label>
                      <Select
                        value={selectedStudent}
                        onValueChange={setSelectedStudent}
                        disabled={!selectedClass || isLoadingStudents}
                      >
                        <SelectTrigger id="student" className={isLoadingStudents ? 'opacity-70' : ''}>
                          {isLoadingStudents ? (
                            <div className="flex items-center">
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                              <span>Loading students...</span>
                            </div>
                          ) : (
                            <SelectValue placeholder="Select Student" />
                          )}
                        </SelectTrigger>
                        <SelectContent className="max-h-[200px] overflow-y-auto">
                          {students.map((student: Student) => (
                            <SelectItem key={student.id} value={student.id}>
                              {student.name} ({student.sid})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="payment-purpose" className="text-sm font-medium flex items-center">
                        <FileText className="h-4 w-4 mr-2 text-gray-500" />
                        Purpose of Payment
                      </Label>
                      {activeTab === 'other' ? (
                        <Select
                          value={selectedFeeType}
                          onValueChange={handleFeeTypeChange}
                        >
                          <SelectTrigger id="fee-type" className={isLoadingFeeTypes ? 'opacity-70' : ''}>
                            {isLoadingFeeTypes ? (
                              <div className="flex items-center">
                                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                <span>Loading fee types...</span>
                              </div>
                            ) : (
                              <SelectValue placeholder="Select Fee Type" />
                            )}
                          </SelectTrigger>
                          <SelectContent className="max-h-[200px] overflow-y-auto">
                            {feeTypes.map((feeType: any) => (
                              <SelectItem key={feeType.id} value={feeType.id}>
                                {feeType.name} ({new Intl.NumberFormat('en-ET', {
                                  style: 'currency',
                                  currency: 'ETB'
                                }).format(feeType.amount)})
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      ) : (
                        <Select
                          value={paymentPurpose}
                          onValueChange={handlePaymentPurposeChange}
                        >
                          <SelectTrigger id="payment-purpose">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent className="max-h-[200px] overflow-y-auto">
                            {/* Default options */}
                            <SelectItem value="Tuition Fee (Monthly)">Tuition Fee (Monthly)</SelectItem>
                            <SelectItem value="Registration Fee">Registration Fee</SelectItem>
                            <SelectItem value="Exam Fee">Exam Fee</SelectItem>
                            <SelectItem value="Library Fee">Library Fee</SelectItem>

                            {/* Add a divider if we have fee types */}
                            {!isLoadingFeeTypes && feeTypes.length > 0 && (
                              <div className="px-2 py-1.5 text-xs text-gray-500 border-t">Other Fee Types</div>
                            )}

                            {/* Include all fee types from the Fee Type list */}
                            {!isLoadingFeeTypes && feeTypes.map((feeType: any) => (
                              <SelectItem key={feeType.id} value={feeType.name}>
                                {feeType.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="amount" className="text-sm font-medium flex items-center">
                        <DollarSign className="h-4 w-4 mr-2 text-gray-500" />
                        Amount (Birr)
                      </Label>
                      <Input
                        id="amount"
                        type="number"
                        placeholder="Enter amount"
                        value={amount}
                        onChange={(e) => {
                          setAmount(e.target.value)
                          // Only allow editing amount for non-tuition fee or when tuition fee amount isn't available
                          if (activeTab !== 'tuition-fee' || !tuitionFeeAmount) {
                            if (isMultiMonth) {
                              setBaseMonthlyAmount(e.target.value)
                              updateTotalAmount(selectedMonths)
                            }
                          }
                        }}
                        className="h-10"
                        required
                        readOnly={
                          // Make read-only for tuition fee tab when tuition fee amount is available
                          (activeTab === 'tuition-fee' && !!tuitionFeeAmount) ||
                          // Also read-only when in multi-month mode with selected months
                          (isMultiMonth && selectedMonths.length > 0)
                        }
                      />
                      {activeTab === 'tuition-fee' && tuitionFeeAmount && !isMultiMonth && (
                        <div className="mt-2 text-xs text-blue-600 dark:text-blue-400">
                          Using monthly fee amount from Fee Types
                        </div>
                      )}
                      {isMultiMonth && selectedMonths.length > 0 && (
                        <div className="mt-2 text-sm text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 p-2 rounded-md">
                          <div className="font-medium">Payment Breakdown:</div>
                          <div className="mt-1 space-y-1">
                            {(tuitionFeeAmount || baseMonthlyAmount) && (
                              <div>Monthly fee: {new Intl.NumberFormat('en-ET', {
                                style: 'currency',
                                currency: 'ETB'
                              }).format(parseFloat(tuitionFeeAmount || baseMonthlyAmount))}</div>
                            )}
                            <div>Months: {selectedMonths.length} ({selectedMonths.sort((a, b) => parseInt(a) - parseInt(b)).map(m => getMonthName(m)).join(', ')})</div>
                            <div className="font-medium">Total: {new Intl.NumberFormat('en-ET', {
                              style: 'currency',
                              currency: 'ETB'
                            }).format(parseFloat(amount))}</div>
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="payment-method" className="text-sm font-medium flex items-center">
                        <CreditCard className="h-4 w-4 mr-2 text-gray-500" />
                        Method of Payment
                      </Label>
                      <Select
                        value={paymentMethod}
                        onValueChange={setPaymentMethod}
                      >
                        <SelectTrigger id="payment-method">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="cash">Cash</SelectItem>
                          <SelectItem value="transfer">Transfer</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {paymentMethod === 'transfer' && (
                      <div className="space-y-2">
                        <Label htmlFor="transfer-id" className="text-sm font-medium flex items-center">
                          <FileText className="h-4 w-4 mr-2 text-gray-500" />
                          Transfer ID
                        </Label>
                        <Input
                          id="transfer-id"
                          placeholder="Enter transfer ID"
                          value={transferId}
                          onChange={(e) => setTransferId(e.target.value)}
                          className="h-10"
                          required
                        />
                      </div>
                    )}
                  </div>

                  <div className="flex justify-end space-x-4 pt-4 border-t">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => {
                        setSelectedStudent('')
                        setAmount('')
                        setTransferId('')
                      }}
                      disabled={isSubmitting}
                    >
                      <AlertCircle className="h-4 w-4 mr-2" />
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      className="bg-green-600 hover:bg-green-700"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Processing...
                        </>
                      ) : (
                        <>
                          <CheckCircle2 className="h-4 w-4 mr-2" />
                          Record Payment
                        </>
                      )}
                    </Button>
                  </div>
                </form>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </main>

      {/* Overlay for mobile menu */}
      {mobileSidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 md:hidden z-40"
          onClick={toggleMobileSidebar}
        ></div>
      )}

      {/* Payment History Dialog */}
      <PaymentHistoryDialog
        open={isPaymentHistoryOpen}
        onOpenChange={setIsPaymentHistoryOpen}
      />
    </div>
  )
}

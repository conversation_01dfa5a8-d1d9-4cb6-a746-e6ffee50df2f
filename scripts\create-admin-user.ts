import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function createAdminUser() {
  try {
    console.log('👤 Creating Admin user...')

    // Hash the password "password"
    const password = 'password'
    const hashedPassword = await bcrypt.hash(password, 10)
    
    console.log('✅ Password hashed successfully')

    // Create/update the admin user
    const adminUser = await prisma.user.upsert({
      where: {
        email: '<EMAIL>'
      },
      update: {
        password: hashedPassword,
        name: 'Admin User',
        role: 'ADMIN',
        status: 'active'
      },
      create: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Admin User',
        role: 'ADMIN',
        status: 'active'
      }
    })

    console.log('✅ Admin user created/updated:', {
      id: adminUser.id,
      email: adminUser.email,
      name: adminUser.name,
      role: adminUser.role
    })

    console.log('🎉 Admin user ready!')
    console.log('📧 Email: <EMAIL>')
    console.log('🔑 Password: password')

  } catch (error) {
    console.error('❌ Error creating Admin user:', error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

createAdminUser()

import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { verifyJWT } from '@/lib/jwt'
import { cookies } from 'next/headers'

// GET all teacher assignments
export async function GET(request: Request) {
  try {
    console.log('Fetching all teacher assignments')

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const teacherId = searchParams.get('teacherId')
    const type = searchParams.get('type')

    // Build the where clause
    let whereClause: any = {}

    if (teacherId) {
      whereClause.teacherId = teacherId
    }

    if (type === 'hr') {
      whereClause.isHRTeacher = true
    } else if (type === 'subject') {
      whereClause.isHRTeacher = false
    }

    // Skip authentication in development mode
    if (process.env.NODE_ENV === 'development') {
      console.log('Development mode - bypassing authentication')

      try {
        // Fetch teacher assignments with filters
        const assignments = await prisma.teacherAssignment.findMany({
          where: whereClause
        })

        // Fetch related data separately
        const teacherIds = assignments.map(a => a.teacherId);
        const teachers = await prisma.teacher.findMany({
          where: {
            id: { in: teacherIds }
          }
        });

        const classIds = assignments.filter(a => a.classId).map(a => a.classId as string);
        const classes = classIds.length > 0 ? await prisma.class.findMany({
          where: {
            id: { in: classIds }
          }
        }) : [];

        const subjectIds = assignments.filter(a => a.subjectId).map(a => a.subjectId as string);
        const subjects = subjectIds.length > 0 ? await prisma.subject.findMany({
          where: {
            id: { in: subjectIds }
          }
        }) : [];

        // Transform the data to match the expected format
        const formattedAssignments = assignments.map(assignment => {
          const teacher = teachers.find(t => t.id === assignment.teacherId);
          const classObj = assignment.classId ? classes.find(c => c.id === assignment.classId) : null;
          const subject = assignment.subjectId ? subjects.find(s => s.id === assignment.subjectId) : null;

          return {
            id: assignment.id,
            teacherId: assignment.teacherId,
            teacherName: teacher?.name || 'Unknown Teacher',
            classId: assignment.classId,
            className: classObj?.name || null,
            subjectId: assignment.subjectId,
            subjectName: subject?.name || null,
            isHRTeacher: assignment.isHRTeacher,
            createdAt: assignment.createdAt,
            updatedAt: assignment.updatedAt
          };
        })

        console.log(`Returning ${formattedAssignments.length} teacher assignments`)
        return NextResponse.json(formattedAssignments)
      } catch (dbError) {
        console.error('Database error:', dbError)
        return NextResponse.json(
          { error: 'Failed to fetch teacher assignments from database' },
          { status: 500 }
        )
      }
    } else {
      // Production mode - verify authentication
      const cookieStore = cookies()
      const token = cookieStore.get('token')?.value

      if (!token) {
        console.error('No authentication token found')
        return NextResponse.json(
          { error: 'Unauthorized - Authentication required' },
          { status: 401 }
        )
      }

      try {
        // Verify the token
        const decoded = await verifyJWT(token)

        // Fetch teacher assignments with filters
        const assignments = await prisma.teacherAssignment.findMany({
          where: whereClause
        })

        // Fetch related data separately
        const teacherIds = assignments.map(a => a.teacherId);
        const teachers = await prisma.teacher.findMany({
          where: {
            id: { in: teacherIds }
          }
        });

        const classIds = assignments.filter(a => a.classId).map(a => a.classId as string);
        const classes = classIds.length > 0 ? await prisma.class.findMany({
          where: {
            id: { in: classIds }
          }
        }) : [];

        const subjectIds = assignments.filter(a => a.subjectId).map(a => a.subjectId as string);
        const subjects = subjectIds.length > 0 ? await prisma.subject.findMany({
          where: {
            id: { in: subjectIds }
          }
        }) : [];

        // Transform the data to match the expected format
        const formattedAssignments = assignments.map(assignment => {
          const teacher = teachers.find(t => t.id === assignment.teacherId);
          const classObj = assignment.classId ? classes.find(c => c.id === assignment.classId) : null;
          const subject = assignment.subjectId ? subjects.find(s => s.id === assignment.subjectId) : null;

          return {
            id: assignment.id,
            teacherId: assignment.teacherId,
            teacherName: teacher?.name || 'Unknown Teacher',
            classId: assignment.classId,
            className: classObj?.name || null,
            subjectId: assignment.subjectId,
            subjectName: subject?.name || null,
            isHRTeacher: assignment.isHRTeacher,
            createdAt: assignment.createdAt,
            updatedAt: assignment.updatedAt
          };
        })

        console.log(`Returning ${formattedAssignments.length} teacher assignments`)
        return NextResponse.json(formattedAssignments)
      } catch (jwtError) {
        console.error('JWT verification error:', jwtError)
        return NextResponse.json(
          { error: 'Invalid or expired token' },
          { status: 401 }
        )
      }
    }
  } catch (error) {
    console.error('Error fetching teacher assignments:', error)
    return NextResponse.json(
      { error: 'Failed to fetch teacher assignments' },
      { status: 500 }
    )
  }
}

// POST to create a new teacher assignment
export async function POST(request: Request) {
  try {
    console.log('Creating new teacher assignment')

    // Get the request body
    const data = await request.json()

    // Validate the assignment data
    if (!data.teacherId) {
      console.error('Invalid assignment data: Missing teacherId')
      return NextResponse.json(
        { error: 'Teacher ID is required' },
        { status: 400 }
      )
    }

    // Skip authentication in development mode
    if (process.env.NODE_ENV === 'development') {
      console.log('Development mode - bypassing authentication');

      // Validate the assignment data
      if (!data.teacherId) {
        console.error('Invalid assignment data: Missing teacherId');
        return NextResponse.json(
          { error: 'Teacher ID is required' },
          { status: 400 }
        );
      }

      // Check if the teacher exists
      const teacher = await prisma.teacher.findUnique({
        where: { id: data.teacherId }
      });

      if (!teacher) {
        console.error(`Teacher with ID ${data.teacherId} not found`);
        return NextResponse.json(
          { error: 'Teacher not found' },
          { status: 404 }
        );
      }

      // If this is an HR teacher assignment, validate the class
      if (data.isHRTeacher) {
        if (!data.classId) {
          console.error('Invalid HR teacher assignment: Missing classId');
          return NextResponse.json(
            { error: 'Class ID is required for HR teacher assignments' },
            { status: 400 }
          );
        }

        // Check if the class exists
        const classObj = await prisma.class.findUnique({
          where: { id: data.classId }
        });

        if (!classObj) {
          console.error(`Class with ID ${data.classId} not found`);
          return NextResponse.json(
            { error: 'Class not found' },
            { status: 404 }
          );
        }

        // Check if the class already has an HR teacher that is different from the one being assigned
        if (classObj.hrTeacherId && classObj.hrTeacherId !== data.teacherId) {
          console.error(`Class ${classObj.name} already has an HR teacher`);

          // If this is an update operation (indicated by a query parameter), allow it
          const { searchParams } = new URL(request.url);
          const isUpdate = searchParams.get('update') === 'true';

          if (isUpdate) {
            console.log(`Updating HR teacher for class ${classObj.name} from ${classObj.hrTeacherId} to ${data.teacherId}`);
          } else {
            return NextResponse.json(
              { error: `Class ${classObj.name} already has an HR teacher` },
              { status: 400 }
            );
          }
        }

        // Update the class with the HR teacher
        await prisma.class.update({
          where: { id: data.classId },
          data: { hrTeacherId: data.teacherId }
        });

        console.log(`Updated class ${classObj.name} with HR teacher ${data.teacherId}`);
      }

      // If this is a subject assignment, validate the subject
      if (data.subjectId) {
        // Check if the subject exists
        const subject = await prisma.subject.findUnique({
          where: { id: data.subjectId }
        });

        if (!subject) {
          console.error(`Subject with ID ${data.subjectId} not found`);
          return NextResponse.json(
            { error: 'Subject not found' },
            { status: 404 }
          );
        }
      }

      // Create the teacher assignment
      const assignment = await prisma.teacherAssignment.create({
        data: {
          teacherId: data.teacherId,
          classId: data.classId || null,
          subjectId: data.subjectId || null,
          isHRTeacher: data.isHRTeacher || false
        }
      });

      // Fetch related data
      const teacherData = await prisma.teacher.findUnique({
        where: { id: data.teacherId }
      });

      let classObj = null;
      if (data.classId) {
        classObj = await prisma.class.findUnique({
          where: { id: data.classId }
        });
      }

      let subject = null;
      if (data.subjectId) {
        subject = await prisma.subject.findUnique({
          where: { id: data.subjectId }
        });
      }

      // Format the response
      const formattedAssignment = {
        id: assignment.id,
        teacherId: assignment.teacherId,
        teacherName: teacherData?.name || 'Unknown Teacher',
        classId: assignment.classId,
        className: classObj?.name || null,
        subjectId: assignment.subjectId,
        subjectName: subject?.name || null,
        isHRTeacher: assignment.isHRTeacher,
        createdAt: assignment.createdAt,
        updatedAt: assignment.updatedAt
      };

      console.log('New teacher assignment created:', formattedAssignment);
      return NextResponse.json({
        message: 'Teacher assignment created successfully',
        assignment: formattedAssignment
      });
    }

    // Production mode - verify authentication
    try {
      const cookieStore = cookies();
      const token = cookieStore.get('token')?.value;

      if (!token) {
        console.error('No authentication token found');
        return NextResponse.json(
          { error: 'Unauthorized - Authentication required' },
          { status: 401 }
        );
      }

      // Verify the token
      const decoded = await verifyJWT(token);

      // Implementation for production would go here
      // For now, just return an error
      throw new Error("Production implementation not completed");
    } catch (jwtError) {
      console.error('JWT verification error:', jwtError);
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      );
    }
  } catch (error) {
    console.error('Error creating teacher assignment:', error);
    return NextResponse.json(
      { error: 'Failed to create teacher assignment' },
      { status: 500 }
    );
  }
}

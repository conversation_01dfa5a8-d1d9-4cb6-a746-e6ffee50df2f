'use client'

import React, { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useSearchParams } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/app/components/ui/card'
import { Badge } from '@/app/components/ui/badge'
import { Button } from '@/app/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/ui/select'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/app/components/ui/dialog'
import { Alert, AlertDescription, AlertTitle } from '@/app/components/ui/alert'
import { Progress } from '@/app/components/ui/progress'
import { 
  FileText, 
  Calendar, 
  TrendingUp,
  Award,
  Clock,
  RefreshCw,
  User,
  AlertCircle,
  Lock,
  Info,
  Download,
  Eye
} from 'lucide-react'

interface Student {
  id: string
  sid: string
  name: string
  className: string
  academicYear: string
  photoUrl?: string
}

interface ReportData {
  student: Student
  marks: Record<string, Record<string, number>>
  attendance: {
    totalDays: number
    presentDays: number
    absentDays: number
    permissionDays: number
    attendancePercentage: number
  }
  academicPerformance: {
    averageMarks: number
    totalSubjects: number
    highestMark: number
    lowestMark: number
  }
}

interface ReportsData {
  students: Student[]
  reportData?: ReportData[]
  accessAllowed: boolean
  accessMessage: string
  semester: string
  academicYear: string
  message: string
}

export default function ParentReportsPage() {
  const searchParams = useSearchParams()
  const initialStudentId = searchParams.get('studentId')
  
  const [selectedStudent, setSelectedStudent] = useState<string>(initialStudentId || 'all')
  const [selectedSemester, setSelectedSemester] = useState<string>('First Semester')
  const [showAccessDialog, setShowAccessDialog] = useState(false)

  // Fetch reports data
  const { 
    data: reportsData, 
    isLoading, 
    error, 
    refetch 
  } = useQuery<ReportsData>({
    queryKey: ['parent-reports', selectedStudent, selectedSemester],
    queryFn: async () => {
      const params = new URLSearchParams({
        semester: selectedSemester,
        ...(selectedStudent !== 'all' && { studentId: selectedStudent })
      })
      
      const res = await fetch(`/api/parent/reports?${params}`)
      if (!res.ok) {
        const errorData = await res.json()
        throw new Error(errorData.error || 'Failed to fetch reports data')
      }
      return res.json()
    }
  })

  const handleReportCardRequest = () => {
    if (!reportsData?.accessAllowed) {
      setShowAccessDialog(true)
    }
  }

  const getGradeColor = (marks: number) => {
    if (marks >= 90) return 'text-green-600 bg-green-50 border-green-200'
    if (marks >= 80) return 'text-blue-600 bg-blue-50 border-blue-200'
    if (marks >= 70) return 'text-yellow-600 bg-yellow-50 border-yellow-200'
    if (marks >= 60) return 'text-orange-600 bg-orange-50 border-orange-200'
    return 'text-red-600 bg-red-50 border-red-200'
  }

  const getGrade = (marks: number) => {
    if (marks >= 90) return 'A+'
    if (marks >= 80) return 'A'
    if (marks >= 70) return 'B'
    if (marks >= 60) return 'C'
    return 'D'
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-gray-400" />
          <p className="text-gray-500">Loading report cards...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 mx-auto mb-4 text-red-500" />
          <h2 className="text-xl font-semibold text-gray-700 mb-2">Error Loading Reports</h2>
          <p className="text-gray-500 mb-4">
            {error instanceof Error ? error.message : 'Something went wrong'}
          </p>
          <Button onClick={() => refetch()} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  const students = reportsData?.students || []
  const reportData = reportsData?.reportData || []
  const accessAllowed = reportsData?.accessAllowed || false

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-800">Report Cards</h1>
          <p className="text-gray-500">Access your children's academic report cards</p>
        </div>
        <Button onClick={() => refetch()} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Access Status Alert */}
      {!accessAllowed && (
        <Alert className="border-amber-200 bg-amber-50">
          <Lock className="h-4 w-4 text-amber-600" />
          <AlertTitle className="text-amber-800">Report Card Access Restricted</AlertTitle>
          <AlertDescription className="text-amber-700">
            {reportsData?.accessMessage}
          </AlertDescription>
        </Alert>
      )}

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Report Card Options</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Student</label>
              <Select value={selectedStudent} onValueChange={setSelectedStudent}>
                <SelectTrigger>
                  <SelectValue placeholder="Select student" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Children</SelectItem>
                  {students.map((student) => (
                    <SelectItem key={student.id} value={student.id}>
                      {student.name} ({student.sid})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Semester</label>
              <Select value={selectedSemester} onValueChange={setSelectedSemester}>
                <SelectTrigger>
                  <SelectValue placeholder="Select semester" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="First Semester">First Semester</SelectItem>
                  <SelectItem value="Second Semester">Second Semester</SelectItem>
                  <SelectItem value="Annual">Annual Report</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-end">
              <Button 
                onClick={handleReportCardRequest}
                className="w-full"
                disabled={!accessAllowed}
              >
                {accessAllowed ? (
                  <>
                    <Eye className="h-4 w-4 mr-2" />
                    View Report Card
                  </>
                ) : (
                  <>
                    <Lock className="h-4 w-4 mr-2" />
                    Access Restricted
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Report Cards Display */}
      {accessAllowed && reportData.length > 0 && (
        <div className="space-y-6">
          {reportData.map((report) => (
            <Card key={report.student.id} className="overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-xl flex items-center">
                      <User className="h-5 w-5 mr-2" />
                      {report.student.name}
                    </CardTitle>
                    <CardDescription className="text-base">
                      SID: {report.student.sid} • Class {report.student.className} • {report.student.academicYear}
                    </CardDescription>
                  </div>
                  <Badge variant="outline" className="bg-white">
                    {selectedSemester}
                  </Badge>
                </div>
              </CardHeader>
              
              <CardContent className="p-6 space-y-6">
                {/* Academic Performance Summary */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">
                      {report.academicPerformance.averageMarks}
                    </div>
                    <div className="text-sm text-blue-600">Average Score</div>
                    <Badge variant="outline" className={`mt-2 ${getGradeColor(report.academicPerformance.averageMarks)}`}>
                      {getGrade(report.academicPerformance.averageMarks)}
                    </Badge>
                  </div>
                  
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">
                      {report.academicPerformance.highestMark}
                    </div>
                    <div className="text-sm text-green-600">Highest Score</div>
                  </div>
                  
                  <div className="text-center p-4 bg-purple-50 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">
                      {report.academicPerformance.totalSubjects}
                    </div>
                    <div className="text-sm text-purple-600">Total Subjects</div>
                  </div>
                  
                  <div className="text-center p-4 bg-orange-50 rounded-lg">
                    <div className="text-2xl font-bold text-orange-600">
                      {report.attendance.attendancePercentage}%
                    </div>
                    <div className="text-sm text-orange-600">Attendance</div>
                  </div>
                </div>

                {/* Subject-wise Performance */}
                <div>
                  <h3 className="text-lg font-semibold mb-4 flex items-center">
                    <Award className="h-5 w-5 mr-2" />
                    Subject Performance
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {Object.entries(report.marks).map(([subject, termMarks]) => {
                      const marks = Object.values(termMarks)
                      const average = marks.length > 0 ? Math.round(marks.reduce((a, b) => a + b, 0) / marks.length) : 0
                      
                      return (
                        <div key={subject} className="p-4 border rounded-lg">
                          <div className="flex justify-between items-center mb-2">
                            <h4 className="font-medium">{subject}</h4>
                            <Badge variant="outline" className={getGradeColor(average)}>
                              {getGrade(average)}
                            </Badge>
                          </div>
                          <div className="space-y-2">
                            <Progress value={average} className="h-2" />
                            <div className="text-sm text-gray-600">
                              Average: {average}/100
                            </div>
                            <div className="text-xs text-gray-500">
                              {Object.entries(termMarks).map(([term, mark]) => (
                                <span key={term} className="mr-2">
                                  {term}: {mark}
                                </span>
                              ))}
                            </div>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </div>

                {/* Attendance Summary */}
                <div>
                  <h3 className="text-lg font-semibold mb-4 flex items-center">
                    <Calendar className="h-5 w-5 mr-2" />
                    Attendance Summary
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="text-center p-3 bg-green-50 rounded">
                      <div className="font-semibold text-green-600">{report.attendance.presentDays}</div>
                      <div className="text-sm text-green-600">Present Days</div>
                    </div>
                    <div className="text-center p-3 bg-red-50 rounded">
                      <div className="font-semibold text-red-600">{report.attendance.absentDays}</div>
                      <div className="text-sm text-red-600">Absent Days</div>
                    </div>
                    <div className="text-center p-3 bg-yellow-50 rounded">
                      <div className="font-semibold text-yellow-600">{report.attendance.permissionDays}</div>
                      <div className="text-sm text-yellow-600">Permission Days</div>
                    </div>
                    <div className="text-center p-3 bg-blue-50 rounded">
                      <div className="font-semibold text-blue-600">{report.attendance.totalDays}</div>
                      <div className="text-sm text-blue-600">Total Days</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Access Restriction Dialog */}
      <Dialog open={showAccessDialog} onOpenChange={setShowAccessDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <Lock className="h-5 w-5 mr-2 text-amber-600" />
              Report Card Access Restricted
            </DialogTitle>
            <DialogDescription className="space-y-3">
              <div className="flex items-start space-x-2">
                <Info className="h-4 w-4 mt-0.5 text-blue-500" />
                <div>
                  <p className="font-medium text-gray-700">
                    {reportsData?.accessMessage}
                  </p>
                </div>
              </div>
              
              <div className="bg-blue-50 p-3 rounded-lg">
                <p className="text-sm text-blue-700">
                  Report cards are made available at the end of each semester to ensure 
                  all assessments and evaluations are complete.
                </p>
              </div>
              
              <div className="text-xs text-gray-500">
                <p>• First Semester reports: Available from June</p>
                <p>• Second Semester reports: Available from December</p>
                <p>• Annual reports: Available from December</p>
              </div>
            </DialogDescription>
          </DialogHeader>
          
          <div className="flex justify-end">
            <Button onClick={() => setShowAccessDialog(false)}>
              Understood
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}

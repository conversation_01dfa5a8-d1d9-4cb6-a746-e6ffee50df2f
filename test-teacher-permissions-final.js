const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function main() {
  try {
    console.log('=== Final Test: Teacher Permissions Display ===\n')

    // 1. Test the exact same API calls as the UI
    console.log('1. Testing Teachers API (/api/users/teachers)...')
    const teachers = await prisma.teacher.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        subject: true,
        fatherName: true,
        gender: true,
        mobile: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: {
        name: 'asc',
      },
    })
    console.log(`✅ Found ${teachers.length} teachers`)

    // 2. Test the teacher permissions API
    console.log('\n2. Testing Teacher Permissions API (/api/teacher-permissions)...')
    const permissions = await prisma.teacherPermission.findMany({
      select: {
        id: true,
        teacherId: true,
        classId: true,
        canViewAttendance: true,
        canTakeAttendance: true,
        canAddMarks: true,
        canEditMarks: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: [
        { teacherId: 'asc' },
        { classId: 'asc' }
      ],
    })

    // Get teacher and class details separately
    const teacherIds = [...new Set(permissions.map(p => p.teacherId))]
    const classIds = [...new Set(permissions.map(p => p.classId))]

    const [permissionTeachers, classes] = await Promise.all([
      prisma.teacher.findMany({
        where: { id: { in: teacherIds } },
        select: { id: true, name: true, email: true, subject: true }
      }),
      prisma.class.findMany({
        where: { id: { in: classIds } },
        select: { id: true, name: true }
      })
    ])

    // Create lookup maps
    const teacherMap = new Map(permissionTeachers.map(t => [t.id, t]))
    const classMap = new Map(classes.map(c => [c.id, c]))

    // Combine permissions with details (same as API)
    const permissionsWithDetails = permissions.map(permission => ({
      id: permission.id,
      teacherId: permission.teacherId,
      classId: permission.classId,
      teacher: teacherMap.get(permission.teacherId) || null,
      class: classMap.get(permission.classId) || null,
      canViewAttendance: permission.canViewAttendance,
      canTakeAttendance: permission.canTakeAttendance,
      canAddMarks: permission.canAddMarks,
      canEditMarks: permission.canEditMarks,
      createdAt: permission.createdAt,
      updatedAt: permission.updatedAt,
    }))

    console.log(`✅ Found ${permissionsWithDetails.length} teacher permissions`)

    // 3. Test the exact matching logic used in the UI
    console.log('\n3. Testing UI Matching Logic...')
    console.log('=' .repeat(80))

    for (const teacher of teachers) {
      // This is the exact filter used in the UI
      const teacherPerms = permissionsWithDetails.filter((perm) => perm.teacherId === teacher.id)
      
      console.log(`\n👨‍🏫 ${teacher.name} (${teacher.subject})`)
      console.log(`   ID: ${teacher.id}`)
      console.log(`   Email: ${teacher.email}`)
      
      if (teacherPerms.length > 0) {
        console.log(`   ✅ Classes: ${teacherPerms.length}`)
        teacherPerms.forEach((perm, index) => {
          console.log(`   ${index + 1}. ${perm.class ? perm.class.name : perm.classId}`)
          console.log(`      - View Attendance: ${perm.canViewAttendance ? 'Yes' : 'No'}`)
          console.log(`      - Take Attendance: ${perm.canTakeAttendance ? 'Yes' : 'No'}`)
          console.log(`      - Add Marks: ${perm.canAddMarks ? 'Yes' : 'No'}`)
          console.log(`      - Edit Marks: ${perm.canEditMarks ? 'Yes' : 'No'}`)
        })
      } else {
        console.log(`   ❌ No classes assigned`)
      }
    }

    // 4. Test Emily Davis specifically
    console.log('\n4. Emily Davis Specific Test...')
    const emily = teachers.find(t => t.name.includes('Emily Davis'))
    if (emily) {
      const emilyPerms = permissionsWithDetails.filter(p => p.teacherId === emily.id)
      console.log(`✅ Emily Davis found: ${emily.id}`)
      console.log(`✅ Emily's permissions: ${emilyPerms.length}`)
      
      if (emilyPerms.length > 0) {
        console.log('   Classes in UI should show:')
        emilyPerms.forEach(perm => {
          console.log(`   - Badge: "${perm.class ? perm.class.name : perm.classId}"`)
        })
      } else {
        console.log('   ❌ Emily should show "No classes assigned"')
      }
    } else {
      console.log('❌ Emily Davis not found in teachers')
    }

    // 5. Test the multiple class assignment functionality
    console.log('\n5. Testing Multiple Class Assignment...')
    
    // Get all classes for assignment testing
    const allClasses = await prisma.class.findMany({
      select: { id: true, name: true },
      orderBy: { name: 'asc' }
    })

    console.log(`Available classes for assignment: ${allClasses.length}`)
    allClasses.forEach(cls => {
      console.log(`  - ${cls.name} (${cls.id})`)
    })

    // Test batch assignment data structure
    if (emily && allClasses.length > 0) {
      const testBatchAssignment = {
        teacherId: emily.id,
        classIds: allClasses.slice(0, 3).map(c => c.id), // First 3 classes
        permissions: {
          canViewAttendance: true,
          canTakeAttendance: true,
          canAddMarks: true,
          canEditMarks: true
        }
      }

      console.log('\nTest batch assignment data:')
      console.log(JSON.stringify(testBatchAssignment, null, 2))
    }

    // 6. Summary for troubleshooting
    console.log('\n6. Troubleshooting Summary:')
    console.log('=' .repeat(50))
    
    console.log(`\n📊 Data Summary:`)
    console.log(`   - Total Teachers: ${teachers.length}`)
    console.log(`   - Total Permissions: ${permissionsWithDetails.length}`)
    console.log(`   - Teachers with Permissions: ${new Set(permissionsWithDetails.map(p => p.teacherId)).size}`)
    console.log(`   - Classes with Permissions: ${new Set(permissionsWithDetails.map(p => p.classId)).size}`)

    console.log(`\n🔍 Expected UI Behavior:`)
    console.log(`   - Teacher Permissions tab should show ${teachers.length} teachers`)
    console.log(`   - Each teacher row should show their assigned classes as badges`)
    console.log(`   - Emily Davis should show her assigned classes`)
    console.log(`   - Teachers without permissions should show "No classes assigned"`)

    console.log(`\n✅ If classes are still not showing in the UI:`)
    console.log(`   1. Check browser console for JavaScript errors`)
    console.log(`   2. Verify the API endpoints are returning correct data`)
    console.log(`   3. Check if React Query is properly caching the data`)
    console.log(`   4. Ensure the component is re-rendering after data loads`)

  } catch (error) {
    console.error('Error:', error)
  } finally {
    await prisma.$disconnect()
  }
}

main()

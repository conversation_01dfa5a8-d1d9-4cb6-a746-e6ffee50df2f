'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from './contexts/auth-context';

export default function Home() {
  const router = useRouter();
  const { isAuthenticated, isLoading } = useAuth();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!mounted) return;

    if (isLoading) {
      // Wait for authentication check to complete
      return;
    }

    if (isAuthenticated) {
      // If user is already authenticated, redirect to dashboard
      console.log('Root page: User is authenticated, redirecting to dashboard...');
      window.location.href = '/dashboard';
    } else {
      // If user is not authenticated, redirect to website homepage
      console.log('Root page: User is not authenticated, redirecting to website homepage...');
      window.location.href = '/website';
    }
  }, [mounted, isAuthenticated, isLoading]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-gray-900">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600 mx-auto"></div>
        <p className="mt-4 text-gray-600 dark:text-gray-400">Redirecting...</p>
      </div>
    </div>
  );
}
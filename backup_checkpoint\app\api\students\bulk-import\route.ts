import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { parse } from 'papaparse'
import { formatStudentName } from '@/app/utils/formatters'
import { generateStudentSID, bulkGenerateSIDs } from '@/app/lib/student-utils'

// Define the expected CSV structure
interface StudentCSV {
  name: string
  fatherName: string
  gfName: string
  className: string
  age: string
  gender: string
}

// Validate a single student record
function validateStudent(student: any, rowIndex: number): { valid: boolean; errors: string[] } {
  const errors: string[] = []

  // Check required fields
  if (!student.name) errors.push(`Row ${rowIndex}: Name is required`)
  if (!student.className) errors.push(`Row ${rowIndex}: Class is required`)
  if (!student.age) errors.push(`Row ${rowIndex}: Age is required`)
  if (!student.gender) errors.push(`Row ${rowIndex}: Gender is required`)

  // Validate age
  const age = parseInt(student.age)
  if (isNaN(age) || age < 5 || age > 20) {
    errors.push(`Row ${rowIndex}: Age must be a number between 5 and 20`)
  }

  // Validate gender
  if (student.gender && !['Male', 'Female', 'Other'].includes(student.gender)) {
    errors.push(`Row ${rowIndex}: Gender must be 'Male', 'Female', or 'Other'`)
  }

  return { valid: errors.length === 0, errors }
}

export async function POST(request: Request) {
  try {
    const formData = await request.formData()
    const file = formData.get('file') as File

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      )
    }

    // Read and parse the CSV file
    const csvText = await file.text()
    const { data, errors } = parse(csvText, {
      header: true,
      skipEmptyLines: true,
    })

    if (errors.length > 0) {
      return NextResponse.json(
        {
          error: 'Failed to parse CSV file',
          details: errors.map(e => e.message).join(', ')
        },
        { status: 400 }
      )
    }

    // Limit the number of records to prevent abuse
    const MAX_RECORDS = 500
    if (data.length > MAX_RECORDS) {
      return NextResponse.json(
        { error: `Too many records. Maximum allowed is ${MAX_RECORDS}` },
        { status: 400 }
      )
    }

    // Validate all records first
    const validationResults = data.map((row: any, index: number) =>
      validateStudent(row, index + 1)
    )

    const allErrors = validationResults.flatMap(result => result.errors)

    // If there are validation errors, return them
    if (allErrors.length > 0) {
      return NextResponse.json(
        {
          error: 'Validation errors in CSV file',
          errors: allErrors,
          total: data.length,
          failed: data.length
        },
        { status: 400 }
      )
    }

    // Process the data in a transaction
    const results = await prisma.$transaction(async (tx) => {
      const successfulImports: any[] = []
      const failedImports: { row: number; error: string }[] = []

      // Group students by class for SID generation
      const studentsByClass: Record<string, StudentCSV[]> = {}
      for (const student of data as StudentCSV[]) {
        if (!studentsByClass[student.className]) {
          studentsByClass[student.className] = []
        }
        studentsByClass[student.className].push(student)
      }

      // Generate SIDs for each class
      const sidMap = new Map<string, string>()
      for (const className in studentsByClass) {
        // Check if the class exists
        const classExists = await tx.class.findFirst({
          where: { name: className },
        })

        if (!classExists) {
          // Create the class if it doesn't exist
          await tx.class.create({
            data: {
              name: className,
              totalStudents: 0,
              totalSubjects: 0,
            },
          })
        }

        // Get the highest SID for this class
        const students = studentsByClass[className]
        let nextSidNumber = 1

        // Find the highest existing SID for this class
        const highestSidStudent = await tx.student.findFirst({
          where: { className },
          orderBy: { sid: 'desc' },
        })

        if (highestSidStudent) {
          const match = highestSidStudent.sid.match(/^(\w+)(\d+)$/)
          if (match && match[2]) {
            nextSidNumber = parseInt(match[2]) + 1
          }
        }

        // Generate SIDs for each student in this class
        for (let i = 0; i < students.length; i++) {
          const sid = `${className}${nextSidNumber + i}`
          sidMap.set(JSON.stringify({ className, index: i }), sid)
        }
      }

      // Process each student
      for (let i = 0; i < data.length; i++) {
        const studentData = data[i] as StudentCSV
        const rowIndex = i + 1

        try {
          // Get the generated SID for this student
          const sid = sidMap.get(JSON.stringify({ className: studentData.className, index: studentsByClass[studentData.className].indexOf(studentData) }))

          if (!sid) {
            throw new Error('Failed to generate SID for student')
          }

          // Create the student with formatted names and SID
          const student = await tx.student.create({
            data: {
              sid,
              name: formatStudentName(studentData.name),
              fatherName: formatStudentName(studentData.fatherName || ''),
              gfName: formatStudentName(studentData.gfName || ''),
              className: studentData.className,
              age: parseInt(studentData.age),
              gender: studentData.gender,
            },
          })

          successfulImports.push(student)

          // Update the class student count
          await tx.class.updateMany({
            where: { name: studentData.className },
            data: {
              totalStudents: {
                increment: 1,
              },
            },
          })
        } catch (error) {
          console.error(`Error importing student at row ${rowIndex}:`, error)
          failedImports.push({
            row: rowIndex,
            error: error instanceof Error ? error.message : 'Unknown error',
          })
        }
      }

      return {
        total: data.length,
        success: successfulImports.length,
        failed: failedImports.length,
        errors: failedImports.map(f => `Row ${f.row}: ${f.error}`),
      }
    })

    return NextResponse.json(results)
  } catch (error) {
    console.error('Error in bulk import:', error)
    return NextResponse.json(
      { error: 'Failed to process import', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

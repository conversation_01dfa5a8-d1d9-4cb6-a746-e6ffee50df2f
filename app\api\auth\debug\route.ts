import { NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { verifyJWT } from '@/lib/jwt'

export async function GET() {
  try {
    console.log('Debug API called to check authentication')

    const cookieStore = cookies()
    // Return all cookies for debugging
    const allCookies = cookieStore.getAll()
    console.log('All cookies found:', allCookies.length)

    // Check for NextAuth session token
    const nextAuthToken = cookieStore.get('next-auth.session-token')
    console.log('NextAuth token found:', !!nextAuthToken)

    if (nextAuthToken) {
      return NextResponse.json({
        authenticated: true,
        message: 'NextAuth session token found',
        authType: 'next-auth',
        cookies: allCookies.map(c => ({ name: c.name, value: c.value ? c.value.substring(0, 10) + '...' : null }))
      })
    }

    // Check for our custom token
    const token = cookieStore.get('token')
    console.log('Custom token found:', !!token)

    if (!token) {
      return NextResponse.json({
        authenticated: false,
        message: 'No authentication token found',
        error: 'No token found',
        cookies: allCookies.map(c => ({ name: c.name, value: c.value ? c.value.substring(0, 10) + '...' : null }))
      })
    }

    try {
      // Verify the token
      console.log('Verifying token...')
      const decoded = await verifyJWT(token.value)
      console.log('Token verified for user:', decoded.email)
      
      // Add debugging information for token verification
      console.log('Token payload:', {
        id: decoded.id,
        email: decoded.email,
        role: decoded.role,
        iat: decoded.iat,
        exp: decoded.exp
      })

      // Get user from database if needed
      // For now, just return the decoded token data with all required fields
      return NextResponse.json({
        authenticated: true,
        message: 'Valid custom token',
        authType: 'custom-jwt',
        user: {
          id: decoded.id,
          email: decoded.email,
          role: decoded.role,
          name: decoded.name || 'User' // Provide a default name if not in token
        },
        tokenInfo: {
          issued: decoded.iat ? new Date(decoded.iat * 1000).toISOString() : null,
          expires: decoded.exp ? new Date(decoded.exp * 1000).toISOString() : null,
          remainingTime: decoded.exp ? Math.floor((decoded.exp * 1000 - Date.now()) / 1000 / 60) + ' minutes' : 'unknown'
        },
        cookies: allCookies.map(c => ({ name: c.name, value: c.value ? c.value.substring(0, 10) + '...' : null }))
      })
    } catch (error) {
      console.error('Token verification error:', error)

      // Check if token is expired
      const isExpired = error instanceof Error && error.message === 'Token expired'

      return NextResponse.json({
        authenticated: false,
        message: isExpired ? 'Token expired' : 'Invalid token',
        error: error instanceof Error ? error.message : 'Unknown error',
        cookies: allCookies.map(c => ({ name: c.name, value: c.value ? c.value.substring(0, 10) + '...' : null }))
      }, { status: 401 })
    }
  } catch (error) {
    return NextResponse.json({
      authenticated: false,
      message: 'Error checking authentication',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}

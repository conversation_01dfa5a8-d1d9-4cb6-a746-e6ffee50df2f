"use client"

import React, { useState } from 'react'
import { <PERSON><PERSON> } from '@/app/components/ui/button'
import { Card, CardContent } from '@/app/components/ui/card'
import { Input } from '@/app/components/ui/input'
import { Label } from '@/app/components/ui/label'
import { useToast } from '@/app/components/ui/use-toast'
import { Plus, Edit, Trash, Save, Info } from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/app/components/ui/dialog'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/app/components/ui/table'

// Define the tuition rate type
interface TuitionRate {
  id: string
  level: string
  annualTuition: string
  registrationFee: string
  booksFee: string
  activityFee: string
  technologyFee: string
}

// Mock data for initial tuition rates
const initialTuitionRates: TuitionRate[] = [
  {
    id: '1',
    level: "Kindergarten",
    annualTuition: "$8,500",
    registrationFee: "$300",
    booksFee: "$250",
    activityFee: "$200",
    technologyFee: "$150",
  },
  {
    id: '2',
    level: "Elementary School (Grades 1-5)",
    annualTuition: "$9,200",
    registrationFee: "$300",
    booksFee: "$350",
    activityFee: "$250",
    technologyFee: "$200",
  },
  {
    id: '3',
    level: "Middle School (Grades 6-8)",
    annualTuition: "$9,800",
    registrationFee: "$300",
    booksFee: "$450",
    activityFee: "$300",
    technologyFee: "$250",
  },
  {
    id: '4',
    level: "High School (Grades 9-12)",
    annualTuition: "$10,500",
    registrationFee: "$300",
    booksFee: "$550",
    activityFee: "$350",
    technologyFee: "$300",
  },
]

export default function TuitionRatesTab() {
  const { toast } = useToast()
  const [tuitionRates, setTuitionRates] = useState<TuitionRate[]>(initialTuitionRates)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [currentRate, setCurrentRate] = useState<TuitionRate | null>(null)
  const [formData, setFormData] = useState<Omit<TuitionRate, 'id'>>({
    level: '',
    annualTuition: '',
    registrationFee: '',
    booksFee: '',
    activityFee: '',
    technologyFee: '',
  })
  const [isLoading, setIsLoading] = useState(false)

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const resetForm = () => {
    setFormData({
      level: '',
      annualTuition: '',
      registrationFee: '',
      booksFee: '',
      activityFee: '',
      technologyFee: '',
    })
  }

  const openAddDialog = () => {
    resetForm()
    setIsAddDialogOpen(true)
  }

  const openEditDialog = (rate: TuitionRate) => {
    setCurrentRate(rate)
    setFormData({
      level: rate.level,
      annualTuition: rate.annualTuition,
      registrationFee: rate.registrationFee,
      booksFee: rate.booksFee,
      activityFee: rate.activityFee,
      technologyFee: rate.technologyFee,
    })
    setIsEditDialogOpen(true)
  }

  const openDeleteDialog = (rate: TuitionRate) => {
    setCurrentRate(rate)
    setIsDeleteDialogOpen(true)
  }

  const handleAddRate = async () => {
    setIsLoading(true)
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // In a real implementation, you would save to the database here
      const newRate: TuitionRate = {
        id: Date.now().toString(),
        ...formData
      }
      
      setTuitionRates(prev => [...prev, newRate])
      setIsAddDialogOpen(false)
      resetForm()
      
      toast({
        title: "Rate Added",
        description: `${formData.level} tuition rate has been added successfully.`,
        variant: "default",
      })
    } catch (error) {
      console.error('Error adding rate:', error)
      toast({
        title: "Error",
        description: "Failed to add tuition rate. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleEditRate = async () => {
    if (!currentRate) return
    
    setIsLoading(true)
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // In a real implementation, you would update the database here
      setTuitionRates(prev => 
        prev.map(rate => 
          rate.id === currentRate.id 
            ? { ...rate, ...formData } 
            : rate
        )
      )
      
      setIsEditDialogOpen(false)
      
      toast({
        title: "Rate Updated",
        description: `${formData.level} tuition rate has been updated successfully.`,
        variant: "default",
      })
    } catch (error) {
      console.error('Error updating rate:', error)
      toast({
        title: "Error",
        description: "Failed to update tuition rate. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleDeleteRate = async () => {
    if (!currentRate) return
    
    setIsLoading(true)
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // In a real implementation, you would delete from the database here
      setTuitionRates(prev => 
        prev.filter(rate => rate.id !== currentRate.id)
      )
      
      setIsDeleteDialogOpen(false)
      
      toast({
        title: "Rate Deleted",
        description: `${currentRate.level} tuition rate has been deleted successfully.`,
        variant: "default",
      })
    } catch (error) {
      console.error('Error deleting rate:', error)
      toast({
        title: "Error",
        description: "Failed to delete tuition rate. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-start space-x-2 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <Info className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5" />
        <div className="text-sm text-gray-600 dark:text-gray-300">
          <p>Manage the tuition rates and fees displayed on the website. Add, edit, or remove grade level tuition information.</p>
        </div>
      </div>

      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Tuition Rates by Grade Level</h3>
        <Button onClick={openAddDialog} className="flex items-center">
          <Plus className="mr-2 h-4 w-4" />
          Add New Rate
        </Button>
      </div>

      <Card>
        <CardContent className="p-0 overflow-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Grade Level</TableHead>
                <TableHead>Annual Tuition</TableHead>
                <TableHead>Registration Fee</TableHead>
                <TableHead>Books Fee</TableHead>
                <TableHead>Activity Fee</TableHead>
                <TableHead>Technology Fee</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {tuitionRates.map((rate) => (
                <TableRow key={rate.id}>
                  <TableCell className="font-medium">{rate.level}</TableCell>
                  <TableCell>{rate.annualTuition}</TableCell>
                  <TableCell>{rate.registrationFee}</TableCell>
                  <TableCell>{rate.booksFee}</TableCell>
                  <TableCell>{rate.activityFee}</TableCell>
                  <TableCell>{rate.technologyFee}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openEditDialog(rate)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openDeleteDialog(rate)}
                      >
                        <Trash className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Add Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Add New Tuition Rate</DialogTitle>
            <DialogDescription>
              Add a new grade level tuition rate and associated fees.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-1 gap-4">
              <div className="space-y-2">
                <Label htmlFor="level">Grade Level</Label>
                <Input
                  id="level"
                  name="level"
                  value={formData.level}
                  onChange={handleInputChange}
                  placeholder="e.g., Middle School (Grades 6-8)"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="annualTuition">Annual Tuition</Label>
                <Input
                  id="annualTuition"
                  name="annualTuition"
                  value={formData.annualTuition}
                  onChange={handleInputChange}
                  placeholder="e.g., $9,800"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="registrationFee">Registration Fee</Label>
                <Input
                  id="registrationFee"
                  name="registrationFee"
                  value={formData.registrationFee}
                  onChange={handleInputChange}
                  placeholder="e.g., $300"
                />
              </div>
            </div>
            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="booksFee">Books Fee</Label>
                <Input
                  id="booksFee"
                  name="booksFee"
                  value={formData.booksFee}
                  onChange={handleInputChange}
                  placeholder="e.g., $450"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="activityFee">Activity Fee</Label>
                <Input
                  id="activityFee"
                  name="activityFee"
                  value={formData.activityFee}
                  onChange={handleInputChange}
                  placeholder="e.g., $300"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="technologyFee">Technology Fee</Label>
                <Input
                  id="technologyFee"
                  name="technologyFee"
                  value={formData.technologyFee}
                  onChange={handleInputChange}
                  placeholder="e.g., $250"
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddRate} disabled={isLoading}>
              {isLoading ? (
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
              ) : (
                <Save className="mr-2 h-4 w-4" />
              )}
              Add Rate
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Edit Tuition Rate</DialogTitle>
            <DialogDescription>
              Update the tuition rate and associated fees.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-1 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-level">Grade Level</Label>
                <Input
                  id="edit-level"
                  name="level"
                  value={formData.level}
                  onChange={handleInputChange}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-annualTuition">Annual Tuition</Label>
                <Input
                  id="edit-annualTuition"
                  name="annualTuition"
                  value={formData.annualTuition}
                  onChange={handleInputChange}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-registrationFee">Registration Fee</Label>
                <Input
                  id="edit-registrationFee"
                  name="registrationFee"
                  value={formData.registrationFee}
                  onChange={handleInputChange}
                />
              </div>
            </div>
            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-booksFee">Books Fee</Label>
                <Input
                  id="edit-booksFee"
                  name="booksFee"
                  value={formData.booksFee}
                  onChange={handleInputChange}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-activityFee">Activity Fee</Label>
                <Input
                  id="edit-activityFee"
                  name="activityFee"
                  value={formData.activityFee}
                  onChange={handleInputChange}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-technologyFee">Technology Fee</Label>
                <Input
                  id="edit-technologyFee"
                  name="technologyFee"
                  value={formData.technologyFee}
                  onChange={handleInputChange}
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleEditRate} disabled={isLoading}>
              {isLoading ? (
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
              ) : (
                <Save className="mr-2 h-4 w-4" />
              )}
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the tuition rate for {currentRate?.level}? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleDeleteRate}
              disabled={isLoading}
            >
              {isLoading ? (
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
              ) : (
                <Trash className="mr-2 h-4 w-4" />
              )}
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

"use client";

import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { FaCalendarAlt, FaClock, FaMapMarkerAlt, FaSearch, FaFilter, FaTimes } from 'react-icons/fa';
import { Button } from '@/app/components/ui/button';
import { Input } from '@/app/components/ui/input';
import {
  <PERSON><PERSON>,
  <PERSON>bs<PERSON>ontent,
  TabsList,
  TabsTrigger,
} from '@/app/components/ui/tabs';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.5 },
  },
};

// News data
const newsItems = [
  {
    id: 1,
    title: "Students Win Regional Science Competition",
    excerpt: "Our students took first place in the regional science competition with their innovative project on renewable energy.",
    content: "We are proud to announce that our science team has won first place in the Regional Science Competition. The team of five students from grades 9-12 presented their innovative project on renewable energy, demonstrating both scientific excellence and practical application. Their project, titled 'Harnessing Solar Energy in Urban Settings,' impressed the judges with its thorough research, creative design, and potential real-world impact. The students will now advance to the state competition next month. Congratulations to our brilliant young scientists and their dedicated teacher mentors!",
    date: "May 10, 2023",
    image: "/images/portrait.jpg", // Replace with actual news image
    category: "Achievement",
    author: "Admin Staff",
  },
  {
    id: 2,
    title: "New Library Resources Available",
    excerpt: "We have added over 500 new books and digital resources to our library collection to support student learning.",
    content: "The Alfalah Islamic School library is excited to announce the addition of over 500 new resources to our collection. These new materials include fiction and non-fiction books, reference materials, educational DVDs, and digital resources. The expansion focuses on Islamic literature, STEM subjects, and multicultural perspectives. Students now have access to an expanded digital library that can be accessed from home. We invite all students and parents to visit the library to explore these new resources. Special thanks to our Parent Association for their generous donation that made this expansion possible.",
    date: "May 5, 2023",
    image: "/images/portrait.jpg", // Replace with actual news image
    category: "Announcement",
    author: "Library Staff",
  },
  {
    id: 3,
    title: "Annual Charity Drive Exceeds Goal",
    excerpt: "Thanks to the generosity of our school community, we exceeded our charity drive goal by 150%.",
    content: "Our annual charity drive has concluded with outstanding results! Thanks to the incredible generosity of our school community, we exceeded our initial goal by 150%. The funds raised will support local food banks, refugee assistance programs, and international humanitarian aid. Students from all grade levels participated enthusiastically, organizing bake sales, car washes, and donation campaigns. This initiative not only raised funds for important causes but also taught our students valuable lessons about compassion, community service, and social responsibility. We extend our heartfelt thanks to everyone who contributed to this successful effort.",
    date: "April 28, 2023",
    image: "/images/portrait.jpg", // Replace with actual news image
    category: "Community",
    author: "Community Outreach Committee",
  },
  {
    id: 4,
    title: "School Receives Educational Excellence Award",
    excerpt: "Alfalah Islamic School has been recognized for its outstanding academic programs and student achievements.",
    content: "We are honored to announce that Alfalah Islamic School has received the prestigious Educational Excellence Award from the National Association of Independent Schools. This recognition celebrates our commitment to academic rigor, innovative teaching methods, and the holistic development of our students. The award committee specifically noted our integration of Islamic values with modern educational practices, our strong STEM program, and our students' consistent academic achievements. This recognition is a testament to the hard work of our dedicated teachers, staff, and administrators, as well as the support of our parent community and the diligence of our students.",
    date: "April 15, 2023",
    image: "/images/portrait.jpg", // Replace with actual news image
    category: "Achievement",
    author: "Principal's Office",
  },
  {
    id: 5,
    title: "Summer School Registration Now Open",
    excerpt: "Registration for our summer enrichment and remedial programs is now open for all students.",
    content: "Registration for our 2023 Summer School program is now open! We are offering a variety of enrichment and remedial courses for students in all grade levels. The program will run from June 15 to July 30, with flexible scheduling options. Enrichment courses include Quran Memorization, Advanced Mathematics, Creative Writing, Robotics, and Art. Remedial courses are available for students who need additional support in core subjects. Early registration is encouraged as spaces are limited. Financial assistance is available for eligible families. Please visit the school office or our website to register.",
    date: "April 10, 2023",
    image: "/images/portrait.jpg", // Replace with actual news image
    category: "Announcement",
    author: "Academic Affairs",
  },
  {
    id: 6,
    title: "Students Participate in Community Clean-Up Day",
    excerpt: "Our students demonstrated their commitment to environmental stewardship by participating in the city-wide clean-up initiative.",
    content: "Last weekend, over 100 Alfalah Islamic School students and their families participated in the annual City Clean-Up Day. Our students worked diligently to clean parks, streets, and waterways, collecting over 200 bags of trash and recyclables. This initiative aligns with our school's emphasis on environmental stewardship and community service. The event also provided an opportunity for students to learn about environmental issues and the importance of taking action to protect our planet. We are proud of our students for embodying the Islamic principle of caring for the earth and being responsible citizens.",
    date: "March 25, 2023",
    image: "/images/portrait.jpg", // Replace with actual news image
    category: "Community",
    author: "Environmental Club",
  },
];

// Events data
const upcomingEvents = [
  {
    id: 1,
    title: "Parent-Teacher Conference",
    date: "May 20, 2023",
    time: "3:00 PM - 7:00 PM",
    location: "School Auditorium",
    description: "Meet with your child's teachers to discuss academic progress and address any concerns. Appointments can be scheduled through the parent portal or by contacting the school office.",
    category: "Academic",
  },
  {
    id: 2,
    title: "Annual Sports Day",
    date: "June 10, 2023",
    time: "9:00 AM - 4:00 PM",
    location: "School Sports Ground",
    description: "Join us for a day of athletic competitions, team sports, and fun activities. Students will compete in various events representing their houses. Parents and family members are welcome to attend and cheer for the participants.",
    category: "Sports",
  },
  {
    id: 3,
    title: "Graduation Ceremony",
    date: "June 15, 2023",
    time: "10:00 AM - 12:00 PM",
    location: "Main Hall",
    description: "Celebrate the achievements of our graduating class of 2023. The ceremony will include speeches, award presentations, and the conferring of diplomas. A reception for graduates and their families will follow the ceremony.",
    category: "Ceremony",
  },
  {
    id: 4,
    title: "Science Fair",
    date: "May 25, 2023",
    time: "1:00 PM - 5:00 PM",
    location: "Science Building",
    description: "Students will showcase their science projects and experiments. Visitors can explore innovative ideas and discoveries across various scientific disciplines. Judges will evaluate projects and awards will be presented at the end of the event.",
    category: "Academic",
  },
  {
    id: 5,
    title: "Islamic Art Exhibition",
    date: "June 5, 2023",
    time: "10:00 AM - 3:00 PM",
    location: "Art Gallery",
    description: "Explore beautiful Islamic art created by our talented students. The exhibition will feature calligraphy, geometric patterns, paintings, and sculptures inspired by Islamic artistic traditions. Refreshments will be served.",
    category: "Cultural",
  },
  {
    id: 6,
    title: "End of Year Concert",
    date: "June 12, 2023",
    time: "6:00 PM - 8:00 PM",
    location: "School Auditorium",
    description: "Enjoy performances by our school choir, band, and individual student musicians. The concert will showcase a variety of musical styles and talents. Tickets are available at the school office.",
    category: "Cultural",
  },
];

// Category colors for events
const getEventCategoryColor = (category: string) => {
  switch (category.toLowerCase()) {
    case 'academic':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';
    case 'sports':
      return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
    case 'ceremony':
      return 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300';
    case 'cultural':
      return 'bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
  }
};

// Format date
const formatDate = (dateString: string) => {
  const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'long', day: 'numeric' };
  return new Date(dateString).toLocaleDateString('en-US', options);
};

export default function NewsEventsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');

  // Filter news items
  const filteredNews = newsItems.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchTerm.toLowerCase()) || 
                          item.excerpt.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === '' || item.category.toLowerCase() === selectedCategory.toLowerCase();
    return matchesSearch && matchesCategory;
  });

  // Filter events
  const filteredEvents = upcomingEvents.filter(event => {
    const matchesSearch = event.title.toLowerCase().includes(searchTerm.toLowerCase()) || 
                          event.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === '' || event.category.toLowerCase() === selectedCategory.toLowerCase();
    return matchesSearch && matchesCategory;
  });

  // Clear filters
  const clearFilters = () => {
    setSearchTerm('');
    setSelectedCategory('');
  };

  return (
    <div className="min-h-screen pt-20">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-blue-900 to-indigo-900 text-white">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{ backgroundImage: 'url(/images/pattern.svg)', backgroundSize: '20px' }}></div>
        </div>
        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center max-w-3xl mx-auto"
          >
            <h1 className="text-4xl md:text-5xl font-bold mb-6">News & Events</h1>
            <p className="text-xl text-blue-100">
              Stay updated with the latest happenings at Alfalah Islamic School
            </p>
          </motion.div>
        </div>
      </section>

      {/* Search and Filter Section */}
      <section className="py-8 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center justify-between gap-4">
            <div className="w-full md:w-1/2 relative">
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <Input
                type="text"
                placeholder="Search news and events..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="w-full md:w-1/2 flex items-center gap-2">
              <div className="flex-grow">
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                >
                  <option value="">All Categories</option>
                  <option value="achievement">Achievement</option>
                  <option value="announcement">Announcement</option>
                  <option value="community">Community</option>
                  <option value="academic">Academic</option>
                  <option value="sports">Sports</option>
                  <option value="ceremony">Ceremony</option>
                  <option value="cultural">Cultural</option>
                </select>
              </div>
              {(searchTerm || selectedCategory) && (
                <Button 
                  variant="outline" 
                  onClick={clearFilters}
                  className="flex items-center gap-1"
                >
                  <FaTimes size={12} />
                  Clear
                </Button>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* News and Events Tabs */}
      <section className="py-12 bg-gray-50 dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <Tabs defaultValue="news" className="w-full">
            <TabsList className="grid w-full max-w-md mx-auto grid-cols-2 mb-8">
              <TabsTrigger value="news">School News</TabsTrigger>
              <TabsTrigger value="events">Upcoming Events</TabsTrigger>
            </TabsList>

            {/* News Tab */}
            <TabsContent value="news">
              {filteredNews.length > 0 ? (
                <motion.div
                  variants={containerVariants}
                  initial="hidden"
                  animate="visible"
                  className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                >
                  {filteredNews.map((item) => (
                    <motion.div
                      key={item.id}
                      variants={itemVariants}
                      className="bg-white dark:bg-gray-900 rounded-lg shadow-md overflow-hidden flex flex-col h-full"
                    >
                      <div className="relative h-48">
                        <Image
                          src={item.image}
                          alt={item.title}
                          fill
                          className="object-cover"
                        />
                        <div className="absolute top-2 left-2 bg-blue-600 text-white text-xs px-2 py-1 rounded">
                          {item.category}
                        </div>
                      </div>
                      <div className="p-6 flex-grow flex flex-col">
                        <div className="flex items-center text-gray-500 dark:text-gray-400 text-sm mb-2">
                          <FaCalendarAlt className="mr-1" />
                          <span>{formatDate(item.date)}</span>
                        </div>
                        <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">{item.title}</h3>
                        <p className="text-gray-600 dark:text-gray-300 mb-4 flex-grow">{item.excerpt}</p>
                        <Link href={`/website/news-events/${item.id}`} className="text-blue-600 dark:text-blue-400 hover:underline mt-auto">
                          Read More →
                        </Link>
                      </div>
                    </motion.div>
                  ))}
                </motion.div>
              ) : (
                <div className="text-center py-12">
                  <div className="text-gray-400 text-5xl mb-4">📰</div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">No News Found</h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    No news articles match your current search criteria.
                  </p>
                  <Button 
                    variant="outline" 
                    onClick={clearFilters}
                    className="mt-4"
                  >
                    Clear Filters
                  </Button>
                </div>
              )}
            </TabsContent>

            {/* Events Tab */}
            <TabsContent value="events" id="events">
              {filteredEvents.length > 0 ? (
                <motion.div
                  variants={containerVariants}
                  initial="hidden"
                  animate="visible"
                  className="grid grid-cols-1 md:grid-cols-2 gap-6"
                >
                  {filteredEvents.map((event) => (
                    <motion.div
                      key={event.id}
                      variants={itemVariants}
                      className="bg-white dark:bg-gray-900 rounded-lg shadow-md overflow-hidden"
                    >
                      <div className="p-6">
                        <div className="flex justify-between items-start mb-4">
                          <div>
                            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-1">{event.title}</h3>
                            <span className={`px-2 py-1 text-xs rounded-full ${getEventCategoryColor(event.category)}`}>
                              {event.category}
                            </span>
                          </div>
                          <div className="bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 px-3 py-2 rounded-lg text-center min-w-[80px]">
                            <div className="text-sm font-semibold">{new Date(event.date).toLocaleDateString('en-US', { month: 'short' })}</div>
                            <div className="text-xl font-bold">{new Date(event.date).getDate()}</div>
                          </div>
                        </div>
                        <p className="text-gray-600 dark:text-gray-300 mb-4">{event.description}</p>
                        <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                          <div className="flex items-center">
                            <FaCalendarAlt className="mr-2 text-blue-600" />
                            <span>{formatDate(event.date)}</span>
                          </div>
                          <div className="flex items-center">
                            <FaClock className="mr-2 text-blue-600" />
                            <span>{event.time}</span>
                          </div>
                          <div className="flex items-center">
                            <FaMapMarkerAlt className="mr-2 text-blue-600" />
                            <span>{event.location}</span>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </motion.div>
              ) : (
                <div className="text-center py-12">
                  <div className="text-gray-400 text-5xl mb-4">📅</div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">No Events Found</h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    No events match your current search criteria.
                  </p>
                  <Button 
                    variant="outline" 
                    onClick={clearFilters}
                    className="mt-4"
                  >
                    Clear Filters
                  </Button>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>
      </section>

      {/* Photo Gallery Preview */}
      <section className="py-16 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Photo Gallery</h2>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Explore moments from recent school events and activities
            </p>
          </motion.div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="relative h-40 md:h-64 rounded-lg overflow-hidden">
              <Image
                src="/images/portrait.jpg" // Replace with actual gallery image
                alt="School Event"
                fill
                className="object-cover hover:scale-105 transition-transform duration-300"
              />
            </div>
            <div className="relative h-40 md:h-64 rounded-lg overflow-hidden">
              <Image
                src="/images/portrait.jpg" // Replace with actual gallery image
                alt="School Event"
                fill
                className="object-cover hover:scale-105 transition-transform duration-300"
              />
            </div>
            <div className="relative h-40 md:h-64 rounded-lg overflow-hidden">
              <Image
                src="/images/portrait.jpg" // Replace with actual gallery image
                alt="School Event"
                fill
                className="object-cover hover:scale-105 transition-transform duration-300"
              />
            </div>
            <div className="relative h-40 md:h-64 rounded-lg overflow-hidden">
              <Image
                src="/images/portrait.jpg" // Replace with actual gallery image
                alt="School Event"
                fill
                className="object-cover hover:scale-105 transition-transform duration-300"
              />
            </div>
          </div>

          <div className="text-center mt-8">
            <Button className="bg-blue-600 hover:bg-blue-700 text-white">
              View Full Gallery
            </Button>
          </div>
        </div>
      </section>

      {/* Newsletter Signup */}
      <section className="py-12 bg-gray-50 dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto bg-white dark:bg-gray-900 rounded-lg shadow-md p-8">
            <div className="text-center mb-6">
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Stay Updated</h3>
              <p className="text-gray-600 dark:text-gray-300">
                Subscribe to our newsletter to receive the latest news and event announcements
              </p>
            </div>
            <form className="flex flex-col sm:flex-row gap-4">
              <Input
                type="email"
                placeholder="Your email address"
                className="flex-grow"
              />
              <Button className="bg-blue-600 hover:bg-blue-700 text-white whitespace-nowrap">
                Subscribe
              </Button>
            </form>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-4 text-center">
              We respect your privacy. Unsubscribe at any time.
            </p>
          </div>
        </div>
      </section>
    </div>
  );
}

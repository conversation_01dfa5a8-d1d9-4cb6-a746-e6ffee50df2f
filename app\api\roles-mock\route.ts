import { NextResponse } from 'next/server'
import { verifyJWT } from '@/lib/jwt'
import { cookies } from 'next/headers'

// GET all roles
export async function GET() {
  try {
    console.log('Fetching all roles')

    // Skip authentication checks for development
    if (process.env.NODE_ENV !== 'production') {
      console.log('Development mode - bypassing authentication checks')

      // Mock roles data
      const roles = [
        {
          id: 'admin',
          name: 'Administrator',
          description: 'Full system access',
          permissions: [
            'View Students', 'Add Students', 'Edit Students', 'Delete Students',
            'View Classes', 'Add Classes', 'Edit Classes', 'Delete Classes',
            'View Teachers', 'Add Teachers', 'Edit Teachers', 'Delete Teachers',
            'View Marks', 'Add Marks', 'Edit Marks', 'Delete Marks',
            'View Attendance', 'Add Attendance', 'Edit Attendance',
            'Generate Reports', 'System Settings',
            'Manage Users', 'Manage Roles'
          ],
          systemDefined: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: 'teacher',
          name: 'Teacher',
          description: 'Access to classes and students',
          permissions: [
            'View Students', 'View Classes',
            'View Marks', 'Add Marks', 'Edit Marks',
            'View Attendance', 'Add Attendance', 'Edit Attendance',
            'Generate Reports'
          ],
          systemDefined: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: 'hrteacher',
          name: 'HR Teacher',
          description: 'Manage class and attendance',
          permissions: [
            'View Students', 'Edit Students',
            'View Classes', 'Edit Classes',
            'View Marks', 'Add Marks', 'Edit Marks',
            'View Attendance', 'Add Attendance', 'Edit Attendance',
            'Generate Reports'
          ],
          systemDefined: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: 'staff',
          name: 'Staff',
          description: 'Limited system access',
          permissions: [
            'View Students', 'View Classes',
            'View Marks', 'View Attendance',
            'Generate Reports'
          ],
          systemDefined: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: 'custom-role-1',
          name: 'Principal',
          description: 'School principal with oversight privileges',
          permissions: [
            'View Students', 'View Classes', 'View Teachers',
            'View Marks', 'View Attendance', 'Generate Reports',
            'System Settings'
          ],
          systemDefined: false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ];

      console.log(`Returning ${roles.length} roles`);
      return NextResponse.json(roles);
    }

    try {
      // This code will run in production mode
      // Verify authentication
      const cookieStore = cookies();
      const token = cookieStore.get('token')?.value;

      if (!token) {
        console.error('No authentication token found');
        return NextResponse.json(
          { error: 'Unauthorized - Authentication required' },
          { status: 401 }
        );
      }

      // Verify the token
      const decoded = await verifyJWT(token);

      // Mock roles data
      const roles = [
        // Same roles as above
      ];

      console.log(`Returning ${roles.length} roles`);
      return NextResponse.json(roles);
    } catch (jwtError) {
      console.error('JWT verification error:', jwtError)
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      )
    }
  } catch (error) {
    console.error('Error fetching roles:', error)
    return NextResponse.json(
      { error: 'Failed to fetch roles' },
      { status: 500 }
    )
  }
}

// POST to create a new role
export async function POST(request: Request) {
  try {
    console.log('Creating new role')

    // Skip authentication checks for development
    if (process.env.NODE_ENV !== 'production') {
      console.log('Development mode - bypassing authentication checks');

      // Get the request body
      const data = await request.json();

      // Validate the role data
      if (!data.name || !data.description) {
        console.error('Invalid role data:', data);
        return NextResponse.json(
          { error: 'Role name and description are required' },
          { status: 400 }
        );
      }

      if (!Array.isArray(data.permissions) || data.permissions.length === 0) {
        console.error('Invalid permissions:', data.permissions);
        return NextResponse.json(
          { error: 'At least one permission is required' },
          { status: 400 }
        );
      }

      // Create the new role
      const newRole = {
        id: `role-${Date.now()}`,
        name: data.name,
        description: data.description,
        permissions: data.permissions,
        systemDefined: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      console.log('New role created:', newRole);
      return NextResponse.json({
        message: 'Role created successfully',
        role: newRole
      });
    }

    try {
      // This code will run in production mode
      // Verify authentication
      const cookieStore = cookies();
      const token = cookieStore.get('token')?.value;

      if (!token) {
        console.error('No authentication token found');
        return NextResponse.json(
          { error: 'Unauthorized - Authentication required' },
          { status: 401 }
        );
      }

      // Verify the token
      const decoded = await verifyJWT(token);

      // Only allow admins to create roles
      if (decoded.role !== 'ADMIN') {
        console.error('Unauthorized role creation attempt', {
          requesterId: decoded.id,
          requesterRole: decoded.role
        });
        return NextResponse.json(
          { error: 'Forbidden - Only administrators can create roles' },
          { status: 403 }
        );
      }

      // Get the request body
      const data = await request.json();

      // Validate the role data
      if (!data.name || !data.description) {
        console.error('Invalid role data:', data);
        return NextResponse.json(
          { error: 'Role name and description are required' },
          { status: 400 }
        );
      }

      if (!Array.isArray(data.permissions) || data.permissions.length === 0) {
        console.error('Invalid permissions:', data.permissions);
        return NextResponse.json(
          { error: 'At least one permission is required' },
          { status: 400 }
        );
      }

      // Create the new role
      const newRole = {
        id: `role-${Date.now()}`,
        name: data.name,
        description: data.description,
        permissions: data.permissions,
        systemDefined: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      console.log('New role created:', newRole);
      return NextResponse.json({
        message: 'Role created successfully',
        role: newRole
      });
    } catch (jwtError) {
      console.error('JWT verification error:', jwtError)
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      )
    }
  } catch (error) {
    console.error('Error creating role:', error)
    return NextResponse.json(
      { error: 'Failed to create role' },
      { status: 500 }
    )
  }
}

'use client'

import React, { useState, useEffect } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import {
  Table, TableBody, TableCell, TableHead,
  TableHeader, TableRow
} from '@/app/components/ui/table'
import { Button } from '@/app/components/ui/button'
import { Input } from '@/app/components/ui/input'
import {
  DropdownMenu, DropdownMenuContent, DropdownMenuItem,
  DropdownMenuTrigger, DropdownMenuSeparator
} from '@/app/components/ui/dropdown-menu'
import { Badge } from '@/app/components/ui/badge'
import {
  AlertDialog, AlertDialogAction, AlertDialogCancel,
  AlertDialogContent, AlertDialogDescription, AlertDialogFooter,
  AlertDialogHeader, AlertDialogTitle
} from '@/app/components/ui/alert-dialog'
import {
  Card, CardContent, CardDescription, CardHeader, CardTitle
} from '@/app/components/ui/card'
import {
  Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger
} from '@/app/components/ui/tabs'
import {
  Search, RefreshCw, MoreHorizontal, UserCheck, UserX,
  ShieldAlert, ShieldCheck, Shield, UserCog, XCircle, CheckCircle, KeyRound, Users, Database
} from 'lucide-react'
import { TeacherAssignmentsTab } from './TeacherAssignmentsTab'
import { ParentStudentLinking } from './ParentStudentLinking'
import { AccountResetTab } from './AccountResetTab'

// Define types
interface User {
  id: string
  name: string
  email: string
  role: string
  status: string
  lastLogin?: string
  createdAt: string
  updatedAt: string
}

export function NewRoleManagement() {
  const queryClient = useQueryClient()
  const [activeTab, setActiveTab] = useState('users')
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false)
  const [confirmAction, setConfirmAction] = useState<{
    type: 'role' | 'status'
    value: string
    userId: string
    userName: string
  } | null>(null)

  // Fetch users
  const {
    data: users = [],
    isLoading: isLoadingUsers,
    error: usersError,
    refetch: refetchUsers
  } = useQuery({
    queryKey: ['users'],
    queryFn: async () => {
      const response = await fetch('/api/users')
      if (!response.ok) {
        throw new Error('Failed to fetch users')
      }
      return response.json()
    }
  })

  // Update user role mutation
  const updateUserRole = useMutation({
    mutationFn: async ({ userId, role }: { userId: string; role: string }) => {
      const response = await fetch(`/api/users/${userId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ role }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to update user role')
      }

      return response.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
    },
  })

  // Update user status mutation
  const updateUserStatus = useMutation({
    mutationFn: async ({ userId, status }: { userId: string; status: string }) => {
      const response = await fetch(`/api/users/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to update user status')
      }

      return response.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
    },
  })

  // Handle confirm dialog
  const handleConfirmAction = () => {
    if (!confirmAction) return

    if (confirmAction.type === 'role') {
      updateUserRole.mutate({
        userId: confirmAction.userId,
        role: confirmAction.value
      })
    } else if (confirmAction.type === 'status') {
      updateUserStatus.mutate({
        userId: confirmAction.userId,
        status: confirmAction.value
      })
    }

    setConfirmDialogOpen(false)
    setConfirmAction(null)
  }

  // Filter users based on search term
  const filteredUsers = users.filter((user: User) => {
    const searchLower = searchTerm.toLowerCase()
    return (
      user.name.toLowerCase().includes(searchLower) ||
      user.email.toLowerCase().includes(searchLower) ||
      user.role.toLowerCase().includes(searchLower)
    )
  })

  // Check if user is system admin (cannot be changed)
  const isSystemAdmin = (email: string) => {
    return email === '<EMAIL>' || email === '<EMAIL>'
  }

  // Render role badge
  const renderRoleBadge = (role: string) => {
    switch (role) {
      case 'SUPER_ADMIN':
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            <ShieldAlert className="h-3.5 w-3.5 mr-1" />
            Super Admin
          </Badge>
        )
      case 'ADMIN':
        return (
          <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
            <ShieldCheck className="h-3.5 w-3.5 mr-1" />
            Admin
          </Badge>
        )
      case 'SUPERVISOR':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            <Shield className="h-3.5 w-3.5 mr-1" />
            Supervisor
          </Badge>
        )
      case 'ACCOUNTANT':
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
            <Shield className="h-3.5 w-3.5 mr-1" />
            Accountant
          </Badge>
        )
      case 'TEACHER':
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            <ShieldCheck className="h-3.5 w-3.5 mr-1" />
            Teacher
          </Badge>
        )
      case 'PARENT':
        return (
          <Badge variant="outline" className="bg-pink-50 text-pink-700 border-pink-200">
            <UserCog className="h-3.5 w-3.5 mr-1" />
            Parent
          </Badge>
        )
      case 'UNIT_LEADER':
        return (
          <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
            <Users className="h-3.5 w-3.5 mr-1" />
            Unit Leader
          </Badge>
        )
      case 'DATA_ENCODER':
        return (
          <Badge variant="outline" className="bg-cyan-50 text-cyan-700 border-cyan-200">
            <Database className="h-3.5 w-3.5 mr-1" />
            Data Encoder
          </Badge>
        )
      default:
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
            <UserCog className="h-3.5 w-3.5 mr-1" />
            {role}
          </Badge>
        )
    }
  }

  // Render status badge
  const renderStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            <CheckCircle className="h-3.5 w-3.5 mr-1" />
            Active
          </Badge>
        )
      case 'inactive':
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
            <XCircle className="h-3.5 w-3.5 mr-1" />
            Inactive
          </Badge>
        )
      case 'locked':
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            <XCircle className="h-3.5 w-3.5 mr-1" />
            Locked
          </Badge>
        )
      default:
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
            <XCircle className="h-3.5 w-3.5 mr-1" />
            {status}
          </Badge>
        )
    }
  }

  // Handle role change
  const handleRoleChange = (userId: string, userName: string, role: string) => {
    setConfirmAction({
      type: 'role',
      value: role,
      userId,
      userName
    })
    setConfirmDialogOpen(true)
  }

  // Handle status change
  const handleStatusChange = (userId: string, userName: string, status: string) => {
    setConfirmAction({
      type: 'status',
      value: status,
      userId,
      userName
    })
    setConfirmDialogOpen(true)
  }

  return (
    <div className="space-y-8 p-6">
      {/* Enhanced Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">System Administration</h2>
        <p className="text-gray-600">Comprehensive role and permission management for all system users</p>
      </div>

      <Tabs
        defaultValue="users"
        value={activeTab}
        onValueChange={setActiveTab}
        className="w-full"
      >
        <div className="flex justify-center mb-8">
          <TabsList className="grid grid-cols-4 bg-gradient-to-r from-amber-100 to-orange-100 p-1 rounded-xl shadow-inner">
            <TabsTrigger
              value="users"
              className="data-[state=active]:bg-white data-[state=active]:shadow-lg data-[state=active]:text-amber-700 data-[state=active]:font-semibold transition-all duration-300 rounded-lg px-6 py-3"
            >
              <UserCog className="h-4 w-4 mr-2" />
              User Roles
            </TabsTrigger>
            <TabsTrigger
              value="teachers"
              className="data-[state=active]:bg-white data-[state=active]:shadow-lg data-[state=active]:text-amber-700 data-[state=active]:font-semibold transition-all duration-300 rounded-lg px-6 py-3"
            >
              <Shield className="h-4 w-4 mr-2" />
              Teacher Assignments
            </TabsTrigger>
            <TabsTrigger
              value="parents"
              className="data-[state=active]:bg-white data-[state=active]:shadow-lg data-[state=active]:text-amber-700 data-[state=active]:font-semibold transition-all duration-300 rounded-lg px-6 py-3"
            >
              <ShieldCheck className="h-4 w-4 mr-2" />
              Parent Linking
            </TabsTrigger>
            <TabsTrigger
              value="account-reset"
              className="data-[state=active]:bg-white data-[state=active]:shadow-lg data-[state=active]:text-amber-700 data-[state=active]:font-semibold transition-all duration-300 rounded-lg px-6 py-3"
            >
              <KeyRound className="h-4 w-4 mr-2" />
              Account Reset
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="users" className="mt-6">
          <Card className="border-0 shadow-xl bg-white/90 backdrop-blur-sm rounded-2xl overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-amber-50 to-orange-50 border-b border-amber-100 p-6">
              <div className="flex items-center gap-3">
                <div className="h-10 w-10 rounded-full bg-amber-500/20 flex items-center justify-center">
                  <UserCog className="h-5 w-5 text-amber-600" />
                </div>
                <div>
                  <CardTitle className="text-xl text-amber-700">User Role Management</CardTitle>
                  <CardDescription className="text-amber-600/70">
                    Assign roles to users and manage their account status
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {/* Search and refresh */}
              <div className="flex justify-between items-center mb-6">
                <div className="relative w-64">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                  <Input
                    type="text"
                    placeholder="Search users..."
                    className="pl-9"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => refetchUsers()}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
              </div>

              {/* Users table */}
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredUsers.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={5} className="text-center py-4 text-gray-500">
                          {isLoadingUsers ? 'Loading users...' : 'No users found'}
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredUsers.map((user: User) => (
                        <TableRow key={user.id}>
                          <TableCell className="font-medium">
                            {user.name}
                          </TableCell>
                          <TableCell>{user.email}</TableCell>
                          <TableCell>{renderRoleBadge(user.role)}</TableCell>
                          <TableCell>{renderStatusBadge(user.status)}</TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreHorizontal className="h-4 w-4" />
                                  <span className="sr-only">Open menu</span>
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end" className="w-[200px]">
                                <DropdownMenuItem
                                  className="text-xs font-medium text-gray-500"
                                  disabled
                                >
                                  Change Role
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => handleRoleChange(user.id, user.name, 'SUPER_ADMIN')}
                                  disabled={user.role === 'SUPER_ADMIN' || isSystemAdmin(user.email)}
                                  className={user.role === 'SUPER_ADMIN' ? 'bg-red-50' : ''}
                                >
                                  <ShieldAlert className="h-4 w-4 mr-2 text-red-600" />
                                  Super Admin
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => handleRoleChange(user.id, user.name, 'ADMIN')}
                                  disabled={user.role === 'ADMIN' || isSystemAdmin(user.email)}
                                  className={user.role === 'ADMIN' ? 'bg-purple-50' : ''}
                                >
                                  <ShieldCheck className="h-4 w-4 mr-2 text-purple-600" />
                                  Admin
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => handleRoleChange(user.id, user.name, 'SUPERVISOR')}
                                  disabled={user.role === 'SUPERVISOR' || isSystemAdmin(user.email)}
                                  className={user.role === 'SUPERVISOR' ? 'bg-green-50' : ''}
                                >
                                  <Shield className="h-4 w-4 mr-2 text-green-600" />
                                  Supervisor
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => handleRoleChange(user.id, user.name, 'ACCOUNTANT')}
                                  disabled={user.role === 'ACCOUNTANT' || isSystemAdmin(user.email)}
                                  className={user.role === 'ACCOUNTANT' ? 'bg-yellow-50' : ''}
                                >
                                  <Shield className="h-4 w-4 mr-2 text-yellow-600" />
                                  Accountant
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => handleRoleChange(user.id, user.name, 'TEACHER')}
                                  disabled={user.role === 'TEACHER' || isSystemAdmin(user.email)}
                                  className={user.role === 'TEACHER' ? 'bg-blue-50' : ''}
                                >
                                  <ShieldCheck className="h-4 w-4 mr-2 text-blue-600" />
                                  Teacher
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => handleRoleChange(user.id, user.name, 'PARENT')}
                                  disabled={user.role === 'PARENT' || isSystemAdmin(user.email)}
                                  className={user.role === 'PARENT' ? 'bg-pink-50' : ''}
                                >
                                  <UserCog className="h-4 w-4 mr-2 text-pink-600" />
                                  Parent
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => handleRoleChange(user.id, user.name, 'UNIT_LEADER')}
                                  disabled={user.role === 'UNIT_LEADER' || isSystemAdmin(user.email)}
                                  className={user.role === 'UNIT_LEADER' ? 'bg-orange-50' : ''}
                                >
                                  <Users className="h-4 w-4 mr-2 text-orange-600" />
                                  Unit Leader
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => handleRoleChange(user.id, user.name, 'DATA_ENCODER')}
                                  disabled={user.role === 'DATA_ENCODER' || isSystemAdmin(user.email)}
                                  className={user.role === 'DATA_ENCODER' ? 'bg-cyan-50' : ''}
                                >
                                  <Database className="h-4 w-4 mr-2 text-cyan-600" />
                                  Data Encoder
                                </DropdownMenuItem>

                                <DropdownMenuSeparator />

                                <DropdownMenuItem
                                  className="text-xs font-medium text-gray-500"
                                  disabled
                                >
                                  Change Status
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => handleStatusChange(user.id, user.name, 'active')}
                                  disabled={user.status === 'active' || isSystemAdmin(user.email)}
                                  className={user.status === 'active' ? 'bg-green-50' : ''}
                                >
                                  <UserCheck className="h-4 w-4 mr-2 text-green-600" />
                                  Activate
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => handleStatusChange(user.id, user.name, 'inactive')}
                                  disabled={user.status === 'inactive' || isSystemAdmin(user.email)}
                                  className={user.status === 'inactive' ? 'bg-gray-50' : ''}
                                >
                                  <UserX className="h-4 w-4 mr-2 text-gray-600" />
                                  Deactivate
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="teachers" className="mt-6">
          <Card className="border-0 shadow-xl bg-white/90 backdrop-blur-sm rounded-2xl overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-100 p-6">
              <div className="flex items-center gap-3">
                <div className="h-10 w-10 rounded-full bg-blue-500/20 flex items-center justify-center">
                  <Shield className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <CardTitle className="text-xl text-blue-700">Teacher Assignments</CardTitle>
                  <CardDescription className="text-blue-600/70">
                    Assign teachers to classes and manage their permissions
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-6">
              <TeacherAssignmentsTab />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="parents" className="mt-6">
          <Card className="border-0 shadow-xl bg-white/90 backdrop-blur-sm rounded-2xl overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 border-b border-green-100 p-6">
              <div className="flex items-center gap-3">
                <div className="h-10 w-10 rounded-full bg-green-500/20 flex items-center justify-center">
                  <ShieldCheck className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <CardTitle className="text-xl text-green-700">Parent-Student Linking</CardTitle>
                  <CardDescription className="text-green-600/70">
                    Link parent users to their children using Student ID (SID)
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-6">
              <ParentStudentLinking />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="account-reset" className="mt-6">
          <Card className="border-0 shadow-xl bg-white/90 backdrop-blur-sm rounded-2xl overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-orange-50 to-red-50 border-b border-orange-100 p-6">
              <div className="flex items-center gap-3">
                <div className="h-10 w-10 rounded-full bg-orange-500/20 flex items-center justify-center">
                  <KeyRound className="h-5 w-5 text-orange-600" />
                </div>
                <div>
                  <CardTitle className="text-xl text-orange-700">Account Reset</CardTitle>
                  <CardDescription className="text-orange-600/70">
                    Reset user passwords and manage account recovery for forgotten credentials
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-6">
              <AccountResetTab />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Confirmation Dialog */}
      <AlertDialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {confirmAction?.type === 'role'
                ? `Change Role to ${confirmAction?.value}`
                : `Change Status to ${confirmAction?.value}`}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {confirmAction?.type === 'role'
                ? `Are you sure you want to change ${confirmAction?.userName}'s role to ${confirmAction?.value}?`
                : `Are you sure you want to change ${confirmAction?.userName}'s status to ${confirmAction?.value}?`}
              <br /><br />
              This action can be reversed later if needed.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleConfirmAction}>
              Confirm
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}

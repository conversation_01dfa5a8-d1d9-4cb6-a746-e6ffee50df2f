# HR Teacher Assignment Fix

## Problem
The "Assign HR Teacher" dialog was failing with:
```
Failed to assign HR teacher: Teacher not found
POST http://localhost:3000/api/hr-teachers/assign 404 (Not Found)
```

## Root Cause
The `/api/hr-teachers/assign` endpoint was only checking for teachers in the `teacher` table, but not checking for users with teacher role in the `users` table.

## Solution Implemented

### 1. Updated `/api/hr-teachers/assign` endpoint
- Now checks both `teacher` table and `users` table (role='TEACHER')
- Uses different database models based on teacher source:
  - **Teacher table**: Uses `HRTeacher` model (traditional approach)
  - **User table**: Uses `TeacherAssignment` model with `isHRTeacher: true`

### 2. Updated `/api/hr-teachers/available-classes` endpoint
- Now shows HR teachers from both sources
- Checks both traditional HR teacher assignments and TeacherAssignment records

### 3. Database Schema Considerations
The schema has two ways to assign HR teachers:

**Option 1: Traditional (Teacher table)**
```
Class.hrTeacherId -> Teacher.id
HRTeacher table: { teacherId, classId }
```

**Option 2: Flexible (User table)**
```
TeacherAssignment table: { teacherId (User.id), classId, isHRTeacher: true }
```

### 4. Implementation Logic

**For Assignment:**
- If teacher is from `Teacher` table: Use `HRTeacher` model + update `Class.hrTeacherId`
- If teacher is from `User` table: Use `TeacherAssignment` model with `isHRTeacher: true`

**For Removal:**
- Clear both `HRTeacher` records and `TeacherAssignment` records
- Set `Class.hrTeacherId` to null

**For Display:**
- Check both sources when showing current HR teacher assignments

## Testing Steps

1. **Test with Teacher from Teacher table:**
   - Select a teacher from the dropdown that shows no "(User)" indicator
   - Should assign successfully using traditional HRTeacher model

2. **Test with User from User table:**
   - Select a teacher from the dropdown that shows "(User)" indicator
   - Should assign successfully using TeacherAssignment model

3. **Test removal:**
   - Remove any HR teacher assignment
   - Should clear both possible assignment types

4. **Test display:**
   - HR teacher should show correctly regardless of source
   - Available classes should show current HR teachers from both sources

## Files Modified

- `app/api/hr-teachers/assign/route.ts` - Main fix for assignment logic
- `app/api/hr-teachers/available-classes/route.ts` - Updated to show HR teachers from both sources

## Expected Behavior After Fix

1. ✅ "Assign HR Teacher" dialog should now work with teachers from both sources
2. ✅ No more "Teacher not found" errors
3. ✅ HR teacher assignments should persist correctly
4. ✅ HR teacher display should work for both teacher types
5. ✅ Removal should work for both assignment types

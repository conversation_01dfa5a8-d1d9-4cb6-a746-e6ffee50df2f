import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { verifyJWT } from './lib/jwt'

// Define public paths that don't require authentication
const publicPaths = [
  // Public pages
  '/login',
  '/register',

  // Public API endpoints
  '/api/auth/login',
  '/api/auth/register',
  '/api/auth/logout',
  '/api/auth/me',
  '/api/auth/debug', // Debug endpoint for authentication
  '/api/setup', // Allow access to setup endpoint

  // Static assets
  '/favicon.ico',
  '/_next'
]

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Log the current path and cookies for debugging
  console.log(`Middleware processing: ${pathname}`)
  console.log('Cookies present:', request.cookies.getAll().map(c => c.name))

  // Check if the path is public and should bypass authentication
  const isPublicPath = publicPaths.some(path =>
    pathname === path || pathname.startsWith(path + '/')
  )

  if (isPublicPath) {
    console.log(`Public path: ${pathname}, allowing access`)
    return NextResponse.next()
  }

  // For any other path, require authentication
  console.log(`Protected path: ${pathname}, checking authentication`)

  // Check for any authentication token
  const nextAuthToken = request.cookies.get('next-auth.session-token')?.value
  const token = request.cookies.get('token')?.value
  const authStatus = request.cookies.get('auth_status')?.value

  // Log all cookies for debugging
  console.log('Cookies in middleware:', {
    nextAuthToken: nextAuthToken ? 'present' : 'absent',
    token: token ? 'present' : 'absent',
    authStatus
  })

  // If we have a NextAuth token, consider the user authenticated
  if (nextAuthToken) {
    console.log(`NextAuth token found, allowing access to: ${pathname}`)
    return NextResponse.next()
  }

  // If we have our custom token, verify it
  if (token) {
    try {
      // Verify the token
      await verifyJWT(token)
      console.log(`Valid token, allowing access to: ${pathname}`)
      return NextResponse.next()
    } catch (error) {
      console.log(`Invalid token, redirecting to login`)
      const url = new URL('/login', request.url)
      url.searchParams.set('from', pathname)
      return NextResponse.redirect(url)
    }
  }

  // Check for auth_status cookie (for debugging)
  if (authStatus === 'logged_in') {
    console.log(`Auth status cookie found but no token, possible cookie issue. Allowing access to: ${pathname}`)
    return NextResponse.next()
  }

  // No valid token found, redirect to login
  console.log(`No authentication token found, redirecting to login`)
  const url = new URL('/login', request.url)
  url.searchParams.set('from', pathname)
  return NextResponse.redirect(url)
}

// Configure which routes to run middleware on
export const config = {
  matcher: [
    /*
     * Match all request paths except:
     * 1. _next/static (static files)
     * 2. _next/image (image optimization files)
     * 3. favicon.ico (favicon file)
     * 4. public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public).*)',
  ],
}
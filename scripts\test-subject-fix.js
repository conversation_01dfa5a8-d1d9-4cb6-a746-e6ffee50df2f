// Script to test the subject constraint fix
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    console.log('🧪 Testing Subject Constraint Fix...\n');

    // First, let's get some classes to work with
    const classes = await prisma.class.findMany({
      select: {
        id: true,
        name: true
      },
      orderBy: {
        name: 'asc'
      }
    });

    if (classes.length < 2) {
      console.log('❌ Need at least 2 classes to test. Creating test classes...');
      
      // Create test classes if they don't exist
      const testClasses = ['8A', '8B'];
      for (const className of testClasses) {
        const existingClass = await prisma.class.findUnique({
          where: { name: className }
        });
        
        if (!existingClass) {
          await prisma.class.create({
            data: {
              name: className,
              totalStudents: 0,
              totalSubjects: 0
            }
          });
          console.log(`✅ Created test class: ${className}`);
        }
      }
      
      // Refresh classes list
      const updatedClasses = await prisma.class.findMany({
        select: {
          id: true,
          name: true
        },
        orderBy: {
          name: 'asc'
        }
      });
      classes.push(...updatedClasses);
    }

    console.log(`📚 Found ${classes.length} classes to test with:`);
    classes.forEach(cls => {
      console.log(`   - ${cls.name} (ID: ${cls.id})`);
    });
    console.log('');

    // Test subjects that should be able to exist in multiple classes
    const testSubjects = ['Arabic', 'HPE', 'Social Science', 'General Science', 'Mathematics', 'English'];
    
    console.log('🔬 Testing subject creation across multiple classes...\n');

    // Test creating the same subject for different classes
    for (const subjectName of testSubjects) {
      console.log(`📖 Testing subject: ${subjectName}`);
      
      for (let i = 0; i < Math.min(2, classes.length); i++) {
        const cls = classes[i];
        
        try {
          const subject = await prisma.subject.create({
            data: {
              name: subjectName,
              classId: cls.id
            },
            include: {
              class: {
                select: {
                  name: true
                }
              }
            }
          });
          
          console.log(`   ✅ Created "${subjectName}" for class ${cls.name}`);
        } catch (error) {
          if (error.code === 'P2002') {
            console.log(`   ⚠️  "${subjectName}" already exists for class ${cls.name}`);
          } else {
            console.log(`   ❌ Failed to create "${subjectName}" for class ${cls.name}: ${error.message}`);
          }
        }
      }
      console.log('');
    }

    // Test creating duplicate subject for the same class (should fail)
    console.log('🚫 Testing duplicate subject creation for same class (should fail)...\n');
    
    if (classes.length > 0) {
      const testClass = classes[0];
      
      try {
        await prisma.subject.create({
          data: {
            name: 'Arabic',
            classId: testClass.id
          }
        });
        console.log(`   ❌ ERROR: Duplicate subject creation should have failed!`);
      } catch (error) {
        if (error.code === 'P2002') {
          console.log(`   ✅ Correctly prevented duplicate "Arabic" for class ${testClass.name}`);
        } else {
          console.log(`   ⚠️  Unexpected error: ${error.message}`);
        }
      }
    }

    // Final verification
    console.log('\n📊 Final Database State:');
    const allSubjects = await prisma.subject.findMany({
      include: {
        class: {
          select: {
            name: true
          }
        }
      },
      orderBy: [
        { name: 'asc' },
        { class: { name: 'asc' } }
      ]
    });

    console.log(`\nTotal subjects created: ${allSubjects.length}`);
    
    // Group by subject name
    const subjectGroups = {};
    allSubjects.forEach(subject => {
      if (!subjectGroups[subject.name]) {
        subjectGroups[subject.name] = [];
      }
      subjectGroups[subject.name].push(subject.class.name);
    });

    Object.keys(subjectGroups).forEach(subjectName => {
      const classNames = subjectGroups[subjectName];
      console.log(`📚 ${subjectName}: ${classNames.join(', ')}`);
    });

    console.log('\n🎉 Test completed successfully!');
    console.log('✅ Subjects can now be created for multiple classes');
    console.log('✅ Duplicate subjects for same class are properly prevented');

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();

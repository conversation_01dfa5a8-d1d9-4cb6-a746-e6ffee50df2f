import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { cookies } from 'next/headers'
import { verifyJWT } from '@/lib/jwt'
import { canTeacherViewAttendance, canTeacherTakeAttendance } from '@/lib/teacher-permissions'

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url)
  const className = searchParams.get('class')
  const date = searchParams.get('date')

  try {
    // Verify authentication
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    }

    try {
      // Verify the token
      const decoded = await verifyJWT(token)

      // If the user is a teacher, check if they have permission to view attendance for this class
      if (decoded.role === 'TEACHER' && className) {
        // Try to find the teacher in the User model first
        let teacherId = null;

        // Check if user exists with TEACHER role
        const userTeacher = await prisma.user.findFirst({
          where: {
            email: decoded.email,
            role: 'TEACHER'
          },
          select: { id: true }
        });

        if (userTeacher) {
          teacherId = userTeacher.id;
        } else {
          // If not found in User model, try the Teacher model
          const teacher = await prisma.teacher.findUnique({
            where: { email: decoded.email },
            select: { id: true }
          });

          if (teacher) {
            teacherId = teacher.id;
          }
        }

        if (!teacherId) {
          return NextResponse.json(
            { error: 'Teacher not found' },
            { status: 404 }
          );
        }

        // Check if the teacher has permission to view attendance for this class
        const classObj = await prisma.class.findUnique({
          where: { name: className },
          select: { id: true }
        })

        if (!classObj) {
          return NextResponse.json(
            { error: 'Class not found' },
            { status: 404 }
          )
        }

        const hasPermission = await canTeacherViewAttendance(teacherId, classObj.id)

        if (!hasPermission) {
          return NextResponse.json(
            { error: 'You do not have permission to view attendance for this class' },
            { status: 403 }
          )
        }
      }

      // Real database query
      const attendance = await prisma.attendance.findMany({
        where: {
          className: className || undefined,
          date: date || undefined,
        },
        include: {
          student: true,
        },
      })

      return NextResponse.json(attendance)
    } catch (jwtError) {
      console.error('JWT verification error:', jwtError)
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      )
    }
  } catch (error) {
    console.error('Error in attendance GET handler:', error)
    return NextResponse.json(
      { error: 'Failed to fetch attendance records' },
      { status: 500 }
    )
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { date, className, records } = body

    // Validate required fields
    if (!date || !className || !records || !Array.isArray(records) || records.length === 0) {
      return NextResponse.json(
        { error: 'Date, className, and records are required' },
        { status: 400 }
      )
    }

    // Verify authentication
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    }

    try {
      // Verify the token
      const decoded = await verifyJWT(token)

      // If the user is a teacher, check if they have permission to take attendance for this class
      if (decoded.role === 'TEACHER') {
        // Try to find the teacher in the User model first
        let teacherId = null;

        // Check if user exists with TEACHER role
        const userTeacher = await prisma.user.findFirst({
          where: {
            email: decoded.email,
            role: 'TEACHER'
          },
          select: { id: true }
        });

        if (userTeacher) {
          teacherId = userTeacher.id;
        } else {
          // If not found in User model, try the Teacher model
          const teacher = await prisma.teacher.findUnique({
            where: { email: decoded.email },
            select: { id: true }
          });

          if (teacher) {
            teacherId = teacher.id;
          }
        }

        if (!teacherId) {
          return NextResponse.json(
            { error: 'Teacher not found' },
            { status: 404 }
          );
        }

        // Check if the teacher has permission to take attendance for this class
        const classObj = await prisma.class.findUnique({
          where: { name: className },
          select: { id: true }
        })

        if (!classObj) {
          return NextResponse.json(
            { error: 'Class not found' },
            { status: 404 }
          )
        }

        const hasPermission = await canTeacherTakeAttendance(teacherId, classObj.id)

        if (!hasPermission) {
          return NextResponse.json(
            { error: 'You do not have permission to take attendance for this class' },
            { status: 403 }
          )
        }
      }

      // Begin transaction with real database
      const result = await prisma.$transaction(async (tx) => {
        // Delete existing records for this class and date
        await tx.attendance.deleteMany({
          where: {
            className,
            date,
          },
        })

        // Create new records
        const newRecords = await Promise.all(
          records.map((record: { studentId: string, status: string }) =>
            tx.attendance.create({
              data: {
                date,
                className,
                studentId: record.studentId,
                status: record.status,
              },
            })
          )
        )

        return newRecords
      })

      return NextResponse.json(result)
    } catch (jwtError) {
      console.error('JWT verification error:', jwtError)
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      )
    }
  } catch (error) {
    console.error('Error in attendance POST handler:', error)
    return NextResponse.json(
      { error: 'Failed to save attendance records: ' + (error.message || 'Unknown error') },
      { status: 500 }
    )
  }
}

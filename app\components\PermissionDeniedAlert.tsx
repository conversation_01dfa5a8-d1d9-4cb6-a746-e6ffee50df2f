import React from 'react'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from './ui/alert-dialog'
import { ShieldAlert } from 'lucide-react'

interface PermissionDeniedAlertProps {
  isOpen: boolean
  onClose: () => void
  title?: string
  description?: string
  actionText?: string
}

export function PermissionDeniedAlert({
  isOpen,
  onClose,
  title = 'Access Denied',
  description = 'You do not have permission to access this feature. Please contact your administrator if you believe this is an error.',
  actionText = 'Understood'
}: PermissionDeniedAlertProps) {
  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <div className="flex items-center gap-2 text-red-500">
            <ShieldAlert className="h-5 w-5" />
            <AlertDialogTitle>{title}</AlertDialogTitle>
          </div>
          <AlertDialogDescription>
            {description}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogAction onClick={onClose}>{actionText}</AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}

export default PermissionDeniedAlert

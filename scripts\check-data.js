// <PERSON>ript to check database data
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    // Check if Class 8E exists
    console.log('Checking for Class 8E...');
    const class8E = await prisma.class.findFirst({
      where: {
        name: '8E'
      }
    });
    console.log('Class 8E:', class8E || 'Not found');

    // Check for Maths (8E) subject
    console.log('\nChecking for Maths (8E) subject...');
    const mathsSubject = await prisma.subject.findFirst({
      where: {
        name: {
          contains: 'Maths (8E)'
        }
      }
    });
    console.log('Maths (8E) subject:', mathsSubject || 'Not found');

    // Get all classes
    console.log('\nAll classes:');
    const allClasses = await prisma.class.findMany({
      orderBy: {
        name: 'asc'
      }
    });
    console.log(`Found ${allClasses.length} classes`);
    allClasses.forEach(cls => {
      console.log(`- ${cls.name} (ID: ${cls.id})`);
    });

    // Get all subjects
    console.log('\nAll subjects:');
    const allSubjects = await prisma.subject.findMany({
      orderBy: {
        name: 'asc'
      }
    });
    console.log(`Found ${allSubjects.length} subjects`);
    allSubjects.forEach(subject => {
      console.log(`- ${subject.name} (ID: ${subject.id}, ClassID: ${subject.classId})`);
    });

    // Check marks for Class 8E and Maths (8E)
    console.log('\nChecking marks for Class 8E and Maths (8E)...');
    const marks = await prisma.mark.findMany({
      where: {
        OR: [
          { className: '8E' },
          { subject: { contains: 'Maths' } }
        ]
      },
      include: {
        student: {
          select: {
            name: true
          }
        }
      },
      take: 10
    });
    console.log(`Found ${marks.length} marks`);
    marks.forEach(mark => {
      console.log(`- Student: ${mark.student.name}, Class: ${mark.className}, Subject: ${mark.subject}, Mark: ${mark.marks}`);
    });

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();

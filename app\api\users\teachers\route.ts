import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import {
  combineTeachers,
  type TeacherFromTable,
  type UserWithTeacherRole
} from '@/app/utils/teacher-utils'

export async function GET() {
  try {
    console.log('Fetching unified teachers for dropdowns')

    // Fetch teachers from Teacher table
    const teachersFromTable = await prisma.teacher.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        subject: true,
        mobile: true,
        fatherName: true,
        gender: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: {
        name: 'asc',
      },
    }) as TeacherFromTable[]

    // Fetch users with teacher role
    const usersWithTeacherRole = await prisma.user.findMany({
      where: {
        role: 'TEACHER',
        status: 'active',
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        phone: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: {
        name: 'asc',
      },
    }) as UserWithTeacherRole[]

    console.log(`Found ${teachersFromTable.length} teachers from Teacher table`)
    console.log(`Found ${usersWithTeacherRole.length} users with teacher role`)

    // Combine and deduplicate teachers
    const unifiedTeachers = combineTeachers(teachersFromTable, usersWithTeacherRole)

    console.log(`Returning ${unifiedTeachers.length} unified teachers`)

    return NextResponse.json(unifiedTeachers)
  } catch (error) {
    console.error('Error fetching unified teachers:', error)
    return NextResponse.json(
      { error: 'Failed to fetch teachers' },
      { status: 500 }
    )
  }
}

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
});

async function testPrismaConnection() {
  console.log('🔌 Testing Prisma connection to MySQL...');
  
  try {
    // Test basic connection
    await prisma.$connect();
    console.log('✅ Prisma connected successfully!');
    
    // Check if tables exist by trying to count records
    console.log('📋 Checking tables...');
    
    const userCount = await prisma.user.count();
    console.log(`✅ Users table: ${userCount} records`);
    
    const classCount = await prisma.class.count();
    console.log(`✅ Classes table: ${classCount} records`);
    
    const studentCount = await prisma.student.count();
    console.log(`✅ Students table: ${studentCount} records`);
    
    const teacherCount = await prisma.teacher.count();
    console.log(`✅ Teachers table: ${teacherCount} records`);
    
    const subjectCount = await prisma.subject.count();
    console.log(`✅ Subjects table: ${subjectCount} records`);
    
    const markCount = await prisma.mark.count();
    console.log(`✅ Marks table: ${markCount} records`);
    
    const attendanceCount = await prisma.attendance.count();
    console.log(`✅ Attendance table: ${attendanceCount} records`);
    
    console.log('\n🎉 All tables are accessible through Prisma!');
    console.log('🚀 Your database is ready for use!');
    
  } catch (error) {
    console.error('❌ Prisma connection failed:', error.message);
    console.error('Error details:', error);
  } finally {
    await prisma.$disconnect();
    console.log('🔌 Prisma connection closed');
  }
}

testPrismaConnection().catch(console.error);

import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: Request) {
  try {
    // Get URL parameters
    const { searchParams } = new URL(request.url)
    const studentId = searchParams.get('studentId')
    const className = searchParams.get('className')
    const paymentPurpose = searchParams.get('paymentPurpose')
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')
    const forMonth = searchParams.get('forMonth')

    // Build the where clause based on the provided filters
    const where: any = {}

    if (studentId) {
      where.student = {
        sid: studentId
      }
    }

    if (className) {
      where.student = {
        ...where.student,
        className
      }
    }

    if (paymentPurpose && paymentPurpose !== 'all') {
      where.feeType = {
        name: paymentPurpose
      }
    }

    // Date range filtering
    if (startDate || endDate) {
      where.paymentDate = {}

      if (startDate) {
        where.paymentDate.gte = new Date(startDate)
      }

      if (endDate) {
        // Set the end date to the end of the day
        const endDateTime = new Date(endDate)
        endDateTime.setHours(23, 59, 59, 999)
        where.paymentDate.lte = endDateTime
      }
    }

    // Filter by forMonth if provided
    if (forMonth) {
      console.log(`Filtering payments by month: ${forMonth}`);

      // Handle both single month and multi-month formats
      // For multi-month payments, the forMonth field contains a comma-separated list of months
      where.OR = [
        { forMonth },
        { forMonth: { contains: `,${forMonth},` } },
        { forMonth: { contains: `,${forMonth}` } },
        { forMonth: { contains: `${forMonth},` } }
      ];

      // Log the query we're about to execute
      console.log('Query where clause:', JSON.stringify(where, null, 2));
    }

    try {
      console.log('Executing payment history query with filters:', JSON.stringify(where, null, 2));

      // Fetch payments with related data
      const payments = await prisma.payment.findMany({
        where,
        include: {
          student: {
            select: {
              id: true,
              sid: true,
              name: true,
              className: true,
            },
          },
          feeType: true,
        },
        orderBy: {
          paymentDate: 'desc',
        },
      })

      console.log(`Found ${payments.length} payments in database`);

      // Log some details about the payments
      if (payments.length > 0) {
        console.log('First few payments:',
          payments.slice(0, 3).map(p => ({
            id: p.id,
            invoiceNumber: p.invoiceNumber,
            studentName: p.student.name,
            forMonth: p.forMonth,
            paymentDate: p.paymentDate
          }))
        );
      }

      // Transform the data to match the expected format in the frontend
      const formattedPayments = payments.map(payment => ({
        id: payment.id,
        invoiceNumber: payment.invoiceNumber,
        studentId: payment.student.sid,
        studentName: payment.student.name,
        className: payment.student.className,
        paymentDate: payment.paymentDate.toISOString().split('T')[0],
        forMonth: payment.forMonth || null, // Include the month this payment is for
        amount: payment.amount,
        paymentPurpose: payment.feeType.name,
        paymentMethod: payment.paymentMethod,
        transferId: payment.transferId || undefined,
        status: payment.status,
        notes: payment.notes || null, // Include payment notes
        createdAt: payment.createdAt.toISOString()
      }))

      console.log(`Returning ${formattedPayments.length} formatted payments`);

      // Log months present in the data
      const months = formattedPayments
        .filter(p => p.forMonth)
        .map(p => p.forMonth)
        .filter((v, i, a) => a.indexOf(v) === i)
        .sort();

      console.log('Months present in data:', months);

      return NextResponse.json(formattedPayments)
    } catch (dbError) {
      console.error('Database error fetching payments:', dbError);
      return NextResponse.json(
        { error: 'Failed to fetch payment history from database' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error fetching payment history:', error)
    return NextResponse.json(
      { error: 'Failed to process payment history request' },
      { status: 500 }
    );
  }
}

"use client";

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { FaBook, FaChalkboardTeacher, FaGraduationCap, FaFlask, FaCalculator, FaGlobe, FaPalette, FaRunning, FaQuran } from 'react-icons/fa';
import { Button } from '@/app/components/ui/button';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.5 },
  },
};

// Curriculum data
const curriculumAreas = [
  {
    id: 1,
    title: 'Islamic Studies',
    description: 'Our comprehensive Islamic Studies program focuses on Quran memorization, tafsir, hadith, fiqh, and Islamic history, providing students with a strong foundation in their faith.',
    icon: <FaQuran className="text-4xl text-emerald-600" />,
    color: 'bg-emerald-50 dark:bg-emerald-900/20',
    borderColor: 'border-emerald-200 dark:border-emerald-800',
    subjects: ['Quran Memorization', 'Tafsir', 'Hadith', 'Fiqh', 'Islamic History', 'Islamic Ethics'],
  },
  {
    id: 2,
    title: 'Language Arts',
    description: 'Our Language Arts curriculum develops strong reading, writing, speaking, and listening skills through literature, grammar, vocabulary, and creative expression.',
    icon: <FaBook className="text-4xl text-blue-600" />,
    color: 'bg-blue-50 dark:bg-blue-900/20',
    borderColor: 'border-blue-200 dark:border-blue-800',
    subjects: ['Reading', 'Writing', 'Grammar', 'Vocabulary', 'Literature', 'Public Speaking'],
  },
  {
    id: 3,
    title: 'Mathematics',
    description: 'Our Mathematics program builds strong foundational skills and critical thinking through a progressive curriculum from basic arithmetic to advanced mathematics.',
    icon: <FaCalculator className="text-4xl text-purple-600" />,
    color: 'bg-purple-50 dark:bg-purple-900/20',
    borderColor: 'border-purple-200 dark:border-purple-800',
    subjects: ['Arithmetic', 'Algebra', 'Geometry', 'Statistics', 'Calculus', 'Problem Solving'],
  },
  {
    id: 4,
    title: 'Science',
    description: 'Our Science curriculum fosters curiosity and scientific inquiry through hands-on experiments and exploration of life, physical, and earth sciences.',
    icon: <FaFlask className="text-4xl text-green-600" />,
    color: 'bg-green-50 dark:bg-green-900/20',
    borderColor: 'border-green-200 dark:border-green-800',
    subjects: ['Biology', 'Chemistry', 'Physics', 'Environmental Science', 'Astronomy', 'Laboratory Skills'],
  },
  {
    id: 5,
    title: 'Social Studies',
    description: 'Our Social Studies program explores history, geography, civics, and cultures, helping students understand their place in the world and develop global citizenship.',
    icon: <FaGlobe className="text-4xl text-amber-600" />,
    color: 'bg-amber-50 dark:bg-amber-900/20',
    borderColor: 'border-amber-200 dark:border-amber-800',
    subjects: ['History', 'Geography', 'Civics', 'Economics', 'World Cultures', 'Current Events'],
  },
  {
    id: 6,
    title: 'Arts & Music',
    description: 'Our Arts program nurtures creativity and self-expression through visual arts, music, and performance, allowing students to develop their artistic talents.',
    icon: <FaPalette className="text-4xl text-pink-600" />,
    color: 'bg-pink-50 dark:bg-pink-900/20',
    borderColor: 'border-pink-200 dark:border-pink-800',
    subjects: ['Visual Arts', 'Music', 'Drama', 'Calligraphy', 'Art History', 'Digital Media'],
  },
  {
    id: 7,
    title: 'Physical Education',
    description: 'Our Physical Education program promotes health, fitness, and teamwork through a variety of sports, games, and activities that develop motor skills and healthy habits.',
    icon: <FaRunning className="text-4xl text-red-600" />,
    color: 'bg-red-50 dark:bg-red-900/20',
    borderColor: 'border-red-200 dark:border-red-800',
    subjects: ['Team Sports', 'Individual Sports', 'Health & Nutrition', 'Fitness', 'Swimming', 'Outdoor Activities'],
  },
  {
    id: 8,
    title: 'Technology',
    description: 'Our Technology curriculum develops digital literacy, coding skills, and responsible technology use, preparing students for success in an increasingly digital world.',
    icon: <FaGraduationCap className="text-4xl text-indigo-600" />,
    color: 'bg-indigo-50 dark:bg-indigo-900/20',
    borderColor: 'border-indigo-200 dark:border-indigo-800',
    subjects: ['Computer Science', 'Coding', 'Digital Literacy', 'Robotics', 'Web Design', 'Multimedia'],
  },
];

export default function CurriculumPage() {
  return (
    <div className="min-h-screen pt-20">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-blue-900 to-indigo-900 text-white">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{ backgroundImage: 'url(/images/pattern.svg)', backgroundSize: '20px' }}></div>
        </div>
        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center max-w-3xl mx-auto"
          >
            <h1 className="text-4xl md:text-5xl font-bold mb-6">Our Curriculum</h1>
            <p className="text-xl text-blue-100">
              A comprehensive educational program that integrates academic excellence with Islamic values.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Introduction Section */}
      <section className="py-16 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.7 }}
              viewport={{ once: true }}
            >
              <div className="relative h-[400px] rounded-lg overflow-hidden shadow-xl">
                <Image
                  src="/images/portrait.jpg" // Replace with actual image
                  alt="Students learning at Alfalah Islamic School"
                  fill
                  className="object-cover"
                />
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.7 }}
              viewport={{ once: true }}
              className="space-y-6"
            >
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Educational Philosophy</h2>
              <p className="text-gray-600 dark:text-gray-300">
                At Alfalah Islamic School, our curriculum is designed to provide a well-rounded education that nurtures both academic excellence and Islamic values. We believe in developing the whole child—intellectually, spiritually, physically, and socially.
              </p>
              <p className="text-gray-600 dark:text-gray-300">
                Our integrated approach ensures that Islamic principles are woven throughout all subject areas, creating a seamless educational experience that prepares students for success in this world and the hereafter.
              </p>
              <p className="text-gray-600 dark:text-gray-300">
                We follow national educational standards while incorporating Islamic perspectives, ensuring our students receive a high-quality education that meets or exceeds requirements while remaining true to our faith-based mission.
              </p>
              <div className="pt-4 flex space-x-4">
                <Link href="/website/academics/departments">
                  <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                    Explore Departments
                  </Button>
                </Link>
                <Link href="/website/academics/calendar">
                  <Button variant="outline">
                    Academic Calendar
                  </Button>
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Curriculum Areas */}
      <section id="curriculum-areas" className="py-16 bg-gray-50 dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Curriculum Areas</h2>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Our comprehensive curriculum covers all major subject areas, providing students with the knowledge and skills they need for academic success.
            </p>
          </motion.div>

          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          >
            {curriculumAreas.map((area) => (
              <motion.div key={area.id} variants={itemVariants}>
                <div className={`h-full p-6 rounded-lg border ${area.borderColor} ${area.color} transition-colors duration-300 flex flex-col`}>
                  <div className="mb-4">{area.icon}</div>
                  <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">{area.title}</h3>
                  <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">{area.description}</p>
                  <div className="mt-auto pt-4">
                    <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-2">Key Subjects:</h4>
                    <div className="flex flex-wrap gap-2">
                      {area.subjects.map((subject, index) => (
                        <span key={index} className="text-xs bg-white dark:bg-gray-700 px-2 py-1 rounded-full text-gray-700 dark:text-gray-300">
                          {subject}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Grade Level Approach */}
      <section className="py-16 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Grade Level Approach</h2>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Our curriculum is tailored to meet the developmental needs of students at each grade level.
            </p>
          </motion.div>

          <div className="max-w-4xl mx-auto">
            <div className="space-y-8">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                viewport={{ once: true }}
                className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg shadow-md"
              >
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Elementary School (Grades 1-5)</h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  In elementary school, we focus on building strong foundational skills in reading, writing, mathematics, and Islamic studies. Students develop a love for learning through hands-on activities, project-based learning, and exploration.
                </p>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
                  <div className="text-center">
                    <div className="text-blue-600 font-bold text-lg">Reading</div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Phonics, comprehension, fluency</p>
                  </div>
                  <div className="text-center">
                    <div className="text-blue-600 font-bold text-lg">Math</div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Number sense, operations, problem-solving</p>
                  </div>
                  <div className="text-center">
                    <div className="text-blue-600 font-bold text-lg">Quran</div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Memorization, tajweed, understanding</p>
                  </div>
                  <div className="text-center">
                    <div className="text-blue-600 font-bold text-lg">Science</div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Observation, exploration, discovery</p>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                viewport={{ once: true }}
                className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg shadow-md"
              >
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Middle School (Grades 6-8)</h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  In middle school, students deepen their understanding of core subjects while developing critical thinking, research, and analytical skills. The curriculum becomes more specialized, with dedicated teachers for each subject area.
                </p>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
                  <div className="text-center">
                    <div className="text-green-600 font-bold text-lg">Literature</div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Analysis, interpretation, writing</p>
                  </div>
                  <div className="text-center">
                    <div className="text-green-600 font-bold text-lg">Algebra</div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Equations, functions, applications</p>
                  </div>
                  <div className="text-center">
                    <div className="text-green-600 font-bold text-lg">Islamic Studies</div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Fiqh, seerah, Islamic history</p>
                  </div>
                  <div className="text-center">
                    <div className="text-green-600 font-bold text-lg">Lab Science</div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Experiments, research, documentation</p>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                viewport={{ once: true }}
                className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg shadow-md"
              >
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">High School (Grades 9-12)</h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  Our high school curriculum prepares students for college and beyond with rigorous academic courses, advanced placement options, and specialized electives. Students develop independence, responsibility, and leadership skills.
                </p>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
                  <div className="text-center">
                    <div className="text-purple-600 font-bold text-lg">Advanced Math</div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Calculus, statistics, advanced algebra</p>
                  </div>
                  <div className="text-center">
                    <div className="text-purple-600 font-bold text-lg">Sciences</div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Biology, chemistry, physics</p>
                  </div>
                  <div className="text-center">
                    <div className="text-purple-600 font-bold text-lg">Islamic Studies</div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Comparative religion, ethics, philosophy</p>
                  </div>
                  <div className="text-center">
                    <div className="text-purple-600 font-bold text-lg">Electives</div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Technology, arts, languages, business</p>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-12 bg-blue-600">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="text-white mb-6 md:mb-0">
              <h2 className="text-2xl font-bold mb-2">Ready to learn more?</h2>
              <p className="text-blue-100">Explore our departments or contact us for more information about our curriculum.</p>
            </div>
            <div className="flex flex-col sm:flex-row gap-4">
              <Link href="/website/academics/departments">
                <Button className="bg-white text-blue-600 hover:bg-blue-50">
                  Explore Departments
                </Button>
              </Link>
              <Link href="/website/contact">
                <Button variant="outline" className="border-white text-white hover:bg-blue-700">
                  Contact Us
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

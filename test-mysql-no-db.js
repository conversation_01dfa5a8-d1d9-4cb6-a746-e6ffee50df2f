const mysql = require('mysql2/promise');

async function testConnectionWithoutDB() {
  console.log('🔌 Testing MySQL connection without specifying database...');
  
  const config = {
    host: '**************',
    port: 3306,
    user: 'alfalasw_school',
    password: 'M1+fOZ8?Q],G',
    // No database specified
    connectTimeout: 10000,
  };

  try {
    console.log('📋 Connecting without database...');
    
    const connection = await mysql.createConnection(config);
    console.log('✅ Successfully connected to MySQL server!');
    
    // List available databases
    console.log('📋 Listing available databases...');
    const [databases] = await connection.execute('SHOW DATABASES');
    console.log('Available databases:', databases.map(db => db.Database));
    
    // Check if our target database exists
    const targetDB = 'alfalasw_school';
    const dbExists = databases.some(db => db.Database === targetDB);
    console.log(`Database '${targetDB}' exists:`, dbExists);
    
    if (!dbExists) {
      console.log(`🔧 Creating database '${targetDB}'...`);
      await connection.execute(`CREATE DATABASE IF NOT EXISTS \`${targetDB}\``);
      console.log(`✅ Database '${targetDB}' created successfully!`);
    }
    
    await connection.end();
    console.log('✅ Connection closed successfully');
    return true;
    
  } catch (error) {
    console.error('❌ Connection failed:', error.message);
    console.error('📊 Error details:', {
      code: error.code,
      errno: error.errno,
      sqlState: error.sqlState
    });
    return false;
  }
}

testConnectionWithoutDB().catch(console.error);

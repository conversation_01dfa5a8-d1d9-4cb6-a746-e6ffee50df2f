import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET all hero slides
export async function GET() {
  try {
    const heroSlides = await prisma.heroSlide.findMany({
      orderBy: {
        order: 'asc'
      }
    })
    
    return NextResponse.json(heroSlides)
  } catch (error) {
    console.error('Error fetching hero slides:', error)
    return NextResponse.json({
      error: error.message || 'Failed to fetch hero slides'
    }, { status: 500 })
  }
}

// POST a new hero slide
export async function POST(request: Request) {
  try {
    const data = await request.json()
    
    // Validate required fields
    if (!data.title || !data.subtitle || !data.imageUrl || !data.ctaText || !data.ctaLink) {
      return NextResponse.json({
        error: 'All fields are required'
      }, { status: 400 })
    }
    
    // Get the highest order value to place the new slide at the end
    const highestOrder = await prisma.heroSlide.findFirst({
      orderBy: {
        order: 'desc'
      },
      select: {
        order: true
      }
    })
    
    const newOrder = highestOrder ? highestOrder.order + 1 : 0
    
    // Create the new slide
    const heroSlide = await prisma.heroSlide.create({
      data: {
        title: data.title,
        subtitle: data.subtitle,
        imageUrl: data.imageUrl,
        ctaText: data.ctaText,
        ctaLink: data.ctaLink,
        isActive: data.isActive !== undefined ? data.isActive : true,
        order: newOrder
      }
    })
    
    return NextResponse.json(heroSlide)
  } catch (error) {
    console.error('Error creating hero slide:', error)
    return NextResponse.json({
      error: error.message || 'Failed to create hero slide'
    }, { status: 500 })
  }
}

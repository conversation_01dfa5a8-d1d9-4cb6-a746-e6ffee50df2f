'use client';

import React, { useEffect, useState, createContext, useContext } from 'react';
import { createPortal } from 'react-dom';
import { X } from 'lucide-react';
import { Button } from './ui/button';
// Helper function for class names
const cn = (...classes: (string | undefined)[]) => {
  return classes.filter(Boolean).join(' ');
};

// Create a context to track if we're inside a RecursionSafeDialog
const RecursionSafeDialogContext = createContext(false);

/**
 * A dialog component that doesn't use Radix UI's focus trapping to avoid recursion issues
 * This is a simpler implementation that doesn't rely on Radix UI's focus management
 */
export function RecursionSafeDialog({
  open,
  onOpenChange,
  children,
  maxWidth = 'max-w-lg',
  className = '',
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  children: React.ReactNode;
  maxWidth?: string;
  className?: string;
}) {
  // Use local state to track open state
  const [isOpen, setIsOpen] = useState(open);
  const [isMounted, setIsMounted] = useState(false);

  // Check if we're on the client side
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Sync with parent open state
  useEffect(() => {
    setIsOpen(open);
  }, [open]);

  // Handle close
  const handleClose = () => {
    setIsOpen(false);
    onOpenChange(false);
  };

  // Handle escape key
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        handleClose();
      }
    };

    document.addEventListener('keydown', handleEscapeKey);
    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [isOpen]);

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const dialogElement = document.getElementById('recursion-safe-dialog');
      if (dialogElement && !dialogElement.contains(event.target as Node) && isOpen) {
        // Uncomment the next line if you want to close on outside click
        // handleClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  // Prevent body scroll when dialog is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  if (!isOpen || !isMounted) return null;

  const dialogContent = (
    <RecursionSafeDialogContext.Provider value={true}>
      <div className="fixed inset-0 z-[99999] flex items-center justify-center p-4 recursion-safe-dialog-container">
        {/* Enhanced Backdrop with stronger blur and animation */}
        <div
          className="fixed inset-0 enhanced-dialog-backdrop backdrop-enter"
          onClick={handleClose}
        />

        {/* Dialog with enhanced animations, shadow, and glassmorphism effect */}
        <div
          id="recursion-safe-dialog"
          className={`relative bg-white/95 dark:bg-gray-900/95 rounded-2xl enhanced-dialog ${maxWidth} w-full max-h-[90vh] overflow-auto p-6 ${className} dialog-enter`}
          role="dialog"
          aria-modal="true"
          style={{
            backdropFilter: 'blur(20px)',
            WebkitBackdropFilter: 'blur(20px)',
          }}
        >
          {/* Enhanced Close button with glassmorphism */}
          <Button
            variant="ghost"
            size="icon"
            className="absolute right-4 top-4 rounded-full h-9 w-9 bg-white/20 hover:bg-white/30 dark:bg-black/20 dark:hover:bg-black/30 backdrop-blur-sm border border-white/20 dark:border-white/10 opacity-80 hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-blue-400/50 transition-all duration-200 disabled:pointer-events-none shadow-lg hover:shadow-xl transform hover:scale-105"
            onClick={handleClose}
          >
            <X className="h-4 w-4 text-gray-700 dark:text-gray-300" />
            <span className="sr-only">Close</span>
          </Button>

          {/* Content */}
          {children}
        </div>
      </div>
    </RecursionSafeDialogContext.Provider>
  );

  // Use createPortal to render the dialog at the document body level
  return createPortal(dialogContent, document.body);
}

// Custom components that mimic Radix UI Dialog components
export function RecursionSafeDialogContent({
  children,
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div className={cn("grid gap-4", className)} {...props}>
      {children}
    </div>
  );
}

export function RecursionSafeDialogHeader({
  children,
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  const isInsideDialog = useContext(RecursionSafeDialogContext);

  if (!isInsideDialog) {
    console.warn("RecursionSafeDialogHeader should be used within RecursionSafeDialog");
  }

  return (
    <div
      className={cn(
        "flex flex-col space-y-1.5 text-center sm:text-left mb-4",
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
}

export function RecursionSafeDialogFooter({
  children,
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  const isInsideDialog = useContext(RecursionSafeDialogContext);

  if (!isInsideDialog) {
    console.warn("RecursionSafeDialogFooter should be used within RecursionSafeDialog");
  }

  return (
    <div
      className={cn(
        "flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 mt-4",
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
}

export function RecursionSafeDialogTitle({
  children,
  className,
  ...props
}: React.HTMLAttributes<HTMLHeadingElement>) {
  const isInsideDialog = useContext(RecursionSafeDialogContext);

  if (!isInsideDialog) {
    console.warn("RecursionSafeDialogTitle should be used within RecursionSafeDialog");
  }

  return (
    <h2
      className={cn(
        "text-lg font-semibold leading-none tracking-tight",
        className
      )}
      {...props}
    >
      {children}
    </h2>
  );
}

export function RecursionSafeDialogDescription({
  children,
  className,
  ...props
}: React.HTMLAttributes<HTMLParagraphElement>) {
  const isInsideDialog = useContext(RecursionSafeDialogContext);

  if (!isInsideDialog) {
    console.warn("RecursionSafeDialogDescription should be used within RecursionSafeDialog");
  }

  return (
    <p
      className={cn("text-sm text-muted-foreground", className)}
      {...props}
    >
      {children}
    </p>
  );
}

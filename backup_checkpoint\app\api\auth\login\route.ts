import { NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'

export async function POST(request: Request) {
  let prisma: PrismaClient | null = null;

  try {
    const { email, password } = await request.json()
    console.log('Login attempt for email:', email)

    // Create a new PrismaClient instance specifically for this request
    prisma = new PrismaClient({
      log: ['error', 'warn'],
      datasources: {
        db: {
          url: process.env.DATABASE_URL
        }
      }
    });

    // Connect to the database
    try {
      await prisma.$connect()
      console.log('Database connection successful')
    } catch (connectionError) {
      console.error('Database connection failed:', connectionError)

      // Try to create a sample user if this might be the first login
      try {
        const createUserResponse = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/auth/create-sample-user`)
        const createUserData = await createUserResponse.json()

        if (createUserData.status === 'success') {
          console.log('Created sample user for first login')

          // If the sample user was created and the login credentials match, proceed with login
          if (email === createUserData.user.email && password === 'admin123') {
            return NextResponse.json({
              user: {
                id: 'sample-user-id',
                email: createUserData.user.email,
                name: createUserData.user.name,
                role: createUserData.user.role
              },
              message: 'Login successful with sample user'
            })
          }
        }
      } catch (createUserError) {
        console.error('Error creating sample user:', createUserError)
      }

      return NextResponse.json(
        {
          error: 'Database connection failed. Please check your database configuration.',
          details: connectionError instanceof Error ? connectionError.message : 'Unknown error'
        },
        { status: 500 }
      )
    }

    // Find user
    const user = await prisma.user.findUnique({
      where: { email },
    })

    console.log('User found:', user ? 'Yes' : 'No')

    if (!user) {
      // If no user found, try to create a sample admin user
      if (email === '<EMAIL>' && password === 'admin123') {
        try {
          console.log('Attempting to create sample admin user')
          const createUserResponse = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/auth/create-sample-user`)
          const createUserData = await createUserResponse.json()

          if (createUserData.status === 'success') {
            console.log('Sample admin user created or already exists')

            // Try to find the user again
            const newUser = await prisma.user.findUnique({
              where: { email },
            })

            if (newUser) {
              console.log('Sample admin user found after creation')
              // Continue with the newly created user
              return await handleValidUser(newUser, password, prisma)
            }
          }
        } catch (createUserError) {
          console.error('Error creating sample admin user:', createUserError)
        }
      }

      return NextResponse.json(
        { error: 'Invalid credentials' },
        { status: 401 }
      )
    }

    // Check if account is locked or inactive
    if (user.status !== 'active') {
      console.log(`Account status is ${user.status}`)
      return NextResponse.json(
        { error: `Account is ${user.status}. Please contact an administrator.` },
        { status: 401 }
      )
    }

    // Verify password
    try {
      console.log('Verifying password...')
      const isValid = await bcrypt.compare(password, user.password)
      console.log('Password valid:', isValid ? 'Yes' : 'No')

      if (!isValid) {
        return NextResponse.json(
          { error: 'Invalid credentials' },
          { status: 401 }
        )
      }

      // If password is valid, proceed with login
      return await handleValidUser(user, password, prisma)
    } catch (error) {
      console.error('Error comparing passwords:', error)
      return NextResponse.json(
        { error: 'Error validating credentials' },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Unexpected error during login:', error)
    return NextResponse.json(
      { error: 'Login failed. Please try again later.' },
      { status: 500 }
    )
  } finally {
    // Clean up the connection
    if (prisma) {
      await prisma.$disconnect().catch(e => console.error('Error disconnecting from database:', e))
    }
  }
}

// Helper function to handle a valid user login
async function handleValidUser(user: any, password: string, prisma: PrismaClient) {
  try {

    // Update last login
    try {
      console.log('Updating last login time for user:', user.id)
      await prisma.user.update({
        where: { id: user.id },
        data: { lastLogin: new Date() },
      })
    } catch (updateError) {
      console.error('Error updating last login time:', updateError)
      // Continue with login even if this fails
    }

    // Generate JWT token
    console.log('Generating JWT token...')
    const jwtSecret = process.env.JWT_SECRET || 'your-secret-key-here';
    const token = jwt.sign(
      {
        userId: user.id,
        email: user.email,
        role: user.role
      },
      jwtSecret,
      { expiresIn: '24h' }
    )

    // Set HTTP-only cookie
    const response = NextResponse.json(
      {
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role
        },
        message: 'Login successful'
      },
      { status: 200 }
    )

    // Set the token cookie with more permissive settings for development
    // Log the token for debugging
    console.log('Setting auth token cookie:', token.substring(0, 20) + '...')

    response.cookies.set('token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production', // Set to false for development, true for production
      sameSite: 'lax', // Use 'lax' instead of 'strict' for better compatibility
      maxAge: 86400, // 24 hours
      path: '/' // Ensure the cookie is available for all paths
    })

    // Also set a non-httpOnly cookie for client-side access (for debugging)
    response.cookies.set('auth_status', 'logged_in', {
      httpOnly: false,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 86400,
      path: '/'
    })

    console.log('Login successful for user:', user.email)
    return response
  } catch (error) {
    console.error('Error in handleValidUser:', error)
    return NextResponse.json(
      { error: 'Login failed', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}
'use client'

import React from 'react'
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, Card<PERSON>itle } from '../ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '../ui/tabs'
import { Badge } from '../ui/badge'
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts'
import { cn } from '@/app/lib/utils'
import { User } from 'lucide-react'

interface Mark {
  id: string
  studentId: string
  subject: string
  marks: number
  className: string
  term: string
  createdAt: string
  updatedAt: string
  student?: {
    name: string
    gender: string
  }
}

interface StudentPerformance {
  studentId: string
  studentName: string
  marks: number[]
  average: number
}

interface AcademicPerformanceProps {
  recentMarks: Mark[]
  topPerformers: StudentPerformance[]
  failingStudents: StudentPerformance[]
}

export function AcademicPerformance({
  recentMarks,
  topPerformers,
  failingStudents
}: AcademicPerformanceProps) {
  // Prepare data for the bar chart
  const chartData = recentMarks.map(mark => ({
    name: mark.subject,
    score: mark.marks,
    student: mark.student?.name || 'Unknown',
  })).slice(0, 7); // Limit to 7 entries for better visualization

  return (
    <div className="mb-6">
      <Tabs defaultValue="recent" className="w-full">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 sm:mb-0">
            Academic Performance
          </h2>
          <TabsList className="bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
            <TabsTrigger value="recent" className="rounded-md text-sm px-3 py-1.5">
              Recent Grades
            </TabsTrigger>
            <TabsTrigger value="top" className="rounded-md text-sm px-3 py-1.5">
              Top Performers
            </TabsTrigger>
            <TabsTrigger value="failing" className="rounded-md text-sm px-3 py-1.5">
              Needs Improvement
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="recent" className="mt-0">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="border-0 shadow-lg dark:bg-gray-800">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg font-medium">Recent Test Results</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentMarks.slice(0, 5).map((mark) => (
                    <div key={mark.id} className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className={cn(
                          "h-9 w-9 mr-3 rounded-full flex items-center justify-center",
                          mark.student?.gender === 'Female' ? 'bg-pink-100 text-pink-800' : 'bg-blue-100 text-blue-800'
                        )}>
                          {mark.student?.name?.charAt(0) || <User size={16} />}
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            {mark.student?.name || 'Unknown Student'}
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            {mark.subject} • {mark.className}
                          </p>
                        </div>
                      </div>
                      <Badge className={cn(
                        "ml-auto",
                        mark.marks >= 80 ? "bg-green-100 text-green-800 hover:bg-green-100" :
                        mark.marks >= 60 ? "bg-yellow-100 text-yellow-800 hover:bg-yellow-100" :
                        "bg-red-100 text-red-800 hover:bg-red-100"
                      )}>
                        {mark.marks}%
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg dark:bg-gray-800">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg font-medium">Performance by Subject</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[250px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={chartData}
                      margin={{ top: 10, right: 10, left: 0, bottom: 20 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" vertical={false} />
                      <XAxis
                        dataKey="name"
                        tick={{ fontSize: 12 }}
                        angle={-45}
                        textAnchor="end"
                        height={60}
                      />
                      <YAxis domain={[0, 100]} />
                      <Tooltip
                        formatter={(value) => [`${value}%`, 'Score']}
                        labelFormatter={(label) => `Subject: ${label}`}
                        contentStyle={{
                          backgroundColor: 'rgba(255, 255, 255, 0.9)',
                          borderRadius: '8px',
                          border: 'none',
                          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                        }}
                      />
                      <Bar
                        dataKey="score"
                        fill="#6366F1"
                        radius={[4, 4, 0, 0]}
                        name="Score"
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="top" className="mt-0">
          <Card className="border-0 shadow-lg dark:bg-gray-800">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-medium">Top Performing Students</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {topPerformers.map((student, index) => (
                  <div key={student.studentId} className="flex items-center">
                    <div className="flex-shrink-0 mr-4">
                      <div className="w-10 h-10 rounded-full bg-indigo-100 dark:bg-indigo-900/50 flex items-center justify-center text-indigo-600 dark:text-indigo-400 font-bold">
                        {index + 1}
                      </div>
                    </div>
                    <div className="flex-grow">
                      <p className="text-sm font-medium text-gray-900 dark:text-white">{student.studentName}</p>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5 mt-2">
                        <div
                          className="bg-indigo-600 h-2.5 rounded-full"
                          style={{ width: `${student.average}%` }}
                        ></div>
                      </div>
                    </div>
                    <div className="flex-shrink-0 ml-4">
                      <span className="text-lg font-bold text-indigo-600 dark:text-indigo-400">
                        {student.average.toFixed(1)}%
                      </span>
                    </div>
                  </div>
                ))}

                {topPerformers.length === 0 && (
                  <div className="text-center py-6">
                    <p className="text-gray-500 dark:text-gray-400">No top performers data available</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="failing" className="mt-0">
          <Card className="border-0 shadow-lg dark:bg-gray-800">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-medium">Students Needing Improvement</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {failingStudents.map((student) => (
                  <div key={student.studentId} className="flex items-center">
                    <div className="flex-shrink-0 mr-4">
                      <div className="w-10 h-10 rounded-full bg-red-100 dark:bg-red-900/50 flex items-center justify-center text-red-600 dark:text-red-400">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                      </div>
                    </div>
                    <div className="flex-grow">
                      <p className="text-sm font-medium text-gray-900 dark:text-white">{student.studentName}</p>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5 mt-2">
                        <div
                          className="bg-red-600 h-2.5 rounded-full"
                          style={{ width: `${student.average}%` }}
                        ></div>
                      </div>
                    </div>
                    <div className="flex-shrink-0 ml-4">
                      <span className="text-lg font-bold text-red-600 dark:text-red-400">
                        {student.average.toFixed(1)}%
                      </span>
                    </div>
                  </div>
                ))}

                {failingStudents.length === 0 && (
                  <div className="text-center py-6">
                    <p className="text-gray-500 dark:text-gray-400">No students currently need improvement</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// Fallback data structure for error cases
const DEFAULT_PROMOTION_POLICY = {
  id: 'default-policy',
  passingScore: 50,
  maxFailedSubjects: 2,
  applyToAllClasses: true,
  isActive: true,
  description: 'Default promotion policy: Students who score below 50 in three or more subjects will be detained.',
  createdBy: 'system',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString()
}

// GET - Fetch promotion policy
export async function GET() {
  try {
    // Try to get existing promotion policy from database
    let policy = await prisma.promotionPolicy.findFirst({
      where: { isActive: true },
      orderBy: { createdAt: 'desc' }
    })

    // If no policy exists, create default policy
    if (!policy) {
      policy = await prisma.promotionPolicy.create({
        data: {
          passingScore: 50,
          maxFailedSubjects: 2,
          applyToAllClasses: true,
          isActive: true,
          description: 'Default promotion policy: Students who score below 50 in three or more subjects will be detained.',
          createdBy: 'system'
        }
      })
    }

    return NextResponse.json(policy)
  } catch (error) {
    console.error('Error fetching promotion policy:', error)
    // Fallback to default policy if database error
    return NextResponse.json(DEFAULT_PROMOTION_POLICY)
  }
}

// POST - Create or update promotion policy
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { passingScore, maxFailedSubjects, applyToAllClasses, description, createdBy } = body

    // Validate input
    if (typeof passingScore !== 'number' || passingScore < 0 || passingScore > 100) {
      return NextResponse.json(
        { error: 'Passing score must be a number between 0 and 100' },
        { status: 400 }
      )
    }

    if (typeof maxFailedSubjects !== 'number' || maxFailedSubjects < 0 || maxFailedSubjects > 10) {
      return NextResponse.json(
        { error: 'Max failed subjects must be a number between 0 and 10' },
        { status: 400 }
      )
    }

    // Deactivate existing policies
    await prisma.promotionPolicy.updateMany({
      where: { isActive: true },
      data: { isActive: false }
    })

    // Create new policy
    const newPolicy = await prisma.promotionPolicy.create({
      data: {
        passingScore,
        maxFailedSubjects,
        applyToAllClasses: applyToAllClasses ?? true,
        isActive: true,
        description: description || `Students who score below ${passingScore} in ${maxFailedSubjects + 1} or more subjects will be detained.`,
        createdBy: createdBy || 'admin'
      }
    })

    return NextResponse.json(newPolicy)
  } catch (error) {
    console.error('Error creating promotion policy:', error)
    return NextResponse.json(
      { error: 'Failed to create promotion policy' },
      { status: 500 }
    )
  }
}

// PUT - Update existing promotion policy
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, passingScore, maxFailedSubjects, applyToAllClasses, description } = body

    // Validate input
    if (typeof passingScore !== 'number' || passingScore < 0 || passingScore > 100) {
      return NextResponse.json(
        { error: 'Passing score must be a number between 0 and 100' },
        { status: 400 }
      )
    }

    if (typeof maxFailedSubjects !== 'number' || maxFailedSubjects < 0 || maxFailedSubjects > 10) {
      return NextResponse.json(
        { error: 'Max failed subjects must be a number between 0 and 10' },
        { status: 400 }
      )
    }

    const updatedPolicy = await prisma.promotionPolicy.update({
      where: { id },
      data: {
        passingScore,
        maxFailedSubjects,
        applyToAllClasses: applyToAllClasses ?? true,
        description: description || `Students who score below ${passingScore} in ${maxFailedSubjects + 1} or more subjects will be detained.`,
        updatedAt: new Date()
      }
    })

    return NextResponse.json(updatedPolicy)
  } catch (error) {
    console.error('Error updating promotion policy:', error)
    return NextResponse.json(
      { error: 'Failed to update promotion policy' },
      { status: 500 }
    )
  }
}

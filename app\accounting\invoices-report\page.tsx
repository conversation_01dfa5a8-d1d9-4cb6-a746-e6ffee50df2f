'use client'

import React, { useState, useEffect, useRef } from 'react'
import './invoices-report.css'
import Sidebar from '../../components/Sidebar'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'
import { But<PERSON> } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Label } from '../../components/ui/label'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '../../components/ui/tabs'
import {
  FileText,
  Calendar,
  Download,
  Printer,
  RefreshCw,
  Filter,
  X,
  CheckCircle,
  AlertCircle,
  Loader2,
  DollarSign,
  CreditCard,
  BookOpen,
  GraduationCap,
  Library,
  Search,
  ChevronDown,
  ChevronUp,
  FileSpreadsheet
} from 'lucide-react'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../components/ui/table'
import { useQuery } from '@tanstack/react-query'
import { useToast } from '../../components/ui/use-toast'
import { format } from 'date-fns'
import { useReactToPrint } from 'react-to-print'
import dynamic from 'next/dynamic'

// Import Header component with no SSR to avoid hydration issues
const Header = dynamic(() => import('../../components/Header'), {
  ssr: false
})

// Define interfaces
interface Student {
  id: string
  sid: string
  name: string
  className: string
}

interface Class {
  id: string
  name: string
}

interface PaymentRecord {
  id: string
  invoiceNumber: string
  studentId: string
  studentName: string
  className: string
  paymentDate: string
  forMonth?: string | null
  amount: number
  paymentPurpose: string
  paymentMethod: string
  transferId?: string
  status: string
  createdAt: string
}

export default function InvoicesReportPage() {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false)
  const [activeTab, setActiveTab] = useState('tuition-fee')
  const [selectedClass, setSelectedClass] = useState('all')
  const [selectedMonth, setSelectedMonth] = useState('all')
  const [selectedYear, setSelectedYear] = useState('2023-2024')
  const [isFilterExpanded, setIsFilterExpanded] = useState(true)
  const [isLoading, setIsLoading] = useState(false)
  const [paymentRecords, setPaymentRecords] = useState<PaymentRecord[]>([])
  const printRef = useRef<HTMLDivElement>(null)
  const { toast } = useToast()

  // Academic years are now selected from the dropdown

  // Fetch classes
  const { data: classes = [] } = useQuery({
    queryKey: ['classes'],
    queryFn: async () => {
      const response = await fetch('/api/classes')
      if (!response.ok) {
        throw new Error('Failed to fetch classes')
      }
      return response.json()
    }
  })

  // Update payment purpose based on active tab
  useEffect(() => {
    // Reset filters when tab changes
    setSelectedClass('all')
    setSelectedMonth('all')
    setPaymentRecords([])
  }, [activeTab])

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  const toggleMobileSidebar = () => {
    setMobileSidebarOpen(!mobileSidebarOpen)
  }

  // Handle filter application
  const applyFilters = async () => {
    setIsLoading(true)

    try {
      // Determine payment purpose based on active tab
      let paymentPurpose = ''
      switch (activeTab) {
        case 'tuition-fee':
          paymentPurpose = 'Tuition Fee (Monthly)'
          break
        case 'registration-fee':
          paymentPurpose = 'Registration Fee'
          break
        case 'exam-fee':
          paymentPurpose = 'Exam Fee'
          break
        case 'library-fee':
          paymentPurpose = 'Library Fee'
          break
      }

      // Build query parameters
      const queryParams = new URLSearchParams()
      if (selectedClass && selectedClass !== 'all') queryParams.append('className', selectedClass)
      if (paymentPurpose) queryParams.append('paymentPurpose', paymentPurpose)
      // Add academic year to query
      if (selectedYear) {
        queryParams.append('academicYear', selectedYear)
      }

      if (selectedMonth && selectedMonth !== 'all') {
        // Convert month and year to date range
        // Extract the first year from the academic year (e.g., "2023" from "2023-2024")
        const yearPart = parseInt(selectedYear.split('-')[0])
        const monthIndex = parseInt(selectedMonth) - 1 // JavaScript months are 0-indexed
        const startDate = new Date(yearPart, monthIndex, 1)
        const endDate = new Date(yearPart, monthIndex + 1, 0) // Last day of month

        queryParams.append('startDate', startDate.toISOString().split('T')[0])
        queryParams.append('endDate', endDate.toISOString().split('T')[0])
      }

      // Fetch payment records
      const response = await fetch(`/api/accounting/payment-report?${queryParams.toString()}`)

      if (!response.ok) {
        throw new Error('Failed to fetch payment records')
      }

      const data = await response.json()
      setPaymentRecords(data)

      toast({
        title: 'Report Generated',
        description: `Found ${data.length} payment records`,
      })
    } catch (error) {
      console.error('Error fetching payment records:', error)
      toast({
        title: 'Error',
        description: 'Failed to generate report. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Reset filters
  const resetFilters = () => {
    setSelectedClass('all')
    setSelectedMonth('all')
    setSelectedYear('2023-2024')
    setPaymentRecords([])

    toast({
      title: 'Filters Reset',
      description: 'All filters have been cleared',
    })
  }

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ET', {
      style: 'currency',
      currency: 'ETB',
      minimumFractionDigits: 2
    }).format(amount)
  }

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM dd, yyyy')
    } catch (error) {
      return dateString
    }
  }

  // Handle print
  const handlePrint = useReactToPrint({
    content: () => printRef.current,
    documentTitle: `Invoice Report - ${activeTab} - ${new Date().toLocaleDateString()}`,
    onAfterPrint: () => {
      toast({
        title: 'Print Successful',
        description: 'Invoice report has been sent to printer',
      })
    },
  })

  // Handle export to CSV
  const handleExport = () => {
    if (!paymentRecords || paymentRecords.length === 0) {
      toast({
        title: 'No Data to Export',
        description: 'There are no invoice records to export',
        variant: 'destructive'
      })
      return
    }

    // Create CSV content
    const headers = [
      'Invoice Number',
      'Student ID',
      'Student Name',
      'Class',
      'Payment Date',
      'Amount',
      'Payment Purpose',
      'Status'
    ]

    const csvContent = [
      headers.join(','),
      ...paymentRecords.map(payment => [
        payment.invoiceNumber,
        payment.studentId,
        `"${payment.studentName}"`, // Wrap in quotes to handle commas in names
        payment.className,
        payment.paymentDate,
        payment.amount,
        `"${payment.paymentPurpose}"`,
        payment.status
      ].join(','))
    ].join('\n')

    // Create a blob and download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.setAttribute('href', url)
    link.setAttribute('download', `invoice_report_${activeTab}_${new Date().toISOString().split('T')[0]}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    toast({
      title: 'Export Successful',
      description: 'Invoice report has been exported to CSV',
    })
  }

  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
      <Sidebar
        isCollapsed={sidebarCollapsed}
        toggleSidebar={toggleSidebar}
        isMobileOpen={mobileSidebarOpen}
      />

      <main className={`main-content ${sidebarCollapsed ? 'sidebar-collapsed' : ''} w-full h-screen overflow-y-auto`}>
        <Header toggleMobileSidebar={toggleMobileSidebar} />

        <div className="container mx-auto px-4 py-6">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold text-gray-800 dark:text-white">
              Invoices Report
            </h1>
          </div>

          <Card className="shadow-lg border-0">
            <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border-b">
              <CardTitle className="flex items-center text-xl">
                <FileSpreadsheet className="mr-2 h-5 w-5 text-purple-600 dark:text-purple-400" />
                Invoice Reports
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                <TabsList className="w-full grid grid-cols-4 mb-8 rounded-lg bg-blue-50 dark:bg-gray-800 p-1">
                  <TabsTrigger
                    value="tuition-fee"
                    className="rounded-md py-2 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:shadow-sm"
                  >
                    <DollarSign className="h-4 w-4 mr-2 text-blue-600 dark:text-blue-400" />
                    Tuition Fee
                  </TabsTrigger>
                  <TabsTrigger
                    value="registration-fee"
                    className="rounded-md py-2 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:shadow-sm"
                  >
                    <GraduationCap className="h-4 w-4 mr-2 text-green-600 dark:text-green-400" />
                    Registration Fee
                  </TabsTrigger>
                  <TabsTrigger
                    value="exam-fee"
                    className="rounded-md py-2 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:shadow-sm"
                  >
                    <BookOpen className="h-4 w-4 mr-2 text-amber-600 dark:text-amber-400" />
                    Exam Fee
                  </TabsTrigger>
                  <TabsTrigger
                    value="library-fee"
                    className="rounded-md py-2 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:shadow-sm"
                  >
                    <Library className="h-4 w-4 mr-2 text-purple-600 dark:text-purple-400" />
                    Library Fee
                  </TabsTrigger>
                </TabsList>

                <div className="space-y-6">
                  {/* Filter Section */}
                  <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                    <div
                      className="flex justify-between items-center p-4 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 cursor-pointer"
                      onClick={() => setIsFilterExpanded(!isFilterExpanded)}
                    >
                      <div className="flex items-center">
                        <Filter className="h-4 w-4 mr-2 text-blue-600" />
                        <span className="font-medium">Report Filters</span>
                      </div>
                      <div>
                        {isFilterExpanded ? (
                          <ChevronUp className="h-4 w-4 text-gray-500" />
                        ) : (
                          <ChevronDown className="h-4 w-4 text-gray-500" />
                        )}
                      </div>
                    </div>

                    {isFilterExpanded && (
                      <div className="p-4">
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="academic-year" className="text-sm font-medium flex items-center">
                              <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                              Academic Year
                            </Label>
                            <Select value={selectedYear} onValueChange={(value) => setSelectedYear(value)}>
                              <SelectTrigger id="academic-year" className="h-10">
                                <SelectValue placeholder="Select academic year" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="2023-2024">2023-2024</SelectItem>
                                <SelectItem value="2024-2025">2024-2025</SelectItem>
                                <SelectItem value="2025-2026">2025-2026</SelectItem>
                                <SelectItem value="2026-2027">2026-2027</SelectItem>
                                <SelectItem value="2027-2028">2027-2028</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="month" className="text-sm font-medium flex items-center">
                              <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                              Month
                            </Label>
                            <Select value={selectedMonth} onValueChange={setSelectedMonth}>
                              <SelectTrigger id="month" className="h-10">
                                <SelectValue placeholder="Select month" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="all">All Months</SelectItem>
                                <SelectItem value="1">January</SelectItem>
                                <SelectItem value="2">February</SelectItem>
                                <SelectItem value="3">March</SelectItem>
                                <SelectItem value="4">April</SelectItem>
                                <SelectItem value="5">May</SelectItem>
                                <SelectItem value="6">June</SelectItem>
                                <SelectItem value="7">July</SelectItem>
                                <SelectItem value="8">August</SelectItem>
                                <SelectItem value="9">September</SelectItem>
                                <SelectItem value="10">October</SelectItem>
                                <SelectItem value="11">November</SelectItem>
                                <SelectItem value="12">December</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="class" className="text-sm font-medium flex items-center">
                              <BookOpen className="h-4 w-4 mr-2 text-gray-500" />
                              Class
                            </Label>
                            <Select value={selectedClass} onValueChange={setSelectedClass}>
                              <SelectTrigger id="class" className="h-10">
                                <SelectValue placeholder="Select class" />
                              </SelectTrigger>
                              <SelectContent className="max-h-[200px] overflow-y-auto">
                                <SelectItem value="all">All Classes</SelectItem>
                                {classes.map((cls: Class) => (
                                  <SelectItem key={cls.id} value={cls.name}>
                                    {cls.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>

                          <div className="flex items-end space-x-2">
                            <Button
                              onClick={applyFilters}
                              className="h-10 bg-blue-600 hover:bg-blue-700"
                              disabled={isLoading}
                            >
                              {isLoading ? (
                                <>
                                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                  Loading...
                                </>
                              ) : (
                                <>
                                  <Search className="h-4 w-4 mr-2" />
                                  Generate Report
                                </>
                              )}
                            </Button>
                            <Button
                              variant="outline"
                              onClick={resetFilters}
                              className="h-10"
                              disabled={isLoading}
                            >
                              <X className="h-4 w-4 mr-2" />
                              Reset
                            </Button>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Report Display Section */}
                <div className="mt-6">
                  {isLoading ? (
                    <div className="flex items-center justify-center h-64 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                      <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
                      <span className="ml-2 text-lg">Generating report...</span>
                    </div>
                  ) : paymentRecords.length > 0 ? (
                    <div>
                      <div className="flex justify-between items-center mb-4">
                        <h3 className="text-lg font-semibold flex items-center">
                          <FileText className="h-5 w-5 mr-2 text-blue-600" />
                          Invoice Report Results
                          <span className="ml-2 text-sm font-normal text-gray-500">
                            ({paymentRecords.length} records found)
                          </span>
                        </h3>
                        <div className="flex space-x-2">
                          <Button variant="outline" size="sm" onClick={handlePrint} className="text-sm">
                            <Printer className="h-4 w-4 mr-2" />
                            Print
                          </Button>
                          <Button variant="outline" size="sm" onClick={handleExport} className="text-sm">
                            <Download className="h-4 w-4 mr-2" />
                            Export
                          </Button>
                          <Button variant="outline" size="sm" onClick={applyFilters} className="text-sm">
                            <RefreshCw className="h-4 w-4 mr-2" />
                            Refresh
                          </Button>
                        </div>
                      </div>

                      {/* Printable Report Section */}
                      <div ref={printRef}>
                        <div className="print-header hidden print:block mb-6">
                          <h1 className="text-2xl font-bold text-center">Alfalah Islamic School</h1>
                          <h2 className="text-xl font-semibold text-center">
                            {activeTab === 'tuition-fee' && 'Tuition Fee Invoice Report'}
                            {activeTab === 'registration-fee' && 'Registration Fee Invoice Report'}
                            {activeTab === 'exam-fee' && 'Exam Fee Invoice Report'}
                            {activeTab === 'library-fee' && 'Library Fee Invoice Report'}
                          </h2>
                          <div className="text-center text-gray-600 mt-2">
                            <p>Academic Year: {selectedYear}</p>
                            {selectedClass && selectedClass !== 'all' && <p>Class: {selectedClass}</p>}
                            {selectedMonth && selectedMonth !== 'all' && (
                              <p>Month: {new Date(0, parseInt(selectedMonth) - 1).toLocaleString('default', { month: 'long' })}</p>
                            )}
                            <p>Generated on: {new Date().toLocaleDateString()}</p>
                          </div>
                        </div>

                        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                          <div className="overflow-x-auto w-full">
                            <Table className="w-full">
                              <TableHeader className="bg-gray-50 dark:bg-gray-800 sticky top-0">
                                <TableRow>
                                  <TableHead className="w-[120px]">Invoice #</TableHead>
                                  <TableHead className="w-[180px]">Student</TableHead>
                                  <TableHead className="w-[100px]">Class</TableHead>
                                  <TableHead className="w-[120px]">Date</TableHead>
                                  <TableHead className="w-[120px]">Amount</TableHead>
                                  <TableHead className="w-[100px]">Status</TableHead>
                                </TableRow>
                              </TableHeader>
                              <TableBody>
                                {paymentRecords.map((payment) => (
                                  <TableRow key={payment.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                                    <TableCell className="font-medium w-[120px]">{payment.invoiceNumber}</TableCell>
                                    <TableCell className="w-[180px]">
                                      <div className="flex flex-col">
                                        <span>{payment.studentName}</span>
                                        <span className="text-xs text-gray-500">{payment.studentId}</span>
                                      </div>
                                    </TableCell>
                                    <TableCell className="w-[100px]">{payment.className}</TableCell>
                                    <TableCell className="w-[120px]">{formatDate(payment.paymentDate)}</TableCell>
                                    <TableCell className="font-medium w-[120px]">{formatCurrency(payment.amount)}</TableCell>
                                    <TableCell className="w-[100px]">
                                      <span className={`status-badge inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                        payment.status === 'paid'
                                          ? 'status-paid bg-green-100 text-green-800'
                                          : 'status-unpaid bg-amber-100 text-amber-800'
                                      }`}>
                                        {payment.status === 'paid' ? (
                                          <CheckCircle className="h-3 w-3 mr-1" />
                                        ) : (
                                          <AlertCircle className="h-3 w-3 mr-1" />
                                        )}
                                        {payment.status === 'paid' ? 'Paid' : 'Unpaid'}
                                      </span>
                                    </TableCell>
                                  </TableRow>
                                ))}
                              </TableBody>
                            </Table>
                          </div>
                        </div>

                        {/* Summary Section */}
                        <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div className="summary-card bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
                            <h4 className="text-sm font-medium text-gray-500 mb-2">Total Amount</h4>
                            <p className="text-2xl font-bold text-blue-600">
                              {formatCurrency(
                                paymentRecords.reduce((sum, payment) => sum + payment.amount, 0)
                              )}
                            </p>
                          </div>
                          <div className="summary-card bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
                            <h4 className="text-sm font-medium text-gray-500 mb-2">Paid Invoices</h4>
                            <p className="text-2xl font-bold text-green-600">
                              {paymentRecords.filter(p => p.status === 'paid').length}
                            </p>
                          </div>
                          <div className="summary-card bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
                            <h4 className="text-sm font-medium text-gray-500 mb-2">Unpaid Invoices</h4>
                            <p className="text-2xl font-bold text-amber-600">
                              {paymentRecords.filter(p => p.status !== 'paid').length}
                            </p>
                          </div>
                        </div>

                        <div className="print-footer hidden print:block mt-8 text-center text-sm text-gray-500">
                          <p>© {new Date().getFullYear()} Alfalah Islamic School - All Rights Reserved</p>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center h-64 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                      <FileText className="h-12 w-12 mb-2 text-gray-300" />
                      <p className="text-lg text-gray-500">No invoice records found</p>
                      <p className="text-sm text-gray-400 mt-1">Adjust your filters or select different criteria</p>
                    </div>
                  )}
                </div>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </main>

      {/* Overlay for mobile menu */}
      {mobileSidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 md:hidden z-40"
          onClick={toggleMobileSidebar}
        ></div>
      )}
    </div>
  )
}

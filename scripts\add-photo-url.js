// Script to add photoUrl column to student table
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Checking if photoUrl column exists in student table...');
    
    // First check if the column already exists
    const tableInfo = await prisma.$queryRaw`DESCRIBE student`;
    const columnExists = tableInfo.some(column => column.Field === 'photoUrl');
    
    if (columnExists) {
      console.log('photoUrl column already exists in student table.');
    } else {
      console.log('photoUrl column does not exist. Adding it now...');
      
      // Add the photoUrl column
      await prisma.$executeRaw`ALTER TABLE student ADD COLUMN photoUrl VARCHAR(191) NULL`;
      
      console.log('photoUrl column added successfully!');
    }
    
    // Verify the column was added
    const updatedTableInfo = await prisma.$queryRaw`DESCRIBE student`;
    console.log('\nUpdated student table schema:');
    updatedTableInfo.forEach(column => {
      console.log(`${column.Field}: ${column.Type} ${column.Null === 'YES' ? '(nullable)' : '(required)'}`);
    });
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();

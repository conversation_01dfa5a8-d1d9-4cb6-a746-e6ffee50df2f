import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET all journal vouchers
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const paidTo = searchParams.get('paidTo')
    const paymentMethod = searchParams.get('paymentMethod')

    // Build filter conditions
    const where: any = {}

    if (status) {
      where.status = status
    }

    if (paidTo) {
      where.paidTo = {
        contains: paidTo
      }
    }

    if (paymentMethod) {
      where.paymentMethod = paymentMethod
    }

    // Fetch vouchers with filters
    const vouchers = await prisma.journalVoucher.findMany({
      where,
      orderBy: {
        date: 'desc'
      }
    })

    return NextResponse.json(vouchers)
  } catch (error) {
    console.error('Error fetching journal vouchers:', error)
    return NextResponse.json(
      { error: 'Failed to fetch journal vouchers' },
      { status: 500 }
    )
  }
}

// CREATE a new journal voucher
export async function POST(request: Request) {
  try {
    const data = await request.json()

    // Validate required fields
    if (!data.paidTo || !data.accountCode || !data.amount || !data.paymentMethod || !data.purpose) {
      return NextResponse.json(
        { error: 'Paid to, account code, amount, payment method, and purpose are required' },
        { status: 400 }
      )
    }

    // Generate a unique voucher number
    const date = new Date()
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')

    // Count existing vouchers to generate sequential number
    const voucherCount = await prisma.journalVoucher.count()
    const sequentialNumber = String(voucherCount + 1).padStart(3, '0')

    const voucherNo = `JV-${year}${month}-${sequentialNumber}`

    // Create the voucher
    const voucher = await prisma.journalVoucher.create({
      data: {
        voucherNo,
        date: data.date ? new Date(data.date) : new Date(),
        paidTo: data.paidTo,
        accountCode: data.accountCode,
        amount: parseFloat(data.amount),
        paymentMethod: data.paymentMethod,
        chequeNo: data.paymentMethod === 'cheque' ? data.chequeNo : null,
        purpose: data.purpose,
        status: 'pending'
      }
    })

    return NextResponse.json(voucher, { status: 201 })
  } catch (error) {
    console.error('Error creating journal voucher:', error)
    return NextResponse.json(
      { error: 'Failed to create journal voucher' },
      { status: 500 }
    )
  }
}

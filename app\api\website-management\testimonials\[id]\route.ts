import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET a specific testimonial
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id
    
    const testimonial = await prisma.testimonial.findUnique({
      where: { id }
    })
    
    if (!testimonial) {
      return NextResponse.json({
        error: 'Testimonial not found'
      }, { status: 404 })
    }
    
    return NextResponse.json(testimonial)
  } catch (error) {
    console.error('Error fetching testimonial:', error)
    return NextResponse.json({
      error: error.message || 'Failed to fetch testimonial'
    }, { status: 500 })
  }
}

// PUT (update) a testimonial
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id
    const data = await request.json()
    
    // Check if the testimonial exists
    const existingTestimonial = await prisma.testimonial.findUnique({
      where: { id }
    })
    
    if (!existingTestimonial) {
      return NextResponse.json({
        error: 'Testimonial not found'
      }, { status: 404 })
    }
    
    // Validate rating if provided
    if (data.rating !== undefined) {
      const rating = Number(data.rating)
      if (isNaN(rating) || rating < 1 || rating > 5) {
        return NextResponse.json({
          error: 'Rating must be a number between 1 and 5'
        }, { status: 400 })
      }
    }
    
    // Update the testimonial
    const updatedTestimonial = await prisma.testimonial.update({
      where: { id },
      data: {
        name: data.name !== undefined ? data.name : existingTestimonial.name,
        role: data.role !== undefined ? data.role : existingTestimonial.role,
        content: data.content !== undefined ? data.content : existingTestimonial.content,
        imageUrl: data.imageUrl !== undefined ? data.imageUrl : existingTestimonial.imageUrl,
        rating: data.rating !== undefined ? Number(data.rating) : existingTestimonial.rating,
        isActive: data.isActive !== undefined ? data.isActive : existingTestimonial.isActive,
        order: data.order !== undefined ? data.order : existingTestimonial.order
      }
    })
    
    return NextResponse.json(updatedTestimonial)
  } catch (error) {
    console.error('Error updating testimonial:', error)
    return NextResponse.json({
      error: error.message || 'Failed to update testimonial'
    }, { status: 500 })
  }
}

// DELETE a testimonial
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id
    
    // Check if the testimonial exists
    const existingTestimonial = await prisma.testimonial.findUnique({
      where: { id }
    })
    
    if (!existingTestimonial) {
      return NextResponse.json({
        error: 'Testimonial not found'
      }, { status: 404 })
    }
    
    // Delete the testimonial
    await prisma.testimonial.delete({
      where: { id }
    })
    
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting testimonial:', error)
    return NextResponse.json({
      error: error.message || 'Failed to delete testimonial'
    }, { status: 500 })
  }
}

import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Test sidebar access configuration
    const testResults = {
      message: 'Sidebar access test results',
      roles: {
        UNIT_LEADER: {
          expectedAccess: [
            'dashboard', 'students', 'students-all-students',
            'teachers', 'class', 'marklist', 'attendance', 'visualizer', 'profile'
          ],
          description: 'Unit Leader should have access to view students, teachers, classes, marks, attendance, and visualizer'
        },
        DATA_ENCODER: {
          expectedAccess: [
            'dashboard', 'students', 'students-all-students',
            'teachers', 'class', 'marklist', 'profile'
          ],
          description: 'Data Encoder should have access to add/edit students, teachers, classes, and marks'
        }
      },
      sidebarConfig: {
        dashboard: ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'ACCOUNTANT', 'TEACHER', 'PARENT', 'UNIT_LEADER', 'DATA_ENCODER'],
        students: ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'TEACHER', 'UNIT_LEADER', 'DATA_ENCODER'],
        teachers: ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'TEACHER', 'UNIT_LEADER', 'DATA_ENCODER'],
        class: ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'TEACHER', 'UNIT_LEADER', 'DATA_ENCODER'],
        marklist: ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'TEACHER', 'UNIT_LEADER', 'DATA_ENCODER'],
        attendance: ['SUPER_ADMIN', 'ADMIN', 'TEACHER', 'UNIT_LEADER'],
        visualizer: ['SUPER_ADMIN', 'SUPERVISOR', 'UNIT_LEADER'],
        profile: ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'ACCOUNTANT', 'TEACHER', 'PARENT', 'UNIT_LEADER', 'DATA_ENCODER']
      },
      status: 'success',
      timestamp: new Date().toISOString()
    };

    return NextResponse.json(testResults);
  } catch (error) {
    console.error('Error in sidebar access test:', error);
    
    return NextResponse.json({
      status: 'error',
      message: 'Failed to run sidebar access test',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

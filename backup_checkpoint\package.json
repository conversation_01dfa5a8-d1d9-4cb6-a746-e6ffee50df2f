{"name": "akademi-dashboard", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@prisma/client": "^6.5.0", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-roving-focus": "^1.1.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.6", "@tanstack/react-query": "^5.71.10", "@tanstack/react-table": "^8.21.3", "@types/papaparse": "^5.3.15", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "0.263.1", "next": "13.4.19", "next-themes": "^0.4.6", "papaparse": "^5.5.2", "prisma": "^6.5.0", "react": "18.2.0", "react-day-picker": "^9.6.4", "react-dom": "18.2.0", "react-to-print": "^3.0.5", "tailwind-merge": "^1.14.0"}, "devDependencies": {"@types/jsonwebtoken": "^9.0.9", "@types/node": "20.5.9", "@types/react": "18.2.21", "@types/react-dom": "18.2.7", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "tailwindcss-animate": "1.0.7", "typescript": "5.2.2"}}
'use client'

import React, { useState, useEffect } from 'react'
import Image from 'next/image'
import { Input } from '../components/ui/input'
import { Button } from '../components/ui/button'
import { useToast } from '../components/ui/use-toast'
import { Label } from '../components/ui/label'
import { Eye, EyeOff, Mail, Lock, User, ArrowRight } from 'lucide-react'
import { useTheme } from 'next-themes'
// Custom tabs implementation
const Tabs = ({ value, onValueChange, className, children }: {
  value: string;
  onValueChange: (value: string) => void;
  className?: string;
  children: React.ReactNode
}) => {
  return <div className={className}>{children}</div>;
};

const TabsList = ({ className, children }: { className?: string; children: React.ReactNode }) => {
  return <div className={`inline-flex h-10 items-center justify-center rounded-md bg-gray-100 dark:bg-gray-800 p-1 ${className}`}>{children}</div>;
};

const TabsTrigger = ({ value, children, className }: { value: string; children: React.ReactNode; className?: string }) => {
  const { activeTab, setActiveTab } = useTabsContext();
  const isActive = value === activeTab;

  return (
    <button
      className={`inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium transition-all focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50 ${isActive ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm' : 'text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'} ${className}`}
      onClick={() => setActiveTab(value)}
    >
      {children}
    </button>
  );
};

const TabsContent = ({ value, children, className }: { value: string; children: React.ReactNode; className?: string }) => {
  const { activeTab } = useTabsContext();
  if (value !== activeTab) return null;
  return <div className={className}>{children}</div>;
};

// Context for tabs
const TabsContext = React.createContext<{ activeTab: string; setActiveTab: (value: string) => void }>({
  activeTab: '',
  setActiveTab: () => {}
});

const useTabsContext = () => React.useContext(TabsContext);
// Custom checkbox implementation
const Checkbox = ({ id, name, checked, onCheckedChange, required }: {
  id: string;
  name?: string;
  checked?: boolean;
  onCheckedChange?: (checked: boolean) => void;
  required?: boolean
}) => {
  return (
    <div className="relative flex items-center justify-center h-4 w-4">
      <input
        type="checkbox"
        id={id}
        name={name}
        checked={checked}
        onChange={(e) => onCheckedChange?.(e.target.checked)}
        className="sr-only"
        required={required}
      />
      <div className={`h-4 w-4 rounded-sm border ${checked ? 'bg-indigo-600 border-indigo-600' : 'border-gray-300 dark:border-gray-600'}`}>
        {checked && (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="h-4 w-4 text-white"
          >
            <polyline points="20 6 9 17 4 12" />
          </svg>
        )}
      </div>
    </div>
  );
};


export default function LoginPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [showRegisterPassword, setShowRegisterPassword] = useState(false)
  const [activeTab, setActiveTab] = useState('login')
  const { toast } = useToast()
  useTheme() // Used for theme context
  const [mounted, setMounted] = useState(false)

  // Provide the tabs context
  const tabsContextValue = React.useMemo(() => ({
    activeTab,
    setActiveTab
  }), [activeTab])

  // Login form state
  const [loginData, setLoginData] = useState({
    email: '',
    password: '',
    rememberMe: false
  })

  // Register form state
  const [registerData, setRegisterData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    agreeToTerms: false
  })

  // CSS animations are handled via Tailwind classes

  // Handle component mount and check authentication
  useEffect(() => {
    setMounted(true)

    // Check if user is already authenticated
    const checkAuth = async () => {
      try {
        const response = await fetch('/api/auth/debug')
        const data = await response.json()

        if (data.authenticated && data.user) {
          console.log('User is already authenticated, redirecting to dashboard')
          window.location.replace('/dashboard')
        } else {
          console.log('User is not authenticated, showing login form')
          // Clear any stale cookies that might be causing issues
          document.cookie.split(';').forEach(cookie => {
            const [name] = cookie.trim().split('=')
            if (name === 'token' || name === 'auth_status') {
              document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`
            }
          })
        }
      } catch (error) {
        console.error('Error checking authentication:', error)
      }
    }

    // Only check auth after component is mounted
    if (mounted) {
      checkAuth()
    }
  }, [])

  // Handle login form input changes
  const handleLoginChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target
    setLoginData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
  }

  // Handle register form input changes
  const handleRegisterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target
    setRegisterData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
  }

  // Create sample user for development
  const createSampleUser = async () => {
    try {
      console.log('Creating sample user for development...')
      const response = await fetch('/api/auth/create-sample-user')
      const data = await response.json()

      if (response.ok) {
        console.log('Sample user info:', data)
        toast({
          title: "Sample User Info",
          description: `Email: ${data.user.email}, Password: ${data.user.password}`,
        })

        // Pre-fill the login form with sample user credentials
        setLoginData(prev => ({
          ...prev,
          email: data.user.email,
          password: data.user.password
        }))
      } else {
        console.error('Failed to create sample user:', data)
        toast({
          title: "Error",
          description: data.error || 'Failed to create sample user',
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('Error creating sample user:', error)
      toast({
        title: "Error",
        description: 'Failed to create sample user. Please check the console for details.',
        variant: "destructive",
      })
    }
  }

  // Call createSampleUser when component mounts in development
  useEffect(() => {
    if (process.env.NODE_ENV !== 'production' && mounted) {
      createSampleUser()
    }
  }, [mounted])

  // Handle login form submission
  const handleLoginSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      // Validate inputs
      if (!loginData.email || !loginData.password) {
        toast({
          title: "Error",
          description: "Please enter both email and password",
          variant: "destructive",
        })
        setIsLoading(false)
        return
      }

      console.log('Attempting login with:', { email: loginData.email, password: '******' })

      // Make a request to the login API
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: loginData.email,
          password: loginData.password,
        }),
        credentials: 'include', // Important: include cookies in the request
      })

      const data = await response.json()
      console.log('Login response status:', response.status)

      if (!response.ok) {
        // Handle specific error cases
        if (response.status === 401) {
          throw new Error(data.error || 'Invalid email or password')
        } else if (response.status === 500 && data.error?.includes('Database')) {
          // If there's a database connection error, suggest creating a sample user
          toast({
            title: "Database Error",
            description: "Database connection failed. Creating a sample user...",
            variant: "destructive",
          })

          // Try to create a sample user
          await createSampleUser()

          // Wait a bit and then throw the original error
          await new Promise(resolve => setTimeout(resolve, 2000))
          throw new Error(data.error || 'Database connection failed')
        } else {
          throw new Error(data.error || 'Login failed')
        }
      }

      toast({
        title: "Success",
        description: data.message || "Logged in successfully. Redirecting...",
      })

      // Redirect to dashboard after successful login
      setTimeout(() => {
        window.location.replace('/dashboard')
      }, 500)

    } catch (error) {
      console.error('Login error:', error)
      toast({
        title: "Authentication Error",
        description: error instanceof Error ? error.message : 'Login failed',
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Handle register form submission
  const handleRegisterSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validate passwords match
    if (registerData.password !== registerData.confirmPassword) {
      toast({
        title: "Error",
        description: "Passwords do not match",
        variant: "destructive",
      })
      return
    }

    // Validate terms agreement
    if (!registerData.agreeToTerms) {
      toast({
        title: "Error",
        description: "You must agree to the terms and conditions",
        variant: "destructive",
      })
      return
    }

    setIsLoading(true)

    try {
      // Make a request to the register API
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: registerData.name,
          email: registerData.email,
          password: registerData.password,
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Registration failed')
      }

      toast({
        title: "Success",
        description: "Account created successfully. Please log in.",
      })

      // Switch to login tab
      setActiveTab('login')

      // Pre-fill login form with registration email
      setLoginData(prev => ({
        ...prev,
        email: registerData.email
      }))

    } catch (error) {
      console.error('Registration error:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Registration failed',
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Don't render until mounted to prevent hydration issues
  if (!mounted) {
    return null
  }

  return (
    <div className="min-h-screen flex flex-col md:flex-row overflow-hidden">
      {/* Left side - Decorative panel */}
      <div className="hidden md:flex md:w-1/2 bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-500 p-12 relative overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-10 z-0"></div>
        <div className="absolute inset-0 bg-[url('/images/pattern.svg')] bg-repeat opacity-20 z-0"></div>

        <div className="relative z-10 flex flex-col h-full justify-between text-white opacity-0 animate-fadeIn">
          <div className="flex flex-col items-center space-y-4 opacity-0 animate-fadeIn-delay-100">
            <div className="relative w-40 h-40 rounded-full overflow-hidden border-4 border-white shadow-lg">
              <Image
                src="/images/logo.jpg"
                alt="School Logo"
                fill
                style={{ objectFit: 'cover' }}
                className="rounded-full"
              />
            </div>
            <h1 className="text-2xl font-bold text-center">Alfalah School</h1>
          </div>

          <div className="space-y-6 opacity-0 animate-fadeIn-delay-200">
            <h2 className="text-4xl md:text-5xl font-bold leading-tight text-center">
              Welcome to the School Management System
            </h2>
            <p className="text-lg md:text-xl opacity-90 max-w-md mx-auto text-center">
              Streamline your educational institution with our comprehensive management solution.
            </p>

            <div className="flex space-x-3 justify-center">
              <div className="h-2 w-12 rounded-full bg-white opacity-90"></div>
              <div className="h-2 w-6 rounded-full bg-white opacity-50"></div>
              <div className="h-2 w-4 rounded-full bg-white opacity-30"></div>
            </div>
          </div>

          <div className="text-sm opacity-0 animate-fadeIn-delay-300 text-center">
            © 2023 Alfalah School Management System. All rights reserved.
          </div>
        </div>
      </div>

      {/* Right side - Auth forms */}
      <div className="w-full md:w-1/2 flex items-center justify-center p-6 md:p-12 bg-white dark:bg-gray-900">
        <div className="w-full max-w-md space-y-8 opacity-0 animate-fadeIn">
          {/* Mobile logo */}
          <div className="md:hidden flex flex-col items-center space-y-4 mb-6">
            <div className="relative w-24 h-24 rounded-full overflow-hidden border-4 border-indigo-100 shadow-md">
              <Image
                src="/images/logo.jpg"
                alt="School Logo"
                fill
                style={{ objectFit: 'cover' }}
                className="rounded-full"
              />
            </div>
          </div>

          <div className="text-center space-y-2">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white">
              {activeTab === 'login' ? 'Sign in to your account' : 'Create a new account'}
            </h2>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {activeTab === 'login'
                ? 'Enter your credentials to access the dashboard'
                : 'Fill in the information to create your account'}
            </p>
          </div>

          <TabsContext.Provider value={tabsContextValue}>
            <Tabs
              value={activeTab}
              onValueChange={setActiveTab}
              className="w-full"
            >
              <TabsList className="grid w-full grid-cols-2 mb-8">
                <TabsTrigger value="login" className="text-sm">Sign In</TabsTrigger>
                <TabsTrigger value="register" className="text-sm">Register</TabsTrigger>
              </TabsList>

              <TabsContent value="login" className="space-y-4 mt-0">
              <form onSubmit={handleLoginSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="login-email" className="text-sm font-medium flex items-center">
                    <Mail className="h-4 w-4 mr-2 text-gray-400" />
                    Email Address
                  </Label>
                  <div className="relative">
                    <Input
                      id="login-email"
                      name="email"
                      type="email"
                      placeholder="<EMAIL>"
                      value={loginData.email}
                      onChange={handleLoginChange}
                      className="pl-3 pr-3 py-2 h-11"
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="login-password" className="text-sm font-medium flex items-center">
                      <Lock className="h-4 w-4 mr-2 text-gray-400" />
                      Password
                    </Label>
                    <a href="#" className="text-xs text-indigo-600 hover:text-indigo-500 dark:text-indigo-400">
                      Forgot password?
                    </a>
                  </div>
                  <div className="relative">
                    <Input
                      id="login-password"
                      name="password"
                      type={showPassword ? "text" : "password"}
                      placeholder="••••••••"
                      value={loginData.password}
                      onChange={handleLoginChange}
                      className="pl-3 pr-10 py-2 h-11"
                      required
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                    >
                      {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </button>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="remember-me"
                    name="rememberMe"
                    checked={loginData.rememberMe}
                    onCheckedChange={(checked) =>
                      setLoginData(prev => ({ ...prev, rememberMe: checked === true }))
                    }
                  />
                  <label
                    htmlFor="remember-me"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-gray-700 dark:text-gray-300"
                  >
                    Remember me
                  </label>
                </div>

                <div className="space-y-3">
                  <Button
                    type="submit"
                    className="w-full h-11 mt-2 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white font-medium rounded-md transition-all duration-200 flex items-center justify-center space-x-2"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <>
                        <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        <span>Signing in...</span>
                      </>
                    ) : (
                      <>
                        <span>Sign in</span>
                        <ArrowRight className="h-4 w-4 ml-1" />
                      </>
                    )}
                  </Button>

                  <div className="text-center">
                    <button
                      type="button"
                      onClick={() => {
                        createSampleUser();
                        toast({
                          title: "Login Help",
                          description: "For development: Use <EMAIL> / admin123. In production, contact your administrator for login credentials.",
                        })
                      }}
                      className="text-sm text-indigo-600 hover:text-indigo-500 dark:text-indigo-400 hover:underline"
                    >
                      Need help logging in?
                    </button>
                  </div>
                </div>
              </form>
            </TabsContent>

            <TabsContent value="register" className="space-y-4 mt-0">
              <form onSubmit={handleRegisterSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="register-name" className="text-sm font-medium flex items-center">
                    <User className="h-4 w-4 mr-2 text-gray-400" />
                    Full Name
                  </Label>
                  <Input
                    id="register-name"
                    name="name"
                    type="text"
                    placeholder="John Doe"
                    value={registerData.name}
                    onChange={handleRegisterChange}
                    className="pl-3 pr-3 py-2 h-11"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="register-email" className="text-sm font-medium flex items-center">
                    <Mail className="h-4 w-4 mr-2 text-gray-400" />
                    Email Address
                  </Label>
                  <Input
                    id="register-email"
                    name="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={registerData.email}
                    onChange={handleRegisterChange}
                    className="pl-3 pr-3 py-2 h-11"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="register-password" className="text-sm font-medium flex items-center">
                    <Lock className="h-4 w-4 mr-2 text-gray-400" />
                    Password
                  </Label>
                  <div className="relative">
                    <Input
                      id="register-password"
                      name="password"
                      type={showRegisterPassword ? "text" : "password"}
                      placeholder="••••••••"
                      value={registerData.password}
                      onChange={handleRegisterChange}
                      className="pl-3 pr-10 py-2 h-11"
                      required
                    />
                    <button
                      type="button"
                      onClick={() => setShowRegisterPassword(!showRegisterPassword)}
                      className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                    >
                      {showRegisterPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </button>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="register-confirm-password" className="text-sm font-medium flex items-center">
                    <Lock className="h-4 w-4 mr-2 text-gray-400" />
                    Confirm Password
                  </Label>
                  <Input
                    id="register-confirm-password"
                    name="confirmPassword"
                    type="password"
                    placeholder="••••••••"
                    value={registerData.confirmPassword}
                    onChange={handleRegisterChange}
                    className="pl-3 pr-3 py-2 h-11"
                    required
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="terms"
                    name="agreeToTerms"
                    checked={registerData.agreeToTerms}
                    onCheckedChange={(checked) =>
                      setRegisterData(prev => ({ ...prev, agreeToTerms: checked === true }))
                    }
                    required
                  />
                  <label
                    htmlFor="terms"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-gray-700 dark:text-gray-300"
                  >
                    I agree to the{" "}
                    <a href="#" className="text-indigo-600 hover:text-indigo-500 dark:text-indigo-400">
                      terms and conditions
                    </a>
                  </label>
                </div>

                <Button
                  type="submit"
                  className="w-full h-11 mt-2 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white font-medium rounded-md transition-all duration-200 flex items-center justify-center space-x-2"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      <span>Creating account...</span>
                    </>
                  ) : (
                    <>
                      <span>Create account</span>
                      <ArrowRight className="h-4 w-4 ml-1" />
                    </>
                  )}
                </Button>
              </form>
            </TabsContent>
            </Tabs>
          </TabsContext.Provider>

          <div className="mt-6 text-center text-sm text-gray-500 dark:text-gray-400">
            {activeTab === 'login' ? (
              <p>
                Don't have an account?{" "}
                <button
                  type="button"
                  onClick={() => setActiveTab('register')}
                  className="font-medium text-indigo-600 hover:text-indigo-500 dark:text-indigo-400"
                >
                  Register now
                </button>
              </p>
            ) : (
              <p>
                Already have an account?{" "}
                <button
                  type="button"
                  onClick={() => setActiveTab('login')}
                  className="font-medium text-indigo-600 hover:text-indigo-500 dark:text-indigo-400"
                >
                  Sign in
                </button>
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

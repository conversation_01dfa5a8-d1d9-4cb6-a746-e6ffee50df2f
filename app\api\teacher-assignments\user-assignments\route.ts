import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET all teacher assignments for users with TEACHER role
export async function GET(request: Request) {
  try {
    console.log('Fetching teacher assignments for users with TEACHER role')

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const teacherId = searchParams.get('teacherId')
    const type = searchParams.get('type')

    // Build the where clause
    let whereClause: any = {}

    if (teacherId) {
      whereClause.teacherId = teacherId
    }

    if (type === 'hr') {
      whereClause.isHRTeacher = true
    } else if (type === 'subject') {
      whereClause.isHRTeacher = false
    }

    // Fetch teacher assignments
    const assignments = await prisma.teacherAssignment.findMany({
      where: whereClause,
      include: {
        teacher: {
          select: {
            id: true,
            name: true,
            email: true,
            subject: true
          }
        },
        class: {
          select: {
            id: true,
            name: true
          }
        },
        subject: {
          select: {
            id: true,
            name: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    // Transform the data to include teacher and class/subject names
    const formattedAssignments = assignments.map(assignment => ({
      id: assignment.id,
      teacherId: assignment.teacherId,
      teacherName: assignment.teacher?.name || 'Unknown Teacher',
      classId: assignment.classId,
      className: assignment.class?.name || null,
      subjectId: assignment.subjectId,
      subjectName: assignment.subject?.name || null,
      isHRTeacher: assignment.isHRTeacher,
      createdAt: assignment.createdAt,
      updatedAt: assignment.updatedAt
    }))

    console.log(`Found ${formattedAssignments.length} teacher assignments`)
    return NextResponse.json(formattedAssignments)
  } catch (error) {
    console.error('Error fetching teacher assignments:', error)
    return NextResponse.json(
      { error: 'Failed to fetch teacher assignments' },
      { status: 500 }
    )
  }
}

// POST to create a new teacher assignment for a user with TEACHER role
export async function POST(request: Request) {
  try {
    console.log('Creating teacher assignment for user with TEACHER role')

    // Get the request body
    const data = await request.json()

    // Validate required fields
    if (!data.teacherId || (!data.classId && !data.subjectId)) {
      return NextResponse.json(
        { error: 'Teacher ID and either Class ID or Subject ID are required' },
        { status: 400 }
      )
    }

    // Check if the user with TEACHER role exists
    const teacher = await prisma.user.findFirst({
      where: { 
        id: data.teacherId,
        role: 'TEACHER'
      }
    })

    if (!teacher) {
      return NextResponse.json(
        { error: 'Teacher not found or user is not a teacher' },
        { status: 404 }
      )
    }

    // Check if the class exists if classId is provided
    if (data.classId) {
      const classObj = await prisma.class.findUnique({
        where: { id: data.classId }
      })

      if (!classObj) {
        return NextResponse.json(
          { error: 'Class not found' },
          { status: 404 }
        )
      }
    }

    // Check if the subject exists if subjectId is provided
    if (data.subjectId) {
      const subject = await prisma.subject.findUnique({
        where: { id: data.subjectId }
      })

      if (!subject) {
        return NextResponse.json(
          { error: 'Subject not found' },
          { status: 404 }
        )
      }
    }

    // Create the teacher assignment
    const assignment = await prisma.teacherAssignment.create({
      data: {
        teacherId: data.teacherId,
        classId: data.classId || null,
        subjectId: data.subjectId || null,
        isHRTeacher: data.isHRTeacher || false
      },
      include: {
        teacher: true,
        class: true,
        subject: true
      }
    })

    console.log(`Created teacher assignment for ${teacher.name}`)
    return NextResponse.json({
      message: 'Teacher assignment created successfully',
      assignment
    })
  } catch (error) {
    console.error('Error creating teacher assignment:', error)
    return NextResponse.json(
      { error: 'Failed to create teacher assignment' },
      { status: 500 }
    )
  }
}

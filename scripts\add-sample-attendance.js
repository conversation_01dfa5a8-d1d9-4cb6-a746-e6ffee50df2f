const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function addSampleAttendance() {
  try {
    console.log('Adding sample attendance data for semester testing...');

    // Get students
    const students = await prisma.student.findMany({
      select: { id: true, className: true, name: true }
    });

    if (students.length === 0) {
      console.log('No students found. Please add students first.');
      return;
    }

    console.log(`Found ${students.length} students`);

    // Define sample dates for both semesters
    const firstSemesterDates = [
      '2024-09-15', '2024-09-22', '2024-10-05', '2024-10-12', '2024-10-19',
      '2024-11-02', '2024-11-09', '2024-11-16', '2024-12-07', '2024-12-14',
      '2025-01-11', '2025-01-18', '2025-01-25'
    ];

    const secondSemesterDates = [
      '2025-02-08', '2025-02-15', '2025-02-22', '2025-03-01', '2025-03-08',
      '2025-03-15', '2025-04-05', '2025-04-12', '2025-04-19', '2025-05-03',
      '2025-05-10', '2025-05-17', '2025-06-07', '2025-06-14', '2025-06-21'
    ];

    const allDates = [...firstSemesterDates, ...secondSemesterDates];
    const statuses = ['present', 'absent', 'permission'];

    let addedCount = 0;

    for (const student of students) {
      for (const date of allDates) {
        // Check if attendance already exists for this student and date
        const existing = await prisma.attendance.findFirst({
          where: {
            studentId: student.id,
            date: date,
            className: student.className
          }
        });

        if (!existing) {
          // Randomly assign status (90% present, 7% absent, 3% permission)
          const rand = Math.random();
          let status;
          if (rand < 0.90) status = 'present';
          else if (rand < 0.97) status = 'absent';
          else status = 'permission';

          await prisma.attendance.create({
            data: {
              date: date,
              className: student.className,
              studentId: student.id,
              status: status
            }
          });

          addedCount++;
        }
      }
    }

    console.log(`Added ${addedCount} new attendance records`);
    console.log('Sample attendance data added successfully!');

    // Show summary
    const totalRecords = await prisma.attendance.count();
    console.log(`Total attendance records in database: ${totalRecords}`);

  } catch (error) {
    console.error('Error adding sample attendance:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
addSampleAttendance();

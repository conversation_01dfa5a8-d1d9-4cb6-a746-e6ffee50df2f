'use client'

import { useState, useEffect } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { Button } from '@/app/components/ui/button'
import { Label } from '@/app/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/app/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/app/components/ui/dialog'
import { Input } from '@/app/components/ui/input'
import { Textarea } from '@/app/components/ui/textarea'
import { RefreshCw, Plus, GraduationCap, Users, BookOpen, UserCheck } from 'lucide-react'
import { MarkClass, MarkSubject, ACADEMIC_YEARS, TERMS } from '@/app/types/mark-management'
import { useToast } from '@/app/components/ui/use-toast'

// Types for Add New Mark
interface Student {
  id: string
  name: string
  sid: string
  className: string
  academicYear: string
}

interface AddNewMarkDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: () => void
  isEditMode?: boolean
  initialData?: any
}

export function AddNewMarkDialog({ 
  open, 
  onOpenChange, 
  onSuccess,
  isEditMode = false,
  initialData
}: AddNewMarkDialogProps) {
  const [formData, setFormData] = useState({
    id: '',
    studentId: '',
    studentName: '',
    className: '',
    subject: '',
    term: 'First Semester',
    academicYear: '2024-2025',
    totalMarks: 100,
    marks: 0,
    remarks: ''
  })
  
  const queryClient = useQueryClient()
  const { toast } = useToast()

  // Fetch available classes
  const {
    data: classes = [],
    isLoading: isLoadingClasses,
    error: classesError
  } = useQuery<MarkClass[]>({
    queryKey: ['marks-available-classes'],
    queryFn: async () => {
      const res = await fetch('/api/marks/available-classes')
      if (!res.ok) throw new Error('Failed to fetch classes')
      return res.json()
    },
    enabled: open
  })

  // Fetch subjects for selected class
  const {
    data: subjects = [],
    isLoading: isLoadingSubjects,
    error: subjectsError
  } = useQuery<MarkSubject[]>({
    queryKey: ['marks-available-subjects', formData.className],
    queryFn: async () => {
      if (!formData.className) return []
      const res = await fetch(`/api/marks/available-subjects?className=${formData.className}`)
      if (!res.ok) throw new Error('Failed to fetch subjects')
      return res.json()
    },
    enabled: open && !!formData.className
  })

  // Fetch students for selected class
  const {
    data: students = [],
    isLoading: isLoadingStudents
  } = useQuery<Student[]>({
    queryKey: ['marks-available-students', formData.className],
    queryFn: async () => {
      if (!formData.className) return []
      const res = await fetch(`/api/marks/available-students?className=${formData.className}`)
      if (!res.ok) throw new Error('Failed to fetch students')
      return res.json()
    },
    enabled: open && !!formData.className
  })

  // Submit mark mutation
  const submitMarkMutation = useMutation({
    mutationFn: async (markData: any) => {
      const url = isEditMode ? `/api/marks/update-single/${markData.id}` : '/api/marks/create-single'
      const method = isEditMode ? 'PUT' : 'POST'
      
      const res = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(markData)
      })
      
      if (!res.ok) {
        const error = await res.json()
        throw new Error(error.error || `Failed to ${isEditMode ? 'update' : 'create'} mark`)
      }
      return res.json()
    },
    onSuccess: (data) => {
      // Invalidate and refetch related queries
      queryClient.invalidateQueries({ queryKey: ['marks'] })
      queryClient.invalidateQueries({ queryKey: ['marks-available-classes'] })

      // Reset form
      handleClose()

      // Call success callback
      onSuccess?.()

      toast({
        title: "Success",
        description: `Mark ${isEditMode ? 'updated' : 'added'} successfully`,
        variant: "default"
      })
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: `Failed to ${isEditMode ? 'update' : 'add'} mark: ${error.message}`,
        variant: "destructive"
      })
    }
  })

  // Initialize form with initial data if editing
  useEffect(() => {
    if (isEditMode && initialData && open) {
      setFormData({
        id: initialData.id || '',
        studentId: initialData.studentId || '',
        studentName: initialData.studentName || '',
        className: initialData.className || initialData.class || '',
        subject: initialData.subject || '',
        term: initialData.term || 'First Semester',
        academicYear: initialData.academicYear || '2024-2025',
        totalMarks: initialData.totalMarks || 100,
        marks: initialData.marks || initialData.obtainedMarks || 0,
        remarks: initialData.remarks || ''
      })
    } else if (!isEditMode && open) {
      // Reset form for new mark
      setFormData({
        id: '',
        studentId: '',
        studentName: '',
        className: '',
        subject: '',
        term: 'First Semester',
        academicYear: '2024-2025',
        totalMarks: 100,
        marks: 0,
        remarks: ''
      })
    }
  }, [isEditMode, initialData, open])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: name === 'totalMarks' || name === 'marks' 
        ? parseInt(value) || 0 
        : value
    }))
  }

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))

    // Clear student when class changes
    if (name === 'className') {
      setFormData(prev => ({
        ...prev,
        studentId: '',
        studentName: ''
      }))
    }
  }

  const handleStudentSelect = (studentId: string) => {
    const selectedStudent = students.find(s => s.id === studentId)
    if (selectedStudent) {
      setFormData(prev => ({
        ...prev,
        studentId: selectedStudent.id,
        studentName: selectedStudent.name
      }))
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    // Validate required fields
    if (!formData.studentId || !formData.className || !formData.subject || !formData.term) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields",
        variant: "destructive"
      })
      return
    }

    if (formData.marks > formData.totalMarks) {
      toast({
        title: "Validation Error",
        description: "Obtained marks cannot be greater than total marks",
        variant: "destructive"
      })
      return
    }

    // Prepare data for submission
    const markData = {
      ...formData,
      // Ensure we use the correct field names for the API
      obtainedMarks: formData.marks, // For backward compatibility
      class: formData.className // For backward compatibility
    }

    submitMarkMutation.mutate(markData)
  }

  const handleClose = () => {
    setFormData({
      id: '',
      studentId: '',
      studentName: '',
      className: '',
      subject: '',
      term: 'First Semester',
      academicYear: '2024-2025',
      totalMarks: 100,
      marks: 0,
      remarks: ''
    })
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            {isEditMode ? 'Edit Mark' : 'Add New Mark'}
          </DialogTitle>
          <DialogDescription>
            {isEditMode 
              ? 'Update the student\'s mark details'
              : 'Enter the student\'s mark details'
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Class and Student Selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Class */}
            <div className="space-y-2">
              <Label htmlFor="className">Class *</Label>
              <Select
                value={formData.className}
                onValueChange={(value) => handleSelectChange('className', value)}
                disabled={isLoadingClasses}
              >
                <SelectTrigger>
                  <Users className="mr-2 h-4 w-4 text-gray-400" />
                  <SelectValue placeholder="Select class" />
                </SelectTrigger>
                <SelectContent>
                  {isLoadingClasses ? (
                    <div className="p-2 text-center text-sm text-gray-500">
                      Loading classes...
                    </div>
                  ) : classesError ? (
                    <div className="p-2 text-center text-sm text-red-500">
                      Error loading classes
                    </div>
                  ) : classes.length > 0 ? (
                    classes.map((cls) => (
                      <SelectItem key={cls.id} value={cls.name}>
                        <div className="flex items-center justify-between w-full">
                          <span>{cls.name}</span>
                          <span className="text-xs text-gray-500 ml-2">
                            {cls.totalSubjects} subjects
                          </span>
                        </div>
                      </SelectItem>
                    ))
                  ) : (
                    <div className="p-2 text-center text-sm text-gray-500">
                      No classes found
                    </div>
                  )}
                </SelectContent>
              </Select>
            </div>

            {/* Student */}
            <div className="space-y-2">
              <Label htmlFor="studentId">Student *</Label>
              <Select
                value={formData.studentId}
                onValueChange={handleStudentSelect}
                disabled={!formData.className || isLoadingStudents}
              >
                <SelectTrigger>
                  <UserCheck className="mr-2 h-4 w-4 text-gray-400" />
                  <SelectValue placeholder="Select student" />
                </SelectTrigger>
                <SelectContent>
                  {isLoadingStudents ? (
                    <div className="p-2 text-center text-sm text-gray-500">
                      Loading students...
                    </div>
                  ) : students.length > 0 ? (
                    students.map((student) => (
                      <SelectItem key={student.id} value={student.id}>
                        <div className="flex items-center justify-between w-full">
                          <span>{student.name}</span>
                          <span className="text-xs text-gray-500 ml-2 font-mono">
                            {student.sid}
                          </span>
                        </div>
                      </SelectItem>
                    ))
                  ) : (
                    <div className="p-2 text-center text-sm text-gray-500">
                      {formData.className ? 'No students found in this class' : 'Select a class first'}
                    </div>
                  )}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Subject and Term */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Subject */}
            <div className="space-y-2">
              <Label htmlFor="subject">Subject *</Label>
              <Select
                value={formData.subject}
                onValueChange={(value) => handleSelectChange('subject', value)}
                disabled={!formData.className || isLoadingSubjects}
              >
                <SelectTrigger>
                  <BookOpen className="mr-2 h-4 w-4 text-gray-400" />
                  <SelectValue placeholder="Select subject" />
                </SelectTrigger>
                <SelectContent>
                  {isLoadingSubjects ? (
                    <div className="p-2 text-center text-sm text-gray-500">
                      Loading subjects...
                    </div>
                  ) : subjectsError ? (
                    <div className="p-2 text-center text-sm text-red-500">
                      Error loading subjects
                    </div>
                  ) : subjects.length > 0 ? (
                    subjects.map((subject) => (
                      <SelectItem key={subject.id} value={subject.name}>
                        {subject.name}
                      </SelectItem>
                    ))
                  ) : (
                    <div className="p-2 text-center text-sm text-gray-500">
                      {formData.className 
                        ? 'No subjects assigned to this class. Please add subjects in Class Management first.' 
                        : 'Select a class first'
                      }
                    </div>
                  )}
                </SelectContent>
              </Select>
            </div>

            {/* Semester */}
            <div className="space-y-2">
              <Label htmlFor="term">Semester *</Label>
              <Select
                value={formData.term}
                onValueChange={(value) => handleSelectChange('term', value)}
              >
                <SelectTrigger>
                  <GraduationCap className="mr-2 h-4 w-4 text-gray-400" />
                  <SelectValue placeholder="Select semester" />
                </SelectTrigger>
                <SelectContent>
                  {TERMS.map(term => (
                    <SelectItem key={term} value={term}>
                      {term}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Academic Year */}
          <div className="space-y-2">
            <Label htmlFor="academicYear">Academic Year *</Label>
            <Select
              value={formData.academicYear}
              onValueChange={(value) => handleSelectChange('academicYear', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select academic year" />
              </SelectTrigger>
              <SelectContent>
                {ACADEMIC_YEARS.map(year => (
                  <SelectItem key={year} value={year}>
                    {year}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Marks */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="totalMarks">Total Marks *</Label>
              <Input
                id="totalMarks"
                name="totalMarks"
                type="number"
                min="1"
                max="1000"
                value={formData.totalMarks}
                onChange={handleInputChange}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="marks">Obtained Marks *</Label>
              <Input
                id="marks"
                name="marks"
                type="number"
                min="0"
                max={formData.totalMarks}
                value={formData.marks}
                onChange={handleInputChange}
                required
              />
            </div>
          </div>

          {/* Remarks */}
          <div className="space-y-2">
            <Label htmlFor="remarks">Remarks (Optional)</Label>
            <Textarea
              id="remarks"
              name="remarks"
              value={formData.remarks}
              onChange={handleInputChange}
              placeholder="Enter any remarks..."
              rows={3}
            />
          </div>
        </form>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={submitMarkMutation.isPending}
          >
            {submitMarkMutation.isPending ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                {isEditMode ? 'Updating...' : 'Adding...'}
              </>
            ) : (
              isEditMode ? 'Update Mark' : 'Add Mark'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

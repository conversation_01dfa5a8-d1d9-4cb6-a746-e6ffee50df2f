'use client'

import React, { useState, useEffect } from 'react'
import dynamic from 'next/dynamic'
import Sidebar from '../../components/Sidebar'
import { Card } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Textarea } from '../../components/ui/textarea'
import { Label } from '../../components/ui/label'
import { Checkbox } from '../../components/ui/checkbox'
import {
  Table, TableBody, TableCaption, TableCell,
  TableHead, TableHeader, TableRow
} from '../../components/ui/table'
import {
  RecursionSafeDialog,
  RecursionSafeDialogContent,
  RecursionSafeDialogHeader,
  RecursionSafeDialogTitle,
  RecursionSafeDialogDescription,
  RecursionSafeDialogFooter
} from '../../components/RecursionSafeDialog'
import {
  DropdownMenu, DropdownMenuContent, DropdownMenuItem,
  DropdownMenuTrigger
} from '../../components/ui/dropdown-menu'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../components/ui/select"
import {
  Globe, Image as ImageIcon, Plus, MoreHorizontal, Pencil, Trash2,
  Eye, EyeOff, AlertCircle, Calendar, Filter, ArrowUpDown, Newspaper, CalendarDays
} from 'lucide-react'
import { useQuery, useMutation, QueryClient, QueryClientProvider } from '@tanstack/react-query'
import Link from 'next/link'
import { useToast } from '../../components/ui/use-toast'
import { Toaster } from '../../components/ui/toaster'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs'
import { format } from 'date-fns'
import { CalendarIcon } from "@radix-ui/react-icons"
import { Popover, PopoverContent, PopoverTrigger } from "../../components/ui/popover"
import { Calendar as CalendarComponent } from "../../components/ui/calendar"
import { cn } from "../../lib/utils"
import { Badge } from "../../components/ui/badge"

// Create a client with default options
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 60000,
      refetchOnWindowFocus: false,
    },
  },
})

// Import Header component with no SSR to avoid hydration issues
const Header = dynamic(() => import('../../components/Header').then(mod => mod.default), {
  ssr: false
})

// Type definition for news/event
interface NewsEvent {
  id: string;
  title: string;
  content: string;
  excerpt: string;
  imageUrl: string;
  date: string;
  category: 'news' | 'event';
  tags: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

function NewsEventsManagement() {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false)
  const [isMounted, setIsMounted] = useState(false)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [currentNewsEvent, setCurrentNewsEvent] = useState<NewsEvent | null>(null)
  const [categoryFilter, setCategoryFilter] = useState<string | null>(null)
  const [date, setDate] = useState<Date | undefined>(new Date())
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    excerpt: '',
    imageUrl: '',
    date: new Date(),
    category: 'news' as 'news' | 'event',
    tags: '',
    isActive: true
  })
  const { toast } = useToast()

  // Fetch news and events from the API
  const { data: newsEvents, isLoading, refetch } = useQuery<NewsEvent[]>({
    queryKey: ['newsEvents', categoryFilter],
    queryFn: async () => {
      try {
        const url = categoryFilter
          ? `/api/website-management/news-events?category=${categoryFilter}`
          : '/api/website-management/news-events'

        const res = await fetch(url)
        if (!res.ok) {
          throw new Error('Failed to fetch news and events')
        }
        return res.json()
      } catch (error) {
        // If API doesn't exist yet, return mock data
        return [
          {
            id: '1',
            title: 'New Science Lab Opening',
            content: 'We are excited to announce the opening of our new state-of-the-art science laboratory. This facility will provide our students with hands-on learning experiences in biology, chemistry, and physics. The lab is equipped with the latest technology and equipment to support our science curriculum.',
            excerpt: 'Alfalah Islamic School opens a new state-of-the-art science laboratory to enhance STEM education.',
            imageUrl: '/images/science-lab.jpg',
            date: new Date('2023-09-15').toISOString(),
            category: 'news',
            tags: 'science,laboratory,stem',
            isActive: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          },
          {
            id: '2',
            title: 'Back to School Night',
            content: 'Join us for our Back to School Night on September 5th at 6:00 PM. This is an opportunity for parents to meet teachers, learn about the curriculum, and get important information about the upcoming school year. We look forward to seeing all parents there!',
            excerpt: 'Parents are invited to meet teachers and learn about the curriculum for the new school year.',
            imageUrl: '/images/back-to-school.jpg',
            date: new Date('2023-09-05').toISOString(),
            category: 'event',
            tags: 'parents,teachers,curriculum',
            isActive: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }
        ].filter(item => !categoryFilter || item.category === categoryFilter)
      }
    },
    staleTime: 60000, // 1 minute
    refetchOnWindowFocus: false,
  })

  // Add news/event mutation
  const addNewsEventMutation = useMutation({
    mutationFn: async (newNewsEvent: Omit<NewsEvent, 'id' | 'createdAt' | 'updatedAt'>) => {
      const res = await fetch('/api/website-management/news-events', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newNewsEvent),
      })

      if (!res.ok) {
        const error = await res.json()
        throw new Error(error.message || 'Failed to add news/event')
      }

      return res.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['newsEvents'] })
      queryClient.invalidateQueries({ queryKey: ['websiteStats'] })
      setIsAddDialogOpen(false)
      resetForm()
      toast({
        title: 'Success',
        description: 'News/event added successfully',
        variant: 'default',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to add news/event',
        variant: 'destructive',
      })
    }
  })

  // Update news/event mutation
  const updateNewsEventMutation = useMutation({
    mutationFn: async (updatedNewsEvent: Partial<NewsEvent> & { id: string }) => {
      const res = await fetch(`/api/website-management/news-events/${updatedNewsEvent.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedNewsEvent),
      })

      if (!res.ok) {
        const error = await res.json()
        throw new Error(error.message || 'Failed to update news/event')
      }

      return res.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['newsEvents'] })
      setIsEditDialogOpen(false)
      resetForm()
      toast({
        title: 'Success',
        description: 'News/event updated successfully',
        variant: 'default',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update news/event',
        variant: 'destructive',
      })
    }
  })

  // Delete news/event mutation
  const deleteNewsEventMutation = useMutation({
    mutationFn: async (id: string) => {
      const res = await fetch(`/api/website-management/news-events/${id}`, {
        method: 'DELETE',
      })

      if (!res.ok) {
        const error = await res.json()
        throw new Error(error.message || 'Failed to delete news/event')
      }

      return res.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['newsEvents'] })
      queryClient.invalidateQueries({ queryKey: ['websiteStats'] })
      setIsDeleteDialogOpen(false)
      toast({
        title: 'Success',
        description: 'News/event deleted successfully',
        variant: 'default',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete news/event',
        variant: 'destructive',
      })
    }
  })

  useEffect(() => {
    setIsMounted(true)
  }, [])

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  const toggleMobileSidebar = () => {
    setMobileSidebarOpen(!mobileSidebarOpen)
  }

  const resetForm = () => {
    setFormData({
      title: '',
      content: '',
      excerpt: '',
      imageUrl: '',
      date: new Date(),
      category: 'news',
      tags: '',
      isActive: true
    })
    setDate(new Date())
    setCurrentNewsEvent(null)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleCategoryChange = (value: 'news' | 'event') => {
    setFormData(prev => ({ ...prev, category: value }))
  }

  const handleDateChange = (date: Date | undefined) => {
    if (date) {
      setDate(date)
      setFormData(prev => ({ ...prev, date }))
    }
  }

  const handleCheckboxChange = (checked: boolean) => {
    setFormData(prev => ({ ...prev, isActive: checked }))
  }

  const handleAddNewsEvent = () => {
    addNewsEventMutation.mutate({
      title: formData.title,
      content: formData.content,
      excerpt: formData.excerpt,
      imageUrl: formData.imageUrl,
      date: formData.date.toISOString(),
      category: formData.category,
      tags: formData.tags || null,
      isActive: formData.isActive
    })
  }

  const handleUpdateNewsEvent = () => {
    if (!currentNewsEvent) return

    updateNewsEventMutation.mutate({
      id: currentNewsEvent.id,
      title: formData.title,
      content: formData.content,
      excerpt: formData.excerpt,
      imageUrl: formData.imageUrl,
      date: formData.date.toISOString(),
      category: formData.category,
      tags: formData.tags || null,
      isActive: formData.isActive
    })
  }

  const handleDeleteNewsEvent = () => {
    if (!currentNewsEvent) return

    deleteNewsEventMutation.mutate(currentNewsEvent.id)
  }

  const openEditDialog = (newsEvent: NewsEvent) => {
    setCurrentNewsEvent(newsEvent)
    setFormData({
      title: newsEvent.title,
      content: newsEvent.content,
      excerpt: newsEvent.excerpt,
      imageUrl: newsEvent.imageUrl,
      date: new Date(newsEvent.date),
      category: newsEvent.category,
      tags: newsEvent.tags || '',
      isActive: newsEvent.isActive
    })
    setDate(new Date(newsEvent.date))
    setIsEditDialogOpen(true)
  }

  const openDeleteDialog = (newsEvent: NewsEvent) => {
    setCurrentNewsEvent(newsEvent)
    setIsDeleteDialogOpen(true)
  }

  const handleFilterChange = (value: string | null) => {
    setCategoryFilter(value)
  }

  // Only show loading while mounting
  if (!isMounted || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-600"></div>
      </div>
    )
  }

  return (
    <div className="flex h-screen bg-gray-100 dark:bg-gray-900">
      <Sidebar
        isCollapsed={sidebarCollapsed}
        toggleSidebar={toggleSidebar}
        isMobileOpen={mobileSidebarOpen}
      />

      <main className={`main-content ${sidebarCollapsed ? 'sidebar-collapsed' : ''} w-full h-screen overflow-y-auto`}>
        <Header toggleMobileSidebar={toggleMobileSidebar} />
        <Toaster />

        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-2xl font-semibold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent inline-block">
                News & Events Management
              </h1>
              <p className="text-gray-600 dark:text-gray-300 mt-1">
                Manage the news and events displayed on the website
              </p>
            </div>
            <div className="flex space-x-2">
              <Link href="/website" target="_blank">
                <Button variant="outline" size="sm" className="flex items-center">
                  <Globe className="h-4 w-4 mr-1" />
                  <span>View Website</span>
                </Button>
              </Link>
              <Button onClick={() => setIsAddDialogOpen(true)} className="flex items-center">
                <Plus className="h-4 w-4 mr-1" />
                <span>Add News/Event</span>
              </Button>
            </div>
          </div>

          <div className="mb-4 flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Label htmlFor="filter" className="mr-2">Filter:</Label>
              <Select
                value={categoryFilter || 'all'}
                onValueChange={(value) => handleFilterChange(value === 'all' ? null : value)}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="All Categories" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="news">News</SelectItem>
                  <SelectItem value="event">Events</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <Card className="overflow-hidden border-0 shadow-md">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[100px]">Image</TableHead>
                  <TableHead>Title</TableHead>
                  <TableHead>Excerpt</TableHead>
                  <TableHead className="w-[120px]">
                    <div className="flex items-center">
                      Date
                      <ArrowUpDown className="ml-1 h-4 w-4" />
                    </div>
                  </TableHead>
                  <TableHead className="w-[100px]">Category</TableHead>
                  <TableHead className="w-[100px]">Status</TableHead>
                  <TableHead className="w-[100px] text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {newsEvents && newsEvents.length > 0 ? (
                  newsEvents.map((newsEvent) => (
                    <TableRow key={newsEvent.id}>
                      <TableCell>
                        <div className="relative h-12 w-20 rounded overflow-hidden">
                          <img
                            src={newsEvent.imageUrl}
                            alt={newsEvent.title}
                            className="object-cover w-full h-full"
                          />
                        </div>
                      </TableCell>
                      <TableCell className="font-medium">{newsEvent.title}</TableCell>
                      <TableCell className="max-w-[300px] truncate">{newsEvent.excerpt}</TableCell>
                      <TableCell>{format(new Date(newsEvent.date), 'MMM dd, yyyy')}</TableCell>
                      <TableCell>
                        <Badge variant={newsEvent.category === 'news' ? 'default' : 'secondary'}>
                          {newsEvent.category === 'news' ? (
                            <Newspaper className="h-3 w-3 mr-1" />
                          ) : (
                            <CalendarDays className="h-3 w-3 mr-1" />
                          )}
                          {newsEvent.category.charAt(0).toUpperCase() + newsEvent.category.slice(1)}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {newsEvent.isActive ? (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                            Active
                          </span>
                        ) : (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                            Inactive
                          </span>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">Open menu</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => openEditDialog(newsEvent)}>
                              <Pencil className="h-4 w-4 mr-2" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => openDeleteDialog(newsEvent)}>
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => updateNewsEventMutation.mutate({ id: newsEvent.id, isActive: !newsEvent.isActive })}>
                              {newsEvent.isActive ? (
                                <>
                                  <EyeOff className="h-4 w-4 mr-2" />
                                  Deactivate
                                </>
                              ) : (
                                <>
                                  <Eye className="h-4 w-4 mr-2" />
                                  Activate
                                </>
                              )}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      <div className="flex flex-col items-center justify-center text-gray-500 dark:text-gray-400">
                        <Newspaper className="h-12 w-12 mb-2 opacity-50" />
                        <p>No news or events found</p>
                        <Button
                          variant="link"
                          onClick={() => setIsAddDialogOpen(true)}
                          className="mt-2"
                        >
                          Add your first news/event
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </Card>
        </div>
      </main>

      {/* Add News/Event Dialog */}
      <RecursionSafeDialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen} maxWidth="max-w-[700px]">
        <RecursionSafeDialogHeader>
          <RecursionSafeDialogTitle>Add New News/Event</RecursionSafeDialogTitle>
          <RecursionSafeDialogDescription>
            Create a new news article or event for the website. All fields are required.
          </RecursionSafeDialogDescription>
        </RecursionSafeDialogHeader>

        <RecursionSafeDialogContent>
          <Tabs defaultValue="details" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="details">Basic Details</TabsTrigger>
              <TabsTrigger value="content">Content & Media</TabsTrigger>
            </TabsList>
            <TabsContent value="details" className="py-4">
              <div className="grid gap-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="title">Title</Label>
                    <Input
                      id="title"
                      name="title"
                      value={formData.title}
                      onChange={handleInputChange}
                      placeholder="Enter title"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="category">Category</Label>
                    <Select
                      value={formData.category}
                      onValueChange={(value: 'news' | 'event') => handleCategoryChange(value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="news">News</SelectItem>
                        <SelectItem value="event">Event</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="excerpt">Excerpt</Label>
                  <Input
                    id="excerpt"
                    name="excerpt"
                    value={formData.excerpt}
                    onChange={handleInputChange}
                    placeholder="Enter a short excerpt (summary)"
                  />
                  <p className="text-xs text-gray-500">
                    A brief summary that will be displayed in listings (max 150 characters)
                  </p>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="date">Date</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant={"outline"}
                          className={cn(
                            "w-full justify-start text-left font-normal",
                            !date && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {date ? format(date, "PPP") : <span>Pick a date</span>}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <CalendarComponent
                          mode="single"
                          selected={date}
                          onSelect={handleDateChange}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="tags">Tags</Label>
                    <Input
                      id="tags"
                      name="tags"
                      value={formData.tags}
                      onChange={handleInputChange}
                      placeholder="Enter tags separated by commas"
                    />
                    <p className="text-xs text-gray-500">
                      E.g., science,competition,event
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isActive"
                    checked={formData.isActive}
                    onCheckedChange={handleCheckboxChange}
                  />
                  <Label htmlFor="isActive">Active</Label>
                </div>
              </div>
            </TabsContent>
            <TabsContent value="content" className="py-4">
              <div className="grid gap-4">
                <div className="space-y-2">
                  <Label htmlFor="imageUrl">Image URL</Label>
                  <Input
                    id="imageUrl"
                    name="imageUrl"
                    value={formData.imageUrl}
                    onChange={handleInputChange}
                    placeholder="Enter image URL (e.g., /images/news-1.jpg)"
                  />
                  <p className="text-xs text-gray-500">
                    Recommended image size: 800x450 pixels (16:9 ratio)
                  </p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="content">Content</Label>
                  <Textarea
                    id="content"
                    name="content"
                    value={formData.content}
                    onChange={handleInputChange}
                    placeholder="Enter the full content"
                    rows={8}
                  />
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </RecursionSafeDialogContent>

        <RecursionSafeDialogFooter>
          <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
            Cancel
          </Button>
          <Button onClick={handleAddNewsEvent} disabled={addNewsEventMutation.isPending}>
            {addNewsEventMutation.isPending ? 'Adding...' : 'Add News/Event'}
          </Button>
        </RecursionSafeDialogFooter>
      </RecursionSafeDialog>

      {/* Edit News/Event Dialog */}
      <RecursionSafeDialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen} maxWidth="max-w-[700px]">
        <RecursionSafeDialogHeader>
          <RecursionSafeDialogTitle>Edit News/Event</RecursionSafeDialogTitle>
          <RecursionSafeDialogDescription>
            Update the news/event information. All fields are required.
          </RecursionSafeDialogDescription>
        </RecursionSafeDialogHeader>

        <RecursionSafeDialogContent>
          <Tabs defaultValue="details" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="details">Basic Details</TabsTrigger>
              <TabsTrigger value="content">Content & Media</TabsTrigger>
            </TabsList>
            <TabsContent value="details" className="py-4">
              <div className="grid gap-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="edit-title">Title</Label>
                    <Input
                      id="edit-title"
                      name="title"
                      value={formData.title}
                      onChange={handleInputChange}
                      placeholder="Enter title"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="edit-category">Category</Label>
                    <Select
                      value={formData.category}
                      onValueChange={(value: 'news' | 'event') => handleCategoryChange(value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="news">News</SelectItem>
                        <SelectItem value="event">Event</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-excerpt">Excerpt</Label>
                  <Input
                    id="edit-excerpt"
                    name="excerpt"
                    value={formData.excerpt}
                    onChange={handleInputChange}
                    placeholder="Enter a short excerpt (summary)"
                  />
                  <p className="text-xs text-gray-500">
                    A brief summary that will be displayed in listings (max 150 characters)
                  </p>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="edit-date">Date</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant={"outline"}
                          className={cn(
                            "w-full justify-start text-left font-normal",
                            !date && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {date ? format(date, "PPP") : <span>Pick a date</span>}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <CalendarComponent
                          mode="single"
                          selected={date}
                          onSelect={handleDateChange}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="edit-tags">Tags</Label>
                    <Input
                      id="edit-tags"
                      name="tags"
                      value={formData.tags}
                      onChange={handleInputChange}
                      placeholder="Enter tags separated by commas"
                    />
                    <p className="text-xs text-gray-500">
                      E.g., science,competition,event
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="edit-isActive"
                    checked={formData.isActive}
                    onCheckedChange={handleCheckboxChange}
                  />
                  <Label htmlFor="edit-isActive">Active</Label>
                </div>
              </div>
            </TabsContent>
            <TabsContent value="content" className="py-4">
              <div className="grid gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-imageUrl">Image URL</Label>
                  <Input
                    id="edit-imageUrl"
                    name="imageUrl"
                    value={formData.imageUrl}
                    onChange={handleInputChange}
                    placeholder="Enter image URL (e.g., /images/news-1.jpg)"
                  />
                  <p className="text-xs text-gray-500">
                    Recommended image size: 800x450 pixels (16:9 ratio)
                  </p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-content">Content</Label>
                  <Textarea
                    id="edit-content"
                    name="content"
                    value={formData.content}
                    onChange={handleInputChange}
                    placeholder="Enter the full content"
                    rows={8}
                  />
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </RecursionSafeDialogContent>

        <RecursionSafeDialogFooter>
          <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
            Cancel
          </Button>
          <Button onClick={handleUpdateNewsEvent} disabled={updateNewsEventMutation.isPending}>
            {updateNewsEventMutation.isPending ? 'Updating...' : 'Update News/Event'}
          </Button>
        </RecursionSafeDialogFooter>
      </RecursionSafeDialog>

      {/* Delete Confirmation Dialog */}
      <RecursionSafeDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen} maxWidth="max-w-[425px]">
        <RecursionSafeDialogHeader>
          <RecursionSafeDialogTitle>Confirm Deletion</RecursionSafeDialogTitle>
          <RecursionSafeDialogDescription>
            Are you sure you want to delete this news/event? This action cannot be undone.
          </RecursionSafeDialogDescription>
        </RecursionSafeDialogHeader>

        <RecursionSafeDialogContent>
          <div className="flex items-center space-x-2 text-amber-600 dark:text-amber-400">
            <AlertCircle className="h-5 w-5" />
            <p className="text-sm">This will permanently remove the news/event from the website.</p>
          </div>
        </RecursionSafeDialogContent>

        <RecursionSafeDialogFooter>
          <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleDeleteNewsEvent}
            disabled={deleteNewsEventMutation.isPending}
          >
            {deleteNewsEventMutation.isPending ? 'Deleting...' : 'Delete News/Event'}
          </Button>
        </RecursionSafeDialogFooter>
      </RecursionSafeDialog>

      {/* Overlay for mobile menu */}
      {mobileSidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 md:hidden z-40"
          onClick={toggleMobileSidebar}
        ></div>
      )}
    </div>
  )
}

// Export a wrapper component that provides the QueryClient
export default function NewsEventsManagementPage() {
  return (
    <QueryClientProvider client={queryClient}>
      <NewsEventsManagement />
    </QueryClientProvider>
  )
}

"use client"

import React from 'react'
import { Github, Mail, Phone, Heart } from 'lucide-react'
import Link from 'next/link'

export const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear()
  
  return (
    <footer className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 mt-auto">
      <div className="mx-auto w-full max-w-screen-xl px-4 py-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 md:gap-4">
          <div className="flex flex-col items-start">
            <h3 className="text-sm font-semibold text-gray-900 dark:text-white mb-2">Alfalah School</h3>
            <p className="text-xs text-gray-600 dark:text-gray-400 mb-4">
              Providing quality education and academic excellence since 2000.
            </p>
            <div className="flex space-x-3">
              <a href="#" className="text-gray-500 hover:text-gray-900 dark:hover:text-white">
                <Github size={18} />
                <span className="sr-only">GitHub</span>
              </a>
              <a href="mailto:<EMAIL>" className="text-gray-500 hover:text-gray-900 dark:hover:text-white">
                <Mail size={18} />
                <span className="sr-only">Email</span>
              </a>
              <a href="tel:+1234567890" className="text-gray-500 hover:text-gray-900 dark:hover:text-white">
                <Phone size={18} />
                <span className="sr-only">Phone</span>
              </a>
            </div>
          </div>
          
          <div className="md:flex md:justify-center">
            <div>
              <h3 className="text-sm font-semibold text-gray-900 dark:text-white mb-2">Quick Links</h3>
              <ul className="text-xs text-gray-600 dark:text-gray-400 space-y-2">
                <li>
                  <Link href="/students" className="hover:underline">Students</Link>
                </li>
                <li>
                  <Link href="/teachers" className="hover:underline">Teachers</Link>
                </li>
                <li>
                  <Link href="/classes" className="hover:underline">Classes</Link>
                </li>
                <li>
                  <Link href="/attendance" className="hover:underline">Attendance</Link>
                </li>
              </ul>
            </div>
          </div>
          
          <div className="md:flex md:justify-end">
            <div>
              <h3 className="text-sm font-semibold text-gray-900 dark:text-white mb-2">Resources</h3>
              <ul className="text-xs text-gray-600 dark:text-gray-400 space-y-2">
                <li>
                  <Link href="/transcript" className="hover:underline">Transcripts</Link>
                </li>
                <li>
                  <Link href="/reportcard" className="hover:underline">Report Cards</Link>
                </li>
                <li>
                  <Link href="/settings" className="hover:underline">Settings</Link>
                </li>
                <li>
                  <Link href="#" className="hover:underline">Help Center</Link>
                </li>
              </ul>
            </div>
          </div>
        </div>
        
        <hr className="my-3 border-gray-200 dark:border-gray-700" />
        
        <div className="flex flex-col items-center md:flex-row md:justify-between">
          <span className="text-xs text-gray-500 dark:text-gray-400">
            © {currentYear} <span className="font-semibold">Alfalah School</span>. All Rights Reserved.
          </span>
          <div className="flex items-center mt-2 md:mt-0 text-xs text-gray-500 dark:text-gray-400">
            <span>Made with</span>
            <Heart size={12} className="mx-1 text-red-500" />
            <span>by Admin</span>
          </div>
        </div>
      </div>
    </footer>
  )
} 
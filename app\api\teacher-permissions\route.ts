import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET all teacher permissions with teacher and class details
export async function GET() {
  try {
    console.log('Fetching teacher permissions with details...')
    
    const permissions = await prisma.teacherPermission.findMany({
      select: {
        id: true,
        teacherId: true,
        classId: true,
        canViewAttendance: true,
        canTakeAttendance: true,
        canAddMarks: true,
        canEditMarks: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: [
        { teacherId: 'asc' },
        { classId: 'asc' }
      ],
    })

    // Get teacher and class details separately to avoid complex joins
    const teacherIds = [...new Set(permissions.map(p => p.teacherId))]
    const classIds = [...new Set(permissions.map(p => p.classId))]

    // Fetch teachers from both Teacher table and User table
    const [teachersFromTable, usersWithTeacherRole, classes] = await Promise.all([
      prisma.teacher.findMany({
        where: { id: { in: teacherIds } },
        select: { id: true, name: true, email: true, subject: true }
      }),
      prisma.user.findMany({
        where: {
          id: { in: teacherIds },
          role: 'TEACHER',
          status: 'active'
        },
        select: { id: true, name: true, email: true }
      }),
      prisma.class.findMany({
        where: { id: { in: classIds } },
        select: { id: true, name: true }
      })
    ])

    // Combine teachers from both sources
    const allTeachers = [
      ...teachersFromTable,
      ...usersWithTeacherRole.map(user => ({
        id: user.id,
        name: user.name,
        email: user.email,
        subject: null // Users might not have a subject
      }))
    ]

    // Create lookup maps for performance
    const teacherMap = new Map(allTeachers.map(t => [t.id, t]))
    const classMap = new Map(classes.map(c => [c.id, c]))

    // Combine permissions with teacher and class details
    const permissionsWithDetails = permissions.map(permission => ({
      id: permission.id,
      teacherId: permission.teacherId,
      classId: permission.classId,
      teacher: teacherMap.get(permission.teacherId) || null,
      class: classMap.get(permission.classId) || null,
      canViewAttendance: permission.canViewAttendance,
      canTakeAttendance: permission.canTakeAttendance,
      canAddMarks: permission.canAddMarks,
      canEditMarks: permission.canEditMarks,
      createdAt: permission.createdAt,
      updatedAt: permission.updatedAt,
    }))

    console.log(`Found ${permissionsWithDetails.length} teacher permissions`)
    
    return NextResponse.json({
      permissions: permissionsWithDetails,
      total: permissionsWithDetails.length
    })
  } catch (error) {
    console.error('Error fetching teacher permissions:', error)
    return NextResponse.json(
      { error: 'Failed to fetch teacher permissions' },
      { status: 500 }
    )
  }
}

// POST to create or update teacher permissions
export async function POST(request: Request) {
  try {
    console.log('Creating/updating teacher permissions...')
    const data = await request.json()
    
    const { teacherId, classId, permissions } = data
    
    // Validate required fields
    if (!teacherId || !classId || !permissions) {
      return NextResponse.json(
        { error: 'Teacher ID, class ID, and permissions are required' },
        { status: 400 }
      )
    }

    // Validate permissions object
    const requiredPermissions = ['canViewAttendance', 'canTakeAttendance', 'canAddMarks', 'canEditMarks']
    for (const perm of requiredPermissions) {
      if (typeof permissions[perm] !== 'boolean') {
        return NextResponse.json(
          { error: `Permission '${perm}' must be a boolean value` },
          { status: 400 }
        )
      }
    }

    // Check if teacher exists
    const teacher = await prisma.teacher.findUnique({
      where: { id: teacherId },
      select: { id: true, name: true, email: true }
    })

    if (!teacher) {
      return NextResponse.json(
        { error: 'Teacher not found' },
        { status: 404 }
      )
    }

    // Check if class exists
    const classExists = await prisma.class.findUnique({
      where: { id: classId },
      select: { id: true, name: true }
    })

    if (!classExists) {
      return NextResponse.json(
        { error: 'Class not found' },
        { status: 404 }
      )
    }

    // Upsert teacher permissions (create or update)
    const teacherPermission = await prisma.teacherPermission.upsert({
      where: {
        teacherId_classId: {
          teacherId,
          classId
        }
      },
      update: {
        canViewAttendance: permissions.canViewAttendance,
        canTakeAttendance: permissions.canTakeAttendance,
        canAddMarks: permissions.canAddMarks,
        canEditMarks: permissions.canEditMarks,
        updatedAt: new Date()
      },
      create: {
        teacherId,
        classId,
        canViewAttendance: permissions.canViewAttendance,
        canTakeAttendance: permissions.canTakeAttendance,
        canAddMarks: permissions.canAddMarks,
        canEditMarks: permissions.canEditMarks
      },
      select: {
        id: true,
        teacherId: true,
        classId: true,
        canViewAttendance: true,
        canTakeAttendance: true,
        canAddMarks: true,
        canEditMarks: true,
        createdAt: true,
        updatedAt: true,
      }
    })

    console.log(`Teacher permissions saved successfully for teacher ${teacher.name} in class ${classExists.name}`)
    
    return NextResponse.json({
      message: 'Teacher permissions saved successfully',
      permission: {
        ...teacherPermission,
        teacher,
        class: classExists
      }
    })
  } catch (error) {
    console.error('Error saving teacher permissions:', error)
    return NextResponse.json(
      { error: 'Failed to save teacher permissions' },
      { status: 500 }
    )
  }
}

'use client'

import React, { useState, useEffect } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useAuth } from '../../contexts/auth-context'
import {
  Table, TableBody, TableCell, TableHead,
  TableHeader, TableRow
} from '@/app/components/ui/table'
import { Button } from '@/app/components/ui/button'
import { Input } from '@/app/components/ui/input'
import {
  DropdownMenu, DropdownMenuContent, DropdownMenuItem,
  DropdownMenuTrigger, DropdownMenuSeparator
} from '@/app/components/ui/dropdown-menu'
import { Badge } from '@/app/components/ui/badge'
import {
  AlertDialog, AlertDialogAction, AlertDialogCancel,
  AlertDialogContent, AlertDialogDescription, AlertDialogFooter,
  AlertDialogHeader, AlertDialogTitle
} from '@/app/components/ui/alert-dialog'
import {
  Toolt<PERSON>, TooltipContent, <PERSON>ltipProvider, TooltipTrigger
} from '@/app/components/ui/tooltip'
import {
  CheckCircle2, XCircle, AlertCircle, MoreHorizontal,
  Search, UserCog, Shield, ShieldAlert, ShieldCheck,
  UserX, RefreshCw, CheckCircle, Users, Database
} from 'lucide-react'

// Types
interface User {
  id: string
  name: string
  email: string
  role: string
  status: string
  createdAt: string
  updatedAt: string
  lastLogin?: string
}

interface Role {
  id: string
  name: string
  description: string
  permissions: string[]
  systemDefined: boolean
}

export function UserRolesTab() {
  const queryClient = useQueryClient()
  const { forceRefresh } = useAuth()
  const [searchTerm, setSearchTerm] = useState('')
  const [showPendingOnly, setShowPendingOnly] = useState(false)
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false)
  const [confirmAction, setConfirmAction] = useState<{
    type: 'role' | 'status'
    value: string
    userId: string
    userName: string
  } | null>(null)

  // Fetch users
  const {
    data: users = [],
    isLoading: isLoadingUsers,
    error: usersError,
    refetch: refetchUsers
  } = useQuery({
    queryKey: ['users'],
    queryFn: async () => {
      const response = await fetch('/api/users')
      if (!response.ok) {
        throw new Error('Failed to fetch users')
      }
      return response.json()
    }
  })

  // Fetch roles
  const {
    data: roles = [],
    isLoading: isLoadingRoles
  } = useQuery({
    queryKey: ['roles'],
    queryFn: async () => {
      const response = await fetch('/api/roles-mock')
      if (!response.ok) {
        throw new Error('Failed to fetch roles')
      }
      return response.json()
    }
  })

  // Update user role mutation
  const updateUserRole = useMutation({
    mutationFn: async ({ userId, role }: { userId: string, role: string }) => {
      const response = await fetch(`/api/users/${userId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ role }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update user role')
      }

      return response.json()
    },
    onSuccess: async () => {
      // Invalidate users query to refresh the table
      queryClient.invalidateQueries({ queryKey: ['users'] })

      // Force refresh auth context to update user permissions and sidebar
      await forceRefresh()

      // Also invalidate any other relevant queries
      queryClient.invalidateQueries({ queryKey: ['dashboardStats'] })
    }
  })

  // Update user status mutation
  const updateUserStatus = useMutation({
    mutationFn: async ({ userId, status }: { userId: string, status: string }) => {
      const response = await fetch(`/api/users/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update user status')
      }

      return response.json()
    },
    onSuccess: async () => {
      // Invalidate users query to refresh the table
      queryClient.invalidateQueries({ queryKey: ['users'] })

      // Force refresh auth context to update user permissions and sidebar
      await forceRefresh()

      // Also invalidate any other relevant queries
      queryClient.invalidateQueries({ queryKey: ['dashboardStats'] })
    }
  })

  // Check if a user is pending approval (inactive and recently registered)
  const isPendingApproval = (user: User) => {
    return user.status === 'inactive' &&
           user.createdAt &&
           (new Date().getTime() - new Date(user.createdAt).getTime()) < 7 * 24 * 60 * 60 * 1000;
  }

  // Filter users based on search term and pending status
  const filteredUsers = users.filter((user: User) => {
    const searchLower = searchTerm.toLowerCase()
    const matchesSearch = (
      user.name.toLowerCase().includes(searchLower) ||
      user.email.toLowerCase().includes(searchLower) ||
      user.role.toLowerCase().includes(searchLower) ||
      user.status.toLowerCase().includes(searchLower)
    )

    // If showing pending only, filter for inactive recent users
    if (showPendingOnly) {
      return matchesSearch && isPendingApproval(user);
    }

    return matchesSearch;
  })

  // Handle role change confirmation
  const handleRoleChangeConfirm = () => {
    if (confirmAction && confirmAction.type === 'role') {
      updateUserRole.mutate({
        userId: confirmAction.userId,
        role: confirmAction.value
      })
    }
    setConfirmDialogOpen(false)
    setConfirmAction(null)
  }

  // Handle status change confirmation
  const handleStatusChangeConfirm = () => {
    if (confirmAction && confirmAction.type === 'status') {
      updateUserStatus.mutate({
        userId: confirmAction.userId,
        status: confirmAction.value
      })
    }
    setConfirmDialogOpen(false)
    setConfirmAction(null)
  }

  // Get role name from role ID
  const getRoleName = (roleId: string): string => {
    const role = roles.find((r: Role) => r.id.toUpperCase() === roleId)
    return role ? role.name : roleId
  }

  // Render status badge
  const renderStatusBadge = (status: string, createdAt?: string) => {
    // Check if this is a newly registered user (less than 7 days old)
    const isNewUser = createdAt && (new Date().getTime() - new Date(createdAt).getTime()) < 7 * 24 * 60 * 60 * 1000;

    switch (status) {
      case 'active':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            <CheckCircle2 className="h-3.5 w-3.5 mr-1" />
            Active
          </Badge>
        )
      case 'inactive':
        return (
          <div className="flex flex-col">
            <Badge variant={isNewUser ? "default" : "outline"}
                  className={isNewUser
                    ? "bg-amber-500 text-white border-amber-600"
                    : "bg-gray-50 text-gray-700 border-gray-200"}>
              <XCircle className="h-3.5 w-3.5 mr-1" />
              {isNewUser ? "Pending Approval" : "Inactive"}
            </Badge>
            {isNewUser && (
              <span className="text-xs text-amber-600 mt-1">New registration</span>
            )}
          </div>
        )
      case 'locked':
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            <AlertCircle className="h-3.5 w-3.5 mr-1" />
            Locked
          </Badge>
        )
      default:
        return (
          <Badge variant="outline">{status}</Badge>
        )
    }
  }

  // Render role badge
  const renderRoleBadge = (role: string) => {
    switch (role) {
      case 'ADMIN':
        return (
          <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
            <ShieldAlert className="h-3.5 w-3.5 mr-1" />
            Administrator
          </Badge>
        )
      case 'HR_TEACHER':
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            <ShieldCheck className="h-3.5 w-3.5 mr-1" />
            HR Teacher
          </Badge>
        )
      case 'SUBJECT_TEACHER':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            <ShieldCheck className="h-3.5 w-3.5 mr-1" />
            Subject Teacher
          </Badge>
        )
      case 'TEACHER':
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            <ShieldCheck className="h-3.5 w-3.5 mr-1" />
            Teacher
          </Badge>
        )
      case 'STAFF':
        return (
          <Badge variant="outline" className="bg-indigo-50 text-indigo-700 border-indigo-200">
            <Shield className="h-3.5 w-3.5 mr-1" />
            Staff
          </Badge>
        )
      case 'PARENT':
        return (
          <Badge variant="outline" className="bg-pink-50 text-pink-700 border-pink-200">
            <UserCog className="h-3.5 w-3.5 mr-1" />
            Parent
          </Badge>
        )
      case 'UNIT_LEADER':
        return (
          <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
            <Users className="h-3.5 w-3.5 mr-1" />
            Unit Leader
          </Badge>
        )
      case 'DATA_ENCODER':
        return (
          <Badge variant="outline" className="bg-cyan-50 text-cyan-700 border-cyan-200">
            <Database className="h-3.5 w-3.5 mr-1" />
            Data Encoder
          </Badge>
        )
      default:
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
            <UserCog className="h-3.5 w-3.5 mr-1" />
            {role}
          </Badge>
        )
    }
  }

  // Handle role change
  const handleRoleChange = (userId: string, userName: string, role: string) => {
    setConfirmAction({
      type: 'role',
      value: role,
      userId,
      userName
    })
    setConfirmDialogOpen(true)
  }

  // Handle status change
  const handleStatusChange = (userId: string, userName: string, status: string) => {
    setConfirmAction({
      type: 'status',
      value: status,
      userId,
      userName
    })
    setConfirmDialogOpen(true)
  }

  // Check if <NAME_EMAIL>
  const isSystemAdmin = (email: string) => {
    return email === '<EMAIL>'
  }

  return (
    <div className="space-y-4">
      {/* Search and actions */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <div className="relative w-64">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              type="text"
              placeholder="Search users..."
              className="pl-9"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <Button
            variant={showPendingOnly ? "default" : "outline"}
            size="sm"
            onClick={() => setShowPendingOnly(!showPendingOnly)}
            className={showPendingOnly ? "bg-amber-500 hover:bg-amber-600" : ""}
          >
            <UserCheck className="h-4 w-4 mr-2" />
            {showPendingOnly ? "Showing Pending" : "Show Pending Approvals"}
          </Button>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => refetchUsers()}
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Users table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Role</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoadingUsers ? (
              <TableRow>
                <TableCell colSpan={5} className="h-24 text-center">
                  <div className="flex justify-center items-center">
                    <RefreshCw className="h-5 w-5 animate-spin text-gray-400 mr-2" />
                    <span>Loading users...</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : usersError ? (
              <TableRow>
                <TableCell colSpan={5} className="h-24 text-center">
                  <div className="flex flex-col items-center justify-center text-red-500">
                    <XCircle className="h-5 w-5 mb-1" />
                    <span>Error loading users</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => refetchUsers()}
                      className="mt-2"
                    >
                      Try Again
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ) : filteredUsers.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="h-24 text-center text-gray-500">
                  No users found
                </TableCell>
              </TableRow>
            ) : (
              filteredUsers.map((user: User) => (
                <TableRow
                  key={user.id}
                  className={isSystemAdmin(user.email) ? 'bg-indigo-50' : ''}
                >
                  <TableCell className="font-medium">
                    {user.name}
                    {isSystemAdmin(user.email) && (
                      <Badge variant="outline" className="ml-2 bg-indigo-100 text-indigo-800 border-indigo-300">
                        System Admin
                      </Badge>
                    )}
                  </TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>{renderRoleBadge(user.role)}</TableCell>
                  <TableCell>{renderStatusBadge(user.status, user.createdAt)}</TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Open menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-[200px]">
                        <DropdownMenuItem
                          className="text-xs font-medium text-gray-500"
                          disabled
                        >
                          Change Role
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleRoleChange(user.id, user.name, 'ADMIN')}
                          disabled={user.role === 'ADMIN' || isSystemAdmin(user.email)}
                          className={user.role === 'ADMIN' ? 'bg-purple-50' : ''}
                        >
                          <ShieldAlert className="h-4 w-4 mr-2" />
                          Administrator
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleRoleChange(user.id, user.name, 'HR_TEACHER')}
                          disabled={user.role === 'HR_TEACHER' || isSystemAdmin(user.email)}
                          className={user.role === 'HR_TEACHER' ? 'bg-blue-50' : ''}
                        >
                          <ShieldCheck className="h-4 w-4 mr-2" />
                          HR Teacher
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleRoleChange(user.id, user.name, 'SUBJECT_TEACHER')}
                          disabled={user.role === 'SUBJECT_TEACHER' || isSystemAdmin(user.email)}
                          className={user.role === 'SUBJECT_TEACHER' ? 'bg-green-50' : ''}
                        >
                          <ShieldCheck className="h-4 w-4 mr-2" />
                          Subject Teacher
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleRoleChange(user.id, user.name, 'PARENT')}
                          disabled={user.role === 'PARENT' || isSystemAdmin(user.email)}
                          className={user.role === 'PARENT' ? 'bg-pink-50' : ''}
                        >
                          <UserCog className="h-4 w-4 mr-2" />
                          Parent
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleRoleChange(user.id, user.name, 'UNIT_LEADER')}
                          disabled={user.role === 'UNIT_LEADER' || isSystemAdmin(user.email)}
                          className={user.role === 'UNIT_LEADER' ? 'bg-orange-50' : ''}
                        >
                          <Users className="h-4 w-4 mr-2" />
                          Unit Leader
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleRoleChange(user.id, user.name, 'DATA_ENCODER')}
                          disabled={user.role === 'DATA_ENCODER' || isSystemAdmin(user.email)}
                          className={user.role === 'DATA_ENCODER' ? 'bg-cyan-50' : ''}
                        >
                          <Database className="h-4 w-4 mr-2" />
                          Data Encoder
                        </DropdownMenuItem>

                        <DropdownMenuSeparator />

                        <DropdownMenuItem
                          className="text-xs font-medium text-gray-500"
                          disabled
                        >
                          Change Status
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleStatusChange(user.id, user.name, 'active')}
                          disabled={user.status === 'active' || isSystemAdmin(user.email)}
                          className={user.status === 'active' ? 'bg-green-50' : ''}
                        >
                          <CheckCircle className="h-4 w-4 mr-2" />
                          Active
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleStatusChange(user.id, user.name, 'inactive')}
                          disabled={user.status === 'inactive' || isSystemAdmin(user.email)}
                          className={user.status === 'inactive' ? 'bg-gray-50' : ''}
                        >
                          <XCircle className="h-4 w-4 mr-2" />
                          Inactive
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleStatusChange(user.id, user.name, 'locked')}
                          disabled={user.status === 'locked' || isSystemAdmin(user.email)}
                          className={user.status === 'locked' ? 'bg-red-50' : ''}
                        >
                          <UserX className="h-4 w-4 mr-2" />
                          Locked
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Confirmation Dialog */}
      <AlertDialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {confirmAction?.type === 'role'
                ? `Change Role to ${confirmAction.value}?`
                : `Change Status to ${confirmAction?.value}?`}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {confirmAction?.type === 'role'
                ? `Are you sure you want to change ${confirmAction.userName}'s role to ${confirmAction.value}?`
                : `Are you sure you want to change ${confirmAction?.userName}'s status to ${confirmAction?.value}?`}
              <br /><br />
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={
              confirmAction?.type === 'role'
                ? handleRoleChangeConfirm
                : handleStatusChangeConfirm
            }>
              Confirm
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}

import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

// This endpoint should be accessible without authentication for debugging
export const dynamic = 'force-dynamic';

export async function GET() {
  let prisma: PrismaClient | null = null;

  try {
    console.log('Testing database connection...');
    console.log('Database URL:', process.env.DATABASE_URL);

    // Create a new PrismaClient instance specifically for this test
    prisma = new PrismaClient({
      log: ['query', 'info', 'warn', 'error'],
      datasources: {
        db: {
          url: process.env.DATABASE_URL
        }
      }
    });

    // Try to connect to the database
    console.log('Connecting to database...');
    await prisma.$connect();
    console.log('Database connection successful');

    // Try to query the database
    console.log('Querying database...');

    // Check if tables exist by trying to count records
    let studentCount = 0;
    let classCount = 0;
    let userCount = 0;

    try {
      studentCount = await prisma.student.count();
      console.log(`Student count: ${studentCount}`);
    } catch (studentError) {
      console.error('Error counting students:', studentError);
    }

    try {
      classCount = await prisma.class.count();
      console.log(`Class count: ${classCount}`);
    } catch (classError) {
      console.error('Error counting classes:', classError);
    }

    try {
      userCount = await prisma.user.count();
      console.log(`User count: ${userCount}`);
    } catch (userError) {
      console.error('Error counting users:', userError);
    }

    // If we get here, the connection is working
    return NextResponse.json({
      status: 'success',
      message: 'Database connection successful',
      data: {
        studentCount,
        classCount,
        userCount
      }
    });
  } catch (error) {
    console.error('Database connection error:', error);

    // Return detailed error information
    return NextResponse.json({
      status: 'error',
      message: 'Database connection failed',
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      databaseUrl: process.env.DATABASE_URL ? '***' + process.env.DATABASE_URL.substring(process.env.DATABASE_URL.indexOf('@')) : 'Not set'
    }, { status: 500 });
  } finally {
    // Clean up the connection
    if (prisma) {
      await prisma.$disconnect().catch(e => console.error('Error disconnecting from database:', e));
    }
  }
}

"use client"

import React, { useState } from 'react'
import { <PERSON><PERSON> } from './ui/button'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/table'
import { Pencil, Trash2, Plus, Users, BookOpen } from 'lucide-react'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select'

interface User {
  id: string
  name: string
  isStaff: boolean
  isHRTeacher: boolean
  isAdmin: boolean
}

interface Role {
  id: string
  name: string
  description: string
  permissions: string[]
}

interface TeacherStaffAssignment {
  id: string
  teacherName: string
  hrClass: string
  subjects: string[]
  assignedClasses: string[]
}

// Sample data
const sampleUsers: User[] = [
  { id: '1', name: '<PERSON>', isStaff: true, isHRTeacher: true, isAdmin: false },
  { id: '2', name: '<PERSON>', isStaff: true, isHRTeacher: false, isAdmin: false },
  { id: '3', name: '<PERSON>', isStaff: false, isHRTeacher: false, isAdmin: true },
  { id: '4', name: '<PERSON>', isStaff: true, isHRTeacher: true, isAdmin: false },
  { id: '5', name: '<PERSON>', isStaff: false, isHRTeacher: false, isAdmin: false },
]

const sampleRoles: Role[] = [
  { id: '1', name: 'Administrator', description: 'Full system access', permissions: ['View', 'Create', 'Edit', 'Delete'] },
  { id: '2', name: 'Teacher', description: 'Access to classes and students', permissions: ['View', 'Create', 'Edit'] },
  { id: '3', name: 'HR Teacher', description: 'Manage class and attendance', permissions: ['View', 'Create', 'Edit'] },
  { id: '4', name: 'Staff', description: 'Limited system access', permissions: ['View'] },
]

// List of classes
const classesList = [
  '1A', '1B', '1C', '1D', '2A', '2B', '2C', '2D', '3A', '3B', '3C', '3D', 
  '4A', '4B', '4C', '4D', '5A', '5B', '5C', '5D', '6A', '6B', '6C', '6D', 
  '7A', '7B', '7C', '7D', '8A', '8B', '8C', '8D', '9A', '9B', '9C', '9D', 
  '10A', '10B', '10C', '10D', '11A', '11B', '11C', '11D', '12A', '12B', '12C', '12D'
]

const sampleTeachers = ['John Smith', 'Sarah Johnson', 'Emily Davis', 'David Wilson', 'Jessica Martin']
const sampleSubjects = ['Mathematics', 'English', 'Science', 'History', 'Computer Science', 'Physics', 'Chemistry', 'Biology']

const sampleAssignments: TeacherStaffAssignment[] = [
  { 
    id: '1', 
    teacherName: 'John Smith', 
    hrClass: 'Class VII-A', 
    subjects: ['Mathematics', 'Physics'], 
    assignedClasses: ['Class VII-A', 'Class VIII-B'] 
  },
  { 
    id: '2', 
    teacherName: 'Emily Davis', 
    hrClass: 'Class IX-A', 
    subjects: ['English', 'History'], 
    assignedClasses: ['Class IX-A', 'Class X-C'] 
  },
]

export function Roles() {
  const [activeTab, setActiveTab] = useState('allUser')
  const [users, setUsers] = useState<User[]>(sampleUsers)
  const [roles, setRoles] = useState<Role[]>(sampleRoles)
  const [assignments, setAssignments] = useState<TeacherStaffAssignment[]>(sampleAssignments)
  
  // Form state
  const [selectedTeacher, setSelectedTeacher] = useState('')
  const [selectedHRClass, setSelectedHRClass] = useState('none')
  const [showSubjectsList, setShowSubjectsList] = useState(false)
  const [showClassesList, setShowClassesList] = useState(false)
  const [selectedSubjects, setSelectedSubjects] = useState<string[]>([])
  const [selectedClasses, setSelectedClasses] = useState<string[]>([])
  
  const handleUserCheckChange = (userId: string, field: 'isStaff' | 'isHRTeacher' | 'isAdmin') => {
    setUsers(users.map(user => 
      user.id === userId ? { ...user, [field]: !user[field] } : user
    ))
  }
  
  const handleDeleteRole = (id: string) => {
    setRoles(roles.filter(role => role.id !== id))
  }
  
  const handleDeleteAssignment = (id: string) => {
    setAssignments(assignments.filter(assignment => assignment.id !== id))
  }
  
  const handleSubjectToggle = (subject: string) => {
    if (selectedSubjects.includes(subject)) {
      setSelectedSubjects(selectedSubjects.filter(s => s !== subject))
    } else {
      setSelectedSubjects([...selectedSubjects, subject])
    }
  }
  
  const handleClassToggle = (className: string) => {
    if (selectedClasses.includes(className)) {
      setSelectedClasses(selectedClasses.filter(c => c !== className))
    } else {
      setSelectedClasses([...selectedClasses, className])
    }
  }
  
  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (selectedTeacher && (selectedSubjects.length > 0 || selectedClasses.length > 0)) {
      const newAssignment: TeacherStaffAssignment = {
        id: (assignments.length + 1).toString(),
        teacherName: selectedTeacher,
        hrClass: selectedHRClass === 'none' ? '' : selectedHRClass,
        subjects: selectedSubjects,
        assignedClasses: selectedClasses
      }
      setAssignments([...assignments, newAssignment])
      // Reset form
      setSelectedTeacher('')
      setSelectedHRClass('none')
      setSelectedSubjects([])
      setSelectedClasses([])
      setShowSubjectsList(false)
      setShowClassesList(false)
    }
  }
  
  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold">Role Management</h1>
        <p className="text-gray-600 mt-1">Manage user roles and permissions</p>
      </div>
      
      {/* Tab Buttons */}
      <div className="flex border-b border-gray-200">
        <button
          className={`py-2 px-4 font-medium text-sm ${
            activeTab === 'allUser' 
              ? 'text-indigo-600 border-b-2 border-indigo-600' 
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('allUser')}
        >
          All User
        </button>
        <button
          className={`py-2 px-4 font-medium text-sm ${
            activeTab === 'userRoleManager' 
              ? 'text-indigo-600 border-b-2 border-indigo-600' 
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('userRoleManager')}
        >
          User Role Manager
        </button>
        <button
          className={`py-2 px-4 font-medium text-sm ${
            activeTab === 'teacherStaff' 
              ? 'text-indigo-600 border-b-2 border-indigo-600' 
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('teacherStaff')}
        >
          Teacher/Staff
        </button>
      </div>
      
      {/* Tab Content */}
      <div className="bg-white p-6 rounded-lg border border-gray-200">
        {/* All User Tab */}
        {activeTab === 'allUser' && (
          <div>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Users</TableHead>
                  <TableHead className="text-center">Staff</TableHead>
                  <TableHead className="text-center">HR Teacher</TableHead>
                  <TableHead className="text-center">Admin</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {users.map((user) => (
                  <TableRow key={user.id} className="h-10">
                    <TableCell className="font-medium py-2">{user.name}</TableCell>
                    <TableCell className="text-center py-2">
                      <input 
                        type="checkbox" 
                        checked={user.isStaff} 
                        onChange={() => handleUserCheckChange(user.id, 'isStaff')}
                        className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600"
                      />
                    </TableCell>
                    <TableCell className="text-center py-2">
                      <input 
                        type="checkbox" 
                        checked={user.isHRTeacher} 
                        onChange={() => handleUserCheckChange(user.id, 'isHRTeacher')}
                        className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600"
                      />
                    </TableCell>
                    <TableCell className="text-center py-2">
                      <input 
                        type="checkbox" 
                        checked={user.isAdmin} 
                        onChange={() => handleUserCheckChange(user.id, 'isAdmin')}
                        className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600"
                      />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
        
        {/* User Role Manager Tab */}
        {activeTab === 'userRoleManager' && (
          <div>
            <div className="flex justify-end mb-4">
              <Button className="bg-indigo-600 hover:bg-indigo-700">
                <Plus className="mr-2 h-4 w-4" /> Add New Role
              </Button>
            </div>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Role Name</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Permission</TableHead>
                  <TableHead className="text-right">Action</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {roles.map((role) => (
                  <TableRow key={role.id}>
                    <TableCell className="font-medium">{role.name}</TableCell>
                    <TableCell>{role.description}</TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {role.permissions.map((permission, index) => (
                          <span 
                            key={index}
                            className="inline-flex items-center rounded-full bg-indigo-50 px-2 py-1 text-xs font-medium text-indigo-700"
                          >
                            {permission}
                          </span>
                        ))}
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button variant="ghost" size="icon" className="h-8 w-8 text-indigo-600">
                          <Pencil className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          className="h-8 w-8 text-red-600"
                          onClick={() => handleDeleteRole(role.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
        
        {/* Teacher/Staff Tab */}
        {activeTab === 'teacherStaff' && (
          <div>
            <h2 className="text-lg font-semibold mb-4">Teacher and HR Assignments</h2>
            
            {/* Assignments Form */}
            <form onSubmit={handleFormSubmit} className="bg-gray-50 p-4 rounded-lg mb-6">
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Teacher Name</label>
                  <select 
                    value={selectedTeacher} 
                    onChange={(e) => setSelectedTeacher(e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-md"
                    required
                  >
                    <option value="">Select Teacher</option>
                    {sampleTeachers.map(teacher => (
                      <option key={teacher} value={teacher}>{teacher}</option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">HR Class</label>
                  <Select 
                    value={selectedHRClass} 
                    onValueChange={setSelectedHRClass}
                  >
                    <SelectTrigger>
                      <Users className="mr-2 h-4 w-4 text-gray-400" />
                      <SelectValue placeholder="Select Class" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">None (Not HR Teacher)</SelectItem>
                      {classesList.map(className => (
                        <SelectItem key={className} value={className}>
                          Class {className}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Subjects</label>
                  <div className="relative">
                    <button 
                      type="button"
                      onClick={() => setShowSubjectsList(!showSubjectsList)}
                      className="w-full p-2 border border-gray-300 rounded-md text-left flex items-center"
                    >
                      <BookOpen className="mr-2 h-4 w-4 text-gray-400" />
                      <span>{selectedSubjects.length ? `${selectedSubjects.length} subjects selected` : 'Select Subjects'}</span>
                      <span className="ml-auto">▼</span>
                    </button>
                    
                    {showSubjectsList && (
                      <div className="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg p-2 max-h-56 overflow-y-auto">
                        {sampleSubjects.map(subject => (
                          <label key={subject} className="flex items-center p-2 hover:bg-gray-100 rounded">
                            <input 
                              type="checkbox" 
                              checked={selectedSubjects.includes(subject)}
                              onChange={() => handleSubjectToggle(subject)}
                              className="mr-2"
                            />
                            {subject}
                          </label>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Assigned Classes</label>
                  <div className="relative">
                    <button 
                      type="button"
                      onClick={() => setShowClassesList(!showClassesList)}
                      className="w-full p-2 border border-gray-300 rounded-md text-left flex items-center"
                    >
                      <Users className="mr-2 h-4 w-4 text-gray-400" />
                      <span>{selectedClasses.length ? `${selectedClasses.length} classes selected` : 'Select Classes'}</span>
                      <span className="ml-auto">▼</span>
                    </button>
                    
                    {showClassesList && (
                      <div className="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg p-2 max-h-56 overflow-y-auto">
                        {classesList.map(className => (
                          <label key={className} className="flex items-center p-2 hover:bg-gray-100 rounded">
                            <input 
                              type="checkbox" 
                              checked={selectedClasses.includes(className)}
                              onChange={() => handleClassToggle(className)}
                              className="mr-2"
                            />
                            Class {className}
                          </label>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
              
              <Button type="submit" disabled={!selectedTeacher || (selectedSubjects.length === 0 && selectedClasses.length === 0)}>
                Assign Teacher
              </Button>
            </form>
            
            {/* Teacher/Staff Assignments Table */}
            {assignments.length > 0 && (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Teacher</TableHead>
                    <TableHead>HR Class</TableHead>
                    <TableHead>Subjects</TableHead>
                    <TableHead>Assigned Classes</TableHead>
                    <TableHead className="text-right">Action</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {assignments.map((assignment) => (
                    <TableRow key={assignment.id}>
                      <TableCell className="font-medium">{assignment.teacherName}</TableCell>
                      <TableCell>{assignment.hrClass || '-'}</TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {assignment.subjects.map((subject, index) => (
                            <span 
                              key={index}
                              className="inline-flex items-center rounded-full bg-green-50 px-2 py-1 text-xs font-medium text-green-700"
                            >
                              {subject}
                            </span>
                          ))}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {assignment.assignedClasses.map((className, index) => (
                            <span 
                              key={index}
                              className="inline-flex items-center rounded-full bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700"
                            >
                              {className}
                            </span>
                          ))}
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button variant="ghost" size="icon" className="h-8 w-8 text-indigo-600">
                            <Pencil className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="ghost" 
                            size="icon" 
                            className="h-8 w-8 text-red-600"
                            onClick={() => handleDeleteAssignment(assignment.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </div>
        )}
      </div>
    </div>
  )
} 
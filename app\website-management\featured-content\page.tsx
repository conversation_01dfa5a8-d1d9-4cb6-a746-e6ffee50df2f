'use client'

import React, { useState, useEffect } from 'react'
import dynamic from 'next/dynamic'
import Sidebar from '../../components/Sidebar'
import { Card } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Textarea } from '../../components/ui/textarea'
import { Label } from '../../components/ui/label'
import { Checkbox } from '../../components/ui/checkbox'
import {
  Table, TableBody, TableCaption, TableCell,
  TableHead, TableHeader, TableRow
} from '../../components/ui/table'
import {
  RecursionSafeDialog,
  RecursionSafeDialogContent,
  RecursionSafeDialogHeader,
  RecursionSafeDialogTitle,
  RecursionSafeDialogDescription,
  RecursionSafeDialogFooter
} from '../../components/RecursionSafeDialog'
import {
  DropdownMenu, DropdownMenuContent, DropdownMenuItem,
  DropdownMenuTrigger
} from '../../components/ui/dropdown-menu'
import {
  Globe, Image as ImageIcon, Plus, MoreHorizontal, Pencil, Trash2,
  Eye, EyeOff, AlertCircle, X, Check, List
} from 'lucide-react'
import { useQuery, useMutation, QueryClient, QueryClientProvider } from '@tanstack/react-query'
import Link from 'next/link'
import { useToast } from '../../components/ui/use-toast'
import { Toaster } from '../../components/ui/toaster'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs'

// Create a client
const queryClient = new QueryClient()

// Import Header component with no SSR to avoid hydration issues
const Header = dynamic(() => import('../../components/Header'), {
  ssr: false
})

// Type definition for feature
interface Feature {
  title: string;
  description: string;
}

// Type definition for featured content
interface FeaturedContent {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  features: Feature[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

function FeaturedContentManagement() {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false)
  const [isMounted, setIsMounted] = useState(false)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [currentFeaturedContent, setCurrentFeaturedContent] = useState<FeaturedContent | null>(null)
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    imageUrl: '',
    features: [{ title: '', description: '' }],
    isActive: true
  })
  const { toast } = useToast()

  // Fetch featured content from the API
  const { data: featuredContentList, isLoading, refetch } = useQuery<FeaturedContent[]>({
    queryKey: ['featuredContent'],
    queryFn: async () => {
      try {
        const res = await fetch('/api/website-management/featured-content')
        if (!res.ok) {
          throw new Error('Failed to fetch featured content')
        }
        return res.json()
      } catch (error) {
        // If API doesn't exist yet, return mock data
        return [
          {
            id: '1',
            title: 'Academic Excellence',
            description: 'At Alfalah Islamic School, we are committed to providing a high-quality education that prepares students for success in college, career, and life. Our rigorous academic program is designed to challenge students and help them reach their full potential.',
            imageUrl: '/images/academics.jpg',
            features: [
              {
                title: 'Qualified Teachers',
                description: 'Our teachers are highly qualified and dedicated to helping students succeed.'
              },
              {
                title: 'Small Class Sizes',
                description: 'We maintain small class sizes to ensure each student receives personalized attention.'
              },
              {
                title: 'Comprehensive Curriculum',
                description: 'Our curriculum integrates Islamic values with academic excellence.'
              },
              {
                title: 'Advanced Placement',
                description: 'We offer advanced placement courses for high-achieving students.'
              }
            ],
            isActive: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }
        ]
      }
    },
    staleTime: 60000, // 1 minute
    refetchOnWindowFocus: false,
  })

  // Add featured content mutation
  const addFeaturedContentMutation = useMutation({
    mutationFn: async (newFeaturedContent: Omit<FeaturedContent, 'id' | 'createdAt' | 'updatedAt'>) => {
      const res = await fetch('/api/website-management/featured-content', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newFeaturedContent),
      })

      if (!res.ok) {
        const error = await res.json()
        throw new Error(error.message || 'Failed to add featured content')
      }

      return res.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['featuredContent'] })
      queryClient.invalidateQueries({ queryKey: ['websiteStats'] })
      setIsAddDialogOpen(false)
      resetForm()
      toast({
        title: 'Success',
        description: 'Featured content added successfully',
        variant: 'default',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to add featured content',
        variant: 'destructive',
      })
    }
  })

  // Update featured content mutation
  const updateFeaturedContentMutation = useMutation({
    mutationFn: async (updatedFeaturedContent: Partial<FeaturedContent> & { id: string }) => {
      const res = await fetch(`/api/website-management/featured-content/${updatedFeaturedContent.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedFeaturedContent),
      })

      if (!res.ok) {
        const error = await res.json()
        throw new Error(error.message || 'Failed to update featured content')
      }

      return res.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['featuredContent'] })
      setIsEditDialogOpen(false)
      resetForm()
      toast({
        title: 'Success',
        description: 'Featured content updated successfully',
        variant: 'default',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update featured content',
        variant: 'destructive',
      })
    }
  })

  // Delete featured content mutation
  const deleteFeaturedContentMutation = useMutation({
    mutationFn: async (id: string) => {
      const res = await fetch(`/api/website-management/featured-content/${id}`, {
        method: 'DELETE',
      })

      if (!res.ok) {
        const error = await res.json()
        throw new Error(error.message || 'Failed to delete featured content')
      }

      return res.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['featuredContent'] })
      queryClient.invalidateQueries({ queryKey: ['websiteStats'] })
      setIsDeleteDialogOpen(false)
      toast({
        title: 'Success',
        description: 'Featured content deleted successfully',
        variant: 'default',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete featured content',
        variant: 'destructive',
      })
    }
  })

  useEffect(() => {
    setIsMounted(true)
  }, [])

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  const toggleMobileSidebar = () => {
    setMobileSidebarOpen(!mobileSidebarOpen)
  }

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      imageUrl: '',
      features: [{ title: '', description: '' }],
      isActive: true
    })
    setCurrentFeaturedContent(null)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleFeatureChange = (index: number, field: 'title' | 'description', value: string) => {
    const updatedFeatures = [...formData.features]
    updatedFeatures[index][field] = value
    setFormData(prev => ({ ...prev, features: updatedFeatures }))
  }

  const addFeature = () => {
    setFormData(prev => ({
      ...prev,
      features: [...prev.features, { title: '', description: '' }]
    }))
  }

  const removeFeature = (index: number) => {
    if (formData.features.length <= 1) {
      toast({
        title: 'Error',
        description: 'You must have at least one feature',
        variant: 'destructive',
      })
      return
    }

    const updatedFeatures = [...formData.features]
    updatedFeatures.splice(index, 1)
    setFormData(prev => ({ ...prev, features: updatedFeatures }))
  }

  const handleCheckboxChange = (checked: boolean) => {
    setFormData(prev => ({ ...prev, isActive: checked }))
  }

  const handleAddFeaturedContent = () => {
    // Validate features
    const validFeatures = formData.features.filter(feature =>
      feature.title.trim() !== '' && feature.description.trim() !== ''
    )

    if (validFeatures.length === 0) {
      toast({
        title: 'Error',
        description: 'You must have at least one valid feature with title and description',
        variant: 'destructive',
      })
      return
    }

    addFeaturedContentMutation.mutate({
      title: formData.title,
      description: formData.description,
      imageUrl: formData.imageUrl,
      features: validFeatures,
      isActive: formData.isActive
    })
  }

  const handleUpdateFeaturedContent = () => {
    if (!currentFeaturedContent) return

    // Validate features
    const validFeatures = formData.features.filter(feature =>
      feature.title.trim() !== '' && feature.description.trim() !== ''
    )

    if (validFeatures.length === 0) {
      toast({
        title: 'Error',
        description: 'You must have at least one valid feature with title and description',
        variant: 'destructive',
      })
      return
    }

    updateFeaturedContentMutation.mutate({
      id: currentFeaturedContent.id,
      title: formData.title,
      description: formData.description,
      imageUrl: formData.imageUrl,
      features: validFeatures,
      isActive: formData.isActive
    })
  }

  const handleDeleteFeaturedContent = () => {
    if (!currentFeaturedContent) return

    deleteFeaturedContentMutation.mutate(currentFeaturedContent.id)
  }

  const openEditDialog = (featuredContent: FeaturedContent) => {
    setCurrentFeaturedContent(featuredContent)
    setFormData({
      title: featuredContent.title,
      description: featuredContent.description,
      imageUrl: featuredContent.imageUrl,
      features: featuredContent.features,
      isActive: featuredContent.isActive
    })
    setIsEditDialogOpen(true)
  }

  const openDeleteDialog = (featuredContent: FeaturedContent) => {
    setCurrentFeaturedContent(featuredContent)
    setIsDeleteDialogOpen(true)
  }

  // Only show loading while mounting
  if (!isMounted || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-600"></div>
      </div>
    )
  }

  return (
    <div className="flex h-screen bg-gray-100 dark:bg-gray-900">
      <Sidebar
        isCollapsed={sidebarCollapsed}
        toggleSidebar={toggleSidebar}
        isMobileOpen={mobileSidebarOpen}
      />

      <main className={`main-content ${sidebarCollapsed ? 'sidebar-collapsed' : ''} w-full h-screen overflow-y-auto`}>
        <Header toggleMobileSidebar={toggleMobileSidebar} />
        <Toaster />

        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-2xl font-semibold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent inline-block">
                Featured Content Management
              </h1>
              <p className="text-gray-600 dark:text-gray-300 mt-1">
                Manage the featured content displayed on the website
              </p>
            </div>
            <div className="flex space-x-2">
              <Link href="/website" target="_blank">
                <Button variant="outline" size="sm" className="flex items-center">
                  <Globe className="h-4 w-4 mr-1" />
                  <span>View Website</span>
                </Button>
              </Link>
              <Button onClick={() => setIsAddDialogOpen(true)} className="flex items-center">
                <Plus className="h-4 w-4 mr-1" />
                <span>Add Featured Content</span>
              </Button>
            </div>
          </div>

          <Card className="overflow-hidden border-0 shadow-md">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[100px]">Image</TableHead>
                  <TableHead>Title</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead className="w-[120px]">Features</TableHead>
                  <TableHead className="w-[100px]">Status</TableHead>
                  <TableHead className="w-[100px] text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {featuredContentList && featuredContentList.length > 0 ? (
                  featuredContentList.map((featuredContent) => (
                    <TableRow key={featuredContent.id}>
                      <TableCell>
                        <div className="relative h-12 w-20 rounded overflow-hidden">
                          <img
                            src={featuredContent.imageUrl}
                            alt={featuredContent.title}
                            className="object-cover w-full h-full"
                          />
                        </div>
                      </TableCell>
                      <TableCell className="font-medium">{featuredContent.title}</TableCell>
                      <TableCell className="max-w-[300px] truncate">{featuredContent.description}</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <List className="h-4 w-4 mr-1" />
                          <span>{featuredContent.features.length}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {featuredContent.isActive ? (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                            Active
                          </span>
                        ) : (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                            Inactive
                          </span>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">Open menu</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => openEditDialog(featuredContent)}>
                              <Pencil className="h-4 w-4 mr-2" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => openDeleteDialog(featuredContent)}>
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => updateFeaturedContentMutation.mutate({ id: featuredContent.id, isActive: !featuredContent.isActive })}>
                              {featuredContent.isActive ? (
                                <>
                                  <EyeOff className="h-4 w-4 mr-2" />
                                  Deactivate
                                </>
                              ) : (
                                <>
                                  <Eye className="h-4 w-4 mr-2" />
                                  Activate
                                </>
                              )}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8">
                      <div className="flex flex-col items-center justify-center text-gray-500 dark:text-gray-400">
                        <ImageIcon className="h-12 w-12 mb-2 opacity-50" />
                        <p>No featured content found</p>
                        <Button
                          variant="link"
                          onClick={() => setIsAddDialogOpen(true)}
                          className="mt-2"
                        >
                          Add your first featured content
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </Card>
        </div>
      </main>

      {/* Add Featured Content Dialog */}
      <RecursionSafeDialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen} maxWidth="max-w-[700px]">
        <RecursionSafeDialogHeader>
          <RecursionSafeDialogTitle>Add New Featured Content</RecursionSafeDialogTitle>
          <RecursionSafeDialogDescription>
            Create new featured content for the website. All fields are required.
          </RecursionSafeDialogDescription>
        </RecursionSafeDialogHeader>
        <RecursionSafeDialogContent>
          <Tabs defaultValue="details" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="details">Details</TabsTrigger>
              <TabsTrigger value="features">Features</TabsTrigger>
            </TabsList>
            <TabsContent value="details" className="py-4">
              <div className="grid gap-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="title">Title</Label>
                    <Input
                      id="title"
                      name="title"
                      value={formData.title}
                      onChange={handleInputChange}
                      placeholder="Enter content title"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="imageUrl">Image URL</Label>
                    <Input
                      id="imageUrl"
                      name="imageUrl"
                      value={formData.imageUrl}
                      onChange={handleInputChange}
                      placeholder="Enter image URL (e.g., /images/academics.jpg)"
                    />
                    <p className="text-xs text-gray-500">
                      Recommended image size: 800x600 pixels
                    </p>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    placeholder="Enter content description"
                    rows={4}
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isActive"
                    checked={formData.isActive}
                    onCheckedChange={handleCheckboxChange}
                  />
                  <Label htmlFor="isActive">Active</Label>
                </div>
              </div>
            </TabsContent>
            <TabsContent value="features" className="py-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium">Features</h3>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addFeature}
                    className="flex items-center"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Add Feature
                  </Button>
                </div>

                <div className="space-y-4">
                  {formData.features.map((feature, index) => (
                    <div key={index} className="p-4 border rounded-md relative">
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute top-2 right-2 h-6 w-6 p-0"
                        onClick={() => removeFeature(index)}
                      >
                        <X className="h-4 w-4" />
                      </Button>

                      <div className="grid gap-4">
                        <div className="space-y-2">
                          <Label htmlFor={`feature-title-${index}`}>Feature Title</Label>
                          <Input
                            id={`feature-title-${index}`}
                            value={feature.title}
                            onChange={(e) => handleFeatureChange(index, 'title', e.target.value)}
                            placeholder="Enter feature title"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor={`feature-description-${index}`}>Feature Description</Label>
                          <Textarea
                            id={`feature-description-${index}`}
                            value={feature.description}
                            onChange={(e) => handleFeatureChange(index, 'description', e.target.value)}
                            placeholder="Enter feature description"
                            rows={2}
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </RecursionSafeDialogContent>
        <RecursionSafeDialogFooter>
          <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
            Cancel
          </Button>
          <Button onClick={handleAddFeaturedContent} disabled={addFeaturedContentMutation.isPending}>
            {addFeaturedContentMutation.isPending ? 'Adding...' : 'Add Featured Content'}
          </Button>
        </RecursionSafeDialogFooter>
      </RecursionSafeDialog>

      {/* Edit Featured Content Dialog */}
      <RecursionSafeDialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen} maxWidth="max-w-[700px]">
        <RecursionSafeDialogHeader>
          <RecursionSafeDialogTitle>Edit Featured Content</RecursionSafeDialogTitle>
          <RecursionSafeDialogDescription>
            Update the featured content information. All fields are required.
          </RecursionSafeDialogDescription>
        </RecursionSafeDialogHeader>
        <RecursionSafeDialogContent>
          <Tabs defaultValue="details" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="details">Details</TabsTrigger>
              <TabsTrigger value="features">Features</TabsTrigger>
            </TabsList>
            <TabsContent value="details" className="py-4">
              <div className="grid gap-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="edit-title">Title</Label>
                    <Input
                      id="edit-title"
                      name="title"
                      value={formData.title}
                      onChange={handleInputChange}
                      placeholder="Enter content title"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="edit-imageUrl">Image URL</Label>
                    <Input
                      id="edit-imageUrl"
                      name="imageUrl"
                      value={formData.imageUrl}
                      onChange={handleInputChange}
                      placeholder="Enter image URL (e.g., /images/academics.jpg)"
                    />
                    <p className="text-xs text-gray-500">
                      Recommended image size: 800x600 pixels
                    </p>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-description">Description</Label>
                  <Textarea
                    id="edit-description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    placeholder="Enter content description"
                    rows={4}
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="edit-isActive"
                    checked={formData.isActive}
                    onCheckedChange={handleCheckboxChange}
                  />
                  <Label htmlFor="edit-isActive">Active</Label>
                </div>
              </div>
            </TabsContent>
            <TabsContent value="features" className="py-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium">Features</h3>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addFeature}
                    className="flex items-center"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Add Feature
                  </Button>
                </div>

                <div className="space-y-4">
                  {formData.features.map((feature, index) => (
                    <div key={index} className="p-4 border rounded-md relative">
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute top-2 right-2 h-6 w-6 p-0"
                        onClick={() => removeFeature(index)}
                      >
                        <X className="h-4 w-4" />
                      </Button>

                      <div className="grid gap-4">
                        <div className="space-y-2">
                          <Label htmlFor={`edit-feature-title-${index}`}>Feature Title</Label>
                          <Input
                            id={`edit-feature-title-${index}`}
                            value={feature.title}
                            onChange={(e) => handleFeatureChange(index, 'title', e.target.value)}
                            placeholder="Enter feature title"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor={`edit-feature-description-${index}`}>Feature Description</Label>
                          <Textarea
                            id={`edit-feature-description-${index}`}
                            value={feature.description}
                            onChange={(e) => handleFeatureChange(index, 'description', e.target.value)}
                            placeholder="Enter feature description"
                            rows={2}
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </RecursionSafeDialogContent>
        <RecursionSafeDialogFooter>
          <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
            Cancel
          </Button>
          <Button onClick={handleUpdateFeaturedContent} disabled={updateFeaturedContentMutation.isPending}>
            {updateFeaturedContentMutation.isPending ? 'Updating...' : 'Update Featured Content'}
          </Button>
        </RecursionSafeDialogFooter>
      </RecursionSafeDialog>

      {/* Delete Confirmation Dialog */}
      <RecursionSafeDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen} maxWidth="max-w-[425px]">
        <RecursionSafeDialogHeader>
          <RecursionSafeDialogTitle>Confirm Deletion</RecursionSafeDialogTitle>
          <RecursionSafeDialogDescription>
            Are you sure you want to delete this featured content? This action cannot be undone.
          </RecursionSafeDialogDescription>
        </RecursionSafeDialogHeader>
        <RecursionSafeDialogContent>
          <div className="flex items-center space-x-2 py-4 text-amber-600 dark:text-amber-400">
            <AlertCircle className="h-5 w-5" />
            <p className="text-sm">This will permanently remove the featured content from the website.</p>
          </div>
        </RecursionSafeDialogContent>
        <RecursionSafeDialogFooter>
          <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleDeleteFeaturedContent}
            disabled={deleteFeaturedContentMutation.isPending}
          >
            {deleteFeaturedContentMutation.isPending ? 'Deleting...' : 'Delete Featured Content'}
          </Button>
        </RecursionSafeDialogFooter>
      </RecursionSafeDialog>

      {/* Overlay for mobile menu */}
      {mobileSidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 md:hidden z-40"
          onClick={toggleMobileSidebar}
        ></div>
      )}
    </div>
  )
}

// Export a wrapper component that provides the QueryClient
export default function FeaturedContentManagementPage() {
  return (
    <QueryClientProvider client={queryClient}>
      <FeaturedContentManagement />
    </QueryClientProvider>
  )
}

// Fallback data for when the database is not available
// This ensures the UI can still function with sample data

// List of subjects for the school
export const subjectsList = [
  'Arabic', 'Quran', 'Islamic Education', 'Terbia', 'English', 'Somali', 'Amharic',
  'Maths', 'Chemistry', 'Biology', 'Physics', 'IT', 'Agriculture', 'Economics',
  'Geography', 'History', 'CTE', 'Citizenship', 'HPE', 'General Science', 'Social Science'
]

export const fallbackClasses = [
  { id: 'class-1', name: '1A', totalStudents: 25, totalSubjects: 6, hrTeacherId: 'teacher-1', createdAt: new Date(), updatedAt: new Date() },
  { id: 'class-2', name: '1B', totalStudents: 22, totalSubjects: 6, hrTeacherId: 'teacher-2', createdAt: new Date(), updatedAt: new Date() },
  { id: 'class-3', name: '2A', totalStudents: 24, totalSubjects: 7, hrTeacherId: 'teacher-3', createdAt: new Date(), updatedAt: new Date() },
  { id: 'class-4', name: '2B', totalStudents: 23, totalSubjects: 7, hrTeacherId: null, createdAt: new Date(), updatedAt: new Date() },
];

export const fallbackTeachers = [
  { id: 'teacher-1', name: '<PERSON> <PERSON>', fatherName: 'David Smith', gender: 'Male', email: '<EMAIL>', subject: 'Mathematics', mobile: '1234567890', createdAt: new Date(), updatedAt: new Date() },
  { id: 'teacher-2', name: 'Sarah Johnson', fatherName: 'Robert Johnson', gender: 'Female', email: '<EMAIL>', subject: 'English', mobile: '2345678901', createdAt: new Date(), updatedAt: new Date() },
  { id: 'teacher-3', name: 'Michael Brown', fatherName: 'James Brown', gender: 'Male', email: '<EMAIL>', subject: 'Science', mobile: '3456789012', createdAt: new Date(), updatedAt: new Date() },
];

export const fallbackStudents = {
  '1A': [
    { id: 'student-1', sid: '1A001', name: 'Alice Cooper', className: '1A', fatherName: 'John Cooper', gfName: 'William Cooper', age: 6, gender: 'Female', createdAt: new Date(), updatedAt: new Date() },
    { id: 'student-2', sid: '1A002', name: 'Bob Wilson', className: '1A', fatherName: 'Thomas Wilson', gfName: 'George Wilson', age: 6, gender: 'Male', createdAt: new Date(), updatedAt: new Date() },
  ],
  '1B': [
    { id: 'student-3', sid: '1B001', name: 'Charlie Davis', className: '1B', fatherName: 'Richard Davis', gfName: 'Edward Davis', age: 6, gender: 'Male', createdAt: new Date(), updatedAt: new Date() },
  ],
  '2A': [
    { id: 'student-4', sid: '2A001', name: 'Diana Evans', className: '2A', fatherName: 'Michael Evans', gfName: 'Joseph Evans', age: 7, gender: 'Female', createdAt: new Date(), updatedAt: new Date() },
  ],
  '2B': [
    { id: 'student-5', sid: '2B001', name: 'Edward Martin', className: '2B', fatherName: 'Robert Martin', gfName: 'Charles Martin', age: 7, gender: 'Male', createdAt: new Date(), updatedAt: new Date() },
  ],
};

export const fallbackAttendance = {
  '1A': {
    '2023-05-01': [
      { id: 'att-1', studentId: 'student-1', status: 'present', date: '2023-05-01', className: '1A', createdAt: new Date(), updatedAt: new Date() },
      { id: 'att-2', studentId: 'student-2', status: 'absent', date: '2023-05-01', className: '1A', createdAt: new Date(), updatedAt: new Date() },
    ],
    '2023-05-02': [
      { id: 'att-3', studentId: 'student-1', status: 'present', date: '2023-05-02', className: '1A', createdAt: new Date(), updatedAt: new Date() },
      { id: 'att-4', studentId: 'student-2', status: 'present', date: '2023-05-02', className: '1A', createdAt: new Date(), updatedAt: new Date() },
    ],
  },
  '1B': {
    '2023-05-01': [
      { id: 'att-5', studentId: 'student-3', status: 'permission', date: '2023-05-01', className: '1B', createdAt: new Date(), updatedAt: new Date() },
    ],
  },
};

// Helper function to get students for a class
export function getStudentsForClass(className: string) {
  return fallbackStudents[className] || [];
}

// Helper function to get attendance records for a class and date
export function getAttendanceForClassAndDate(className: string, date: string) {
  return fallbackAttendance[className]?.[date] || [];
}

// Helper function to get subjects for a class
export function getSubjectsForClass(className: string) {
  // Return all subjects with IDs for each class
  return subjectsList.map((subject, index) => ({
    id: `subject-${index + 1}`,
    name: subject,
    classId: `class-${className.charAt(0)}`,
    class: { name: className }
  }));
}

// Helper function to generate a report
export function generateAttendanceReport(className: string, type: string, params: any) {
  const students = getStudentsForClass(className);

  // Sample report data
  return {
    summary: {
      totalStudents: students.length,
      presentCount: Math.floor(students.length * 0.8),
      absentCount: Math.floor(students.length * 0.15),
      permissionCount: Math.floor(students.length * 0.05),
      attendanceRate: 80,
      totalRecords: students.length,
    },
    dateRange: {
      start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days ago
      end: new Date().toISOString(), // today
    },
    dailyBreakdown: [
      {
        date: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000).toISOString(), // 6 days ago
        total: students.length,
        present: Math.floor(students.length * 0.8),
        absent: Math.floor(students.length * 0.15),
        permission: Math.floor(students.length * 0.05),
      },
      {
        date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(), // 5 days ago
        total: students.length,
        present: Math.floor(students.length * 0.85),
        absent: Math.floor(students.length * 0.1),
        permission: Math.floor(students.length * 0.05),
      },
      {
        date: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(), // 4 days ago
        total: students.length,
        present: Math.floor(students.length * 0.9),
        absent: Math.floor(students.length * 0.05),
        permission: Math.floor(students.length * 0.05),
      }
    ],
    studentBreakdown: students.map(student => ({
      studentId: student.id,
      studentName: student.name,
      present: Math.floor(Math.random() * 10),
      absent: Math.floor(Math.random() * 3),
      permission: Math.floor(Math.random() * 2),
      total: 10,
    })),
  };
}

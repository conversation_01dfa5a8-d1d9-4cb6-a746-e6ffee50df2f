"use client"

import { useCallback, useRef, useMemo, useEffect, useState } from 'react'

// Debounce hook for search and filter operations
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

// Throttle hook for scroll events
export function useThrottle<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const lastRun = useRef(Date.now())

  return useCallback(
    ((...args) => {
      if (Date.now() - lastRun.current >= delay) {
        callback(...args)
        lastRun.current = Date.now()
      }
    }) as T,
    [callback, delay]
  )
}

// Memoized table data processing
export function useTableDataProcessing<T>(
  data: T[],
  searchTerm: string,
  sortColumn: string | null,
  sortDirection: 'asc' | 'desc',
  filters: Record<string, any>,
  columns: Array<{ id: string; accessorKey?: keyof T; sortable?: boolean }>
) {
  const debouncedSearchTerm = useDebounce(searchTerm, 300)

  return useMemo(() => {
    let processed = [...data]

    // Apply search filter
    if (debouncedSearchTerm) {
      processed = processed.filter(row => {
        return columns.some(column => {
          if (!column.accessorKey) return false
          const value = row[column.accessorKey]
          return String(value).toLowerCase().includes(debouncedSearchTerm.toLowerCase())
        })
      })
    }

    // Apply filters
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        processed = processed.filter(row => {
          const rowValue = (row as any)[key]
          if (Array.isArray(value)) {
            return value.includes(rowValue)
          }
          return String(rowValue).toLowerCase().includes(String(value).toLowerCase())
        })
      }
    })

    // Apply sorting
    if (sortColumn) {
      const column = columns.find(col => col.id === sortColumn)
      if (column?.accessorKey && column.sortable) {
        processed.sort((a, b) => {
          const aVal = a[column.accessorKey!]
          const bVal = b[column.accessorKey!]
          
          if (aVal === bVal) return 0
          
          const comparison = aVal < bVal ? -1 : 1
          return sortDirection === 'asc' ? comparison : -comparison
        })
      }
    }

    return processed
  }, [data, debouncedSearchTerm, sortColumn, sortDirection, filters, columns])
}

// Memory management for large datasets
export function useMemoryManagement<T>(
  data: T[],
  maxCacheSize: number = 1000
) {
  const cache = useRef(new Map<string, T[]>())
  const cacheKeys = useRef<string[]>([])

  const getCachedData = useCallback((key: string): T[] | undefined => {
    return cache.current.get(key)
  }, [])

  const setCachedData = useCallback((key: string, value: T[]) => {
    // Remove oldest entries if cache is full
    if (cache.current.size >= maxCacheSize) {
      const oldestKey = cacheKeys.current.shift()
      if (oldestKey) {
        cache.current.delete(oldestKey)
      }
    }

    cache.current.set(key, value)
    cacheKeys.current.push(key)
  }, [maxCacheSize])

  const clearCache = useCallback(() => {
    cache.current.clear()
    cacheKeys.current = []
  }, [])

  return {
    getCachedData,
    setCachedData,
    clearCache,
    cacheSize: cache.current.size
  }
}

// Intersection Observer for lazy loading
export function useIntersectionObserver(
  options: IntersectionObserverInit = {}
) {
  const [isIntersecting, setIsIntersecting] = useState(false)
  const [entry, setEntry] = useState<IntersectionObserverEntry | null>(null)
  const elementRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const element = elementRef.current
    if (!element) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting)
        setEntry(entry)
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
        ...options
      }
    )

    observer.observe(element)

    return () => {
      observer.disconnect()
    }
  }, [options])

  return {
    elementRef,
    isIntersecting,
    entry
  }
}

// Performance monitoring
export function usePerformanceMonitor() {
  const renderCount = useRef(0)
  const renderTimes = useRef<number[]>([])
  const lastRenderTime = useRef(Date.now())

  useEffect(() => {
    renderCount.current += 1
    const currentTime = Date.now()
    const renderTime = currentTime - lastRenderTime.current
    
    renderTimes.current.push(renderTime)
    
    // Keep only last 100 render times
    if (renderTimes.current.length > 100) {
      renderTimes.current.shift()
    }
    
    lastRenderTime.current = currentTime
  })

  const getAverageRenderTime = useCallback(() => {
    if (renderTimes.current.length === 0) return 0
    const sum = renderTimes.current.reduce((a, b) => a + b, 0)
    return sum / renderTimes.current.length
  }, [])

  const getPerformanceStats = useCallback(() => {
    return {
      renderCount: renderCount.current,
      averageRenderTime: getAverageRenderTime(),
      lastRenderTime: renderTimes.current[renderTimes.current.length - 1] || 0,
      maxRenderTime: Math.max(...renderTimes.current),
      minRenderTime: Math.min(...renderTimes.current)
    }
  }, [getAverageRenderTime])

  return {
    renderCount: renderCount.current,
    getPerformanceStats
  }
}

// Optimized event handlers
export function useOptimizedEventHandlers() {
  const eventHandlers = useRef(new Map<string, (...args: any[]) => void>())

  const createHandler = useCallback(<T extends (...args: any[]) => void>(
    key: string,
    handler: T,
    dependencies: any[] = []
  ): T => {
    const memoizedHandler = useCallback(handler, dependencies)
    eventHandlers.current.set(key, memoizedHandler)
    return memoizedHandler
  }, [])

  const getHandler = useCallback((key: string) => {
    return eventHandlers.current.get(key)
  }, [])

  const clearHandlers = useCallback(() => {
    eventHandlers.current.clear()
  }, [])

  return {
    createHandler,
    getHandler,
    clearHandlers
  }
}

// Batch updates for better performance
export function useBatchUpdates<T>(
  initialState: T,
  batchDelay: number = 16 // One frame at 60fps
) {
  const [state, setState] = useState<T>(initialState)
  const pendingUpdates = useRef<Partial<T>[]>([])
  const timeoutRef = useRef<NodeJS.Timeout>()

  const batchUpdate = useCallback((update: Partial<T>) => {
    pendingUpdates.current.push(update)

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    timeoutRef.current = setTimeout(() => {
      setState(prevState => {
        const updates = pendingUpdates.current
        pendingUpdates.current = []
        
        return updates.reduce((acc, update) => ({ ...acc, ...update }), prevState)
      })
    }, batchDelay)
  }, [batchDelay])

  const flushUpdates = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    if (pendingUpdates.current.length > 0) {
      setState(prevState => {
        const updates = pendingUpdates.current
        pendingUpdates.current = []
        
        return updates.reduce((acc, update) => ({ ...acc, ...update }), prevState)
      })
    }
  }, [])

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  return {
    state,
    batchUpdate,
    flushUpdates
  }
}

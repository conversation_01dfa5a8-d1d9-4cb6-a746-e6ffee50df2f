import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET all teacher permissions using direct SQL
export async function GET() {
  try {
    console.log('Fetching all teacher permissions (direct SQL)')

    // Use raw SQL to fetch permissions
    const permissions = await prisma.$queryRaw`
      SELECT * FROM teacher_permissions
    `

    console.log(`Found ${Array.isArray(permissions) ? permissions.length : 0} teacher permissions`)
    return NextResponse.json({ permissions })
  } catch (error) {
    console.error('Error fetching teacher permissions:', error)
    return NextResponse.json(
      { error: 'Failed to fetch teacher permissions' },
      { status: 500 }
    )
  }
}

// POST to create or update teacher permissions using direct SQL
export async function POST(request: Request) {
  try {
    console.log('Creating/updating teacher permissions (direct SQL)')

    // Get the request body
    const data = await request.json()
    console.log('Received data:', JSON.stringify(data, null, 2))

    // Validate required fields
    if (!data.teacherId || !data.classId || !data.permissions) {
      return NextResponse.json(
        { error: 'Teacher ID, Class ID, and permissions are required' },
        { status: 400 }
      )
    }

    // Check if the user with TEACHER role exists
    const teacher = await prisma.user.findFirst({
      where: { 
        id: data.teacherId,
        role: 'TEACHER'
      }
    })

    if (!teacher) {
      return NextResponse.json(
        { error: 'Teacher not found or user is not a teacher' },
        { status: 404 }
      )
    }

    // Check if the class exists
    const classObj = await prisma.class.findUnique({
      where: { id: data.classId }
    })

    if (!classObj) {
      return NextResponse.json(
        { error: 'Class not found' },
        { status: 404 }
      )
    }

    // Check if permissions already exist
    const existingPermissions = await prisma.$queryRaw`
      SELECT * FROM teacher_permissions 
      WHERE teacherId = ${data.teacherId} AND classId = ${data.classId}
    `

    let result;
    const canViewAttendance = data.permissions.canViewAttendance ? 1 : 0;
    const canTakeAttendance = data.permissions.canTakeAttendance ? 1 : 0;
    const canAddMarks = data.permissions.canAddMarks ? 1 : 0;
    const canEditMarks = data.permissions.canEditMarks ? 1 : 0;
    const now = new Date();

    if (Array.isArray(existingPermissions) && existingPermissions.length > 0) {
      // Update existing permissions
      const permissionId = existingPermissions[0].id;
      console.log(`Updating permissions with ID: ${permissionId}`)
      
      result = await prisma.$executeRaw`
        UPDATE teacher_permissions 
        SET 
          canViewAttendance = ${canViewAttendance},
          canTakeAttendance = ${canTakeAttendance},
          canAddMarks = ${canAddMarks},
          canEditMarks = ${canEditMarks},
          updatedAt = ${now}
        WHERE id = ${permissionId}
      `
      
      console.log(`Updated permissions for teacher ${teacher.name} in class ${classObj.name}`)
      
      // Fetch the updated record
      const updatedPermission = await prisma.$queryRaw`
        SELECT * FROM teacher_permissions WHERE id = ${permissionId}
      `
      
      return NextResponse.json({
        message: 'Permissions updated successfully',
        permissions: updatedPermission[0]
      })
    } else {
      // Create new permissions
      console.log('Creating new permissions')
      
      // Generate a unique ID
      const id = `perm_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
      
      result = await prisma.$executeRaw`
        INSERT INTO teacher_permissions (
          id, teacherId, classId, 
          canViewAttendance, canTakeAttendance, 
          canAddMarks, canEditMarks,
          createdAt, updatedAt
        ) VALUES (
          ${id}, ${data.teacherId}, ${data.classId},
          ${canViewAttendance}, ${canTakeAttendance},
          ${canAddMarks}, ${canEditMarks},
          ${now}, ${now}
        )
      `
      
      console.log(`Created permissions for teacher ${teacher.name} in class ${classObj.name}`)
      
      // Fetch the created record
      const createdPermission = await prisma.$queryRaw`
        SELECT * FROM teacher_permissions WHERE id = ${id}
      `
      
      return NextResponse.json({
        message: 'Permissions created successfully',
        permissions: createdPermission[0]
      })
    }
  } catch (error) {
    console.error('Error creating/updating teacher permissions:', error)
    // Log more detailed error information
    if (error instanceof Error) {
      console.error('Error message:', error.message)
      console.error('Error stack:', error.stack)
    }
    return NextResponse.json(
      { error: 'Failed to create/update teacher permissions', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    )
  }
}

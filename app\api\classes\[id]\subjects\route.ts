import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// GET - Fetch subjects for a specific class
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const classId = params.id

    if (!classId) {
      return NextResponse.json(
        { error: 'Class ID is required' },
        { status: 400 }
      )
    }

    // Fetch subjects for the specific class
    const subjects = await prisma.subject.findMany({
      where: {
        classId: classId
      },
      select: {
        id: true,
        name: true,
        classId: true
      }
    })

    return NextResponse.json(subjects)
  } catch (error) {
    console.error('Error fetching class subjects:', error)
    return NextResponse.json(
      { error: 'Failed to fetch class subjects' },
      { status: 500 }
    )
  }
}

// DELETE - Remove all subjects for a specific class
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const classId = params.id

    if (!classId) {
      return NextResponse.json(
        { error: 'Class ID is required' },
        { status: 400 }
      )
    }

    // Check if the class exists
    const classExists = await prisma.class.findUnique({
      where: { id: classId }
    })

    if (!classExists) {
      return NextResponse.json(
        { error: 'Class not found' },
        { status: 404 }
      )
    }

    // Delete all subjects for this class
    const deleteResult = await prisma.subject.deleteMany({
      where: {
        classId: classId
      }
    })

    console.log(`Deleted ${deleteResult.count} subjects for class ${classId}`)

    return NextResponse.json({
      message: `Successfully deleted ${deleteResult.count} subjects`,
      deletedCount: deleteResult.count
    })
  } catch (error) {
    console.error('Error deleting class subjects:', error)
    return NextResponse.json(
      { error: 'Failed to delete class subjects' },
      { status: 500 }
    )
  }
}

import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function PUT(request: Request) {
  try {
    const body = await request.json()
    const { marks } = body

    // Validate request body
    if (!marks || !Array.isArray(marks) || marks.length === 0) {
      return NextResponse.json(
        { error: 'Marks array is required and must not be empty' },
        { status: 400 }
      )
    }

    console.log(`Updating ${marks.length} marks in batch`)

    // Validate each mark object
    for (const mark of marks) {
      if (!mark.id || typeof mark.marks !== 'number' || typeof mark.totalMarks !== 'number') {
        return NextResponse.json(
          { error: 'Each mark must have id, marks, and totalMarks' },
          { status: 400 }
        )
      }

      if (mark.marks < 0 || mark.marks > mark.totalMarks) {
        return NextResponse.json(
          { error: `Invalid marks: ${mark.marks}. Must be between 0 and ${mark.totalMarks}` },
          { status: 400 }
        )
      }
    }

    // Perform batch update using transaction
    const updateResults = await prisma.$transaction(
      marks.map(mark =>
        prisma.mark.update({
          where: { id: mark.id },
          data: {
            marks: mark.marks,
            totalMarks: mark.totalMarks,
            updatedAt: new Date(),
          },
          include: {
            student: {
              select: {
                name: true,
                sid: true,
              },
            },
          },
        })
      )
    )

    console.log(`Successfully updated ${updateResults.length} marks`)

    // Return success response with updated marks info
    const updatedMarksInfo = updateResults.map(mark => ({
      id: mark.id,
      studentName: mark.student.name,
      studentSid: mark.student.sid,
      marks: mark.marks,
      totalMarks: mark.totalMarks,
      subject: mark.subject,
      className: mark.className,
      term: mark.term,
      academicYear: mark.academicYear,
    }))

    return NextResponse.json({
      message: `Successfully updated ${updateResults.length} marks`,
      updatedCount: updateResults.length,
      updatedMarks: updatedMarksInfo,
    })
  } catch (error) {
    console.error('Error updating marks in batch:', error)
    
    // Handle specific Prisma errors
    if (error instanceof Error) {
      if (error.message.includes('Record to update not found')) {
        return NextResponse.json(
          { error: 'One or more marks not found. They may have been deleted.' },
          { status: 404 }
        )
      }
    }

    return NextResponse.json(
      { error: 'Failed to update marks' },
      { status: 500 }
    )
  }
}

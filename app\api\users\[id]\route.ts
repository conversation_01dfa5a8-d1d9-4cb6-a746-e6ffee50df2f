import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { verifyJWT } from '@/lib/jwt'
import { cookies } from 'next/headers'

// GET a single user by ID
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    console.log(`Fetching user with ID: ${params.id}`)

    // Verify authentication
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    if (!token) {
      console.error('No authentication token found')
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    }

    try {
      // Verify the token
      const decoded = await verifyJWT(token)

      // Only allow admins, super admins, or the user themselves to access user data
      if (decoded.role !== 'ADMIN' && decoded.role !== 'SUPER_ADMIN' && decoded.id !== params.id) {
        console.error('Unauthorized access attempt', {
          requesterId: decoded.id,
          requestedId: params.id,
          requesterRole: decoded.role
        })
        return NextResponse.json(
          { error: 'Forbidden - Insufficient permissions' },
          { status: 403 }
        )
      }

      // Fetch the user
      const user = await prisma.user.findUnique({
        where: { id: params.id },
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          status: true,
          createdAt: true,
          updatedAt: true,
          lastLogin: true,
        },
      })

      if (!user) {
        console.error(`User with ID ${params.id} not found`)
        return NextResponse.json(
          { error: 'User not found' },
          { status: 404 }
        )
      }

      console.log(`Successfully fetched user: ${user.email}`)
      return NextResponse.json(user)

    } catch (jwtError) {
      console.error('JWT verification error:', jwtError)
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      )
    }
  } catch (error) {
    console.error('Error fetching user:', error)
    return NextResponse.json(
      { error: 'Failed to fetch user' },
      { status: 500 }
    )
  }
}

// PATCH to update a user's role
export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    console.log(`Updating user with ID: ${params.id} - Updated with new roles`)

    // Verify authentication
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    // For development mode, bypass authentication
    if (process.env.NODE_ENV === 'development') {
      console.log('Development mode - bypassing authentication checks')
    } else if (!token) {
      console.error('No authentication token found')
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    } else {
      try {
        // Verify the token
        const decoded = await verifyJWT(token)

        // Only allow admins and super admins to update user roles
        if (decoded.role !== 'ADMIN' && decoded.role !== 'SUPER_ADMIN') {
          console.error('Unauthorized role update attempt', {
            requesterId: decoded.id,
            requesterRole: decoded.role
          })
          return NextResponse.json(
            { error: 'Forbidden - Only administrators can update user roles' },
            { status: 403 }
          )
        }
      } catch (jwtError) {
        console.error('JWT verification error:', jwtError)
        return NextResponse.json(
          { error: 'Invalid or expired token' },
          { status: 401 }
        )
      }
    }

    // Get the request body
    const data = await request.json()

    // Validate the role
    const validRoles = ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'ACCOUNTANT', 'TEACHER', 'PARENT', 'UNIT_LEADER', 'DATA_ENCODER']
    console.log(`Role validation - Received role: ${data.role}, Valid roles: ${validRoles.join(', ')}`)

    if (!validRoles.includes(data.role)) {
      console.error(`Invalid role: ${data.role}`)
      return NextResponse.json(
        { error: 'Invalid role. Must be one of: SUPER_ADMIN, ADMIN, SUPERVISOR, ACCOUNTANT, TEACHER, PARENT, UNIT_LEADER, DATA_ENCODER' },
        { status: 400 }
      )
    }

    console.log(`Role validation passed for: ${data.role}`)

    // Check if the user exists
    const existingUser = await prisma.user.findUnique({
      where: { id: params.id },
      select: { id: true, email: true, role: true }
    })

    if (!existingUser) {
      console.error(`User with ID ${params.id} not found`)
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Special case for system admin accounts - always keep as SUPER_ADMIN or ADMIN
    if ((existingUser.email === '<EMAIL>' || existingUser.email === '<EMAIL>') &&
        (existingUser.role === 'SUPER_ADMIN' && data.role !== 'SUPER_ADMIN') ||
        (existingUser.role === 'ADMIN' && data.role !== 'ADMIN')) {
      console.warn(`Attempt to change ${existingUser.email} role prevented`)
      return NextResponse.json(
        {
          error: `Cannot change role for ${existingUser.email}`,
          message: 'System administrator accounts must maintain their role for system access.'
        },
        { status: 400 }
      )
    }

    // Update the user's role
    console.log(`Attempting to update user ${params.id} role to: ${data.role}`)

    try {
      const updatedUser = await prisma.user.update({
        where: { id: params.id },
        data: {
          role: data.role,
          updatedAt: new Date()
        },
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          status: true,
          updatedAt: true,
        },
      })

      console.log(`Successfully updated user role in database: ${updatedUser.email} -> ${updatedUser.role}`)
      return NextResponse.json({
        message: 'User role updated successfully',
        user: updatedUser
      })
    } catch (prismaError) {
      console.error('Prisma error when updating user role:', prismaError)

      // Check if it's an enum constraint error
      if (prismaError instanceof Error && prismaError.message.includes('Invalid enum value')) {
        return NextResponse.json(
          { error: `Invalid role value: ${data.role}. This role is not supported by the database.` },
          { status: 400 }
        )
      }

      throw prismaError // Re-throw if it's not an enum error
    }
  } catch (error) {
    console.error('Error updating user role:', error)
    return NextResponse.json(
      { error: 'Failed to update user role' },
      { status: 500 }
    )
  }
}

// PUT to update a user's status (active/inactive)
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    console.log(`Updating user status with ID: ${params.id}`)

    // Verify authentication
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    // For development mode, bypass authentication
    if (process.env.NODE_ENV === 'development') {
      console.log('Development mode - bypassing authentication checks')
    } else if (!token) {
      console.error('No authentication token found')
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    } else {
      try {
        // Verify the token
        const decoded = await verifyJWT(token)

        // Only allow admins and super admins to update user status
        if (decoded.role !== 'ADMIN' && decoded.role !== 'SUPER_ADMIN') {
          console.error('Unauthorized status update attempt', {
            requesterId: decoded.id,
            requesterRole: decoded.role
          })
          return NextResponse.json(
            { error: 'Forbidden - Only administrators can update user status' },
            { status: 403 }
          )
        }
      } catch (jwtError) {
        console.error('JWT verification error:', jwtError)
        return NextResponse.json(
          { error: 'Invalid or expired token' },
          { status: 401 }
        )
      }
    }

    // Get the request body
    const data = await request.json()

    // Validate the status
    const validStatuses = ['active', 'inactive', 'locked', 'pending']
    if (!validStatuses.includes(data.status)) {
      console.error(`Invalid status: ${data.status}`)
      return NextResponse.json(
        { error: 'Invalid status. Must be one of: active, inactive, locked, pending' },
        { status: 400 }
      )
    }

    // Check if the user exists
    const existingUser = await prisma.user.findUnique({
      where: { id: params.id },
      select: { id: true, email: true, status: true }
    })

    if (!existingUser) {
      console.error(`User with ID ${params.id} not found`)
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Special case for system admin accounts - prevent locking or deactivating
    if ((existingUser.email === '<EMAIL>' || existingUser.email === '<EMAIL>') &&
        data.status !== 'active') {
      console.warn(`Attempt to deactivate ${existingUser.email} prevented`)
      return NextResponse.json(
        {
          error: `Cannot deactivate ${existingUser.email}`,
          message: 'System administrator accounts must remain active for system access.'
        },
        { status: 400 }
      )
    }

    // Update the user's status
    const updatedUser = await prisma.user.update({
      where: { id: params.id },
      data: {
        status: data.status,
        updatedAt: new Date()
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        status: true,
        updatedAt: true,
      },
    })

    console.log(`Successfully updated user status: ${updatedUser.email} -> ${updatedUser.status}`)
    return NextResponse.json({
      message: 'User status updated successfully',
      user: updatedUser
    })
  } catch (error) {
    console.error('Error updating user status:', error)
    return NextResponse.json(
      { error: 'Failed to update user status' },
      { status: 500 }
    )
  }
}

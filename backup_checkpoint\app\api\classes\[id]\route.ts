import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const cls = await prisma.class.findUnique({
      where: {
        id: params.id,
      },
      include: {
        hrTeacher: {
          select: {
            id: true,
            name: true,
            subject: true,
          },
        },
        subjects: true,
      },
    })
    
    if (!cls) {
      return NextResponse.json(
        { error: 'Class not found' },
        { status: 404 }
      )
    }
    
    return NextResponse.json(cls)
  } catch (error) {
    console.error('Error fetching class:', error)
    return NextResponse.json(
      { error: 'Failed to fetch class' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const data = await request.json()
    const updatedClass = await prisma.class.update({
      where: {
        id: params.id,
      },
      data: {
        name: data.name,
        hrTeacherId: data.hrTeacherId === 'none' ? null : data.hrTeacherId,
        totalSubjects: data.totalSubjects !== undefined ? data.totalSubjects : undefined,
        totalStudents: data.totalStudents !== undefined ? data.totalStudents : undefined,
      },
      include: {
        hrTeacher: {
          select: {
            id: true,
            name: true,
            subject: true,
          },
        },
      },
    })
    return NextResponse.json(updatedClass)
  } catch (error) {
    console.error('Error updating class:', error)
    return NextResponse.json(
      { error: 'Failed to update class' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    await prisma.class.delete({
      where: {
        id: params.id,
      },
    })
    return NextResponse.json({ message: 'Class deleted successfully' })
  } catch (error) {
    console.error('Error deleting class:', error)
    return NextResponse.json(
      { error: 'Failed to delete class' },
      { status: 500 }
    )
  }
}

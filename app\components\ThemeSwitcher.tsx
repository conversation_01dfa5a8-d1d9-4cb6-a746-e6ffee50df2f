'use client';

import React from 'react';
import { useEnhancedTheme, ThemeName } from '../contexts/enhanced-theme-context';
import { <PERSON>, Moon, Palette, Eye, RotateCcw } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu";
import { Button } from './ui/button';
import { Switch } from './ui/switch';
import { Label } from './ui/label';

export const ThemeSwitcher = () => {
  const { currentTheme, setTheme, reducedMotion, toggleReducedMotion } = useEnhancedTheme();
  
  const themeOptions: { name: ThemeName; icon: React.ReactNode; label: string }[] = [
    { name: 'light', icon: <Sun className="h-4 w-4" />, label: 'Light' },
    { name: 'dark', icon: <Moon className="h-4 w-4" />, label: 'Dark' },
    { name: 'solarized', icon: <Palette className="h-4 w-4" />, label: 'Solarized' },
    { name: 'high-contrast', icon: <Eye className="h-4 w-4" />, label: 'High Contrast' }
  ];
  
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="relative rounded-full">
          {currentTheme.name === 'light' && <Sun className="h-5 w-5" />}
          {currentTheme.name === 'dark' && <Moon className="h-5 w-5" />}
          {currentTheme.name === 'solarized' && <Palette className="h-5 w-5" />}
          {currentTheme.name === 'high-contrast' && <Eye className="h-5 w-5" />}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel>Appearance</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {themeOptions.map((option) => (
          <DropdownMenuItem
            key={option.name}
            className="flex items-center gap-2 cursor-pointer"
            onClick={() => setTheme(option.name)}
          >
            <div className={`p-1 rounded-full ${currentTheme.name === option.name ? 'bg-primary text-primary-foreground' : ''}`}>
              {option.icon}
            </div>
            <span>{option.label}</span>
            {currentTheme.name === option.name && (
              <span className="ml-auto text-xs bg-primary/20 text-primary px-1.5 py-0.5 rounded">
                Active
              </span>
            )}
          </DropdownMenuItem>
        ))}
        <DropdownMenuSeparator />
        <div className="p-2">
          <div className="flex items-center space-x-2">
            <Switch
              id="reduced-motion"
              checked={reducedMotion}
              onCheckedChange={toggleReducedMotion}
            />
            <Label htmlFor="reduced-motion" className="flex items-center gap-2 cursor-pointer">
              <RotateCcw className="h-4 w-4" />
              <span>Reduced Motion</span>
            </Label>
          </div>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

// Compact version for mobile or space-constrained areas
export const CompactThemeSwitcher = () => {
  const { currentTheme, setTheme } = useEnhancedTheme();
  
  const themeOptions: { name: ThemeName; icon: React.ReactNode }[] = [
    { name: 'light', icon: <Sun className="h-4 w-4" /> },
    { name: 'dark', icon: <Moon className="h-4 w-4" /> },
    { name: 'solarized', icon: <Palette className="h-4 w-4" /> },
    { name: 'high-contrast', icon: <Eye className="h-4 w-4" /> }
  ];
  
  // Cycle through themes
  const cycleTheme = () => {
    const currentIndex = themeOptions.findIndex(option => option.name === currentTheme.name);
    const nextIndex = (currentIndex + 1) % themeOptions.length;
    setTheme(themeOptions[nextIndex].name);
  };
  
  return (
    <Button 
      variant="ghost" 
      size="icon" 
      className="rounded-full"
      onClick={cycleTheme}
      aria-label="Change theme"
    >
      {currentTheme.name === 'light' && <Sun className="h-5 w-5" />}
      {currentTheme.name === 'dark' && <Moon className="h-5 w-5" />}
      {currentTheme.name === 'solarized' && <Palette className="h-5 w-5" />}
      {currentTheme.name === 'high-contrast' && <Eye className="h-5 w-5" />}
    </Button>
  );
};

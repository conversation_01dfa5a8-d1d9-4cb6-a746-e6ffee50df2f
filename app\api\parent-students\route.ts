import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { cookies } from 'next/headers'
import { verifyJWT } from '@/lib/jwt'

// GET all parent-student relationships
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const parentId = searchParams.get('parentId')

    // Verify authentication
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    }

    const decoded = await verifyJWT(token)
    const userRole = decoded.role

    // Only SUPER_ADMIN and the parent themselves can view relationships
    if (userRole !== 'SUPER_ADMIN' && decoded.id !== parentId) {
      return NextResponse.json(
        { error: 'Forbidden - Insufficient permissions' },
        { status: 403 }
      )
    }

    let whereClause: any = {}
    if (parentId) {
      whereClause.parentId = parentId
    }

    const relationships = await prisma.parentStudent.findMany({
      where: whereClause,
      include: {
        parent: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        },
        student: {
          select: {
            id: true,
            sid: true,
            name: true,
            className: true,
            fatherName: true,
            age: true,
            gender: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json(relationships)
  } catch (error) {
    console.error('Error fetching parent-student relationships:', error)
    return NextResponse.json(
      { error: 'Failed to fetch relationships' },
      { status: 500 }
    )
  }
}

// POST - Create new parent-student relationship
export async function POST(request: Request) {
  try {
    const data = await request.json()
    const { parentId, studentId } = data

    // Validate required fields
    if (!parentId || !studentId) {
      return NextResponse.json(
        { error: 'Parent ID and Student ID are required' },
        { status: 400 }
      )
    }

    // Verify authentication
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    }

    const decoded = await verifyJWT(token)
    
    // Only SUPER_ADMIN can create relationships
    if (decoded.role !== 'SUPER_ADMIN') {
      return NextResponse.json(
        { error: 'Forbidden - Only Super Admin can create parent-student relationships' },
        { status: 403 }
      )
    }

    // Verify parent exists and has PARENT role
    const parent = await prisma.user.findUnique({
      where: { id: parentId },
      select: { id: true, role: true, name: true }
    })

    if (!parent || parent.role !== 'PARENT') {
      return NextResponse.json(
        { error: 'Invalid parent - User must have PARENT role' },
        { status: 400 }
      )
    }

    // Verify student exists
    const student = await prisma.student.findUnique({
      where: { id: studentId },
      select: { id: true, sid: true, name: true }
    })

    if (!student) {
      return NextResponse.json(
        { error: 'Student not found' },
        { status: 404 }
      )
    }

    // Check if relationship already exists
    const existingRelationship = await prisma.parentStudent.findUnique({
      where: {
        parentId_studentId: {
          parentId,
          studentId
        }
      }
    })

    if (existingRelationship) {
      return NextResponse.json(
        { error: 'Relationship already exists' },
        { status: 409 }
      )
    }

    // Create the relationship
    const relationship = await prisma.parentStudent.create({
      data: {
        parentId,
        studentId
      },
      include: {
        parent: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        },
        student: {
          select: {
            id: true,
            sid: true,
            name: true,
            className: true,
            fatherName: true,
            age: true,
            gender: true
          }
        }
      }
    })

    return NextResponse.json({
      message: 'Parent-student relationship created successfully',
      relationship
    })
  } catch (error) {
    console.error('Error creating parent-student relationship:', error)
    return NextResponse.json(
      { error: 'Failed to create relationship' },
      { status: 500 }
    )
  }
}

// DELETE - Remove parent-student relationship
export async function DELETE(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const relationshipId = searchParams.get('id')

    if (!relationshipId) {
      return NextResponse.json(
        { error: 'Relationship ID is required' },
        { status: 400 }
      )
    }

    // Verify authentication
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    }

    const decoded = await verifyJWT(token)
    
    // Only SUPER_ADMIN can delete relationships
    if (decoded.role !== 'SUPER_ADMIN') {
      return NextResponse.json(
        { error: 'Forbidden - Only Super Admin can delete parent-student relationships' },
        { status: 403 }
      )
    }

    // Check if relationship exists
    const relationship = await prisma.parentStudent.findUnique({
      where: { id: relationshipId }
    })

    if (!relationship) {
      return NextResponse.json(
        { error: 'Relationship not found' },
        { status: 404 }
      )
    }

    // Delete the relationship
    await prisma.parentStudent.delete({
      where: { id: relationshipId }
    })

    return NextResponse.json({
      message: 'Parent-student relationship deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting parent-student relationship:', error)
    return NextResponse.json(
      { error: 'Failed to delete relationship' },
      { status: 500 }
    )
  }
}

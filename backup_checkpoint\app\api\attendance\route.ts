import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { mockGetHand<PERSON>, mockPostHandler } from './mock-handler'

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url)
  const className = searchParams.get('class')
  const date = searchParams.get('date')

  try {
    // Check if we're using the mock client
    const isMockClient = !!(prisma as any).constructor.name === 'MockPrismaClient'

    if (isMockClient) {
      console.log('Using mock attendance GET handler')
      return mockGetHandler(searchParams)
    }

    // Real database query
    const attendance = await prisma.attendance.findMany({
      where: {
        className: className || undefined,
        date: date || undefined,
      },
      include: {
        student: true,
      },
    })

    return NextResponse.json(attendance)
  } catch (error) {
    console.error('Error in attendance GET handler:', error)

    // If there's an error with the database, try the mock handler
    try {
      console.log('Falling back to mock attendance GET handler')
      return mockGetHandler(searchParams)
    } catch (mockError) {
      return NextResponse.json(
        { error: 'Failed to fetch attendance records' },
        { status: 500 }
      )
    }
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { date, className, records } = body

    // Validate required fields
    if (!date || !className || !records || !Array.isArray(records) || records.length === 0) {
      return NextResponse.json(
        { error: 'Date, className, and records are required' },
        { status: 400 }
      )
    }

    // Check if we're using the mock client
    const isMockClient = !!(prisma as any).constructor.name === 'MockPrismaClient'

    if (isMockClient) {
      console.log('Using mock attendance POST handler')
      return mockPostHandler(body)
    }

    // Begin transaction with real database
    try {
      const result = await prisma.$transaction(async (tx: typeof prisma) => {
        // Delete existing records for this class and date
        await tx.attendance.deleteMany({
          where: {
            className,
            date,
          },
        })

        // Create new records
        const newRecords = await Promise.all(
          records.map((record: { studentId: string, status: string }) =>
            tx.attendance.create({
              data: {
                date,
                className,
                studentId: record.studentId,
                status: record.status,
              },
            })
          )
        )

        return newRecords
      })

      return NextResponse.json(result)
    } catch (dbError) {
      console.error('Database error in attendance POST:', dbError)
      throw dbError // Rethrow to be caught by the outer try/catch
    }
  } catch (error) {
    console.error('Error in attendance POST handler:', error)

    // If there's an error with the database, try the mock handler
    try {
      console.log('Falling back to mock attendance POST handler')
      const body = await request.json()
      return mockPostHandler(body)
    } catch (mockError) {
      return NextResponse.json(
        { error: 'Failed to save attendance records: ' + (error.message || 'Unknown error') },
        { status: 500 }
      )
    }
  }
}

'use client'

import React, { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useSearchParams } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/app/components/ui/card'
import { Badge } from '@/app/components/ui/badge'
import { Button } from '@/app/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/app/components/ui/table'
import { Progress } from '@/app/components/ui/progress'
import { 
  TrendingUp, 
  BookOpen, 
  Award,
  Target,
  RefreshCw,
  User,
  AlertCircle,
  BarChart3
} from 'lucide-react'

interface Mark {
  id: string
  studentId: string
  subject: string
  marks: number
  className: string
  term: string
  createdAt: string
  student: {
    id: string
    sid: string
    name: string
    className: string
    academicYear: string
  }
}

interface Student {
  id: string
  sid: string
  name: string
  className: string
  academicYear: string
  photoUrl?: string
}

interface StudentStats {
  studentId: string
  studentName: string
  className: string
  totalSubjects: number
  averageMarks: number
  highestMark: number
  lowestMark: number
  subjectStats: Array<{
    subject: string
    totalMarks: number
    averageMarks: number
    highestMark: number
    lowestMark: number
    marks: Array<{
      term: string
      marks: number
      createdAt: string
    }>
  }>
}

interface MarksData {
  marks: Mark[]
  students: Student[]
  subjects: string[]
  terms: string[]
  studentStats: StudentStats[]
  filters: {
    term?: string
    subject?: string
    academicYear: string
  }
  message: string
}

export default function ParentMarksPage() {
  const searchParams = useSearchParams()
  const initialStudentId = searchParams.get('studentId')
  
  const [selectedStudent, setSelectedStudent] = useState<string>(initialStudentId || 'all')
  const [selectedTerm, setSelectedTerm] = useState<string>('all')
  const [selectedSubject, setSelectedSubject] = useState<string>('all')

  // Fetch marks data
  const { 
    data: marksData, 
    isLoading, 
    error, 
    refetch 
  } = useQuery<MarksData>({
    queryKey: ['parent-marks', selectedStudent, selectedTerm, selectedSubject],
    queryFn: async () => {
      const params = new URLSearchParams({
        ...(selectedStudent !== 'all' && { studentId: selectedStudent }),
        ...(selectedTerm !== 'all' && { term: selectedTerm }),
        ...(selectedSubject !== 'all' && { subject: selectedSubject })
      })
      
      const res = await fetch(`/api/parent/marks?${params}`)
      if (!res.ok) {
        const errorData = await res.json()
        throw new Error(errorData.error || 'Failed to fetch marks data')
      }
      return res.json()
    }
  })

  const getGradeColor = (marks: number) => {
    if (marks >= 90) return 'text-green-600 bg-green-50 border-green-200'
    if (marks >= 80) return 'text-blue-600 bg-blue-50 border-blue-200'
    if (marks >= 70) return 'text-yellow-600 bg-yellow-50 border-yellow-200'
    if (marks >= 60) return 'text-orange-600 bg-orange-50 border-orange-200'
    return 'text-red-600 bg-red-50 border-red-200'
  }

  const getGrade = (marks: number) => {
    if (marks >= 90) return 'A+'
    if (marks >= 80) return 'A'
    if (marks >= 70) return 'B'
    if (marks >= 60) return 'C'
    return 'D'
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-gray-400" />
          <p className="text-gray-500">Loading marks data...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 mx-auto mb-4 text-red-500" />
          <h2 className="text-xl font-semibold text-gray-700 mb-2">Error Loading Marks</h2>
          <p className="text-gray-500 mb-4">
            {error instanceof Error ? error.message : 'Something went wrong'}
          </p>
          <Button onClick={() => refetch()} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  const students = marksData?.students || []
  const marks = marksData?.marks || []
  const subjects = marksData?.subjects || []
  const terms = marksData?.terms || []
  const studentStats = marksData?.studentStats || []

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-800">Academic Performance</h1>
          <p className="text-gray-500">Track your children's marks and academic progress</p>
        </div>
        <Button onClick={() => refetch()} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Student</label>
              <Select value={selectedStudent} onValueChange={setSelectedStudent}>
                <SelectTrigger>
                  <SelectValue placeholder="Select student" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Children</SelectItem>
                  {students.map((student) => (
                    <SelectItem key={student.id} value={student.id}>
                      {student.name} ({student.sid})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Term</label>
              <Select value={selectedTerm} onValueChange={setSelectedTerm}>
                <SelectTrigger>
                  <SelectValue placeholder="Select term" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Terms</SelectItem>
                  {terms.map((term) => (
                    <SelectItem key={term} value={term}>
                      {term}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Subject</label>
              <Select value={selectedSubject} onValueChange={setSelectedSubject}>
                <SelectTrigger>
                  <SelectValue placeholder="Select subject" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Subjects</SelectItem>
                  {subjects.map((subject) => (
                    <SelectItem key={subject} value={subject}>
                      {subject}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Student Performance Summary */}
      {studentStats.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {studentStats.map((stat) => (
            <Card key={stat.studentId}>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center">
                  <User className="h-4 w-4 mr-2" />
                  {stat.studentName}
                </CardTitle>
                <CardDescription>
                  Class {stat.className} • {stat.totalSubjects} subjects
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-3 gap-2 text-center text-xs">
                  <div className="bg-blue-50 p-2 rounded">
                    <div className="font-semibold text-blue-600">{stat.averageMarks}</div>
                    <div className="text-blue-600">Average</div>
                  </div>
                  <div className="bg-green-50 p-2 rounded">
                    <div className="font-semibold text-green-600">{stat.highestMark}</div>
                    <div className="text-green-600">Highest</div>
                  </div>
                  <div className="bg-orange-50 p-2 rounded">
                    <div className="font-semibold text-orange-600">{stat.lowestMark}</div>
                    <div className="text-orange-600">Lowest</div>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Overall Performance</span>
                    <Badge variant="outline" className={getGradeColor(stat.averageMarks)}>
                      {getGrade(stat.averageMarks)}
                    </Badge>
                  </div>
                  <Progress value={stat.averageMarks} className="h-2" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Detailed Marks Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <BarChart3 className="h-5 w-5 mr-2" />
            Detailed Marks
          </CardTitle>
          <CardDescription>
            Individual marks for each subject and term
          </CardDescription>
        </CardHeader>
        <CardContent>
          {marks.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <BookOpen className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No marks found for the selected criteria</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Student</TableHead>
                  <TableHead>Subject</TableHead>
                  <TableHead>Term</TableHead>
                  <TableHead>Marks</TableHead>
                  <TableHead>Grade</TableHead>
                  <TableHead>Date</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {marks.map((mark) => (
                  <TableRow key={mark.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{mark.student.name}</div>
                        <div className="text-sm text-gray-500">
                          {mark.student.sid} • Class {mark.className}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{mark.subject}</Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                        {mark.term}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="font-semibold text-lg">{mark.marks}/100</div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className={getGradeColor(mark.marks)}>
                        {getGrade(mark.marks)}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-sm text-gray-500">
                      {new Date(mark.createdAt).toLocaleDateString()}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

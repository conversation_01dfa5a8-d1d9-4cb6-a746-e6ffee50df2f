import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET all HR teacher assignments using User model with TEACHER role
export async function GET(request: Request) {
  try {
    console.log('Fetching all HR teacher assignments with User model')

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const classId = searchParams.get('classId')

    // Build the where clause
    let whereClause: any = {}

    if (classId) {
      whereClause.id = classId
    }

    // Fetch classes
    const classes = await prisma.class.findMany({
      where: whereClause,
      orderBy: {
        name: 'asc'
      }
    })

    // For each class, fetch the HR teacher if it exists
    const classesWithTeachers = await Promise.all(
      classes.map(async (cls) => {
        let hrTeacher = null;

        if (cls.hrTeacherId) {
          // Try to find the teacher in the User model
          hrTeacher = await prisma.user.findFirst({
            where: {
              id: cls.hrTeacherId,
              role: 'TEACHER'
            },
            select: {
              id: true,
              name: true,
              email: true
            }
          });

          // If not found in User model, try the Teacher model
          if (!hrTeacher) {
            hrTeacher = await prisma.teacher.findUnique({
              where: {
                id: cls.hrTeacherId
              },
              select: {
                id: true,
                name: true,
                email: true,
                subject: true
              }
            });
          }
        }

        return {
          ...cls,
          hrTeacher
        };
      })
    )

    // Transform the data to match the expected format
    const formattedClasses = classesWithTeachers.map(cls => ({
      id: cls.id,
      name: cls.name,
      hrTeacherId: cls.hrTeacherId,
      hrTeacherName: cls.hrTeacher ? cls.hrTeacher.name : null,
      hrTeacherEmail: cls.hrTeacher ? cls.hrTeacher.email : null,
      hrTeacherSubject: cls.hrTeacher && 'subject' in cls.hrTeacher ? cls.hrTeacher.subject : null,
      totalStudents: cls.totalStudents,
      totalSubjects: cls.totalSubjects
    }))

    console.log(`Returning ${formattedClasses.length} HR teacher assignments`)
    return NextResponse.json(formattedClasses)
  } catch (error) {
    console.error('Error fetching HR teacher assignments:', error)
    return NextResponse.json(
      { error: 'Failed to fetch HR teacher assignments' },
      { status: 500 }
    )
  }
}

// POST to assign an HR teacher to a class using User model with TEACHER role
export async function POST(request: Request) {
  try {
    console.log('Assigning HR teacher (User with TEACHER role) to class')

    // Get the request body
    const data = await request.json()

    // Validate required fields
    if (!data.teacherId || !data.classId) {
      return NextResponse.json(
        { error: 'Teacher ID and Class ID are required' },
        { status: 400 }
      )
    }

    // Check if the user with TEACHER role exists
    const teacher = await prisma.user.findFirst({
      where: {
        id: data.teacherId,
        role: 'TEACHER'
      }
    })

    if (!teacher) {
      // If not found in User model, try the Teacher model for backward compatibility
      const teacherFromTeacherModel = await prisma.teacher.findUnique({
        where: { id: data.teacherId }
      })

      if (!teacherFromTeacherModel) {
        return NextResponse.json(
          { error: 'Teacher not found or user is not a teacher' },
          { status: 404 }
        )
      }

      // Use the teacher from the Teacher model
      console.log(`Using teacher ${teacherFromTeacherModel.name} from Teacher model`)
    } else {
      console.log(`Using teacher ${teacher.name} from User model with TEACHER role`)
    }

    // Check if the class exists
    const classObj = await prisma.class.findUnique({
      where: { id: data.classId },
      include: {
        hrTeacher: true
      }
    })

    if (!classObj) {
      return NextResponse.json(
        { error: 'Class not found' },
        { status: 404 }
      )
    }

    // Get query parameters to check if this is an update
    const { searchParams } = new URL(request.url)
    const isUpdate = searchParams.get('update') === 'true'

    // If this is an update and the class already has an HR teacher
    if (isUpdate && classObj.hrTeacherId) {
      // Update the class with the new HR teacher
      const updatedClass = await prisma.class.update({
        where: { id: data.classId },
        data: {
          hrTeacherId: data.teacherId
        },
        include: {
          hrTeacher: true
        }
      })

      console.log(`Updated HR teacher for class ${updatedClass.name} to ${updatedClass.hrTeacher?.name || 'Unknown'} (using ${teacher ? 'User' : 'Teacher'} model)`)
      return NextResponse.json({
        message: 'HR teacher updated successfully',
        class: updatedClass
      })
    }

    // If this is a new assignment and the class already has an HR teacher
    if (!isUpdate && classObj.hrTeacherId) {
      return NextResponse.json(
        { error: 'Class already has an HR teacher assigned' },
        { status: 400 }
      )
    }

    // Assign the HR teacher to the class
    const updatedClass = await prisma.class.update({
      where: { id: data.classId },
      data: {
        hrTeacherId: data.teacherId
      },
      include: {
        hrTeacher: true
      }
    })

    console.log(`Assigned HR teacher ${updatedClass.hrTeacher?.name || 'Unknown'} to class ${updatedClass.name} (using ${teacher ? 'User' : 'Teacher'} model)`)
    return NextResponse.json({
      message: 'HR teacher assigned successfully',
      class: updatedClass
    })
  } catch (error) {
    console.error('Error assigning HR teacher:', error)
    return NextResponse.json(
      { error: 'Failed to assign HR teacher' },
      { status: 500 }
    )
  }
}

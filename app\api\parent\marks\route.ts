import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { cookies } from 'next/headers'
import { verifyJWT } from '@/lib/jwt'

// GET marks data for parent's children
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const studentId = searchParams.get('studentId')
    const term = searchParams.get('term') // 'First Semester', 'Second Semester', 'Annual'
    const subject = searchParams.get('subject')
    const academicYear = searchParams.get('academicYear') || '2023-2024'

    // Verify authentication
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    }

    const decoded = await verifyJWT(token)
    
    // Only PARENT role can access this endpoint
    if (decoded.role !== 'PARENT') {
      return NextResponse.json(
        { error: 'Forbidden - Only parents can access this endpoint' },
        { status: 403 }
      )
    }

    const parentId = decoded.id

    // If studentId is provided, verify the parent has access to this student
    if (studentId) {
      const relationship = await prisma.parentStudent.findUnique({
        where: {
          parentId_studentId: {
            parentId,
            studentId
          }
        }
      })

      if (!relationship) {
        return NextResponse.json(
          { error: 'Forbidden - You do not have access to this student' },
          { status: 403 }
        )
      }
    }

    // Get all children if no specific student is requested
    let studentIds: string[] = []
    if (studentId) {
      studentIds = [studentId]
    } else {
      const relationships = await prisma.parentStudent.findMany({
        where: { parentId },
        select: { studentId: true }
      })
      studentIds = relationships.map(rel => rel.studentId)
    }

    if (studentIds.length === 0) {
      return NextResponse.json({
        marks: [],
        students: [],
        message: 'No children found for this parent'
      })
    }

    // Build filter for marks
    let marksFilter: any = {
      studentId: { in: studentIds }
    }

    if (term) {
      marksFilter.term = term
    }

    if (subject) {
      marksFilter.subject = subject
    }

    // Fetch marks data
    const marks = await prisma.mark.findMany({
      where: marksFilter,
      include: {
        student: {
          select: {
            id: true,
            sid: true,
            name: true,
            className: true,
            academicYear: true
          }
        }
      },
      orderBy: [
        { student: { className: 'asc' } },
        { student: { name: 'asc' } },
        { subject: 'asc' },
        { term: 'asc' }
      ]
    })

    // Get student details
    const students = await prisma.student.findMany({
      where: { id: { in: studentIds } },
      select: {
        id: true,
        sid: true,
        name: true,
        className: true,
        academicYear: true,
        photoUrl: true
      }
    })

    // Get unique subjects for filtering
    const subjects = [...new Set(marks.map(mark => mark.subject))].sort()

    // Get unique terms for filtering
    const terms = [...new Set(marks.map(mark => mark.term))].sort()

    // Calculate statistics per student
    const studentStats = studentIds.map(sId => {
      const studentMarks = marks.filter(m => m.studentId === sId)
      const student = students.find(s => s.id === sId)
      
      if (studentMarks.length === 0) {
        return {
          studentId: sId,
          studentName: student?.name || 'Unknown',
          className: student?.className || 'Unknown',
          totalSubjects: 0,
          averageMarks: 0,
          highestMark: 0,
          lowestMark: 0,
          subjectStats: []
        }
      }

      const totalMarks = studentMarks.reduce((sum, mark) => sum + mark.marks, 0)
      const averageMarks = Math.round(totalMarks / studentMarks.length)
      const highestMark = Math.max(...studentMarks.map(m => m.marks))
      const lowestMark = Math.min(...studentMarks.map(m => m.marks))

      // Group by subject
      const subjectStats = subjects.map(subj => {
        const subjectMarks = studentMarks.filter(m => m.subject === subj)
        if (subjectMarks.length === 0) return null

        const subjectTotal = subjectMarks.reduce((sum, mark) => sum + mark.marks, 0)
        const subjectAverage = Math.round(subjectTotal / subjectMarks.length)
        
        return {
          subject: subj,
          totalMarks: subjectMarks.length,
          averageMarks: subjectAverage,
          highestMark: Math.max(...subjectMarks.map(m => m.marks)),
          lowestMark: Math.min(...subjectMarks.map(m => m.marks)),
          marks: subjectMarks.map(m => ({
            term: m.term,
            marks: m.marks,
            createdAt: m.createdAt
          }))
        }
      }).filter(Boolean)

      return {
        studentId: sId,
        studentName: student?.name || 'Unknown',
        className: student?.className || 'Unknown',
        totalSubjects: subjects.length,
        averageMarks,
        highestMark,
        lowestMark,
        subjectStats
      }
    })

    return NextResponse.json({
      marks,
      students,
      subjects,
      terms,
      studentStats,
      filters: {
        term,
        subject,
        academicYear
      },
      message: 'Marks data retrieved successfully'
    })
  } catch (error) {
    console.error('Error fetching marks data:', error)
    return NextResponse.json(
      { error: 'Failed to fetch marks data' },
      { status: 500 }
    )
  }
}

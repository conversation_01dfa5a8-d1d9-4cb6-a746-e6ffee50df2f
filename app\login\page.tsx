'use client'

import React, { useState, useEffect } from 'react'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { <PERSON><PERSON>, <PERSON><PERSON>List, Ta<PERSON>Trigger, TabsContent } from '../components/ui/tabs'
import { useEnhancedTheme } from '../contexts/enhanced-theme-context'
import { useAppSettings } from '../contexts/app-settings-context'
import { Sun, Moon } from 'lucide-react'
import '../styles/login-animation.css'

export default function LoginPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const router = useRouter()
  const [mounted, setMounted] = useState(false)
  const [returnPath, setReturnPath] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('login')
  const { currentTheme, setTheme } = useEnhancedTheme()
  const { settings } = useAppSettings()

  // Login form state
  const [loginData, setLoginData] = useState({
    email: '',
    password: '',
    rememberMe: false
  })

  // Register form state
  const [registerData, setRegisterData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    agreeToTerms: false
  })

  // Set mounted state after component mounts
  useEffect(() => {
    setMounted(true)

    // Get the return path from URL if available
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      const fromPath = urlParams.get('from');
      if (fromPath) {
        console.log('Return path found:', fromPath);
        setReturnPath(fromPath);
      }
    }

    // Check if user is already authenticated
    checkAuth()
  }, [])

  // Check if user is already authenticated
  const checkAuth = async () => {
    try {
      console.log('Login page: Checking authentication status...');
      const response = await fetch('/api/auth/me', {
        credentials: 'include' // Important: include cookies in the request
      });

      const data = await response.json();
      console.log('Login page: Auth check response status:', response.status);

      if (response.ok && data.user) {
        // User is authenticated, always redirect to dashboard
        console.log('Login page: User is authenticated, redirecting to dashboard');

        // Use window.location for a full page navigation
        window.location.href = '/dashboard';
      } else {
        console.log('Login page: User is not authenticated, showing login form');
      }
    } catch (error) {
      console.error('Error checking authentication:', error);
    }
  }

  // Handle login form input changes
  const handleLoginChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target
    setLoginData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
  }

  // Handle register form input changes
  const handleRegisterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target
    setRegisterData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
  }

  // Handle login form submission
  const handleLoginSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError(null)

    try {
      // Validate inputs
      if (!loginData.email || !loginData.password) {
        setError("Please enter both email and password")
        setIsLoading(false)
        return
      }

      // Make a request to the login API
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: loginData.email,
          password: loginData.password,
        }),
        credentials: 'include', // Important: include cookies in the request
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Login failed')
      }

      setSuccess("Login successful! Redirecting to dashboard...")

      console.log('Login successful, redirecting to dashboard');

      // Redirect - use window.location for a full page navigation
      setTimeout(() => {
        window.location.href = '/dashboard';
      }, 1000)
    } catch (error) {
      console.error('Login error:', error)

      // Provide more specific error messages based on the error type
      let errorMessage = 'Login failed'

      if (error instanceof Error) {
        if (error.message.includes('Database connection failed')) {
          errorMessage = 'Database connection issue. Please check your internet connection and try again.'
        } else if (error.message.includes('Invalid credentials')) {
          errorMessage = 'Invalid email or password. Please check your credentials and try again.'
        } else if (error.message.includes('pending approval')) {
          errorMessage = 'Your account is pending approval. Please contact the administrator.'
        } else {
          errorMessage = error.message
        }
      }

      setError(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  // Handle register form submission
  const handleRegisterSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError(null)

    try {
      // Validate inputs
      if (!registerData.name || !registerData.email || !registerData.password || !registerData.confirmPassword) {
        setError("Please fill in all required fields")
        setIsLoading(false)
        return
      }

      // Validate passwords match
      if (registerData.password !== registerData.confirmPassword) {
        setError("Passwords do not match")
        setIsLoading(false)
        return
      }

      // Validate terms agreement
      if (!registerData.agreeToTerms) {
        setError("You must agree to the terms and conditions")
        setIsLoading(false)
        return
      }

      // Make a request to the register API
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: registerData.name,
          email: registerData.email,
          password: registerData.password,
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Registration failed')
      }

      // Show the message from the API or a default message
      setSuccess(data.message || "Account created successfully! Your account is pending approval. Please contact the administrator.")

      // Switch to login tab
      setActiveTab('login')

      // Pre-fill login form with registration email
      setLoginData(prev => ({
        ...prev,
        email: registerData.email
      }))

    } catch (error) {
      console.error('Registration error:', error)
      setError(error instanceof Error ? error.message : 'Registration failed')
    } finally {
      setIsLoading(false)
    }
  }

  // Don't render until mounted to prevent hydration issues
  if (!mounted) {
    return null
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-6 login-page-container">
      {/* Wave Animation Background */}
      <div className="wave-container">
        <div className="wave"></div>
        <div className="wave"></div>
        <div className="wave"></div>
      </div>

      <div className="w-full max-w-md bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm rounded-lg shadow-xl p-8 relative z-10">
        {/* Theme Toggle Button */}
        <div className="absolute top-4 right-4">
          <button
            onClick={() => setTheme(currentTheme.name === 'dark' ? 'light' : 'dark')}
            className="p-2 rounded-full bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors duration-200"
            aria-label="Toggle theme"
          >
            {currentTheme.name === 'dark' ? (
              <Sun size={18} className="text-yellow-500" />
            ) : (
              <Moon size={18} className="text-gray-600" />
            )}
          </button>
        </div>

        {/* Logo */}
        <div className="flex flex-col items-center space-y-4 mb-8">
          <Link href="/website" className="relative w-24 h-24 rounded-full overflow-hidden border-4 border-blue-100 dark:border-blue-800 shadow-md hover:border-blue-200 dark:hover:border-blue-700 transition-colors cursor-pointer">
            <Image
              src={settings.schoolLogo || "/images/logo.png"}
              alt={`${settings.schoolName} Logo`}
              fill
              style={{ objectFit: 'cover' }}
              className="rounded-full"
            />
          </Link>
          <h1 className="text-2xl font-bold text-center text-gray-900 dark:text-white">{settings.schoolName}</h1>
        </div>

        <div className="text-center space-y-2 mb-4">
          <h2 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white">
            {activeTab === 'login' ? 'Sign in to your account' : 'Create a new account'}
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {activeTab === 'login'
              ? 'Enter your credentials to access the dashboard'
              : 'Fill in the information to create your account'}
          </p>
        </div>

        {/* Error and success messages */}
        {error && (
          <div className="mb-6 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 rounded-md">
            {error}
          </div>
        )}

        {success && (
          <div className="mb-6 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-green-600 dark:text-green-400 rounded-md">
            {success}
          </div>
        )}

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2 mb-6">
            <TabsTrigger value="login">Login</TabsTrigger>
            <TabsTrigger value="register">Register</TabsTrigger>
          </TabsList>

          {/* Login Tab Content */}
          <TabsContent value="login" className="space-y-4">
            <form onSubmit={handleLoginSubmit} className="space-y-5">
              <div className="space-y-2">
                <label htmlFor="login-email" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Email Address
                </label>
                <input
                  id="login-email"
                  name="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={loginData.email}
                  onChange={handleLoginChange}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                />
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <label htmlFor="login-password" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Password
                  </label>
                  <a href="#" className="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300">
                    Forgot password?
                  </a>
                </div>
                <div className="relative">
                  <input
                    id="login-password"
                    name="password"
                    type={showPassword ? "text" : "password"}
                    placeholder="••••••••"
                    value={loginData.password}
                    onChange={handleLoginChange}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"
                  >
                    {showPassword ? "Hide" : "Show"}
                  </button>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  id="remember-me"
                  name="rememberMe"
                  type="checkbox"
                  checked={loginData.rememberMe}
                  onChange={handleLoginChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800"
                />
                <label
                  htmlFor="remember-me"
                  className="text-sm text-gray-700 dark:text-gray-300"
                >
                  Remember me
                </label>
              </div>

              <button
                type="submit"
                className="w-full py-3 px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
                disabled={isLoading}
              >
                {isLoading && activeTab === 'login' ? "Signing in..." : "Sign in"}
              </button>
            </form>
          </TabsContent>

          {/* Register Tab Content */}
          <TabsContent value="register" className="space-y-4">
            <form onSubmit={handleRegisterSubmit} className="space-y-5">
              <div className="space-y-2">
                <label htmlFor="register-name" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Full Name
                </label>
                <input
                  id="register-name"
                  name="name"
                  type="text"
                  placeholder="John Doe"
                  value={registerData.name}
                  onChange={handleRegisterChange}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                />
              </div>

              <div className="space-y-2">
                <label htmlFor="register-email" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Email Address
                </label>
                <input
                  id="register-email"
                  name="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={registerData.email}
                  onChange={handleRegisterChange}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                />
              </div>

              <div className="space-y-2">
                <label htmlFor="register-password" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Password
                </label>
                <div className="relative">
                  <input
                    id="register-password"
                    name="password"
                    type={showPassword ? "text" : "password"}
                    placeholder="••••••••"
                    value={registerData.password}
                    onChange={handleRegisterChange}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"
                  >
                    {showPassword ? "Hide" : "Show"}
                  </button>
                </div>
              </div>

              <div className="space-y-2">
                <label htmlFor="register-confirm-password" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Confirm Password
                </label>
                <input
                  id="register-confirm-password"
                  name="confirmPassword"
                  type="password"
                  placeholder="••••••••"
                  value={registerData.confirmPassword}
                  onChange={handleRegisterChange}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                />
              </div>

              <div className="flex items-center space-x-2">
                <input
                  id="agree-terms"
                  name="agreeToTerms"
                  type="checkbox"
                  checked={registerData.agreeToTerms}
                  onChange={handleRegisterChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800"
                  required
                />
                <label
                  htmlFor="agree-terms"
                  className="text-sm text-gray-700 dark:text-gray-300"
                >
                  I agree to the terms and conditions
                </label>
              </div>

              <button
                type="submit"
                className="w-full py-3 px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
                disabled={isLoading}
              >
                {isLoading && activeTab === 'register' ? "Registering..." : "Register"}
              </button>
            </form>
          </TabsContent>
        </Tabs>

        {/* Footer */}
        <div className="mt-8 text-center">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Contact administrator for account assistance
          </p>
        </div>
      </div>
    </div>
  )
}

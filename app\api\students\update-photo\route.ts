import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { writeFile } from 'fs/promises'
import { join } from 'path'
import crypto from 'crypto'

// POST to update a student's photo
export async function POST(request: Request) {
  try {
    const formData = await request.formData()
    const studentId = formData.get('studentId') as string
    const file = formData.get('photo') as File

    if (!studentId) {
      return NextResponse.json({
        error: 'Student ID is required'
      }, { status: 400 })
    }

    if (!file) {
      return NextResponse.json({
        error: 'No photo provided'
      }, { status: 400 })
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      return NextResponse.json({
        error: 'File must be an image'
      }, { status: 400 })
    }

    // Get file extension
    const fileExtension = file.name.split('.').pop()?.toLowerCase() || ''
    const validExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp']

    if (!validExtensions.includes(fileExtension)) {
      return NextResponse.json({
        error: 'Invalid file extension. Allowed: jpg, jpeg, png, gif, webp'
      }, { status: 400 })
    }

    // Limit file size (5MB)
    const MAX_SIZE = 5 * 1024 * 1024 // 5MB
    if (file.size > MAX_SIZE) {
      return NextResponse.json({
        error: 'File size exceeds 5MB limit'
      }, { status: 400 })
    }

    // Generate a unique filename using crypto
    const uniqueId = crypto.randomBytes(16).toString('hex')
    const uniqueFilename = `student_${studentId}_${uniqueId}.${fileExtension}`

    // Define the upload directory and path
    const uploadDir = join(process.cwd(), 'public', 'uploads', 'students')
    const filePath = join(uploadDir, uniqueFilename)

    // Convert the file to an ArrayBuffer
    const buffer = await file.arrayBuffer()

    // Write the file to the filesystem
    await writeFile(filePath, Buffer.from(buffer))

    // Return the URL to the uploaded file
    const fileUrl = `/uploads/students/${uniqueFilename}`

    // Update the student record with the photo URL
    await prisma.student.update({
      where: { id: studentId },
      data: { photoUrl: fileUrl }
    })

    return NextResponse.json({
      url: fileUrl,
      filename: uniqueFilename,
      success: true
    })
  } catch (error) {
    console.error('Error updating student photo:', error)
    return NextResponse.json({
      error: 'Failed to update student photo'
    }, { status: 500 })
  }
}

// POST to update multiple students' photos
export async function PUT(request: Request) {
  try {
    const data = await request.json()
    const { studentPhotos } = data

    if (!studentPhotos || !Array.isArray(studentPhotos)) {
      return NextResponse.json({
        error: 'Invalid data format. Expected an array of studentPhotos'
      }, { status: 400 })
    }

    // Update each student's photo URL
    const updates = await Promise.all(
      studentPhotos.map(async (item) => {
        const { studentId, photoUrl } = item
        
        if (!studentId || !photoUrl) {
          return { studentId, success: false, error: 'Missing studentId or photoUrl' }
        }

        try {
          await prisma.student.update({
            where: { id: studentId },
            data: { photoUrl }
          })
          return { studentId, success: true }
        } catch (error) {
          console.error(`Error updating student ${studentId}:`, error)
          return { studentId, success: false, error: 'Database update failed' }
        }
      })
    )

    return NextResponse.json({
      success: true,
      updates
    })
  } catch (error) {
    console.error('Error updating student photos:', error)
    return NextResponse.json({
      error: 'Failed to update student photos'
    }, { status: 500 })
  }
}

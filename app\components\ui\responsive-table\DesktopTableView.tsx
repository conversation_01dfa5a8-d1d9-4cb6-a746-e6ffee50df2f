"use client"

import React, { useState, useMemo, RefObject } from 'react'
import { Search, Filter, Edit, Trash2, MoreHorizontal, ChevronLeft, ChevronRight, ArrowUpDown, ArrowUp, ArrowDown } from 'lucide-react'
import { Button } from '../button'
import { Input } from '../input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../dropdown-menu'
import { cn } from '../../../lib/utils'
import { ResponsiveTableColumn } from './index'

interface DesktopTableViewProps<T = any> {
  data: T[]
  columns: ResponsiveTableColumn<T>[]
  isLoading: boolean
  onRowClick?: (row: T) => void
  onRowEdit?: (row: T) => void
  onRowDelete?: (row: T) => void
  searchable: boolean
  filterable: boolean
  sortable: boolean
  pagination: boolean
  emptyMessage: string
  loadingMessage: string
  stickyHeader: boolean
  getRowKey: (row: T) => string
  pageSize: number
  scrollRef: RefObject<HTMLDivElement>
}

export function DesktopTableView<T = any>({
  data,
  columns,
  isLoading,
  onRowClick,
  onRowEdit,
  onRowDelete,
  searchable,
  filterable,
  sortable,
  pagination,
  emptyMessage,
  loadingMessage,
  stickyHeader,
  getRowKey,
  pageSize,
  scrollRef
}: DesktopTableViewProps<T>) {
  const [searchTerm, setSearchTerm] = useState('')
  const [sortColumn, setSortColumn] = useState<string | null>(null)
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc')
  const [currentPage, setCurrentPage] = useState(1)

  // Filter and sort data
  const processedData = useMemo(() => {
    let filtered = data

    // Apply search filter
    if (searchTerm) {
      filtered = data.filter(row => {
        return columns.some(column => {
          if (!column.accessorKey) return false
          const value = row[column.accessorKey]
          return String(value).toLowerCase().includes(searchTerm.toLowerCase())
        })
      })
    }

    // Apply sorting
    if (sortColumn) {
      const column = columns.find(col => col.id === sortColumn)
      if (column?.accessorKey) {
        filtered = [...filtered].sort((a, b) => {
          const aVal = a[column.accessorKey!]
          const bVal = b[column.accessorKey!]
          
          if (aVal === bVal) return 0
          
          const comparison = aVal < bVal ? -1 : 1
          return sortDirection === 'asc' ? comparison : -comparison
        })
      }
    }

    return filtered
  }, [data, searchTerm, sortColumn, sortDirection, columns])

  // Paginate data
  const paginatedData = useMemo(() => {
    if (!pagination) return processedData
    
    const startIndex = (currentPage - 1) * pageSize
    return processedData.slice(startIndex, startIndex + pageSize)
  }, [processedData, currentPage, pageSize, pagination])

  const totalPages = Math.ceil(processedData.length / pageSize)

  const handleSort = (columnId: string) => {
    if (!sortable) return
    
    const column = columns.find(col => col.id === columnId)
    if (!column?.sortable) return

    if (sortColumn === columnId) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortColumn(columnId)
      setSortDirection('asc')
    }
  }

  const renderCellValue = (column: ResponsiveTableColumn<T>, row: T) => {
    if (column.cell) {
      return column.cell(row)
    }
    if (column.accessorKey) {
      const value = row[column.accessorKey]
      return value !== null && value !== undefined ? String(value) : '-'
    }
    return '-'
  }

  const getSortIcon = (columnId: string) => {
    if (sortColumn !== columnId) {
      return <ArrowUpDown className="h-4 w-4 opacity-50" />
    }
    return sortDirection === 'asc' 
      ? <ArrowUp className="h-4 w-4" />
      : <ArrowDown className="h-4 w-4" />
  }

  if (isLoading) {
    return (
      <div className="desktop-table-view p-6">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-gray-500">{loadingMessage}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="desktop-table-view space-y-6 p-6">
      {/* Search and controls */}
      {(searchable || filterable) && (
        <div className="flex gap-4 items-center justify-between">
          <div className="flex gap-4 items-center flex-1">
            {searchable && (
              <div className="relative max-w-sm">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            )}
            {filterable && (
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
            )}
          </div>
          
          <div className="text-sm text-gray-500">
            Showing {paginatedData.length} of {processedData.length} results
          </div>
        </div>
      )}

      {/* Table container */}
      <div className="border rounded-lg overflow-hidden bg-white shadow-sm">
        <div
          ref={scrollRef}
          className="overflow-x-auto"
          style={{ scrollbarWidth: 'thin' }}
        >
          <Table>
            <TableHeader className={cn(stickyHeader && "sticky top-0 z-20 bg-white")}>
              <TableRow className="bg-gray-50 border-b-2 border-gray-200">
                {columns.map((column) => (
                  <TableHead
                    key={column.id}
                    className={cn(
                      "font-semibold text-gray-900 px-6 py-4 text-left",
                      column.sortable && sortable && "cursor-pointer hover:bg-gray-100 select-none",
                      column.className
                    )}
                    style={{
                      minWidth: column.minWidth || 150,
                      maxWidth: column.maxWidth
                    }}
                    onClick={() => handleSort(column.id)}
                  >
                    <div className="flex items-center gap-2">
                      {column.header}
                      {column.sortable && sortable && (
                        <span className="ml-auto">
                          {getSortIcon(column.id)}
                        </span>
                      )}
                    </div>
                  </TableHead>
                ))}
                {(onRowEdit || onRowDelete) && (
                  <TableHead className="w-[100px] text-center">
                    Actions
                  </TableHead>
                )}
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginatedData.length === 0 ? (
                <TableRow>
                  <TableCell 
                    colSpan={columns.length + (onRowEdit || onRowDelete ? 1 : 0)} 
                    className="h-32 text-center text-gray-500"
                  >
                    <div className="flex flex-col items-center gap-2">
                      <Search className="h-8 w-8 text-gray-300" />
                      {emptyMessage}
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                paginatedData.map((row, index) => (
                  <TableRow
                    key={getRowKey(row)}
                    className={cn(
                      "hover:bg-gray-50 transition-colors border-b border-gray-100",
                      onRowClick && "cursor-pointer",
                      index % 2 === 0 ? "bg-white" : "bg-gray-50/30"
                    )}
                    onClick={() => onRowClick?.(row)}
                  >
                    {columns.map((column) => (
                      <TableCell
                        key={column.id}
                        className={cn(
                          "px-6 py-4",
                          column.className
                        )}
                        style={{
                          minWidth: column.minWidth || 150,
                          maxWidth: column.maxWidth
                        }}
                      >
                        <div className={cn(
                          column.maxWidth && "truncate"
                        )}>
                          {renderCellValue(column, row)}
                        </div>
                      </TableCell>
                    ))}
                    {(onRowEdit || onRowDelete) && (
                      <TableCell className="px-6 py-4 text-center">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            {onRowEdit && (
                              <DropdownMenuItem onClick={(e) => {
                                e.stopPropagation()
                                onRowEdit(row)
                              }}>
                                <Edit className="h-4 w-4 mr-2" />
                                Edit
                              </DropdownMenuItem>
                            )}
                            {onRowDelete && (
                              <DropdownMenuItem 
                                onClick={(e) => {
                                  e.stopPropagation()
                                  onRowDelete(row)
                                }}
                                className="text-red-600"
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Delete
                              </DropdownMenuItem>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    )}
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Pagination */}
      {pagination && totalPages > 1 && (
        <div className="flex justify-between items-center">
          <div className="text-sm text-gray-600">
            Page {currentPage} of {totalPages} ({processedData.length} total results)
          </div>
          
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4 mr-1" />
              Previous
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}
              disabled={currentPage === totalPages}
            >
              Next
              <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}

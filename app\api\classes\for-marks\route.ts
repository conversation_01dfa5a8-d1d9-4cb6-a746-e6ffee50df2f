import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    console.log('Fetching classes for mark entry...')

    const classes = await prisma.class.findMany({
      include: {
        hrTeacher: {
          select: {
            id: true,
            name: true,
            email: true,
            subject: true,
          }
        },
        subjects: {
          select: {
            id: true,
            name: true,
          },
          orderBy: {
            name: 'asc'
          }
        },
        _count: {
          select: {
            students: true,
            subjects: true
          }
        }
      },
      orderBy: {
        name: 'asc',
      },
    })

    // Format response with actual counts and relationships
    const formattedClasses = classes.map(cls => ({
      id: cls.id,
      name: cls.name,
      totalStudents: cls._count.students,
      totalSubjects: cls._count.subjects,
      hrTeacherId: cls.hrTeacherId,
      hrTeacher: cls.hrTeacher ? {
        id: cls.hrTeacher.id,
        name: cls.hrTeacher.name,
        email: cls.hrTeacher.email,
        subject: cls.hrTeacher.subject
      } : null,
      subjects: cls.subjects,
      hasSubjects: cls.subjects.length > 0,
      createdAt: cls.createdAt,
      updatedAt: cls.updatedAt
    }))

    console.log(`Found ${formattedClasses.length} classes for mark entry`)

    return NextResponse.json(formattedClasses)
  } catch (error) {
    console.error('Error fetching classes for marks:', error)
    return NextResponse.json(
      { error: 'Failed to fetch classes for mark entry' },
      { status: 500 }
    )
  }
}

"use client"

import React, { useState } from 'react'
import { <PERSON><PERSON> } from '@/app/components/ui/button'
import { Card, CardContent } from '@/app/components/ui/card'
import { Input } from '@/app/components/ui/input'
import { Label } from '@/app/components/ui/label'
import { Textarea } from '@/app/components/ui/textarea'
import { useToast } from '@/app/components/ui/use-toast'
import { Plus, Edit, Trash, Save, Info } from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/app/components/ui/dialog'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/app/components/ui/table'

// Define the payment plan type
interface PaymentPlan {
  id: string
  name: string
  description: string
  discount: string
  dueDate: string
}

// Mock data for initial payment plans
const initialPaymentPlans: PaymentPlan[] = [
  {
    id: '1',
    name: "Annual Payment Plan",
    description: "Pay the full annual tuition in one payment by August 1st and receive a 3% discount.",
    discount: "3%",
    dueDate: "August 1st",
  },
  {
    id: '2',
    name: "Semester Payment Plan",
    description: "Pay tuition in two equal installments due August 1st and January 1st.",
    discount: "1%",
    dueDate: "August 1st & January 1st",
  },
  {
    id: '3',
    name: "Monthly Payment Plan",
    description: "Pay tuition in 10 monthly installments from August through May.",
    discount: "0%",
    dueDate: "1st of each month",
  },
]

export default function PaymentPlansTab() {
  const { toast } = useToast()
  const [paymentPlans, setPaymentPlans] = useState<PaymentPlan[]>(initialPaymentPlans)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [currentPlan, setCurrentPlan] = useState<PaymentPlan | null>(null)
  const [formData, setFormData] = useState<Omit<PaymentPlan, 'id'>>({
    name: '',
    description: '',
    discount: '',
    dueDate: '',
  })
  const [isLoading, setIsLoading] = useState(false)

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      discount: '',
      dueDate: '',
    })
  }

  const openAddDialog = () => {
    resetForm()
    setIsAddDialogOpen(true)
  }

  const openEditDialog = (plan: PaymentPlan) => {
    setCurrentPlan(plan)
    setFormData({
      name: plan.name,
      description: plan.description,
      discount: plan.discount,
      dueDate: plan.dueDate,
    })
    setIsEditDialogOpen(true)
  }

  const openDeleteDialog = (plan: PaymentPlan) => {
    setCurrentPlan(plan)
    setIsDeleteDialogOpen(true)
  }

  const handleAddPlan = async () => {
    setIsLoading(true)
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // In a real implementation, you would save to the database here
      const newPlan: PaymentPlan = {
        id: Date.now().toString(),
        ...formData
      }
      
      setPaymentPlans(prev => [...prev, newPlan])
      setIsAddDialogOpen(false)
      resetForm()
      
      toast({
        title: "Plan Added",
        description: `${formData.name} payment plan has been added successfully.`,
        variant: "default",
      })
    } catch (error) {
      console.error('Error adding plan:', error)
      toast({
        title: "Error",
        description: "Failed to add payment plan. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleEditPlan = async () => {
    if (!currentPlan) return
    
    setIsLoading(true)
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // In a real implementation, you would update the database here
      setPaymentPlans(prev => 
        prev.map(plan => 
          plan.id === currentPlan.id 
            ? { ...plan, ...formData } 
            : plan
        )
      )
      
      setIsEditDialogOpen(false)
      
      toast({
        title: "Plan Updated",
        description: `${formData.name} payment plan has been updated successfully.`,
        variant: "default",
      })
    } catch (error) {
      console.error('Error updating plan:', error)
      toast({
        title: "Error",
        description: "Failed to update payment plan. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleDeletePlan = async () => {
    if (!currentPlan) return
    
    setIsLoading(true)
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // In a real implementation, you would delete from the database here
      setPaymentPlans(prev => 
        prev.filter(plan => plan.id !== currentPlan.id)
      )
      
      setIsDeleteDialogOpen(false)
      
      toast({
        title: "Plan Deleted",
        description: `${currentPlan.name} payment plan has been deleted successfully.`,
        variant: "default",
      })
    } catch (error) {
      console.error('Error deleting plan:', error)
      toast({
        title: "Error",
        description: "Failed to delete payment plan. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-start space-x-2 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <Info className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5" />
        <div className="text-sm text-gray-600 dark:text-gray-300">
          <p>Manage the payment plans displayed on the website. Add, edit, or remove payment options available to parents.</p>
        </div>
      </div>

      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Available Payment Plans</h3>
        <Button onClick={openAddDialog} className="flex items-center">
          <Plus className="mr-2 h-4 w-4" />
          Add New Plan
        </Button>
      </div>

      <Card>
        <CardContent className="p-0 overflow-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Plan Name</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Discount</TableHead>
                <TableHead>Due Date</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {paymentPlans.map((plan) => (
                <TableRow key={plan.id}>
                  <TableCell className="font-medium">{plan.name}</TableCell>
                  <TableCell className="max-w-md">{plan.description}</TableCell>
                  <TableCell>{plan.discount}</TableCell>
                  <TableCell>{plan.dueDate}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openEditDialog(plan)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openDeleteDialog(plan)}
                      >
                        <Trash className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Add Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Add New Payment Plan</DialogTitle>
            <DialogDescription>
              Add a new payment plan option for parents.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-1 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Plan Name</Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="e.g., Annual Payment Plan"
                />
              </div>
            </div>
            <div className="grid grid-cols-1 gap-4">
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  placeholder="Describe the payment plan details"
                  rows={3}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="discount">Discount</Label>
                <Input
                  id="discount"
                  name="discount"
                  value={formData.discount}
                  onChange={handleInputChange}
                  placeholder="e.g., 3%"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="dueDate">Due Date</Label>
                <Input
                  id="dueDate"
                  name="dueDate"
                  value={formData.dueDate}
                  onChange={handleInputChange}
                  placeholder="e.g., August 1st"
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddPlan} disabled={isLoading}>
              {isLoading ? (
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
              ) : (
                <Save className="mr-2 h-4 w-4" />
              )}
              Add Plan
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Edit Payment Plan</DialogTitle>
            <DialogDescription>
              Update the payment plan details.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-1 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-name">Plan Name</Label>
                <Input
                  id="edit-name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                />
              </div>
            </div>
            <div className="grid grid-cols-1 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-description">Description</Label>
                <Textarea
                  id="edit-description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={3}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-discount">Discount</Label>
                <Input
                  id="edit-discount"
                  name="discount"
                  value={formData.discount}
                  onChange={handleInputChange}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-dueDate">Due Date</Label>
                <Input
                  id="edit-dueDate"
                  name="dueDate"
                  value={formData.dueDate}
                  onChange={handleInputChange}
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleEditPlan} disabled={isLoading}>
              {isLoading ? (
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
              ) : (
                <Save className="mr-2 h-4 w-4" />
              )}
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the {currentPlan?.name}? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleDeletePlan}
              disabled={isLoading}
            >
              {isLoading ? (
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
              ) : (
                <Trash className="mr-2 h-4 w-4" />
              )}
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

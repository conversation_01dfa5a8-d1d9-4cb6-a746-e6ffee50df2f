import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET endpoint to test Prisma operations
export async function GET() {
  try {
    console.log('Testing Prisma operations')

    // Test 1: Count users
    const userCount = await prisma.user.count()
    console.log(`User count: ${userCount}`)

    // Test 2: Count classes
    const classCount = await prisma.class.count()
    console.log(`Class count: ${classCount}`)

    // Test 3: Count teacher permissions
    const permissionCount = await prisma.teacherPermission.count()
    console.log(`Teacher permission count: ${permissionCount}`)

    // Test 4: Get schema information
    const userModel = await prisma.$queryRaw`
      SELECT * FROM information_schema.tables 
      WHERE table_name = 'user'
    `
    console.log('User model schema:', userModel)

    const teacherPermissionModel = await prisma.$queryRaw`
      SELECT * FROM information_schema.tables 
      WHERE table_name = 'teacher_permissions'
    `
    console.log('TeacherPermission model schema:', teacherPermissionModel)

    // Return test results
    return NextResponse.json({
      success: true,
      tests: {
        userCount,
        classCount,
        permissionCount,
        userModel,
        teacherPermissionModel
      }
    })
  } catch (error) {
    console.error('Error testing Prisma operations:', error)
    if (error instanceof Error) {
      console.error('Error message:', error.message)
      console.error('Error stack:', error.stack)
    }
    return NextResponse.json(
      { error: 'Failed to test Prisma operations', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    )
  }
}

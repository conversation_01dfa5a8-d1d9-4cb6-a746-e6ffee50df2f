const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient({
  log: ['error', 'warn'],
});

async function checkDatabaseConnection() {
  try {
    console.log(`[${new Date().toISOString()}] Checking database connection...`);
    
    // Test basic connection
    await prisma.$queryRaw`SELECT 1`;
    
    // Test user table access
    const userCount = await prisma.user.count();
    
    console.log(`[${new Date().toISOString()}] ✅ Database connection successful. Users: ${userCount}`);
    return true;
  } catch (error) {
    console.error(`[${new Date().toISOString()}] ❌ Database connection failed:`, error.message);
    return false;
  }
}

async function monitorDatabase() {
  console.log('🔍 Starting database connection monitoring...');
  console.log('Press Ctrl+C to stop monitoring\n');
  
  // Check immediately
  await checkDatabaseConnection();
  
  // Then check every 30 seconds
  const interval = setInterval(async () => {
    await checkDatabaseConnection();
  }, 30000);
  
  // Handle graceful shutdown
  process.on('SIGINT', () => {
    console.log('\n🛑 Stopping database monitoring...');
    clearInterval(interval);
    prisma.$disconnect().then(() => {
      console.log('Database connection closed.');
      process.exit(0);
    });
  });
}

// Start monitoring if this script is run directly
if (require.main === module) {
  monitorDatabase().catch(console.error);
}

module.exports = { checkDatabaseConnection };

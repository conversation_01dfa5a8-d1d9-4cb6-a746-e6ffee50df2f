// Define the roles that can access each sidebar item
export type Role = 'SUPER_ADMIN' | 'ADMIN' | 'SUPERVISOR' | 'ACCOUNTANT' | 'TEACHER' | 'PARENT' | 'UNIT_LEADER' | 'DATA_ENCODER';

// Define the sidebar item type
export interface SidebarItem {
  id: string;
  label: string;
  icon: string;
  path: string;
  roles: Role[];
  children?: SidebarItem[];
}

// Define the sidebar configuration
export const sidebarConfig: SidebarItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: 'HomeIcon',
    path: '/dashboard',
    roles: ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'ACCOUNTANT', 'TEACHER', 'PARENT', 'UNIT_LEADER', 'DATA_ENCODER']
  },
  {
    id: 'students',
    label: 'Students',
    icon: 'UserIcon',
    path: '/students',
    roles: ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'TEACHER', 'UNIT_LEADER', 'DATA_ENCODER'],
    children: [
      {
        id: 'all-students',
        label: 'All Students',
        icon: 'UserIcon',
        path: '/students',
        roles: ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'TEACHER', 'UNIT_LEADER', 'DATA_ENCODER']
      },
      {
        id: 'id-card',
        label: 'ID Card',
        icon: 'CreditCard',
        path: '/students/id-card',
        roles: ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR']
      }
    ]
  },
  {
    id: 'teachers',
    label: 'Teachers',
    icon: 'UserPlusIcon',
    path: '/teachers',
    roles: ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'TEACHER', 'UNIT_LEADER', 'DATA_ENCODER']
  },
  {
    id: 'class',
    label: 'Class',
    icon: 'ClipboardList',
    path: '/classes',
    roles: ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'TEACHER', 'UNIT_LEADER', 'DATA_ENCODER']
  },
  {
    id: 'attendance',
    label: 'Attendance',
    icon: 'CalendarCheck',
    path: '/attendance',
    roles: ['SUPER_ADMIN', 'ADMIN', 'TEACHER', 'UNIT_LEADER']
  },
  {
    id: 'marklist',
    label: 'Mark List',
    icon: 'ListChecks',
    path: '/marklist',
    roles: ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'TEACHER', 'UNIT_LEADER', 'DATA_ENCODER']
  },
  {
    id: 'reportcard',
    label: 'Report Card',
    icon: 'FileText',
    path: '/reportcard',
    roles: ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR']
  },
  {
    id: 'roles',
    label: 'Roles',
    icon: 'ShieldCheck',
    path: '/roles',
    roles: ['SUPER_ADMIN']
  },

  {
    id: 'visualizer',
    label: 'Visualizer',
    icon: 'BarChart',
    path: '/visualizer',
    roles: ['SUPER_ADMIN', 'SUPERVISOR', 'UNIT_LEADER']
  },
  {
    id: 'analytics',
    label: 'Analytics',
    icon: 'TrendingUp',
    path: '/analytics',
    roles: ['SUPER_ADMIN']
  },
  {
    id: 'progress-manager',
    label: 'Progress Manager',
    icon: 'Activity',
    path: '/progress-manager',
    roles: ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR']
  },
  {
    id: 'accounting',
    label: 'Accounting',
    icon: 'DollarSign',
    path: '/accounting',
    roles: ['SUPER_ADMIN', 'ACCOUNTANT'],
    children: [
      {
        id: 'fee-types',
        label: 'Fee Types',
        icon: 'CreditCard',
        path: '/accounting/fee-types',
        roles: ['SUPER_ADMIN', 'ACCOUNTANT']
      },
      {
        id: 'account-codes',
        label: 'Account Codes',
        icon: 'Hash',
        path: '/accounting/account-codes',
        roles: ['SUPER_ADMIN', 'ACCOUNTANT']
      },
      {
        id: 'fee-invoice',
        label: 'Fee Invoice',
        icon: 'Receipt',
        path: '/accounting/fee-invoice',
        roles: ['SUPER_ADMIN', 'ACCOUNTANT']
      },
      {
        id: 'invoices-report',
        label: 'Invoices Report',
        icon: 'FileSpreadsheet',
        path: '/accounting/invoices-report',
        roles: ['SUPER_ADMIN', 'ACCOUNTANT']
      },
      {
        id: 'bank-payment-voucher',
        label: 'Bank Payment Voucher',
        icon: 'CreditCard',
        path: '/accounting/bank-payment-voucher',
        roles: ['SUPER_ADMIN', 'ACCOUNTANT']
      },
      {
        id: 'journal-voucher',
        label: 'Journal Voucher',
        icon: 'FileText',
        path: '/accounting/journal-voucher',
        roles: ['SUPER_ADMIN', 'ACCOUNTANT']
      }
    ]
  },
  {
    id: 'website',
    label: 'Website',
    icon: 'Globe',
    path: '/website-management',
    roles: ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR'],
    children: [
      {
        id: 'hero-slides',
        label: 'Hero Slides',
        icon: 'Image',
        path: '/website-management/hero-slides',
        roles: ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR']
      },
      {
        id: 'announcements',
        label: 'Announcements',
        icon: 'Bell',
        path: '/website-management/announcements',
        roles: ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR']
      },
      {
        id: 'quick-links',
        label: 'Quick Links',
        icon: 'Link2',
        path: '/website-management/quick-links',
        roles: ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR']
      },
      {
        id: 'featured-content',
        label: 'Featured Content',
        icon: 'Image',
        path: '/website-management/featured-content',
        roles: ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR']
      },
      {
        id: 'news-events',
        label: 'News & Events',
        icon: 'Newspaper',
        path: '/website-management/news-events',
        roles: ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR']
      },
      {
        id: 'academic-calendar',
        label: 'Academic Calendar',
        icon: 'CalendarCheck',
        path: '/website-management/academic-calendar',
        roles: ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR']
      },
      {
        id: 'testimonials',
        label: 'Testimonials',
        icon: 'MessageSquare',
        path: '/website-management/testimonials',
        roles: ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR']
      },
      {
        id: 'tuition-fees',
        label: 'Tuition & Fees',
        icon: 'Wallet',
        path: '/website-management/tuition-fees',
        roles: ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR']
      }
    ]
  },
  {
    id: 'my-children',
    label: 'My Children',
    icon: 'Users',
    path: '/parent/children',
    roles: ['PARENT'],
    children: [
      {
        id: 'children-overview',
        label: 'Overview',
        icon: 'Eye',
        path: '/parent/children',
        roles: ['PARENT']
      },
      {
        id: 'children-attendance',
        label: 'Attendance',
        icon: 'CalendarCheck',
        path: '/parent/attendance',
        roles: ['PARENT']
      },
      {
        id: 'children-marks',
        label: 'Marks',
        icon: 'ListChecks',
        path: '/parent/marks',
        roles: ['PARENT']
      },
      {
        id: 'children-reports',
        label: 'Report Cards',
        icon: 'FileText',
        path: '/parent/reports',
        roles: ['PARENT']
      }
    ]
  },
  {
    id: 'profile',
    label: 'Profile',
    icon: 'User',
    path: '/profile',
    roles: ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'ACCOUNTANT', 'TEACHER', 'PARENT', 'UNIT_LEADER', 'DATA_ENCODER']
  },
  {
    id: 'settings',
    label: 'Settings',
    icon: 'Settings',
    path: '/settings',
    roles: ['SUPER_ADMIN']
  }
];

// Helper function to check if a user role has access to a sidebar item
export const hasAccess = (item: SidebarItem, userRole: string): boolean => {
  return item.roles.includes(userRole as Role);
};

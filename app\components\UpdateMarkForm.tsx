'use client'

import { useState, useCallback } from 'react'
import { useQuery } from '@tanstack/react-query'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/app/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/app/components/ui/table'
import { Button } from '@/app/components/ui/button'
import { Input } from '@/app/components/ui/input'
import { Label } from '@/app/components/ui/label'
import { Loader2, Users, BookOpen, Edit, GraduationCap } from 'lucide-react'
import { toast } from '@/app/components/ui/use-toast'

// Types based on Prisma schema
interface Class {
  id: string
  name: string
  totalStudents: number
  totalSubjects: number
  hasSubjects: boolean
}

interface Subject {
  id: string
  name: string
  classId: string
}

interface StudentMark {
  id: string
  studentId: string
  studentName: string
  studentSid: string
  obtainedMarks: number
  originalMarks: number
  isEdited: boolean
}

interface UpdateMarkFormProps {
  onCancel: () => void
  onSuccess: () => void
}

// Academic years and terms based on Prisma schema
const academicYears = [
  '2020-2021', '2021-2022', '2022-2023', '2023-2024',
  '2024-2025', '2025-2026', '2026-2027', '2027-2028'
]

const terms = [
  'First Semester',
  'Second Semester'
]

export function UpdateMarkForm({ onCancel, onSuccess }: UpdateMarkFormProps) {
  // Form settings
  const [formSettings, setFormSettings] = useState({
    className: '',
    subject: '',
    term: 'First Semester',
    academicYear: '2023-2024',
  })

  // Student mark data
  const [studentMarks, setStudentMarks] = useState<StudentMark[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isSaving, setIsSaving] = useState(false)

  // Fetch classes using existing API endpoint
  const {
    data: classes = [],
    isLoading: isLoadingClasses,
    error: classesError
  } = useQuery<Class[]>({
    queryKey: ['classes-for-marks'],
    queryFn: async () => {
      const res = await fetch('/api/classes/for-marks')
      if (!res.ok) throw new Error('Failed to fetch classes')
      return res.json()
    }
  })

  // Fetch subjects for selected class
  const {
    data: subjects = [],
    isLoading: isLoadingSubjects,
    error: subjectsError
  } = useQuery<Subject[]>({
    queryKey: ['subjects-for-class', formSettings.className],
    queryFn: async () => {
      if (!formSettings.className) return []
      const res = await fetch(`/api/subjects/for-class?className=${formSettings.className}`)
      if (!res.ok) {
        if (res.status === 404) {
          throw new Error(`No subjects found for class '${formSettings.className}'. Please add subjects in Class Management first.`)
        }
        throw new Error('Failed to fetch subjects')
      }
      return res.json()
    },
    enabled: !!formSettings.className
  })

  // Handle form setting changes
  const handleSettingChange = useCallback((name: string, value: string) => {
    setFormSettings(prev => ({
      ...prev,
      [name]: value
    }))

    // Clear student marks when class or subject changes
    if (name === 'className' || name === 'subject') {
      setStudentMarks([])
    }
  }, [])

  // Load marks when class, subject, term, and academicYear are selected
  const loadMarks = async () => {
    if (!formSettings.className || !formSettings.subject || !formSettings.term || !formSettings.academicYear) {
      toast({
        title: "Missing Information",
        description: "Please select class, subject, term, and academic year",
        variant: "destructive"
      })
      return
    }

    setIsLoading(true)
    try {
      const params = new URLSearchParams({
        className: formSettings.className,
        subject: formSettings.subject,
        term: formSettings.term,
        academicYear: formSettings.academicYear
      })
      
      const res = await fetch(`/api/marks/bulk?${params}`)
      if (!res.ok) {
        if (res.status === 404) {
          toast({
            title: "No Marks Found",
            description: `No marks found for ${formSettings.subject} in ${formSettings.className} for ${formSettings.term}`,
            variant: "destructive"
          })
          setStudentMarks([])
          return
        }
        const errorData = await res.json().catch(() => ({}))
        throw new Error(errorData.error || 'Failed to fetch marks')
      }

      const marks = await res.json()

      // Format marks for the form
      const formattedMarks = marks.map((mark: any) => ({
        id: mark.id,
        studentId: mark.studentId,
        studentName: mark.studentName,
        studentSid: mark.studentSid || '',
        obtainedMarks: mark.marks,
        originalMarks: mark.marks,
        isEdited: false
      }))

      setStudentMarks(formattedMarks)
      console.log(`Loaded ${formattedMarks.length} marks for editing`)
      
      toast({
        title: "Marks Loaded",
        description: `Loaded ${formattedMarks.length} marks for editing`
      })
    } catch (error) {
      console.error('Error loading marks:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to load marks',
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Handle mark change for individual student
  const handleMarkChange = useCallback((markId: string, newMarks: number) => {
    setStudentMarks(prev => 
      prev.map(mark => 
        mark.id === markId 
          ? { 
              ...mark, 
              obtainedMarks: newMarks,
              isEdited: newMarks !== mark.originalMarks
            }
          : mark
      )
    )
  }, [])

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    const editedMarks = studentMarks.filter(mark => mark.isEdited)
    
    if (editedMarks.length === 0) {
      toast({
        title: "No Changes",
        description: "No changes to save",
        variant: "destructive"
      })
      return
    }

    setIsSaving(true)
    try {
      // Update marks one by one using existing API endpoint
      const updatePromises = editedMarks.map(async (mark) => {
        const res = await fetch(`/api/marks/${mark.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            marks: mark.obtainedMarks, // Using 'marks' to match Prisma schema
            totalMarks: 100, // Default total marks
            updatedAt: new Date().toISOString()
          }),
        })

        if (!res.ok) {
          const errorData = await res.json()
          throw new Error(errorData.error || `Failed to update mark for ${mark.studentName}`)
        }

        return res.json()
      })

      await Promise.all(updatePromises)
      
      console.log(`Successfully updated ${editedMarks.length} marks`)
      toast({
        title: "Success",
        description: `Successfully updated ${editedMarks.length} marks`
      })
      onSuccess()
    } catch (error) {
      console.error('Error updating marks:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to update marks',
        variant: "destructive"
      })
    } finally {
      setIsSaving(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6 h-full flex flex-col">
      {/* Form Settings */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pb-4 border-b">
        {/* Class */}
        <div className="grid gap-2">
          <Label htmlFor="className" className="text-sm font-medium">
            Class <span className="text-red-500">*</span>
          </Label>
          <Select
            key={`class-select-${formSettings.className || 'empty'}`}
            value={formSettings.className || ""}
            onValueChange={(value) => handleSettingChange('className', value)}
          >
            <SelectTrigger>
              {isLoadingClasses ? (
                <div className="flex items-center">
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Loading classes...
                </div>
              ) : (
                <>
                  <Users className="mr-2 h-4 w-4 text-gray-400" />
                  <SelectValue placeholder="Select Class" />
                </>
              )}
            </SelectTrigger>
            <SelectContent>
              {classesError ? (
                <div className="p-2 text-center text-sm text-red-500">
                  Error loading classes
                </div>
              ) : classes && classes.length > 0 ? (
                classes.map((cls) => (
                  <SelectItem key={cls.id} value={cls.name}>
                    <div className="flex items-center justify-between w-full">
                      <span>{cls.name}</span>
                      <span className="text-xs text-gray-500 ml-2">
                        {cls.totalSubjects} subjects
                      </span>
                    </div>
                  </SelectItem>
                ))
              ) : (
                <div className="p-2 text-center text-sm text-gray-500">
                  No classes found
                </div>
              )}
            </SelectContent>
          </Select>
        </div>

        {/* Subject */}
        <div className="grid gap-2">
          <Label htmlFor="subject" className="text-sm font-medium">
            Subject <span className="text-red-500">*</span>
          </Label>
          <Select
            key={`subject-select-${formSettings.className || 'empty'}-${formSettings.subject || 'empty'}`}
            value={formSettings.subject || ""}
            onValueChange={(value) => handleSettingChange('subject', value)}
            disabled={!formSettings.className || isLoadingSubjects}
          >
            <SelectTrigger>
              {isLoadingSubjects ? (
                <div className="flex items-center">
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Loading subjects...
                </div>
              ) : (
                <>
                  <BookOpen className="mr-2 h-4 w-4 text-gray-400" />
                  <SelectValue placeholder="Select Subject" />
                </>
              )}
            </SelectTrigger>
            <SelectContent>
              {subjectsError ? (
                <div className="p-2 text-center text-sm text-red-500">
                  <div className="font-medium">Error loading subjects</div>
                  <div className="text-xs mt-1">
                    {subjectsError.message.includes('No subjects found') 
                      ? 'No subjects assigned to this class. Please add subjects in Class Management.'
                      : 'Failed to load subjects. Please try again.'
                    }
                  </div>
                </div>
              ) : subjects && subjects.length > 0 ? (
                subjects.map((subject) => (
                  <SelectItem key={subject.id} value={subject.name}>
                    {subject.name}
                  </SelectItem>
                ))
              ) : (
                <div className="p-2 text-center text-sm text-gray-500">
                  {formSettings.className 
                    ? 'No subjects assigned to this class. Please add subjects in Class Management first.' 
                    : 'Select a class first'
                  }
                </div>
              )}
            </SelectContent>
          </Select>
        </div>

        {/* Term */}
        <div className="grid gap-2">
          <Label htmlFor="term" className="text-sm font-medium">
            Term Semester <span className="text-red-500">*</span>
          </Label>
          <Select
            key={`term-select-${formSettings.term || 'empty'}`}
            value={formSettings.term || ""}
            onValueChange={(value) => handleSettingChange('term', value)}
          >
            <SelectTrigger>
              <GraduationCap className="mr-2 h-4 w-4 text-gray-400" />
              <SelectValue placeholder="Select term" />
            </SelectTrigger>
            <SelectContent>
              {terms.map(term => (
                <SelectItem key={term} value={term}>
                  {term}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Academic Year */}
        <div className="grid gap-2">
          <Label htmlFor="academicYear" className="text-sm font-medium">
            Academic Year <span className="text-red-500">*</span>
          </Label>
          <Select
            key={`academic-year-select-${formSettings.academicYear || 'empty'}`}
            value={formSettings.academicYear || ""}
            onValueChange={(value) => handleSettingChange('academicYear', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select academic year" />
            </SelectTrigger>
            <SelectContent>
              {academicYears.map(year => (
                <SelectItem key={year} value={year}>
                  {year}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Load Marks Button */}
      <div className="flex justify-center">
        <Button
          type="button"
          onClick={loadMarks}
          disabled={!formSettings.className || !formSettings.subject || !formSettings.term || !formSettings.academicYear || isLoading}
          className="w-full md:w-1/2"
        >
          {isLoading ? (
            <div className="flex items-center">
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Loading marks...
            </div>
          ) : (
            <div className="flex items-center">
              <Edit className="mr-2 h-4 w-4" />
              Load Student Marks
            </div>
          )}
        </Button>
      </div>

      {/* Student List */}
      {studentMarks.length > 0 && (
        <div className="flex-1 min-h-0">
          <div className="mt-4 border rounded-md overflow-hidden">
            <div className="max-h-[40vh] overflow-y-auto">
              <Table>
                <TableHeader className="sticky top-0 bg-white z-10">
                  <TableRow>
                    <TableHead className="w-12">#</TableHead>
                    <TableHead className="w-24">SID</TableHead>
                    <TableHead>Student</TableHead>
                    <TableHead>Current Mark</TableHead>
                    <TableHead>New Mark</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {studentMarks.map((student, index) => (
                    <TableRow key={student.id} className={student.isEdited ? "bg-blue-50" : ""}>
                      <TableCell className="text-center">{index + 1}</TableCell>
                      <TableCell>
                        <span className="font-mono text-xs font-medium bg-gray-100 px-2 py-1 rounded">
                          {student.studentSid}
                        </span>
                      </TableCell>
                      <TableCell>{student.studentName}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <span>{student.originalMarks}/100</span>
                          <div className="w-20 h-2 bg-gray-100 rounded-full">
                            <div
                              className={`h-full rounded-full ${
                                student.originalMarks >= 80 ? 'bg-green-500' :
                                student.originalMarks >= 60 ? 'bg-yellow-500' :
                                'bg-red-500'
                              }`}
                              style={{ width: `${student.originalMarks}%` }}
                            ></div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Input
                          type="number"
                          min="0"
                          max="100"
                          value={student.obtainedMarks || ''}
                          onChange={(e) => handleMarkChange(student.id, parseInt(e.target.value) || 0)}
                          className={`w-24 text-right ${student.isEdited ? "border-blue-500 ring-1 ring-blue-500" : ""}`}
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </div>
      )}

      {/* Form Actions */}
      <div className="sticky bottom-0 bg-white pt-4 border-t mt-4">
        <div className="flex justify-end space-x-2">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={studentMarks.length === 0 || !studentMarks.some(s => s.isEdited) || isSaving}
          >
            {isSaving ? (
              <div className="flex items-center">
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </div>
            ) : (
              "Update Marks"
            )}
          </Button>
        </div>
      </div>
    </form>
  )
}

'use client';

import React, { useState, useEffect } from 'react';
import { useEnhancedTheme, ThemeName, themes } from '../contexts/enhanced-theme-context';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from './ui/card';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from './ui/tabs';
import { Switch } from './ui/switch';
import { Label } from './ui/label';
import { Button } from './ui/button';
import { Sun, Moon, Palette, Eye, RotateCcw } from 'lucide-react';

const ThemeSettings: React.FC = () => {
  const { currentTheme, setTheme, reducedMotion, toggleReducedMotion } = useEnhancedTheme();

  const themeOptions: { name: ThemeName; icon: React.ReactNode; label: string; description: string }[] = [
    {
      name: 'light',
      icon: <Sun className="h-5 w-5" />,
      label: 'Light',
      description: 'Clean and bright interface for daytime use'
    },
    {
      name: 'dark',
      icon: <Moon className="h-5 w-5" />,
      label: 'Dark',
      description: 'Reduced brightness for nighttime use'
    },
    {
      name: 'solarized',
      icon: <Palette className="h-5 w-5" />,
      label: 'Solarized',
      description: 'Muted warm/cool hues for comfortable reading'
    },
    {
      name: 'high-contrast',
      icon: <Eye className="h-5 w-5" />,
      label: 'High Contrast',
      description: 'Enhanced visibility for accessibility'
    }
  ];

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>Theme Settings</CardTitle>
        <CardDescription>Customize the appearance of the application</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="themes">
          <TabsList className="mb-4">
            <TabsTrigger value="themes">Themes</TabsTrigger>
            <TabsTrigger value="accessibility">Accessibility</TabsTrigger>
          </TabsList>

          <TabsContent value="themes">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {themeOptions.map((option) => (
                <div
                  key={option.name}
                  className={`p-4 rounded-lg border cursor-pointer transition-all ${
                    currentTheme.name === option.name
                      ? 'border-primary ring-2 ring-primary/20'
                      : 'border-border hover:border-primary/50'
                  }`}
                  onClick={() => setTheme(option.name)}
                  style={{
                    backgroundColor: themes[option.name].colors.surface,
                    color: themes[option.name].colors.text,
                  }}
                >
                  <div className="flex items-center gap-3 mb-2">
                    <div className="p-2 rounded-full" style={{
                      backgroundColor: themes[option.name].colors.primary,
                      color: themes[option.name].colors.onPrimary
                    }}>
                      {option.icon}
                    </div>
                    <div>
                      <h3 className="font-medium">{option.label}</h3>
                      <p className="text-sm opacity-80">{option.description}</p>
                    </div>
                    {currentTheme.name === option.name && (
                      <div className="ml-auto text-xs px-2 py-1 rounded-full" style={{
                        backgroundColor: themes[option.name].colors.primary,
                        color: themes[option.name].colors.onPrimary
                      }}>
                        Active
                      </div>
                    )}
                  </div>

                  <div className="grid grid-cols-5 gap-2 mt-4">
                    <div className="h-6 rounded" style={{ backgroundColor: themes[option.name].colors.primary }}></div>
                    <div className="h-6 rounded" style={{ backgroundColor: themes[option.name].colors.secondary }}></div>
                    <div className="h-6 rounded" style={{ backgroundColor: themes[option.name].colors.error }}></div>
                    <div className="h-6 rounded" style={{ backgroundColor: themes[option.name].colors.success }}></div>
                    <div className="h-6 rounded" style={{ backgroundColor: themes[option.name].colors.warning }}></div>
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="accessibility">
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium">Reduced Motion</h3>
                  <p className="text-sm text-muted-foreground">Minimize animations throughout the interface</p>
                </div>
                <Switch
                  checked={reducedMotion}
                  onCheckedChange={toggleReducedMotion}
                  id="reduced-motion-switch"
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium">Font Size</h3>
                  <p className="text-sm text-muted-foreground">Adjust the base font size</p>
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm" onClick={() => {
                    const root = document.documentElement;
                    const currentSize = getComputedStyle(root).getPropertyValue('--font-size-base');
                    const currentSizeNum = parseFloat(currentSize);
                    root.style.setProperty('--font-size-base', `${currentSizeNum - 1}px`);
                  }}>
                    A-
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => {
                    const root = document.documentElement;
                    const currentSize = getComputedStyle(root).getPropertyValue('--font-size-base');
                    const currentSizeNum = parseFloat(currentSize);
                    root.style.setProperty('--font-size-base', `${currentSizeNum + 1}px`);
                  }}>
                    A+
                  </Button>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={() => {
          const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
          setTheme(prefersDark ? 'dark' : 'light');
          localStorage.removeItem('user-theme-preference');
        }}>
          <RotateCcw className="mr-2 h-4 w-4" />
          Reset to System Default
        </Button>
      </CardFooter>
    </Card>
  );
};

export { ThemeSettings };

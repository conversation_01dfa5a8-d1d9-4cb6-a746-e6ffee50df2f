'use client'

import React, { useState, useEffect } from 'react'
import dynamic from 'next/dynamic'
import Sidebar from '../../components/Sidebar'
import { Card } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Label } from '../../components/ui/label'
import { Checkbox } from '../../components/ui/checkbox'
import {
  Table, TableBody, TableCaption, TableCell,
  TableHead, TableHeader, TableRow
} from '../../components/ui/table'
import {
  RecursionSafeDialog,
  RecursionSafeDialogContent,
  RecursionSafeDialogHeader,
  RecursionSafeDialogTitle,
  RecursionSafeDialogDescription,
  RecursionSafeDialogFooter
} from '../../components/RecursionSafeDialog'
import {
  DropdownMenu, DropdownMenuContent, DropdownMenuItem,
  DropdownMenuTrigger
} from '../../components/ui/dropdown-menu'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../components/ui/select"
import {
  Globe, Link2, Plus, MoreHorizontal, Pencil, Trash2,
  Eye, EyeOff, AlertCircle, ExternalLink, Search
} from 'lucide-react'
import { useQuery, useMutation, QueryClient, QueryClientProvider } from '@tanstack/react-query'
import Link from 'next/link'
import { useToast } from '../../components/ui/use-toast'
import { Toaster } from '../../components/ui/toaster'
import * as Icons from 'react-icons/fa'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs'

// Create a client
const queryClient = new QueryClient()

// Import Header component with no SSR to avoid hydration issues
const Header = dynamic(() => import('../../components/Header'), {
  ssr: false
})

// Type definition for quick link
interface QuickLink {
  id: string;
  title: string;
  description: string;
  icon: string;
  url: string;
  color: string;
  borderColor: string;
  hoverColor: string;
  isActive: boolean;
  order: number;
  createdAt: string;
  updatedAt: string;
}

// Color presets for quick links
const colorPresets = [
  {
    name: 'Blue',
    color: 'bg-blue-50 dark:bg-blue-900/20',
    borderColor: 'border-blue-200 dark:border-blue-800',
    hoverColor: 'hover:bg-blue-100 dark:hover:bg-blue-900/30',
  },
  {
    name: 'Green',
    color: 'bg-green-50 dark:bg-green-900/20',
    borderColor: 'border-green-200 dark:border-green-800',
    hoverColor: 'hover:bg-green-100 dark:hover:bg-green-900/30',
  },
  {
    name: 'Amber',
    color: 'bg-amber-50 dark:bg-amber-900/20',
    borderColor: 'border-amber-200 dark:border-amber-800',
    hoverColor: 'hover:bg-amber-100 dark:hover:bg-amber-900/30',
  },
  {
    name: 'Purple',
    color: 'bg-purple-50 dark:bg-purple-900/20',
    borderColor: 'border-purple-200 dark:border-purple-800',
    hoverColor: 'hover:bg-purple-100 dark:hover:bg-purple-900/30',
  },
  {
    name: 'Red',
    color: 'bg-red-50 dark:bg-red-900/20',
    borderColor: 'border-red-200 dark:border-red-800',
    hoverColor: 'hover:bg-red-100 dark:hover:bg-red-900/30',
  },
  {
    name: 'Teal',
    color: 'bg-teal-50 dark:bg-teal-900/20',
    borderColor: 'border-teal-200 dark:border-teal-800',
    hoverColor: 'hover:bg-teal-100 dark:hover:bg-teal-900/30',
  },
]

// Get a list of available Font Awesome icons
const getAvailableIcons = () => {
  // Filter out only the icon components from the Icons object
  return Object.keys(Icons).filter(key =>
    key.startsWith('Fa') &&
    typeof Icons[key as keyof typeof Icons] === 'function' &&
    key !== 'FaBeer' // Example of filtering out specific icons if needed
  );
}

function QuickLinksManagement() {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false)
  const [isMounted, setIsMounted] = useState(false)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [currentQuickLink, setCurrentQuickLink] = useState<QuickLink | null>(null)
  const [iconSearch, setIconSearch] = useState('')
  const [availableIcons] = useState<string[]>(getAvailableIcons())
  const [filteredIcons, setFilteredIcons] = useState<string[]>(availableIcons)
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    icon: 'FaLink',
    url: '',
    colorPreset: 'Blue',
    color: 'bg-blue-50 dark:bg-blue-900/20',
    borderColor: 'border-blue-200 dark:border-blue-800',
    hoverColor: 'hover:bg-blue-100 dark:hover:bg-blue-900/30',
    isActive: true
  })
  const { toast } = useToast()

  // Fetch quick links from the API
  const { data: quickLinks, isLoading, refetch } = useQuery<QuickLink[]>({
    queryKey: ['quickLinks'],
    queryFn: async () => {
      try {
        const res = await fetch('/api/website-management/quick-links')
        if (!res.ok) {
          throw new Error('Failed to fetch quick links')
        }
        return res.json()
      } catch (error) {
        // If API doesn't exist yet, return mock data
        return [
          {
            id: '1',
            title: 'Admissions',
            description: 'Apply for enrollment or check admission requirements',
            icon: 'FaGraduationCap',
            url: '/website/admissions',
            color: 'bg-blue-50 dark:bg-blue-900/20',
            borderColor: 'border-blue-200 dark:border-blue-800',
            hoverColor: 'hover:bg-blue-100 dark:hover:bg-blue-900/30',
            isActive: true,
            order: 0,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          },
          {
            id: '2',
            title: 'Calendar',
            description: 'View academic calendar and upcoming events',
            icon: 'FaCalendarAlt',
            url: '/website/academics#calendar',
            color: 'bg-green-50 dark:bg-green-900/20',
            borderColor: 'border-green-200 dark:border-green-800',
            hoverColor: 'hover:bg-green-100 dark:hover:bg-green-900/30',
            isActive: true,
            order: 1,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }
        ]
      }
    },
    staleTime: 60000, // 1 minute
    refetchOnWindowFocus: false,
  })

  // Add quick link mutation
  const addQuickLinkMutation = useMutation({
    mutationFn: async (newQuickLink: Omit<QuickLink, 'id' | 'createdAt' | 'updatedAt' | 'order'>) => {
      const res = await fetch('/api/website-management/quick-links', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newQuickLink),
      })

      if (!res.ok) {
        const error = await res.json()
        throw new Error(error.message || 'Failed to add quick link')
      }

      return res.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['quickLinks'] })
      queryClient.invalidateQueries({ queryKey: ['websiteStats'] })
      setIsAddDialogOpen(false)
      resetForm()
      toast({
        title: 'Success',
        description: 'Quick link added successfully',
        variant: 'default',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to add quick link',
        variant: 'destructive',
      })
    }
  })

  // Update quick link mutation
  const updateQuickLinkMutation = useMutation({
    mutationFn: async (updatedQuickLink: Partial<QuickLink> & { id: string }) => {
      const res = await fetch(`/api/website-management/quick-links/${updatedQuickLink.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedQuickLink),
      })

      if (!res.ok) {
        const error = await res.json()
        throw new Error(error.message || 'Failed to update quick link')
      }

      return res.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['quickLinks'] })
      setIsEditDialogOpen(false)
      resetForm()
      toast({
        title: 'Success',
        description: 'Quick link updated successfully',
        variant: 'default',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update quick link',
        variant: 'destructive',
      })
    }
  })

  // Delete quick link mutation
  const deleteQuickLinkMutation = useMutation({
    mutationFn: async (id: string) => {
      const res = await fetch(`/api/website-management/quick-links/${id}`, {
        method: 'DELETE',
      })

      if (!res.ok) {
        const error = await res.json()
        throw new Error(error.message || 'Failed to delete quick link')
      }

      return res.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['quickLinks'] })
      queryClient.invalidateQueries({ queryKey: ['websiteStats'] })
      setIsDeleteDialogOpen(false)
      toast({
        title: 'Success',
        description: 'Quick link deleted successfully',
        variant: 'default',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete quick link',
        variant: 'destructive',
      })
    }
  })

  useEffect(() => {
    setIsMounted(true)
  }, [])

  // Filter icons based on search
  useEffect(() => {
    if (iconSearch.trim() === '') {
      setFilteredIcons(availableIcons)
    } else {
      const filtered = availableIcons.filter(icon =>
        icon.toLowerCase().includes(iconSearch.toLowerCase())
      )
      setFilteredIcons(filtered)
    }
  }, [iconSearch, availableIcons])

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  const toggleMobileSidebar = () => {
    setMobileSidebarOpen(!mobileSidebarOpen)
  }

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      icon: 'FaLink',
      url: '',
      colorPreset: 'Blue',
      color: 'bg-blue-50 dark:bg-blue-900/20',
      borderColor: 'border-blue-200 dark:border-blue-800',
      hoverColor: 'hover:bg-blue-100 dark:hover:bg-blue-900/30',
      isActive: true
    })
    setIconSearch('')
    setFilteredIcons(availableIcons)
    setCurrentQuickLink(null)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleIconSelect = (icon: string) => {
    setFormData(prev => ({ ...prev, icon }))
  }

  const handleColorPresetSelect = (presetName: string) => {
    const preset = colorPresets.find(p => p.name === presetName)
    if (preset) {
      setFormData(prev => ({
        ...prev,
        colorPreset: presetName,
        color: preset.color,
        borderColor: preset.borderColor,
        hoverColor: preset.hoverColor
      }))
    }
  }

  const handleCheckboxChange = (checked: boolean) => {
    setFormData(prev => ({ ...prev, isActive: checked }))
  }

  const handleAddQuickLink = () => {
    addQuickLinkMutation.mutate({
      title: formData.title,
      description: formData.description,
      icon: formData.icon,
      url: formData.url,
      color: formData.color,
      borderColor: formData.borderColor,
      hoverColor: formData.hoverColor,
      isActive: formData.isActive
    })
  }

  const handleUpdateQuickLink = () => {
    if (!currentQuickLink) return

    updateQuickLinkMutation.mutate({
      id: currentQuickLink.id,
      title: formData.title,
      description: formData.description,
      icon: formData.icon,
      url: formData.url,
      color: formData.color,
      borderColor: formData.borderColor,
      hoverColor: formData.hoverColor,
      isActive: formData.isActive
    })
  }

  const handleDeleteQuickLink = () => {
    if (!currentQuickLink) return

    deleteQuickLinkMutation.mutate(currentQuickLink.id)
  }

  const openEditDialog = (quickLink: QuickLink) => {
    setCurrentQuickLink(quickLink)

    // Find the color preset that matches the quick link's colors
    const preset = colorPresets.find(p =>
      p.color === quickLink.color &&
      p.borderColor === quickLink.borderColor &&
      p.hoverColor === quickLink.hoverColor
    )

    setFormData({
      title: quickLink.title,
      description: quickLink.description,
      icon: quickLink.icon,
      url: quickLink.url,
      colorPreset: preset ? preset.name : 'Blue',
      color: quickLink.color,
      borderColor: quickLink.borderColor,
      hoverColor: quickLink.hoverColor,
      isActive: quickLink.isActive
    })
    setIsEditDialogOpen(true)
  }

  const openDeleteDialog = (quickLink: QuickLink) => {
    setCurrentQuickLink(quickLink)
    setIsDeleteDialogOpen(true)
  }

  // Render icon component
  const renderIcon = (iconName: string, size = 'text-xl') => {
    // @ts-ignore - Icons is a dynamic import
    const IconComponent = Icons[iconName as keyof typeof Icons]
    if (IconComponent) {
      return <IconComponent className={`${size} text-blue-600`} />
    }
    return <Link2 className={`${size} text-blue-600`} />
  }

  // Only show loading while mounting
  if (!isMounted || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-600"></div>
      </div>
    )
  }

  return (
    <div className="flex h-screen bg-gray-100 dark:bg-gray-900">
      <Sidebar
        isCollapsed={sidebarCollapsed}
        toggleSidebar={toggleSidebar}
        isMobileOpen={mobileSidebarOpen}
      />

      <main className={`main-content ${sidebarCollapsed ? 'sidebar-collapsed' : ''} w-full h-screen overflow-y-auto`}>
        <Header toggleMobileSidebar={toggleMobileSidebar} />
        <Toaster />

        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-2xl font-semibold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent inline-block">
                Quick Links Management
              </h1>
              <p className="text-gray-600 dark:text-gray-300 mt-1">
                Manage the quick links displayed on the website
              </p>
            </div>
            <div className="flex space-x-2">
              <Link href="/website" target="_blank">
                <Button variant="outline" size="sm" className="flex items-center">
                  <Globe className="h-4 w-4 mr-1" />
                  <span>View Website</span>
                </Button>
              </Link>
              <Button onClick={() => setIsAddDialogOpen(true)} className="flex items-center">
                <Plus className="h-4 w-4 mr-1" />
                <span>Add Quick Link</span>
              </Button>
            </div>
          </div>

          <Card className="overflow-hidden border-0 shadow-md">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[80px]">Order</TableHead>
                  <TableHead className="w-[80px]">Icon</TableHead>
                  <TableHead>Title</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead className="w-[180px]">URL</TableHead>
                  <TableHead className="w-[100px]">Status</TableHead>
                  <TableHead className="w-[100px] text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {quickLinks && quickLinks.length > 0 ? (
                  quickLinks.map((quickLink) => (
                    <TableRow key={quickLink.id}>
                      <TableCell className="font-medium">{quickLink.order + 1}</TableCell>
                      <TableCell>
                        <div className="flex items-center justify-center">
                          {renderIcon(quickLink.icon)}
                        </div>
                      </TableCell>
                      <TableCell>{quickLink.title}</TableCell>
                      <TableCell className="max-w-[200px] truncate">{quickLink.description}</TableCell>
                      <TableCell className="max-w-[180px] truncate">
                        <a
                          href={quickLink.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                        >
                          {quickLink.url}
                          <ExternalLink className="h-3 w-3 ml-1" />
                        </a>
                      </TableCell>
                      <TableCell>
                        {quickLink.isActive ? (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                            Active
                          </span>
                        ) : (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                            Inactive
                          </span>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">Open menu</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => openEditDialog(quickLink)}>
                              <Pencil className="h-4 w-4 mr-2" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => openDeleteDialog(quickLink)}>
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => updateQuickLinkMutation.mutate({ id: quickLink.id, isActive: !quickLink.isActive })}>
                              {quickLink.isActive ? (
                                <>
                                  <EyeOff className="h-4 w-4 mr-2" />
                                  Deactivate
                                </>
                              ) : (
                                <>
                                  <Eye className="h-4 w-4 mr-2" />
                                  Activate
                                </>
                              )}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      <div className="flex flex-col items-center justify-center text-gray-500 dark:text-gray-400">
                        <Link2 className="h-12 w-12 mb-2 opacity-50" />
                        <p>No quick links found</p>
                        <Button
                          variant="link"
                          onClick={() => setIsAddDialogOpen(true)}
                          className="mt-2"
                        >
                          Add your first quick link
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </Card>
        </div>
      </main>

      {/* Add Quick Link Dialog */}
      <RecursionSafeDialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen} maxWidth="max-w-[700px]">
        <RecursionSafeDialogHeader>
          <RecursionSafeDialogTitle>Add New Quick Link</RecursionSafeDialogTitle>
          <RecursionSafeDialogDescription>
            Create a new quick link for the website. All fields are required.
          </RecursionSafeDialogDescription>
        </RecursionSafeDialogHeader>

        <RecursionSafeDialogContent>
          <Tabs defaultValue="details" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="details">Details</TabsTrigger>
              <TabsTrigger value="icon">Icon</TabsTrigger>
              <TabsTrigger value="appearance">Appearance</TabsTrigger>
            </TabsList>
            <TabsContent value="details" className="py-4">
              <div className="grid gap-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="title">Title</Label>
                    <Input
                      id="title"
                      name="title"
                      value={formData.title}
                      onChange={handleInputChange}
                      placeholder="Enter quick link title"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="url">URL</Label>
                    <Input
                      id="url"
                      name="url"
                      value={formData.url}
                      onChange={handleInputChange}
                      placeholder="Enter URL (e.g., /website/admissions)"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Input
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    placeholder="Enter quick link description"
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isActive"
                    checked={formData.isActive}
                    onCheckedChange={handleCheckboxChange}
                  />
                  <Label htmlFor="isActive">Active</Label>
                </div>
              </div>
            </TabsContent>
            <TabsContent value="icon" className="py-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="iconSearch">Search Icons</Label>
                  <div className="relative">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-500" />
                    <Input
                      id="iconSearch"
                      placeholder="Search for icons..."
                      className="pl-8"
                      value={iconSearch}
                      onChange={(e) => setIconSearch(e.target.value)}
                    />
                  </div>
                </div>
                <div className="border rounded-md p-4">
                  <div className="text-sm font-medium mb-2">Selected Icon:</div>
                  <div className="flex items-center space-x-2 p-2 bg-gray-100 dark:bg-gray-800 rounded-md">
                    {renderIcon(formData.icon, 'text-2xl')}
                    <span className="text-sm">{formData.icon}</span>
                  </div>
                </div>
                <div className="h-[200px] overflow-y-auto border rounded-md p-4">
                  <div className="grid grid-cols-6 gap-2">
                    {filteredIcons.map((icon) => (
                      <button
                        key={icon}
                        type="button"
                        onClick={() => handleIconSelect(icon)}
                        className={`p-2 rounded-md flex flex-col items-center justify-center text-xs ${
                          formData.icon === icon
                            ? 'bg-blue-100 dark:bg-blue-900/30 border border-blue-300 dark:border-blue-700'
                            : 'hover:bg-gray-100 dark:hover:bg-gray-800'
                        }`}
                      >
                        {renderIcon(icon)}
                        <span className="mt-1 truncate w-full text-center">{icon.replace('Fa', '')}</span>
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </TabsContent>
            <TabsContent value="appearance" className="py-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Color Preset</Label>
                  <div className="grid grid-cols-3 gap-2">
                    {colorPresets.map((preset) => (
                      <button
                        key={preset.name}
                        type="button"
                        onClick={() => handleColorPresetSelect(preset.name)}
                        className={`p-2 rounded-md border ${
                          formData.colorPreset === preset.name
                            ? 'border-blue-500 dark:border-blue-400'
                            : 'border-gray-200 dark:border-gray-700'
                        }`}
                      >
                        <div className={`h-12 rounded-md ${preset.color} ${preset.borderColor} border flex items-center justify-center`}>
                          {renderIcon(formData.icon)}
                        </div>
                        <div className="text-xs mt-1 text-center">{preset.name}</div>
                      </button>
                    ))}
                  </div>
                </div>
                <div className="border rounded-md p-4">
                  <div className="text-sm font-medium mb-2">Preview:</div>
                  <div className={`p-4 rounded-lg border ${formData.borderColor} ${formData.color} transition-colors duration-300 flex flex-col`}>
                    <div className="mb-2">{renderIcon(formData.icon, 'text-2xl')}</div>
                    <h3 className="text-lg font-semibold mb-1 text-gray-900 dark:text-white">
                      {formData.title || 'Quick Link Title'}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      {formData.description || 'Quick link description goes here'}
                    </p>
                    <div className="mt-2 pt-2">
                      <span className="text-blue-600 dark:text-blue-400 text-sm font-medium">Learn more →</span>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </RecursionSafeDialogContent>

        <RecursionSafeDialogFooter>
          <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
            Cancel
          </Button>
          <Button onClick={handleAddQuickLink} disabled={addQuickLinkMutation.isPending}>
            {addQuickLinkMutation.isPending ? 'Adding...' : 'Add Quick Link'}
          </Button>
        </RecursionSafeDialogFooter>
      </RecursionSafeDialog>

      {/* Edit Quick Link Dialog */}
      <RecursionSafeDialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen} maxWidth="max-w-[700px]">
        <RecursionSafeDialogHeader>
          <RecursionSafeDialogTitle>Edit Quick Link</RecursionSafeDialogTitle>
          <RecursionSafeDialogDescription>
            Update the quick link information. All fields are required.
          </RecursionSafeDialogDescription>
        </RecursionSafeDialogHeader>

        <RecursionSafeDialogContent>
          <Tabs defaultValue="details" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="details">Details</TabsTrigger>
              <TabsTrigger value="icon">Icon</TabsTrigger>
              <TabsTrigger value="appearance">Appearance</TabsTrigger>
            </TabsList>
            <TabsContent value="details" className="py-4">
              <div className="grid gap-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="edit-title">Title</Label>
                    <Input
                      id="edit-title"
                      name="title"
                      value={formData.title}
                      onChange={handleInputChange}
                      placeholder="Enter quick link title"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="edit-url">URL</Label>
                    <Input
                      id="edit-url"
                      name="url"
                      value={formData.url}
                      onChange={handleInputChange}
                      placeholder="Enter URL (e.g., /website/admissions)"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-description">Description</Label>
                  <Input
                    id="edit-description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    placeholder="Enter quick link description"
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="edit-isActive"
                    checked={formData.isActive}
                    onCheckedChange={handleCheckboxChange}
                  />
                  <Label htmlFor="edit-isActive">Active</Label>
                </div>
              </div>
            </TabsContent>
            <TabsContent value="icon" className="py-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-iconSearch">Search Icons</Label>
                  <div className="relative">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-500" />
                    <Input
                      id="edit-iconSearch"
                      placeholder="Search for icons..."
                      className="pl-8"
                      value={iconSearch}
                      onChange={(e) => setIconSearch(e.target.value)}
                    />
                  </div>
                </div>
                <div className="border rounded-md p-4">
                  <div className="text-sm font-medium mb-2">Selected Icon:</div>
                  <div className="flex items-center space-x-2 p-2 bg-gray-100 dark:bg-gray-800 rounded-md">
                    {renderIcon(formData.icon, 'text-2xl')}
                    <span className="text-sm">{formData.icon}</span>
                  </div>
                </div>
                <div className="h-[200px] overflow-y-auto border rounded-md p-4">
                  <div className="grid grid-cols-6 gap-2">
                    {filteredIcons.map((icon) => (
                      <button
                        key={icon}
                        type="button"
                        onClick={() => handleIconSelect(icon)}
                        className={`p-2 rounded-md flex flex-col items-center justify-center text-xs ${
                          formData.icon === icon
                            ? 'bg-blue-100 dark:bg-blue-900/30 border border-blue-300 dark:border-blue-700'
                            : 'hover:bg-gray-100 dark:hover:bg-gray-800'
                        }`}
                      >
                        {renderIcon(icon)}
                        <span className="mt-1 truncate w-full text-center">{icon.replace('Fa', '')}</span>
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </TabsContent>
            <TabsContent value="appearance" className="py-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Color Preset</Label>
                  <div className="grid grid-cols-3 gap-2">
                    {colorPresets.map((preset) => (
                      <button
                        key={preset.name}
                        type="button"
                        onClick={() => handleColorPresetSelect(preset.name)}
                        className={`p-2 rounded-md border ${
                          formData.colorPreset === preset.name
                            ? 'border-blue-500 dark:border-blue-400'
                            : 'border-gray-200 dark:border-gray-700'
                        }`}
                      >
                        <div className={`h-12 rounded-md ${preset.color} ${preset.borderColor} border flex items-center justify-center`}>
                          {renderIcon(formData.icon)}
                        </div>
                        <div className="text-xs mt-1 text-center">{preset.name}</div>
                      </button>
                    ))}
                  </div>
                </div>
                <div className="border rounded-md p-4">
                  <div className="text-sm font-medium mb-2">Preview:</div>
                  <div className={`p-4 rounded-lg border ${formData.borderColor} ${formData.color} transition-colors duration-300 flex flex-col`}>
                    <div className="mb-2">{renderIcon(formData.icon, 'text-2xl')}</div>
                    <h3 className="text-lg font-semibold mb-1 text-gray-900 dark:text-white">
                      {formData.title || 'Quick Link Title'}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      {formData.description || 'Quick link description goes here'}
                    </p>
                    <div className="mt-2 pt-2">
                      <span className="text-blue-600 dark:text-blue-400 text-sm font-medium">Learn more →</span>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </RecursionSafeDialogContent>

        <RecursionSafeDialogFooter>
          <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
            Cancel
          </Button>
          <Button onClick={handleUpdateQuickLink} disabled={updateQuickLinkMutation.isPending}>
            {updateQuickLinkMutation.isPending ? 'Updating...' : 'Update Quick Link'}
          </Button>
        </RecursionSafeDialogFooter>
      </RecursionSafeDialog>

      {/* Delete Confirmation Dialog */}
      <RecursionSafeDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen} maxWidth="max-w-[425px]">
        <RecursionSafeDialogHeader>
          <RecursionSafeDialogTitle>Confirm Deletion</RecursionSafeDialogTitle>
          <RecursionSafeDialogDescription>
            Are you sure you want to delete this quick link? This action cannot be undone.
          </RecursionSafeDialogDescription>
        </RecursionSafeDialogHeader>

        <RecursionSafeDialogContent>
          <div className="flex items-center space-x-2 text-amber-600 dark:text-amber-400">
            <AlertCircle className="h-5 w-5" />
            <p className="text-sm">This will permanently remove the quick link from the website.</p>
          </div>
        </RecursionSafeDialogContent>

        <RecursionSafeDialogFooter>
          <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleDeleteQuickLink}
            disabled={deleteQuickLinkMutation.isPending}
          >
            {deleteQuickLinkMutation.isPending ? 'Deleting...' : 'Delete Quick Link'}
          </Button>
        </RecursionSafeDialogFooter>
      </RecursionSafeDialog>

      {/* Overlay for mobile menu */}
      {mobileSidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 md:hidden z-40"
          onClick={toggleMobileSidebar}
        ></div>
      )}
    </div>
  )
}

// Export a wrapper component that provides the QueryClient
export default function QuickLinksManagementPage() {
  return (
    <QueryClientProvider client={queryClient}>
      <QuickLinksManagement />
    </QueryClientProvider>
  )
}

'use client'

import React, { useState, useEffect } from 'react'
import dynamic from 'next/dynamic'
import Sidebar from '../../components/Sidebar'
import { Card } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Textarea } from '../../components/ui/textarea'
import { Label } from '../../components/ui/label'
import { Checkbox } from '../../components/ui/checkbox'
import {
  Table, TableBody, TableCaption, TableCell,
  TableHead, TableHeader, TableRow
} from '../../components/ui/table'
import {
  RecursionSafeDialog,
  RecursionSafeDialogContent,
  RecursionSafeDialogHeader,
  RecursionSafeDialogTitle,
  RecursionSafeDialogDescription,
  RecursionSafeDialogFooter
} from '../../components/RecursionSafeDialog'
import {
  DropdownMenu, DropdownMenuContent, DropdownMenuItem,
  DropdownMenuTrigger
} from '../../components/ui/dropdown-menu'
import {
  Globe, Plus, MoreHorizontal, Pencil, Trash2,
  Eye, EyeOff, AlertCircle, MousePointer
} from 'lucide-react'
import { useQuery, useMutation, QueryClient, QueryClientProvider } from '@tanstack/react-query'
import Link from 'next/link'
import { useToast } from '../../components/ui/use-toast'
import { Toaster } from '../../components/ui/toaster'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs'

// Create a client with default options
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 60000,
      refetchOnWindowFocus: false,
    },
  },
})

// Import Header component with no SSR to avoid hydration issues
const Header = dynamic(() => import('../../components/Header').then(mod => mod.default), {
  ssr: false
})

// Type definition for call to action
interface CallToAction {
  id: string;
  title: string;
  description: string;
  primaryBtnText: string;
  primaryBtnLink: string;
  secondaryBtnText: string | null;
  secondaryBtnLink: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

function CallToActionManagement() {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false)
  const [isMounted, setIsMounted] = useState(false)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [currentCallToAction, setCurrentCallToAction] = useState<CallToAction | null>(null)
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    primaryBtnText: '',
    primaryBtnLink: '',
    secondaryBtnText: '',
    secondaryBtnLink: '',
    isActive: true
  })
  const { toast } = useToast()

  // Fetch call to action items from the API
  const { data: callToActions, isLoading, refetch } = useQuery<CallToAction[]>({
    queryKey: ['callToActions'],
    queryFn: async () => {
      try {
        const res = await fetch('/api/website-management/call-to-action')
        if (!res.ok) {
          throw new Error('Failed to fetch call to action items')
        }
        return res.json()
      } catch (error) {
        // If API doesn't exist yet, return mock data
        return [
          {
            id: '1',
            title: 'Apply for Admission',
            description: 'Join our school community and experience quality education with Islamic values. Applications for the new academic year are now open.',
            primaryBtnText: 'Apply Now',
            primaryBtnLink: '/dashboard',
            secondaryBtnText: 'Contact Us',
            secondaryBtnLink: '/website/contact',
            isActive: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }
        ]
      }
    },
    staleTime: 60000, // 1 minute
    refetchOnWindowFocus: false,
  })

  // Add call to action mutation
  const addCallToActionMutation = useMutation({
    mutationFn: async (newCallToAction: Omit<CallToAction, 'id' | 'createdAt' | 'updatedAt'>) => {
      const res = await fetch('/api/website-management/call-to-action', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newCallToAction),
      })

      if (!res.ok) {
        const error = await res.json()
        throw new Error(error.message || 'Failed to add call to action')
      }

      return res.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['callToActions'] })
      queryClient.invalidateQueries({ queryKey: ['websiteStats'] })
      setIsAddDialogOpen(false)
      resetForm()
      toast({
        title: 'Success',
        description: 'Call to action added successfully',
        variant: 'default',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to add call to action',
        variant: 'destructive',
      })
    }
  })

  // Update call to action mutation
  const updateCallToActionMutation = useMutation({
    mutationFn: async (updatedCallToAction: Partial<CallToAction> & { id: string }) => {
      const res = await fetch(`/api/website-management/call-to-action/${updatedCallToAction.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedCallToAction),
      })

      if (!res.ok) {
        const error = await res.json()
        throw new Error(error.message || 'Failed to update call to action')
      }

      return res.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['callToActions'] })
      setIsEditDialogOpen(false)
      resetForm()
      toast({
        title: 'Success',
        description: 'Call to action updated successfully',
        variant: 'default',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update call to action',
        variant: 'destructive',
      })
    }
  })

  // Delete call to action mutation
  const deleteCallToActionMutation = useMutation({
    mutationFn: async (id: string) => {
      const res = await fetch(`/api/website-management/call-to-action/${id}`, {
        method: 'DELETE',
      })

      if (!res.ok) {
        const error = await res.json()
        throw new Error(error.message || 'Failed to delete call to action')
      }

      return res.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['callToActions'] })
      queryClient.invalidateQueries({ queryKey: ['websiteStats'] })
      setIsDeleteDialogOpen(false)
      toast({
        title: 'Success',
        description: 'Call to action deleted successfully',
        variant: 'default',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete call to action',
        variant: 'destructive',
      })
    }
  })

  useEffect(() => {
    setIsMounted(true)
  }, [])

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  const toggleMobileSidebar = () => {
    setMobileSidebarOpen(!mobileSidebarOpen)
  }

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      primaryBtnText: '',
      primaryBtnLink: '',
      secondaryBtnText: '',
      secondaryBtnLink: '',
      isActive: true
    })
    setCurrentCallToAction(null)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleCheckboxChange = (checked: boolean) => {
    setFormData(prev => ({ ...prev, isActive: checked }))
  }

  const handleAddCallToAction = () => {
    addCallToActionMutation.mutate({
      title: formData.title,
      description: formData.description,
      primaryBtnText: formData.primaryBtnText,
      primaryBtnLink: formData.primaryBtnLink,
      secondaryBtnText: formData.secondaryBtnText || null,
      secondaryBtnLink: formData.secondaryBtnLink || null,
      isActive: formData.isActive
    })
  }

  const handleUpdateCallToAction = () => {
    if (!currentCallToAction) return

    updateCallToActionMutation.mutate({
      id: currentCallToAction.id,
      title: formData.title,
      description: formData.description,
      primaryBtnText: formData.primaryBtnText,
      primaryBtnLink: formData.primaryBtnLink,
      secondaryBtnText: formData.secondaryBtnText || null,
      secondaryBtnLink: formData.secondaryBtnLink || null,
      isActive: formData.isActive
    })
  }

  const handleDeleteCallToAction = () => {
    if (!currentCallToAction) return

    deleteCallToActionMutation.mutate(currentCallToAction.id)
  }

  const openEditDialog = (callToAction: CallToAction) => {
    setCurrentCallToAction(callToAction)
    setFormData({
      title: callToAction.title,
      description: callToAction.description,
      primaryBtnText: callToAction.primaryBtnText,
      primaryBtnLink: callToAction.primaryBtnLink,
      secondaryBtnText: callToAction.secondaryBtnText || '',
      secondaryBtnLink: callToAction.secondaryBtnLink || '',
      isActive: callToAction.isActive
    })
    setIsEditDialogOpen(true)
  }

  const openDeleteDialog = (callToAction: CallToAction) => {
    setCurrentCallToAction(callToAction)
    setIsDeleteDialogOpen(true)
  }

  // Only show loading while mounting
  if (!isMounted || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-600"></div>
      </div>
    )
  }

  return (
    <div className="flex h-screen bg-gray-100 dark:bg-gray-900">
      <Sidebar
        isCollapsed={sidebarCollapsed}
        toggleSidebar={toggleSidebar}
        isMobileOpen={mobileSidebarOpen}
      />

      <main className={`main-content ${sidebarCollapsed ? 'sidebar-collapsed' : ''} w-full h-screen overflow-y-auto`}>
        <Header toggleMobileSidebar={toggleMobileSidebar} />
        <Toaster />

        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-2xl font-semibold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent inline-block">
                Call to Action Management
              </h1>
              <p className="text-gray-600 dark:text-gray-300 mt-1">
                Manage the call-to-action sections displayed on the website
              </p>
            </div>
            <div className="flex space-x-2">
              <Link href="/website" target="_blank">
                <Button variant="outline" size="sm" className="flex items-center">
                  <Globe className="h-4 w-4 mr-1" />
                  <span>View Website</span>
                </Button>
              </Link>
              <Button onClick={() => setIsAddDialogOpen(true)} className="flex items-center">
                <Plus className="h-4 w-4 mr-1" />
                <span>Add Call to Action</span>
              </Button>
            </div>
          </div>

          <Card className="overflow-hidden border-0 shadow-md">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Title</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead className="w-[150px]">Primary Button</TableHead>
                  <TableHead className="w-[150px]">Secondary Button</TableHead>
                  <TableHead className="w-[80px]">Status</TableHead>
                  <TableHead className="w-[100px] text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {callToActions && callToActions.length > 0 ? (
                  callToActions.map((cta) => (
                    <TableRow key={cta.id}>
                      <TableCell className="font-medium">{cta.title}</TableCell>
                      <TableCell className="max-w-[300px] truncate">{cta.description}</TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span className="text-sm font-medium">{cta.primaryBtnText}</span>
                          <span className="text-xs text-gray-500 truncate">{cta.primaryBtnLink}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {cta.secondaryBtnText ? (
                          <div className="flex flex-col">
                            <span className="text-sm font-medium">{cta.secondaryBtnText}</span>
                            <span className="text-xs text-gray-500 truncate">{cta.secondaryBtnLink}</span>
                          </div>
                        ) : (
                          <span className="text-xs text-gray-500">None</span>
                        )}
                      </TableCell>
                      <TableCell>
                        {cta.isActive ? (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                            Active
                          </span>
                        ) : (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                            Inactive
                          </span>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">Open menu</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => openEditDialog(cta)}>
                              <Pencil className="h-4 w-4 mr-2" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => openDeleteDialog(cta)}>
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => updateCallToActionMutation.mutate({ id: cta.id, isActive: !cta.isActive })}>
                              {cta.isActive ? (
                                <>
                                  <EyeOff className="h-4 w-4 mr-2" />
                                  Deactivate
                                </>
                              ) : (
                                <>
                                  <Eye className="h-4 w-4 mr-2" />
                                  Activate
                                </>
                              )}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8">
                      <div className="flex flex-col items-center justify-center text-gray-500 dark:text-gray-400">
                        <MousePointer className="h-12 w-12 mb-2 opacity-50" />
                        <p>No call to action items found</p>
                        <Button
                          variant="link"
                          onClick={() => setIsAddDialogOpen(true)}
                          className="mt-2"
                        >
                          Add your first call to action
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </Card>
        </div>
      </main>

      {/* Add Call to Action Dialog */}
      <RecursionSafeDialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen} maxWidth="max-w-[600px]">
        <RecursionSafeDialogHeader>
          <RecursionSafeDialogTitle>Add New Call to Action</RecursionSafeDialogTitle>
          <RecursionSafeDialogDescription>
            Create a new call-to-action section for the website. Title, description, and primary button fields are required.
          </RecursionSafeDialogDescription>
        </RecursionSafeDialogHeader>
        <RecursionSafeDialogContent>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                placeholder="Enter title"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Enter description"
                rows={3}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="primaryBtnText">Primary Button Text</Label>
                <Input
                  id="primaryBtnText"
                  name="primaryBtnText"
                  value={formData.primaryBtnText}
                  onChange={handleInputChange}
                  placeholder="E.g., Apply Now"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="primaryBtnLink">Primary Button Link</Label>
                <Input
                  id="primaryBtnLink"
                  name="primaryBtnLink"
                  value={formData.primaryBtnLink}
                  onChange={handleInputChange}
                  placeholder="E.g., /dashboard"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="secondaryBtnText">Secondary Button Text (Optional)</Label>
                <Input
                  id="secondaryBtnText"
                  name="secondaryBtnText"
                  value={formData.secondaryBtnText}
                  onChange={handleInputChange}
                  placeholder="E.g., Learn More"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="secondaryBtnLink">Secondary Button Link (Optional)</Label>
                <Input
                  id="secondaryBtnLink"
                  name="secondaryBtnLink"
                  value={formData.secondaryBtnLink}
                  onChange={handleInputChange}
                  placeholder="E.g., /about"
                />
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="isActive"
                checked={formData.isActive}
                onCheckedChange={handleCheckboxChange}
              />
              <Label htmlFor="isActive">Active</Label>
            </div>
          </div>
        </RecursionSafeDialogContent>
        <RecursionSafeDialogFooter>
          <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleAddCallToAction}
            disabled={addCallToActionMutation.isPending || !formData.title || !formData.description || !formData.primaryBtnText || !formData.primaryBtnLink}
          >
            {addCallToActionMutation.isPending ? 'Adding...' : 'Add Call to Action'}
          </Button>
        </RecursionSafeDialogFooter>
      </RecursionSafeDialog>

      {/* Edit Call to Action Dialog */}
      <RecursionSafeDialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen} maxWidth="max-w-[600px]">
        <RecursionSafeDialogHeader>
          <RecursionSafeDialogTitle>Edit Call to Action</RecursionSafeDialogTitle>
          <RecursionSafeDialogDescription>
            Update the call-to-action information. Title, description, and primary button fields are required.
          </RecursionSafeDialogDescription>
        </RecursionSafeDialogHeader>
        <RecursionSafeDialogContent>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="edit-title">Title</Label>
              <Input
                id="edit-title"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                placeholder="Enter title"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-description">Description</Label>
              <Textarea
                id="edit-description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Enter description"
                rows={3}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-primaryBtnText">Primary Button Text</Label>
                <Input
                  id="edit-primaryBtnText"
                  name="primaryBtnText"
                  value={formData.primaryBtnText}
                  onChange={handleInputChange}
                  placeholder="E.g., Apply Now"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-primaryBtnLink">Primary Button Link</Label>
                <Input
                  id="edit-primaryBtnLink"
                  name="primaryBtnLink"
                  value={formData.primaryBtnLink}
                  onChange={handleInputChange}
                  placeholder="E.g., /dashboard"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-secondaryBtnText">Secondary Button Text (Optional)</Label>
                <Input
                  id="edit-secondaryBtnText"
                  name="secondaryBtnText"
                  value={formData.secondaryBtnText}
                  onChange={handleInputChange}
                  placeholder="E.g., Learn More"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-secondaryBtnLink">Secondary Button Link (Optional)</Label>
                <Input
                  id="edit-secondaryBtnLink"
                  name="secondaryBtnLink"
                  value={formData.secondaryBtnLink}
                  onChange={handleInputChange}
                  placeholder="E.g., /about"
                />
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="edit-isActive"
                checked={formData.isActive}
                onCheckedChange={handleCheckboxChange}
              />
              <Label htmlFor="edit-isActive">Active</Label>
            </div>
          </div>
        </RecursionSafeDialogContent>
        <RecursionSafeDialogFooter>
          <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleUpdateCallToAction}
            disabled={updateCallToActionMutation.isPending || !formData.title || !formData.description || !formData.primaryBtnText || !formData.primaryBtnLink}
          >
            {updateCallToActionMutation.isPending ? 'Updating...' : 'Update Call to Action'}
          </Button>
        </RecursionSafeDialogFooter>
      </RecursionSafeDialog>

      {/* Delete Confirmation Dialog */}
      <RecursionSafeDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen} maxWidth="max-w-[425px]">
        <RecursionSafeDialogHeader>
          <RecursionSafeDialogTitle>Confirm Deletion</RecursionSafeDialogTitle>
          <RecursionSafeDialogDescription>
            Are you sure you want to delete this call to action? This action cannot be undone.
          </RecursionSafeDialogDescription>
        </RecursionSafeDialogHeader>
        <RecursionSafeDialogContent>
          <div className="flex items-center space-x-2 py-4 text-amber-600 dark:text-amber-400">
            <AlertCircle className="h-5 w-5" />
            <p className="text-sm">This will permanently remove the call to action from the website.</p>
          </div>
        </RecursionSafeDialogContent>
        <RecursionSafeDialogFooter>
          <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleDeleteCallToAction}
            disabled={deleteCallToActionMutation.isPending}
          >
            {deleteCallToActionMutation.isPending ? 'Deleting...' : 'Delete Call to Action'}
          </Button>
        </RecursionSafeDialogFooter>
      </RecursionSafeDialog>

      {/* Overlay for mobile menu */}
      {mobileSidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 md:hidden z-40"
          onClick={toggleMobileSidebar}
        ></div>
      )}
    </div>
  )
}

// Export a wrapper component that provides the QueryClient
export default function CallToActionManagementPage() {
  return (
    <QueryClientProvider client={queryClient}>
      <CallToActionManagement />
    </QueryClientProvider>
  )
}

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET() {
  try {
    console.log('Initializing attendance records...');
    
    // Check if we already have attendance records
    const attendanceCount = await prisma.attendance.count();
    
    if (attendanceCount > 0) {
      console.log(`Database already has ${attendanceCount} attendance records`);
      return NextResponse.json({
        status: 'success',
        message: 'Attendance records already initialized',
        count: attendanceCount
      });
    }
    
    // Get students
    const students = await prisma.student.findMany();
    
    if (students.length === 0) {
      return NextResponse.json({
        status: 'error',
        message: 'No students found. Please create students first.'
      }, { status: 400 });
    }
    
    // Create attendance records for today
    const today = new Date().toISOString().split('T')[0];
    const yesterday = new Date(Date.now() - 86400000).toISOString().split('T')[0];
    
    const attendanceRecords = [];
    
    // Create attendance for today
    for (const student of students) {
      // Randomly assign status (80% present, 15% absent, 5% permission)
      const random = Math.random();
      let status = 'present';
      
      if (random > 0.95) {
        status = 'permission';
      } else if (random > 0.8) {
        status = 'absent';
      }
      
      const record = await prisma.attendance.create({
        data: {
          date: today,
          className: student.className,
          studentId: student.id,
          status
        }
      });
      
      attendanceRecords.push(record);
    }
    
    // Create attendance for yesterday
    for (const student of students) {
      // Randomly assign status (80% present, 15% absent, 5% permission)
      const random = Math.random();
      let status = 'present';
      
      if (random > 0.95) {
        status = 'permission';
      } else if (random > 0.8) {
        status = 'absent';
      }
      
      const record = await prisma.attendance.create({
        data: {
          date: yesterday,
          className: student.className,
          studentId: student.id,
          status
        }
      });
      
      attendanceRecords.push(record);
    }
    
    return NextResponse.json({
      status: 'success',
      message: `Created ${attendanceRecords.length} attendance records`,
      dates: [today, yesterday],
      records: attendanceRecords
    });
  } catch (error) {
    console.error('Error initializing attendance records:', error);
    return NextResponse.json({
      status: 'error',
      message: 'Failed to initialize attendance records',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

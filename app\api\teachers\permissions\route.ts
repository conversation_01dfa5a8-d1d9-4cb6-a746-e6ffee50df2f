import { NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { verifyJWT } from '@/lib/jwt'
import prisma from '@/lib/prisma'

// GET to retrieve teacher permissions
export async function GET(request: Request) {
  try {
    // Get query parameters
    const url = new URL(request.url)
    const teacherId = url.searchParams.get('teacherId')
    const classId = url.searchParams.get('classId')

    // Verify authentication
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    }

    try {
      // Verify the token
      const decoded = await verifyJWT(token)

      // Build the query based on provided parameters
      const query: any = {}
      
      if (teacherId) {
        query.teacherId = teacherId
      }
      
      if (classId) {
        query.classId = classId
      }

      // Fetch teacher permissions
      const permissions = await prisma.teacherPermission.findMany({
        where: query,
        orderBy: {
          updatedAt: 'desc'
        }
      })

      // If looking for a specific teacher and class, also include teacher and class details
      if (teacherId && classId) {
        const teacher = await prisma.teacher.findUnique({
          where: { id: teacherId },
          select: { id: true, name: true }
        })

        const classDetails = await prisma.class.findUnique({
          where: { id: classId },
          select: { id: true, name: true }
        })

        return NextResponse.json({
          permissions,
          teacher,
          class: classDetails
        })
      }

      return NextResponse.json({ permissions })
    } catch (jwtError) {
      console.error('JWT verification error:', jwtError)
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      )
    }
  } catch (error) {
    console.error('Error fetching teacher permissions:', error)
    return NextResponse.json(
      { error: 'Failed to fetch teacher permissions' },
      { status: 500 }
    )
  }
}

// POST to create or update teacher permissions
export async function POST(request: Request) {
  try {
    // Verify authentication
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    }

    try {
      // Verify the token
      const decoded = await verifyJWT(token)

      // Only allow admins, supervisors, and super admins to manage permissions
      if (!['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR'].includes(decoded.role)) {
        return NextResponse.json(
          { error: 'Forbidden - Insufficient permissions' },
          { status: 403 }
        )
      }

      // Get the request body
      const data = await request.json()
      const { teacherId, classId, permissions } = data

      if (!teacherId || !classId || !permissions) {
        return NextResponse.json(
          { error: 'Missing required fields: teacherId, classId, or permissions' },
          { status: 400 }
        )
      }

      // Check if the teacher exists
      const teacher = await prisma.teacher.findUnique({
        where: { id: teacherId }
      })

      if (!teacher) {
        return NextResponse.json(
          { error: 'Teacher not found' },
          { status: 404 }
        )
      }

      // Check if the class exists
      const classDetails = await prisma.class.findUnique({
        where: { id: classId }
      })

      if (!classDetails) {
        return NextResponse.json(
          { error: 'Class not found' },
          { status: 404 }
        )
      }

      // Create or update the permission record
      const teacherPermission = await prisma.teacherPermission.upsert({
        where: {
          teacherId_classId: {
            teacherId,
            classId
          }
        },
        update: {
          canViewAttendance: permissions.canViewAttendance,
          canTakeAttendance: permissions.canTakeAttendance,
          canAddMarks: permissions.canAddMarks,
          canEditMarks: permissions.canEditMarks,
          updatedAt: new Date()
        },
        create: {
          teacherId,
          classId,
          canViewAttendance: permissions.canViewAttendance,
          canTakeAttendance: permissions.canTakeAttendance,
          canAddMarks: permissions.canAddMarks,
          canEditMarks: permissions.canEditMarks
        }
      })

      return NextResponse.json({
        message: 'Teacher permissions updated successfully',
        permission: teacherPermission
      })
    } catch (jwtError) {
      console.error('JWT verification error:', jwtError)
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      )
    }
  } catch (error) {
    console.error('Error updating teacher permissions:', error)
    return NextResponse.json(
      { error: 'Failed to update teacher permissions' },
      { status: 500 }
    )
  }
}

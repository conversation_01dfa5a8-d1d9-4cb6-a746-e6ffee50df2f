'use client'

import React, { useState, useEffect } from 'react'
import './account-codes.css'
import Sidebar from '../../components/Sidebar'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'
import { But<PERSON> } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Label } from '../../components/ui/label'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../components/ui/table'
import { Hash, Plus, Pencil, Trash2, Loader2, CheckCircle, AlertCircle, X } from 'lucide-react'
import { useToast } from '../../components/ui/use-toast'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../../components/ui/dialog"
import dynamic from 'next/dynamic'

// Import Header component with no SSR to avoid hydration issues
const Header = dynamic(() => import('../../components/Header'), {
  ssr: false
})

interface AccountCode {
  id: string
  code: string
  description: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export default function AccountCodesPage() {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false)
  const [accountCodes, setAccountCodes] = useState<AccountCode[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isFormVisible, setIsFormVisible] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [selectedAccountCode, setSelectedAccountCode] = useState<AccountCode | null>(null)
  const [formData, setFormData] = useState({
    code: '',
    description: '',
    isActive: true
  })
  const { toast } = useToast()

  // Fetch account codes
  const fetchAccountCodes = async () => {
    setIsLoading(true)
    try {
      console.log('Fetching account codes...')
      const response = await fetch('/api/accounting/account-codes')
      const data = await response.json()

      if (!response.ok) {
        const errorMessage = data.details || data.error || 'Failed to fetch account codes';
        console.error('API error response:', data)
        throw new Error(errorMessage)
      }

      console.log('Fetched account codes:', data)

      // Reset form and selected account code when refreshing the list
      resetForm()
      setIsFormVisible(false)

      setAccountCodes(data)
    } catch (error) {
      console.error('Error fetching account codes:', error)
      toast({
        title: 'Database Error',
        description: error.message || 'Failed to load account codes from database',
        variant: 'destructive',
      })

      // Initialize with empty array
      setAccountCodes([])
    } finally {
      setIsLoading(false)
    }
  }

  // Load account codes on component mount
  useEffect(() => {
    fetchAccountCodes()
  }, [])

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  const toggleMobileSidebar = () => {
    setMobileSidebarOpen(!mobileSidebarOpen)
  }

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target
    setFormData({ ...formData, [id]: value })
  }

  // Handle select changes
  const handleSelectChange = (value: string, field: string) => {
    setFormData({ ...formData, [field]: value === 'true' })
  }

  // Reset form
  const resetForm = () => {
    setFormData({
      code: '',
      description: '',
      isActive: true
    })
    setSelectedAccountCode(null)
  }

  // Show form for adding a new account code
  const showAddForm = () => {
    resetForm()
    setIsFormVisible(true)
  }

  // Show form for editing an account code
  const showEditForm = (accountCode: AccountCode) => {
    // Verify the account code exists in our current list
    const exists = accountCodes.some(ac => ac.id === accountCode.id);

    if (!exists) {
      toast({
        title: 'Error',
        description: 'The account code you are trying to edit no longer exists. The list will be refreshed.',
        variant: 'destructive',
      })
      fetchAccountCodes();
      return;
    }

    setSelectedAccountCode(accountCode)
    setFormData({
      code: accountCode.code,
      description: accountCode.description,
      isActive: accountCode.isActive
    })
    setIsFormVisible(true)
  }

  // Show delete confirmation dialog
  const showDeleteDialog = (accountCode: AccountCode) => {
    // Verify the account code exists in our current list
    const exists = accountCodes.some(ac => ac.id === accountCode.id);

    if (!exists) {
      toast({
        title: 'Error',
        description: 'The account code you are trying to delete no longer exists. The list will be refreshed.',
        variant: 'destructive',
      })
      fetchAccountCodes();
      return;
    }

    setSelectedAccountCode(accountCode)
    setIsDeleteDialogOpen(true)
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validate form
    if (!formData.code || !formData.description) {
      toast({
        title: 'Validation Error',
        description: 'Please fill in all required fields',
        variant: 'destructive',
      })
      return
    }

    setIsSubmitting(true)

    try {
      if (selectedAccountCode) {
        // Update existing account code
        console.log(`Updating account code with ID: ${selectedAccountCode.id}`)

        const response = await fetch(`/api/accounting/account-codes/${selectedAccountCode.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(formData),
        })

        const responseData = await response.json()

        if (!response.ok) {
          const errorMessage = responseData.details || responseData.error || 'Failed to update account code';
          console.error('API error response:', responseData)
          throw new Error(errorMessage)
        }

        // Update the local state with the updated account code
        setAccountCodes(prev =>
          prev.map(ac =>
            ac.id === selectedAccountCode.id ? responseData : ac
          )
        )

        toast({
          title: 'Success',
          description: 'Account code updated successfully',
        })
      } else {
        // Create new account code
        const response = await fetch('/api/accounting/account-codes', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(formData),
        })

        const responseData = await response.json()

        if (!response.ok) {
          const errorMessage = responseData.details || responseData.error || 'Failed to create account code';
          console.error('API error response:', responseData)
          throw new Error(errorMessage)
        }

        // Add the new account code to the local state
        setAccountCodes(prev => [...prev, responseData])

        toast({
          title: 'Success',
          description: 'Account code created successfully',
        })
      }

      // Reset form and hide it
      resetForm()
      setIsFormVisible(false)
    } catch (error) {
      console.error('Error saving account code:', error)

      // If the error is related to account code not found, refresh the list
      if (error.message.includes('not found')) {
        toast({
          title: 'Error',
          description: 'The account code you are trying to edit no longer exists. The list will be refreshed.',
          variant: 'destructive',
        })

        // Refresh the account codes list
        fetchAccountCodes()
      } else {
        toast({
          title: 'Error',
          description: error.message || 'Failed to save account code',
          variant: 'destructive',
        })
      }
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle delete confirmation
  const handleDelete = async () => {
    if (!selectedAccountCode) return

    setIsSubmitting(true)

    try {
      console.log(`Deleting account code with ID: ${selectedAccountCode.id}`)

      const response = await fetch(`/api/accounting/account-codes/${selectedAccountCode.id}`, {
        method: 'DELETE',
      })

      const responseData = await response.json()

      if (!response.ok) {
        const errorMessage = responseData.details || responseData.error || 'Failed to delete account code';
        console.error('API error response:', responseData)
        throw new Error(errorMessage)
      }

      // Remove the account code from the list
      setAccountCodes(prev => prev.filter(ac => ac.id !== selectedAccountCode.id))

      toast({
        title: 'Success',
        description: 'Account code deleted successfully',
      })

      setIsDeleteDialogOpen(false)
      setSelectedAccountCode(null)
    } catch (error) {
      console.error('Error deleting account code:', error)

      // If the error is related to account code not found, refresh the list
      if (error.message.includes('not found')) {
        toast({
          title: 'Error',
          description: 'The account code you are trying to delete no longer exists. The list will be refreshed.',
          variant: 'destructive',
        })

        // Refresh the account codes list
        fetchAccountCodes()

        // Close the dialog
        setIsDeleteDialogOpen(false)
        setSelectedAccountCode(null)
      } else {
        toast({
          title: 'Error',
          description: error.message || 'Failed to delete account code',
          variant: 'destructive',
        })
      }
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="flex h-screen bg-gray-100 dark:bg-gray-900">
      <Sidebar
        isCollapsed={sidebarCollapsed}
        toggleSidebar={toggleSidebar}
        isMobileOpen={mobileSidebarOpen}
      />

      <main className={`main-content ${sidebarCollapsed ? 'sidebar-collapsed' : ''} w-full h-screen overflow-y-auto`}>
        <Header toggleMobileSidebar={toggleMobileSidebar} />

        <div className="container mx-auto px-4 py-6">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold text-gray-800 dark:text-white">
              Account Codes
            </h1>
            <Button
              className="bg-blue-600 hover:bg-blue-700"
              onClick={showAddForm}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add New Account Code
            </Button>
          </div>

          {isFormVisible && (
            <Card className="mb-6">
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="text-lg">
                  {selectedAccountCode ? 'Edit Account Code' : 'Add New Account Code'}
                </CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => setIsFormVisible(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="code">Account Code</Label>
                    <Input
                      id="code"
                      placeholder="e.g., 5504"
                      value={formData.code}
                      onChange={(e) => {
                        const value = e.target.value;
                        const uppercaseValue = value.replace(/[a-z]/g, match => match.toUpperCase());
                        setFormData({ ...formData, code: uppercaseValue });
                      }}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Input
                      id="description"
                      placeholder="Brief description of the account code"
                      value={formData.description}
                      onChange={(e) => {
                        const value = e.target.value;
                        const capitalizedValue = value.charAt(0).toUpperCase() + value.slice(1);
                        setFormData({ ...formData, description: capitalizedValue });
                      }}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="isActive">Status</Label>
                    <Select
                      value={formData.isActive ? 'true' : 'false'}
                      onValueChange={(value) => handleSelectChange(value, 'isActive')}
                    >
                      <SelectTrigger id="isActive">
                        <SelectValue placeholder="Select Status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="true">Active</SelectItem>
                        <SelectItem value="false">Inactive</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="md:col-span-2 flex justify-end space-x-2 mt-4">
                    <Button
                      variant="outline"
                      type="button"
                      onClick={() => setIsFormVisible(false)}
                      disabled={isSubmitting}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        <>
                          <CheckCircle className="h-4 w-4 mr-2" />
                          {selectedAccountCode ? 'Update' : 'Save'} Account Code
                        </>
                      )}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          )}

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Account Codes List</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center h-64">
                  <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
                  <span className="ml-2 text-lg">Loading account codes...</span>
                </div>
              ) : accountCodes.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-64 text-gray-500">
                  <Hash className="h-12 w-12 mb-2 opacity-20" />
                  <p>No account codes found</p>
                  <p className="text-sm">Click "Add New Account Code" to create one</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Account ID</TableHead>
                        <TableHead>Description</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {accountCodes.map((code) => (
                        <TableRow key={code.id}>
                          <TableCell className="font-medium">{code.code}</TableCell>
                          <TableCell>{code.description}</TableCell>
                          <TableCell>
                            <span className={`px-2 py-1 rounded-full text-xs ${code.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                              {code.isActive ? 'Active' : 'Inactive'}
                            </span>
                          </TableCell>
                          <TableCell className="text-right">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0 mr-1"
                              onClick={() => showEditForm(code)}
                            >
                              <Pencil className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0 text-red-500"
                              onClick={() => showDeleteDialog(code)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </main>

      {/* Overlay for mobile menu */}
      {mobileSidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 md:hidden z-40"
          onClick={toggleMobileSidebar}
        ></div>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the account code "{selectedAccountCode?.code}"? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

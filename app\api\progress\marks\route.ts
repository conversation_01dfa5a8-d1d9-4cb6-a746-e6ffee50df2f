import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { cookies } from 'next/headers'
import { verifyJWT } from '@/lib/jwt'

// GET marks progress data
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const className = searchParams.get('className') || 'all'
    const term = searchParams.get('term') || 'First Semester'

    // Verify authentication
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    }

    const decoded = await verifyJWT(token)
    
    // Check if user has permission to view progress
    const allowedRoles = ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR']
    if (!allowedRoles.includes(decoded.role)) {
      return NextResponse.json(
        { error: 'Forbidden - Insufficient permissions' },
        { status: 403 }
      )
    }

    // Get classes to analyze
    const classFilter = className === 'all' ? {} : { name: className }
    const classes = await prisma.class.findMany({
      where: classFilter,
      select: {
        name: true,
        subjects: true,
        _count: {
          select: {
            students: true
          }
        }
      }
    })

    const progressData = []
    let totalCompletedSubjects = 0
    let totalInProgressSubjects = 0
    let totalNotStartedSubjects = 0
    let totalSubjects = 0

    for (const classInfo of classes) {
      const className = classInfo.name
      const totalStudents = classInfo._count.students
      const subjects = classInfo.subjects || []

      const subjectProgress = []

      for (const subjectObj of subjects) {
        // Handle both string and object formats
        const subjectName = typeof subjectObj === 'string' ? subjectObj : subjectObj.name

        // Get marks count for this subject, class, and term
        const marksCount = await prisma.mark.count({
          where: {
            className,
            subject: subjectName,
            term
          }
        })

        // Get latest update time and teacher information
        const latestMark = await prisma.mark.findFirst({
          where: {
            className,
            subject: subjectName,
            term
          },
          orderBy: {
            updatedAt: 'desc'
          },
          select: {
            updatedAt: true,
            createdBy: true
          }
        })

        // Get teacher/encoder information
        let teacherInfo = null
        if (latestMark?.createdBy) {
          teacherInfo = await prisma.user.findUnique({
            where: { id: latestMark.createdBy },
            select: {
              name: true,
              email: true,
              role: true
            }
          })
        }

        // Get all unique teachers who have entered marks for this subject
        const allMarksForSubject = await prisma.mark.findMany({
          where: {
            className,
            subject: subjectName,
            term
          },
          select: {
            createdBy: true
          },
          distinct: ['createdBy']
        })

        const teacherIds = allMarksForSubject.map(mark => mark.createdBy).filter(Boolean)
        const teachers = await prisma.user.findMany({
          where: {
            id: { in: teacherIds }
          },
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        })

        // Calculate completion percentage
        const completionPercentage = totalStudents > 0 ? Math.round((marksCount / totalStudents) * 100) : 0

        // Determine status
        let status: 'not_started' | 'in_progress' | 'completed'
        if (marksCount === 0) {
          status = 'not_started'
          totalNotStartedSubjects++
        } else if (completionPercentage < 80) {
          status = 'in_progress'
          totalInProgressSubjects++
        } else {
          status = 'completed'
          totalCompletedSubjects++
        }

        totalSubjects++

        subjectProgress.push({
          name: subjectName,
          submittedCount: marksCount,
          status,
          completionPercentage,
          lastUpdated: latestMark?.updatedAt || null,
          lastUpdatedBy: teacherInfo,
          teachers: teachers,
          teacherCount: teachers.length
        })
      }

      // Calculate overall progress for this class
      const completedSubjects = subjectProgress.filter(s => s.status === 'completed').length
      const overallProgress = subjects.length > 0 ? Math.round((completedSubjects / subjects.length) * 100) : 0

      progressData.push({
        className,
        totalStudents,
        subjects: subjectProgress,
        overallProgress
      })
    }

    // Calculate overall completion percentage
    const overallCompletion = totalSubjects > 0 ? Math.round((totalCompletedSubjects / totalSubjects) * 100) : 0

    const summary = {
      completedSubjects: totalCompletedSubjects,
      inProgressSubjects: totalInProgressSubjects,
      notStartedSubjects: totalNotStartedSubjects,
      totalSubjects,
      overallCompletion,
      term,
      classFilter: className
    }

    return NextResponse.json({
      success: true,
      data: progressData,
      summary,
      message: 'Mark progress data retrieved successfully'
    })
  } catch (error) {
    console.error('Error fetching mark progress:', error)
    return NextResponse.json(
      { error: 'Failed to fetch mark progress' },
      { status: 500 }
    )
  }
}

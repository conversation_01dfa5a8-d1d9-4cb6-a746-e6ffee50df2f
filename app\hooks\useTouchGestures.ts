"use client"

import { useRef, useCallback, useEffect, useState } from 'react'

interface TouchGestureOptions {
  onSwipeLeft?: () => void
  onSwipeRight?: () => void
  onSwipeUp?: () => void
  onSwipeDown?: () => void
  onLongPress?: () => void
  onPullToRefresh?: () => void
  swipeThreshold?: number
  longPressDelay?: number
  pullThreshold?: number
}

interface TouchState {
  startX: number
  startY: number
  currentX: number
  currentY: number
  startTime: number
  isLongPress: boolean
  isPulling: boolean
}

export function useTouchGestures(options: TouchGestureOptions = {}) {
  const {
    onSwipeLeft,
    onSwipeRight,
    onSwipeUp,
    onSwipeDown,
    onLongPress,
    onPullToRefresh,
    swipeThreshold = 50,
    longPressDelay = 500,
    pullThreshold = 100
  } = options

  const elementRef = useRef<HTMLElement>(null)
  const touchState = useRef<TouchState | null>(null)
  const longPressTimer = useRef<NodeJS.Timeout | null>(null)
  const [isPulling, setIsPulling] = useState(false)
  const [pullDistance, setPullDistance] = useState(0)

  const handleTouchStart = useCallback((e: TouchEvent) => {
    const touch = e.touches[0]
    touchState.current = {
      startX: touch.clientX,
      startY: touch.clientY,
      currentX: touch.clientX,
      currentY: touch.clientY,
      startTime: Date.now(),
      isLongPress: false,
      isPulling: false
    }

    // Start long press timer
    if (onLongPress) {
      longPressTimer.current = setTimeout(() => {
        if (touchState.current) {
          touchState.current.isLongPress = true
          onLongPress()
        }
      }, longPressDelay)
    }
  }, [onLongPress, longPressDelay])

  const handleTouchMove = useCallback((e: TouchEvent) => {
    if (!touchState.current) return

    const touch = e.touches[0]
    touchState.current.currentX = touch.clientX
    touchState.current.currentY = touch.clientY

    const deltaX = touch.clientX - touchState.current.startX
    const deltaY = touch.clientY - touchState.current.startY

    // Clear long press if moved too much
    if (Math.abs(deltaX) > 10 || Math.abs(deltaY) > 10) {
      if (longPressTimer.current) {
        clearTimeout(longPressTimer.current)
        longPressTimer.current = null
      }
    }

    // Handle pull to refresh
    if (onPullToRefresh && deltaY > 0 && touchState.current.startY < 50) {
      touchState.current.isPulling = true
      setIsPulling(true)
      setPullDistance(Math.min(deltaY, pullThreshold * 1.5))
      
      if (deltaY > pullThreshold) {
        e.preventDefault()
      }
    }
  }, [onPullToRefresh, pullThreshold])

  const handleTouchEnd = useCallback((e: TouchEvent) => {
    if (!touchState.current) return

    const deltaX = touchState.current.currentX - touchState.current.startX
    const deltaY = touchState.current.currentY - touchState.current.startY
    const deltaTime = Date.now() - touchState.current.startTime

    // Clear long press timer
    if (longPressTimer.current) {
      clearTimeout(longPressTimer.current)
      longPressTimer.current = null
    }

    // Handle pull to refresh
    if (touchState.current.isPulling) {
      if (pullDistance >= pullThreshold && onPullToRefresh) {
        onPullToRefresh()
      }
      setIsPulling(false)
      setPullDistance(0)
    }

    // Handle swipes (only if not long press and quick enough)
    if (!touchState.current.isLongPress && deltaTime < 300) {
      const absX = Math.abs(deltaX)
      const absY = Math.abs(deltaY)

      if (absX > swipeThreshold && absX > absY) {
        // Horizontal swipe
        if (deltaX > 0 && onSwipeRight) {
          onSwipeRight()
        } else if (deltaX < 0 && onSwipeLeft) {
          onSwipeLeft()
        }
      } else if (absY > swipeThreshold && absY > absX) {
        // Vertical swipe
        if (deltaY > 0 && onSwipeDown) {
          onSwipeDown()
        } else if (deltaY < 0 && onSwipeUp) {
          onSwipeUp()
        }
      }
    }

    touchState.current = null
  }, [onSwipeLeft, onSwipeRight, onSwipeUp, onSwipeDown, onPullToRefresh, swipeThreshold, pullDistance, pullThreshold])

  useEffect(() => {
    const element = elementRef.current
    if (!element) return

    element.addEventListener('touchstart', handleTouchStart, { passive: false })
    element.addEventListener('touchmove', handleTouchMove, { passive: false })
    element.addEventListener('touchend', handleTouchEnd, { passive: true })

    return () => {
      element.removeEventListener('touchstart', handleTouchStart)
      element.removeEventListener('touchmove', handleTouchMove)
      element.removeEventListener('touchend', handleTouchEnd)
      
      if (longPressTimer.current) {
        clearTimeout(longPressTimer.current)
      }
    }
  }, [handleTouchStart, handleTouchMove, handleTouchEnd])

  return {
    elementRef,
    isPulling,
    pullDistance,
    isLongPressing: longPressTimer.current !== null
  }
}

// Enhanced drag-to-scroll hook with momentum
export function useDragScroll() {
  const scrollRef = useRef<HTMLDivElement>(null)
  const [isDragging, setIsDragging] = useState(false)
  const dragState = useRef({
    startX: 0,
    startY: 0,
    scrollLeft: 0,
    scrollTop: 0,
    lastX: 0,
    lastY: 0,
    velocityX: 0,
    velocityY: 0,
    lastTime: 0
  })

  const handleMouseDown = useCallback((e: MouseEvent) => {
    const element = scrollRef.current
    if (!element) return

    setIsDragging(true)
    dragState.current = {
      startX: e.pageX,
      startY: e.pageY,
      scrollLeft: element.scrollLeft,
      scrollTop: element.scrollTop,
      lastX: e.pageX,
      lastY: e.pageY,
      velocityX: 0,
      velocityY: 0,
      lastTime: Date.now()
    }

    e.preventDefault()
  }, [])

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging || !scrollRef.current) return

    const element = scrollRef.current
    const currentTime = Date.now()
    const deltaTime = currentTime - dragState.current.lastTime

    if (deltaTime > 0) {
      dragState.current.velocityX = (e.pageX - dragState.current.lastX) / deltaTime
      dragState.current.velocityY = (e.pageY - dragState.current.lastY) / deltaTime
    }

    const x = e.pageX - dragState.current.startX
    const y = e.pageY - dragState.current.startY

    element.scrollLeft = dragState.current.scrollLeft - x
    element.scrollTop = dragState.current.scrollTop - y

    dragState.current.lastX = e.pageX
    dragState.current.lastY = e.pageY
    dragState.current.lastTime = currentTime

    e.preventDefault()
  }, [isDragging])

  const handleMouseUp = useCallback(() => {
    if (!isDragging) return

    setIsDragging(false)

    // Apply momentum scrolling
    const element = scrollRef.current
    if (!element) return

    const { velocityX, velocityY } = dragState.current
    const friction = 0.95
    const minVelocity = 0.1

    let currentVelocityX = velocityX * 100
    let currentVelocityY = velocityY * 100

    const animate = () => {
      if (Math.abs(currentVelocityX) < minVelocity && Math.abs(currentVelocityY) < minVelocity) {
        return
      }

      element.scrollLeft -= currentVelocityX
      element.scrollTop -= currentVelocityY

      currentVelocityX *= friction
      currentVelocityY *= friction

      requestAnimationFrame(animate)
    }

    if (Math.abs(currentVelocityX) > minVelocity || Math.abs(currentVelocityY) > minVelocity) {
      requestAnimationFrame(animate)
    }
  }, [isDragging])

  useEffect(() => {
    const element = scrollRef.current
    if (!element) return

    element.addEventListener('mousedown', handleMouseDown)
    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)

    return () => {
      element.removeEventListener('mousedown', handleMouseDown)
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }
  }, [handleMouseDown, handleMouseMove, handleMouseUp])

  return {
    scrollRef,
    isDragging
  }
}

import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { formatTeacherName } from '@/app/utils/formatters'
import { cookies } from 'next/headers'
import { verifyJWT } from '@/lib/jwt'

export async function GET() {
  try {
    const teachers = await prisma.teacher.findMany({
      select: {
        id: true,
        name: true,
        fatherName: true,
        gender: true,
        email: true,
        subject: true,
        mobile: true,
      },
    })
    return NextResponse.json(teachers)
  } catch (error) {
    console.error('Error fetching teachers:', error)
    return NextResponse.json(
      { error: 'Failed to fetch teachers' },
      { status: 500 }
    )
  }
}

export async function POST(request: Request) {
  try {
    // Verify authentication and authorization
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    }

    const decoded = await verifyJWT(token)

    // Only allow SUPER_ADMIN, ADMIN, and DATA_ENCODER to add teachers
    if (!['SUPER_ADMIN', 'ADMIN', 'DATA_ENCODER'].includes(decoded.role)) {
      return NextResponse.json(
        { error: 'Forbidden - You do not have permission to add teachers' },
        { status: 403 }
      )
    }

    const data = await request.json()
    const teacher = await prisma.teacher.create({
      data: {
        name: formatTeacherName(data.name),
        fatherName: formatTeacherName(data.fatherName),
        gender: data.gender,
        email: data.email,
        subject: data.subject,
        mobile: data.mobile,
      },
    })
    return NextResponse.json(teacher)
  } catch (error) {
    console.error('Error creating teacher:', error)
    return NextResponse.json(
      { error: 'Failed to create teacher' },
      { status: 500 }
    )
  }
}

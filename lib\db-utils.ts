import { prisma, isUsingMockPrisma } from './prisma';
import { exec } from 'child_process';
import { promisify } from 'util';

const execPromise = promisify(exec);

/**
 * Utility function to check if a table exists in the database
 * @param tableName The name of the table to check
 * @returns Promise<boolean> True if the table exists, false otherwise
 */
export async function checkTableExists(tableName: string): Promise<boolean> {
  try {
    // If using mock client, assume table exists
    if (isUsingMockPrisma()) {
      return true;
    }
    
    // Execute a raw query to check if the table exists
    const result = await prisma.$queryRaw`
      SELECT COUNT(*) as count 
      FROM information_schema.tables 
      WHERE table_schema = DATABASE() 
      AND table_name = ${tableName}
    `;
    
    // Check if the table exists
    const count = Array.isArray(result) ? (result[0] as any).count : 0;
    return count > 0;
  } catch (error) {
    console.error(`Error checking if table ${tableName} exists:`, error);
    return false;
  }
}

/**
 * Utility function to run a Prisma migration for website content models
 * @returns Promise<{ success: boolean, message: string }>
 */
export async function migrateWebsiteContentModels(): Promise<{ success: boolean; message: string }> {
  try {
    // If using mock client, return success
    if (isUsingMockPrisma()) {
      return { 
        success: true, 
        message: 'Using mock client, no migration needed' 
      };
    }
    
    // Check if any website content tables exist
    const heroSlideTableExists = await checkTableExists('hero_slide');
    const announcementTableExists = await checkTableExists('announcement');
    const callToActionTableExists = await checkTableExists('call_to_action');
    
    // If all tables exist, no migration needed
    if (heroSlideTableExists && announcementTableExists && callToActionTableExists) {
      return { 
        success: true, 
        message: 'Website content tables already exist, no migration needed' 
      };
    }
    
    // Run Prisma migration
    try {
      const { stdout } = await execPromise('npx prisma db push --accept-data-loss');
      return { 
        success: true, 
        message: `Migration successful: ${stdout}` 
      };
    } catch (migrationError) {
      return { 
        success: false, 
        message: `Migration failed: ${migrationError instanceof Error ? migrationError.message : 'Unknown error'}` 
      };
    }
  } catch (error) {
    return { 
      success: false, 
      message: `Error during migration: ${error instanceof Error ? error.message : 'Unknown error'}` 
    };
  }
}

/**
 * Utility function to seed initial website content if tables are empty
 */
export async function seedWebsiteContent(): Promise<{ success: boolean; message: string }> {
  try {
    // Check if we're using the mock client
    if (isUsingMockPrisma()) {
      return { 
        success: true, 
        message: 'Using mock client, no seeding needed' 
      };
    }
    
    // Check if hero slides table has data
    const heroSlidesCount = await prisma.heroSlide.count();
    
    // If no hero slides, seed initial data
    if (heroSlidesCount === 0) {
      await prisma.heroSlide.createMany({
        data: [
          {
            title: 'Welcome to Alfalah Islamic School',
            subtitle: 'Nurturing minds, enriching souls, building futures',
            imageUrl: '/images/banner.jpg',
            ctaText: 'Apply Now',
            ctaLink: '/dashboard',
            isActive: true,
            order: 0
          },
          {
            title: 'Academic Excellence',
            subtitle: 'Providing quality education with Islamic values since 1995',
            imageUrl: '/images/banner2.jpg',
            ctaText: 'Learn More',
            ctaLink: '/website/academics',
            isActive: true,
            order: 1
          }
        ]
      });
    }
    
    // Check if call to action table has data
    const callToActionCount = await prisma.callToAction.count();
    
    // If no call to actions, seed initial data
    if (callToActionCount === 0) {
      await prisma.callToAction.create({
        data: {
          title: 'Join Our School Community',
          description: 'Enroll your child for the upcoming academic year and give them the gift of quality education with Islamic values.',
          primaryBtnText: 'Apply Now',
          primaryBtnLink: '/admissions',
          secondaryBtnText: 'Learn More',
          secondaryBtnLink: '/about',
          isActive: true
        }
      });
    }
    
    return { 
      success: true, 
      message: 'Website content seeded successfully' 
    };
  } catch (error) {
    return { 
      success: false, 
      message: `Error seeding website content: ${error instanceof Error ? error.message : 'Unknown error'}` 
    };
  }
}

// Mock implementation of PrismaClient for development
class MockPrismaClient {
  // Mock data storage
  private mockData: { [key: string]: any[] } = {
    classes: [
      { id: 'class-1', name: '1A', totalStudents: 25, totalSubjects: 6, hrTeacherId: 'teacher-1', createdAt: new Date(), updatedAt: new Date() },
      { id: 'class-2', name: '1B', totalStudents: 22, totalSubjects: 6, hrTeacherId: 'teacher-2', createdAt: new Date(), updatedAt: new Date() },
      { id: 'class-3', name: '2A', totalStudents: 24, totalSubjects: 7, hrTeacherId: 'teacher-3', createdAt: new Date(), updatedAt: new Date() },
      { id: 'class-4', name: '2B', totalStudents: 23, totalSubjects: 7, hrTeacherId: null, createdAt: new Date(), updatedAt: new Date() },
    ],
    teacherPermissions: [
      { id: 'perm-1', teacherId: 'teacher-1', classId: 'class-1', canViewAttendance: true, canTakeAttendance: true, canAddMarks: true, canEditMarks: true, createdAt: new Date(), updatedAt: new Date() },
      { id: 'perm-2', teacherId: 'teacher-2', classId: 'class-2', canViewAttendance: true, canTakeAttendance: true, canAddMarks: true, canEditMarks: true, createdAt: new Date(), updatedAt: new Date() },
      { id: 'perm-3', teacherId: 'teacher-3', classId: 'class-3', canViewAttendance: true, canTakeAttendance: true, canAddMarks: true, canEditMarks: true, createdAt: new Date(), updatedAt: new Date() },
    ],
    teachers: [
      { id: 'teacher-1', name: 'John Smith', fatherName: 'David Smith', gender: 'Male', email: '<EMAIL>', subject: 'Mathematics', mobile: '1234567890', createdAt: new Date(), updatedAt: new Date() },
      { id: 'teacher-2', name: 'Sarah Johnson', fatherName: 'Robert Johnson', gender: 'Female', email: '<EMAIL>', subject: 'English', mobile: '2345678901', createdAt: new Date(), updatedAt: new Date() },
      { id: 'teacher-3', name: 'Michael Brown', fatherName: 'James Brown', gender: 'Male', email: '<EMAIL>', subject: 'Science', mobile: '3456789012', createdAt: new Date(), updatedAt: new Date() },
    ],
    students: [
      { id: 'student-1', sid: '1A001', name: 'Alice Cooper', className: '1A', fatherName: 'John Cooper', gfName: 'William Cooper', age: 6, gender: 'Female', createdAt: new Date(), updatedAt: new Date() },
      { id: 'student-2', sid: '1A002', name: 'Bob Wilson', className: '1A', fatherName: 'Thomas Wilson', gfName: 'George Wilson', age: 6, gender: 'Male', createdAt: new Date(), updatedAt: new Date() },
      { id: 'student-3', sid: '1B001', name: 'Charlie Davis', className: '1B', fatherName: 'Richard Davis', gfName: 'Edward Davis', age: 6, gender: 'Male', createdAt: new Date(), updatedAt: new Date() },
      { id: 'student-4', sid: '2A001', name: 'Diana Evans', className: '2A', fatherName: 'Michael Evans', gfName: 'Joseph Evans', age: 7, gender: 'Female', createdAt: new Date(), updatedAt: new Date() },
    ],
    attendance: [],
    subjects: [
      { id: 'subject-1', name: 'Mathematics', classId: 'class-1', createdAt: new Date(), updatedAt: new Date() },
      { id: 'subject-2', name: 'English', classId: 'class-1', createdAt: new Date(), updatedAt: new Date() },
      { id: 'subject-3', name: 'Science', classId: 'class-1', createdAt: new Date(), updatedAt: new Date() },
      { id: 'subject-4', name: 'Arabic', classId: 'class-2', createdAt: new Date(), updatedAt: new Date() },
      { id: 'subject-5', name: 'Quran', classId: 'class-2', createdAt: new Date(), updatedAt: new Date() },
      { id: 'subject-6', name: 'Islamic Education', classId: 'class-3', createdAt: new Date(), updatedAt: new Date() },
      { id: 'subject-7', name: 'Somali', classId: 'class-3', createdAt: new Date(), updatedAt: new Date() },
      { id: 'subject-8', name: 'Amharic', classId: 'class-4', createdAt: new Date(), updatedAt: new Date() },
      { id: 'subject-9', name: 'History', classId: 'class-4', createdAt: new Date(), updatedAt: new Date() },
      { id: 'subject-10', name: 'Geography', classId: 'class-4', createdAt: new Date(), updatedAt: new Date() },
    ],
    marks: [
      { id: 'mark-1', studentId: 'student-1', className: '1A', subject: 'Mathematics', marks: 85, term: 'First Semester', academicYear: '2023-2024', createdAt: new Date(), updatedAt: new Date() },
      { id: 'mark-2', studentId: 'student-1', className: '1A', subject: 'English', marks: 78, term: 'First Semester', academicYear: '2023-2024', createdAt: new Date(), updatedAt: new Date() },
      { id: 'mark-3', studentId: 'student-2', className: '1A', subject: 'Mathematics', marks: 92, term: 'First Semester', academicYear: '2023-2024', createdAt: new Date(), updatedAt: new Date() },
      { id: 'mark-4', studentId: 'student-3', className: '1B', subject: 'Arabic', marks: 88, term: 'First Semester', academicYear: '2023-2024', createdAt: new Date(), updatedAt: new Date() },
      { id: 'mark-5', studentId: 'student-4', className: '2A', subject: 'Islamic Education', marks: 76, term: 'First Semester', academicYear: '2023-2024', createdAt: new Date(), updatedAt: new Date() },
      { id: 'mark-6', studentId: 'student-1', className: '1A', subject: 'Mathematics', marks: 90, term: 'Second Semester', academicYear: '2023-2024', createdAt: new Date(), updatedAt: new Date() },
      { id: 'mark-7', studentId: 'student-2', className: '1A', subject: 'Mathematics', marks: 88, term: 'First Semester', academicYear: '2024-2025', createdAt: new Date(), updatedAt: new Date() },
    ],
    users: [
      { id: 'user-1', email: '<EMAIL>', password: '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', name: 'Admin User', role: 'ADMIN', createdAt: new Date(), updatedAt: new Date(), lastLogin: null, status: 'active' }, // password: password
      { id: 'user-2', email: '<EMAIL>', password: '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', name: 'Teacher User', role: 'TEACHER', createdAt: new Date(), updatedAt: new Date(), lastLogin: null, status: 'active' }, // password: password
      { id: 'user-3', email: '<EMAIL>', password: '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', name: 'Staff User', role: 'STAFF', createdAt: new Date(), updatedAt: new Date(), lastLogin: null, status: 'active' }, // password: password
      { id: 'user-4', email: '<EMAIL>', password: '$2a$10$CwTycUXWue0Thq9StjUM0uJ4uZypmHEcxp0E4llvQUbx/A6qdeIWO', name: 'Super Admin', role: 'SUPER_ADMIN', createdAt: new Date(), updatedAt: new Date(), lastLogin: null, status: 'active' }, // password: admin@123
    ],
    classTeacher: [],
    subjectTeacher: [],
  };

  constructor() {
    console.warn('Using MockPrismaClient - using mock data for development');
  }

  // Mock connection methods
  async $connect() { return Promise.resolve(); }
  async $disconnect() { return Promise.resolve(); }

  // Mock data access methods
  user = this.createMockModel('users');
  class = this.createMockModel('classes');
  teacher = this.createMockModel('teachers');
  student = this.createMockModel('students');
  subject = this.createMockModel('subjects');
  mark = this.createMockModel('marks');
  classTeacher = this.createMockModel('classTeacher');
  subjectTeacher = this.createMockModel('subjectTeacher');
  attendance = this.createMockModel('attendance');
  accountCode = this.createMockModel('accountCodes');
  journalVoucher = this.createMockModel('journalVouchers');
  teacherPermission = this.createMockModel('teacherPermissions');

  // Helper to create mock model methods
  createMockModel(modelName: string) {
    return {
      findMany: async (params?: any) => {
        console.log(`Mock findMany for ${modelName}`, params);
        let data = [...this.mockData[modelName]];

        // Handle where clause (very basic implementation)
        if (params?.where) {
          Object.entries(params.where).forEach(([key, value]) => {
            if (value !== undefined) {
              data = data.filter(item => item[key] === value);
            }
          });
        }

        // Handle include (very basic implementation)
        if (params?.include) {
          data = data.map(item => {
            const result = { ...item };

            Object.entries(params.include).forEach(([relationName, include]) => {
              if (include) {
                if (relationName === 'hrTeacher' && item.hrTeacherId) {
                  result.hrTeacher = this.mockData.teachers.find(t => t.id === item.hrTeacherId);
                } else if (relationName === 'students' && item.name) { // For classes
                  result.students = this.mockData.students.filter(s => s.className === item.name);
                } else if (relationName === 'class' && item.className) { // For students
                  result.class = this.mockData.classes.find(c => c.name === item.className);
                } else if (relationName === 'student' && item.studentId) { // For attendance
                  result.student = this.mockData.students.find(s => s.id === item.studentId);
                }
              }
            });

            return result;
          });
        }

        // Handle orderBy (very basic implementation)
        if (params?.orderBy) {
          const [field, direction] = Object.entries(params.orderBy)[0];
          data.sort((a, b) => {
            if (a[field] < b[field]) return direction === 'asc' ? -1 : 1;
            if (a[field] > b[field]) return direction === 'asc' ? 1 : -1;
            return 0;
          });
        }

        return data;
      },
      findUnique: async (params?: any) => {
        console.log(`Mock findUnique for ${modelName}`, params);
        if (params?.where) {
          // Handle different where conditions
          if (params.where.id) {
            return this.mockData[modelName].find(item => item.id === params.where.id) || null;
          }
          if (params.where.email) {
            return this.mockData[modelName].find(item => item.email === params.where.email) || null;
          }
          // Handle other where conditions
          const whereKeys = Object.keys(params.where);
          if (whereKeys.length > 0) {
            return this.mockData[modelName].find(item => {
              return whereKeys.every(key => item[key] === params.where[key]);
            }) || null;
          }
        }
        return null;
      },
      findFirst: async (params?: any) => {
        console.log(`Mock findFirst for ${modelName}`, params);
        let data = this.mockData[modelName];

        // Handle where clause (very basic implementation)
        if (params?.where) {
          Object.entries(params.where).forEach(([key, value]) => {
            if (value !== undefined) {
              data = data.filter(item => item[key] === value);
            }
          });
        }

        return data[0] || null;
      },
      create: async (params: any) => {
        console.log(`Mock create for ${modelName}`, params);
        const newItem = {
          id: `mock-${Date.now()}`,
          ...params.data,
          createdAt: new Date(),
          updatedAt: new Date()
        };
        this.mockData[modelName].push(newItem);
        return newItem;
      },
      createMany: async (params: any) => {
        console.log(`Mock createMany for ${modelName}`, params);
        const count = params.data.length;
        params.data.forEach((item: any) => {
          this.mockData[modelName].push({
            id: `mock-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
            ...item,
            createdAt: new Date(),
            updatedAt: new Date()
          });
        });
        return { count };
      },
      update: async (params: any) => {
        console.log(`Mock update for ${modelName}`, params);
        const index = this.mockData[modelName].findIndex(item => item.id === params.where.id);
        if (index !== -1) {
          this.mockData[modelName][index] = {
            ...this.mockData[modelName][index],
            ...params.data,
            updatedAt: new Date()
          };
          return this.mockData[modelName][index];
        }
        return { id: 'mock-id', ...params.data };
      },
      updateMany: async (params: any) => {
        console.log(`Mock updateMany for ${modelName}`, params);
        let count = 0;
        this.mockData[modelName].forEach((item, index) => {
          let match = true;
          if (params.where) {
            Object.entries(params.where).forEach(([key, value]) => {
              if (item[key] !== value) match = false;
            });
          }
          if (match) {
            this.mockData[modelName][index] = {
              ...item,
              ...params.data,
              updatedAt: new Date()
            };
            count++;
          }
        });
        return { count };
      },
      delete: async (params: any) => {
        console.log(`Mock delete for ${modelName}`, params);
        const index = this.mockData[modelName].findIndex(item => item.id === params.where.id);
        if (index !== -1) {
          const deleted = this.mockData[modelName][index];
          this.mockData[modelName].splice(index, 1);
          return deleted;
        }
        return {};
      },
      deleteMany: async (params: any) => {
        console.log(`Mock deleteMany for ${modelName}`, params);
        let count = 0;
        const newData = this.mockData[modelName].filter(item => {
          let match = true;
          if (params?.where) {
            Object.entries(params.where).forEach(([key, value]) => {
              if (item[key] !== value) match = false;
            });
          }
          if (match) count++;
          return !match;
        });
        this.mockData[modelName] = newData;
        return { count };
      },
      count: async (params?: any) => {
        console.log(`Mock count for ${modelName}`, params);
        if (params?.where) {
          let count = 0;
          this.mockData[modelName].forEach(item => {
            let match = true;
            Object.entries(params.where).forEach(([key, value]) => {
              if (item[key] !== value) match = false;
            });
            if (match) count++;
          });
          return count;
        }
        return this.mockData[modelName].length;
      },
      aggregate: async (params?: any) => {
        console.log(`Mock aggregate for ${modelName}`, params);

        // Handle mark aggregation for performance calculation
        if (modelName === 'marks' && params?._avg?.marks) {
          const marks = this.mockData[modelName];
          if (marks.length === 0) return { _avg: { marks: null } };

          const sum = marks.reduce((acc, curr) => acc + (curr.marks || 0), 0);
          const avg = sum / marks.length;

          return { _avg: { marks: avg } };
        }

        return { _avg: { marks: null } };
      },
    };
  }
}

// Import PrismaClient
import { PrismaClient } from '@prisma/client';

// Define global type
const globalForPrisma = global as unknown as { prisma: PrismaClient };

// Initialize prisma client with singleton pattern
let prisma: PrismaClient;

// Get connection pool settings from environment variables or use defaults
const poolLimit = parseInt(process.env.POOL_CONNECTIONS_LIMIT || '5', 10);
const poolTimeout = parseInt(process.env.POOL_CONNECTIONS_TIMEOUT || '30000', 10);

// Debug: Log the DATABASE_URL (with password masked)
const dbUrl = process.env.DATABASE_URL || '';
const maskedDbUrl = dbUrl.replace(/:([^@]*)@/, ':****@');
console.log('Database URL:', maskedDbUrl);

// Force use of real PrismaClient - no fallback to mock
if (process.env.NODE_ENV === 'production') {
  prisma = new PrismaClient({
    log: ['error', 'warn'],
    datasources: {
      db: {
        url: process.env.DATABASE_URL
      }
    }
  });
  console.log('Production environment: Using new PrismaClient instance');
} else {
  // In development, reuse the same connection
  if (!globalForPrisma.prisma) {
    globalForPrisma.prisma = new PrismaClient({
      log: ['query', 'error', 'warn'],
      datasources: {
        db: {
          url: process.env.DATABASE_URL
        }
      }
    });
    console.log(`Development environment: Created new PrismaClient instance`);
  } else {
    console.log('Development environment: Reusing existing PrismaClient instance');
  }
  prisma = globalForPrisma.prisma;
}

// Variable to track if we're using the mock client
let usingMockPrisma = false;

// Function to check if we're using the mock client
export function isUsingMockPrisma(): boolean {
  return usingMockPrisma;
}

// Function to check database connection
export async function checkDatabaseConnection(): Promise<{ connected: boolean; error?: string }> {
  try {
    // If we're already using the mock client, return disconnected
    if (usingMockPrisma) {
      return { connected: false, error: 'Using mock client' };
    }

    // Try to execute a simple query to check connection
    await prisma.$queryRaw`SELECT 1`;
    return { connected: true };
  } catch (error) {
    console.error('Database connection check failed:', error);
    return {
      connected: false,
      error: error instanceof Error ? error.message : 'Unknown database connection error'
    };
  }
}

// Test the connection
prisma.$connect()
  .then(() => {
    console.log('Successfully connected to the database');
  })
  .catch((error) => {
    console.error('Failed to connect to the database:', error);
    console.error('Database connection is required. Please check your DATABASE_URL and database setup.');
  });

export { prisma };
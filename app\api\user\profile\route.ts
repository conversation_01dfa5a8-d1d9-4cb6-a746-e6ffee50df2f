import { NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { prisma } from '@/lib/prisma'
import { verifyJWT } from '@/lib/jwt'
import { formatUserName } from '@/app/utils/formatters'

export async function PUT(request: Request) {
  try {
    console.log('Profile update request received')

    // Get the token from cookies
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    console.log('Token found:', !!token)

    if (!token) {
      console.error('No token found in cookies')
      return NextResponse.json(
        { error: 'Unauthorized - No token found' },
        { status: 401 }
      )
    }

    try {
      // Verify the token
      const decoded = await verifyJWT(token)
      console.log('Token verified, user ID:', decoded.id)

      // Get the user ID from the token
      const userId = decoded.id

      // Get the request body
      const body = await request.json()
      const { name, email, phone, address, bio, photoUrl, language, emailNotifications, appNotifications, theme } = body
      console.log('Request body:', { name, email, phone, address, bio, photoUrl, language, emailNotifications, appNotifications, theme })

      // Validate the input
      if (!name || !email) {
        console.error('Missing required fields')
        return NextResponse.json(
          { error: 'Name and email are required' },
          { status: 400 }
        )
      }

      // Check if email is already taken by another user
      if (email !== decoded.email) {
        console.log('Email changed, checking if already taken')
        const existingUser = await prisma.user.findUnique({
          where: { email },
        })

        if (existingUser && existingUser.id !== userId) {
          console.error('Email already taken by another user')
          return NextResponse.json(
            { error: 'Email is already taken' },
            { status: 400 }
          )
        }
      }

      // Check if user exists
      const userExists = await prisma.user.findUnique({
        where: { id: userId },
      })

      if (!userExists) {
        console.error('User not found:', userId)
        return NextResponse.json(
          { error: 'User not found' },
          { status: 404 }
        )
      }

      // Update the user with formatted name
      const formattedName = formatUserName(name)
      console.log('Updating user with formatted name:', formattedName)

      // Prepare update data with optional fields
      const updateData: any = {
        name: formattedName,
        email,
      }

      // Add optional fields if provided
      if (phone !== undefined) updateData.phone = phone
      if (address !== undefined) updateData.address = address
      if (bio !== undefined) updateData.bio = bio
      if (photoUrl !== undefined) updateData.photoUrl = photoUrl
      if (language !== undefined) updateData.language = language
      if (emailNotifications !== undefined) updateData.emailNotifications = emailNotifications
      if (appNotifications !== undefined) updateData.appNotifications = appNotifications
      if (theme !== undefined) updateData.theme = theme

      console.log('Update data:', updateData)

      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: updateData,
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          phone: true,
          address: true,
          bio: true,
          photoUrl: true,
          status: true,
          lastLogin: true,
          language: true,
          emailNotifications: true,
          appNotifications: true,
          theme: true,
        },
      })

      console.log('User updated successfully:', updatedUser)

      return NextResponse.json({
        message: 'Profile updated successfully',
        user: updatedUser,
      })
    } catch (jwtError) {
      console.error('JWT verification error:', jwtError)
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      )
    }
  } catch (error) {
    console.error('Error updating profile:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to update profile' },
      { status: 500 }
    )
  }
}

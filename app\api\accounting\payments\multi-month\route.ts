import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// Helper function to check if a model exists in Prisma
function isModelAvailable(modelName: string): boolean {
  return Object.prototype.hasOwnProperty.call(prisma, modelName);
}

// Helper function to generate a unique invoice number
async function generateInvoiceNumber(): Promise<string> {
  const date = new Date();
  const year = date.getFullYear().toString().slice(-2);
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  
  // Get the count of existing payments to generate a sequential number
  let count = 0;
  
  try {
    if (isModelAvailable('payment')) {
      count = await prisma.payment.count();
    }
  } catch (error) {
    console.error('Error counting payments:', error);
  }
  
  // Generate a sequential number with padding
  const sequentialNumber = (count + 1).toString().padStart(4, '0');
  
  return `INV-${year}-${month}-${sequentialNumber}`;
}

// CREATE a multi-month payment
export async function POST(request: Request) {
  try {
    const data = await request.json()

    // Validate required fields
    if (!data.studentId || !data.feeTypeId || !data.amount || !data.paymentMethod || !data.months || !data.months.length) {
      return NextResponse.json(
        { error: 'Student ID, fee type ID, amount, payment method, and months are required' },
        { status: 400 }
      )
    }

    // Check if Payment model is available
    if (!isModelAvailable('payment')) {
      console.warn('Payment model is not available in Prisma client. Using mock data.');

      // Generate a unique invoice number
      const invoiceNumber = await generateInvoiceNumber();

      // Create a mock payment
      const mockPayment = {
        id: `payment-${Date.now()}`,
        invoiceNumber,
        studentId: data.studentId,
        feeTypeId: data.feeTypeId,
        amount: parseFloat(data.amount),
        paymentDate: data.paymentDate ? new Date(data.paymentDate) : new Date(),
        forMonth: data.months.join(','), // Store all months as a comma-separated string
        paymentMethod: data.paymentMethod,
        transferId: data.transferId || null,
        status: data.status || 'paid',
        notes: data.notes || `Multi-month payment for ${data.months.length} months`,
        createdAt: new Date(),
        updatedAt: new Date(),
        student: {
          id: data.studentId,
          sid: 'MOCK-SID',
          name: 'Mock Student',
          className: 'Mock Class'
        },
        feeType: {
          id: data.feeTypeId,
          name: 'Tuition Fee (Monthly)',
          description: 'Monthly tuition fee',
          amount: parseFloat(data.amount) / data.months.length,
          frequency: 'monthly'
        }
      };

      return NextResponse.json(mockPayment, { status: 201 });
    }

    try {
      // Check if student exists
      const student = await prisma.student.findUnique({
        where: { id: data.studentId },
      })

      if (!student) {
        return NextResponse.json(
          { error: 'Student not found' },
          { status: 404 }
        )
      }

      // Check if fee type exists
      const feeType = await prisma.feeType.findUnique({
        where: { id: data.feeTypeId },
      })

      if (!feeType) {
        return NextResponse.json(
          { error: 'Fee type not found' },
          { status: 404 }
        )
      }

      // Generate a unique invoice number
      const invoiceNumber = await generateInvoiceNumber()

      // Create a single payment for multiple months
      const payment = await prisma.payment.create({
        data: {
          invoiceNumber,
          studentId: data.studentId,
          feeTypeId: data.feeTypeId,
          amount: parseFloat(data.amount),
          paymentDate: data.paymentDate ? new Date(data.paymentDate) : new Date(),
          forMonth: data.months.join(','), // Store all months as a comma-separated string
          paymentMethod: data.paymentMethod,
          transferId: data.transferId || null,
          status: data.status || 'paid',
          notes: data.notes || `Multi-month payment for ${data.months.length} months`,
        },
        include: {
          student: {
            select: {
              id: true,
              sid: true,
              name: true,
              className: true,
            },
          },
          feeType: true,
        },
      })

      return NextResponse.json(payment, { status: 201 })
    } catch (dbError) {
      console.error('Database error creating multi-month payment:', dbError);

      // Generate a mock payment as fallback
      const invoiceNumber = await generateInvoiceNumber();
      const mockPayment = {
        id: `payment-${Date.now()}`,
        invoiceNumber,
        studentId: data.studentId,
        feeTypeId: data.feeTypeId,
        amount: parseFloat(data.amount),
        paymentDate: data.paymentDate ? new Date(data.paymentDate) : new Date(),
        forMonth: data.months.join(','), // Store all months as a comma-separated string
        paymentMethod: data.paymentMethod,
        transferId: data.transferId || null,
        status: data.status || 'paid',
        notes: data.notes || `Multi-month payment for ${data.months.length} months`,
        createdAt: new Date(),
        updatedAt: new Date(),
        student: {
          id: data.studentId,
          sid: 'MOCK-SID',
          name: 'Mock Student',
          className: 'Mock Class'
        },
        feeType: {
          id: data.feeTypeId,
          name: 'Tuition Fee (Monthly)',
          description: 'Monthly tuition fee',
          amount: parseFloat(data.amount) / data.months.length,
          frequency: 'monthly'
        }
      };

      return NextResponse.json(mockPayment, { status: 201 });
    }
  } catch (error) {
    console.error('Error creating multi-month payment:', error)
    return NextResponse.json(
      { error: 'Failed to create multi-month payment' },
      { status: 500 }
    )
  }
}

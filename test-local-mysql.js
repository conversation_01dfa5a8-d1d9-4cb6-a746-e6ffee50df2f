const mysql = require('mysql2/promise');

async function testLocalConnection() {
  console.log('🔌 Testing local MySQL connection...');
  
  const config = {
    host: 'localhost',
    port: 3306,
    user: 'root',
    password: 'isru@123',
    database: 'school_management',
    connectTimeout: 10000,
  };

  try {
    console.log('📋 Connecting to local MySQL...');
    console.log('Config:', {
      ...config,
      password: '****' // Hide password in logs
    });
    
    const connection = await mysql.createConnection(config);
    console.log('✅ Successfully connected to local MySQL database!');
    
    // Test a simple query
    const [result] = await connection.execute('SELECT 1 as test, NOW() as current_time');
    console.log('✅ Test query successful:', result);
    
    // Show database info
    const [dbInfo] = await connection.execute('SELECT DATABASE() as current_db, VERSION() as mysql_version');
    console.log('📊 Database info:', dbInfo[0]);
    
    // List existing tables
    const [tables] = await connection.execute('SHOW TABLES');
    console.log('📋 Existing tables:', tables.length > 0 ? tables.map(t => Object.values(t)[0]) : 'No tables found');
    
    await connection.end();
    console.log('✅ Connection closed successfully');
    return true;
    
  } catch (error) {
    console.error('❌ Connection failed:', error.message);
    console.error('📊 Error details:', {
      code: error.code,
      errno: error.errno,
      sqlState: error.sqlState
    });
    
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 Connection refused - possible causes:');
      console.log('   - MySQL server is not running');
      console.log('   - Wrong port number (default is 3306)');
      console.log('   - MySQL Workbench server not started');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('💡 Access denied - possible causes:');
      console.log('   - Wrong username or password');
      console.log('   - User does not have permission');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.log('💡 Database does not exist');
      console.log('   - Create the database "school_management" in MySQL Workbench');
    }
    return false;
  }
}

// Also test connection without database to check if we can create it
async function testConnectionWithoutDB() {
  console.log('\n🔧 Testing connection without database (to create if needed)...');
  
  const config = {
    host: 'localhost',
    port: 3306,
    user: 'root',
    password: 'isru@123',
    // No database specified
    connectTimeout: 10000,
  };

  try {
    const connection = await mysql.createConnection(config);
    console.log('✅ Connected to MySQL server');
    
    // Check if database exists
    const [databases] = await connection.execute('SHOW DATABASES');
    const dbExists = databases.some(db => db.Database === 'school_management');
    console.log('Database "school_management" exists:', dbExists);
    
    if (!dbExists) {
      console.log('🔧 Creating database "school_management"...');
      await connection.execute('CREATE DATABASE school_management');
      console.log('✅ Database created successfully!');
    }
    
    await connection.end();
    return true;
    
  } catch (error) {
    console.error('❌ Failed to connect to MySQL server:', error.message);
    return false;
  }
}

async function main() {
  const success = await testLocalConnection();
  
  if (!success) {
    console.log('\n🔄 Trying to create database first...');
    await testConnectionWithoutDB();
    
    // Try again after creating database
    console.log('\n🔄 Retrying connection...');
    await testLocalConnection();
  }
}

main().catch(console.error);

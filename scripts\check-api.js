// Script to check API endpoints
const fetch = require('node-fetch');

async function main() {
  try {
    // Check classes API
    console.log('Checking visualizer/classes API...');
    const classesResponse = await fetch('http://localhost:3000/api/visualizer/classes');
    const classes = await classesResponse.json();
    console.log(`Found ${classes.length} classes`);
    
    // Check if 8E is in the response
    const class8E = classes.find(cls => cls.name === '8E');
    console.log('Class 8E in API response:', class8E ? 'Yes' : 'No');
    
    if (classes.length > 0) {
      console.log('Sample classes:');
      classes.slice(0, 5).forEach(cls => {
        console.log(`- ${cls.name} (ID: ${cls.id})`);
      });
    }

    // Check subjects API
    console.log('\nChecking visualizer/subjects API...');
    const subjectsResponse = await fetch('http://localhost:3000/api/visualizer/subjects');
    const subjects = await subjectsResponse.json();
    console.log(`Found ${subjects.length} subjects`);
    
    // Check if Maths (8E) is in the response
    const mathsSubject = subjects.find(subj => subj.name === 'Maths (8E)');
    console.log('Maths (8E) in API response:', mathsSubject ? 'Yes' : 'No');
    
    if (subjects.length > 0) {
      console.log('Sample subjects:');
      subjects.slice(0, 5).forEach(subj => {
        console.log(`- ${subj.name} (ID: ${subj.id})`);
      });
    }

    // Check marks API with visualizer flag
    console.log('\nChecking marks API with visualizer flag...');
    const marksResponse = await fetch('http://localhost:3000/api/marks?visualizer=true');
    const marks = await marksResponse.json();
    console.log(`Found ${marks.length} marks`);
    
    // Check if any marks for 8E or Maths (8E) are in the response
    const class8EMarks = marks.filter(mark => mark.className === '8E');
    const mathsMarks = marks.filter(mark => mark.subject === 'Maths (8E)');
    
    console.log('Marks for Class 8E:', class8EMarks.length);
    console.log('Marks for Maths (8E):', mathsMarks.length);
    
    if (class8EMarks.length > 0) {
      console.log('\nSample marks for Class 8E:');
      class8EMarks.slice(0, 3).forEach(mark => {
        console.log(`- Student: ${mark.studentName}, Subject: ${mark.subject}, Mark: ${mark.mark}`);
      });
    }
    
    if (mathsMarks.length > 0) {
      console.log('\nSample marks for Maths (8E):');
      mathsMarks.slice(0, 3).forEach(mark => {
        console.log(`- Student: ${mark.studentName}, Class: ${mark.className}, Mark: ${mark.mark}`);
      });
    }

  } catch (error) {
    console.error('Error:', error);
  }
}

main();

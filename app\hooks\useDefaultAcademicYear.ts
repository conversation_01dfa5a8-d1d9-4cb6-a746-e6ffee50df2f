import { useAppSettings } from '../contexts/app-settings-context'
import { getCurrentAcademicYear } from '../utils/academic-year'

/**
 * Custom hook to get the default academic year from app settings
 * Falls back to current academic year if not set
 */
export function useDefaultAcademicYear(): string {
  const { settings } = useAppSettings()
  
  // Return the default academic year from settings, or fall back to current year
  return settings.defaultAcademicYear || getCurrentAcademicYear()
}

/**
 * Custom hook to get academic year options and current default
 */
export function useAcademicYearSettings() {
  const { settings, updateSettings } = useAppSettings()
  
  const setDefaultAcademicYear = async (academicYear: string) => {
    await updateSettings({ defaultAcademicYear: academicYear })
  }
  
  return {
    defaultAcademicYear: settings.defaultAcademicYear || getCurrentAcademicYear(),
    setDefaultAcademicYear,
    isLoading: false // Could be extended to track loading state
  }
}

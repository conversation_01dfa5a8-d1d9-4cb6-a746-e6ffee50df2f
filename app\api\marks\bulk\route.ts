import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { cookies } from 'next/headers'
import { verifyJWT } from '@/lib/jwt'

// GET existing marks for bulk entry form
export async function GET(request: Request) {
  try {
    // Verify authentication
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    }

    const decoded = await verifyJWT(token)
    const { searchParams } = new URL(request.url)

    const className = searchParams.get('className')
    const subject = searchParams.get('subject')
    const term = searchParams.get('term')
    const academicYear = searchParams.get('academicYear') || '2023-2024'

    if (!className || !subject || !term) {
      return NextResponse.json(
        { error: 'className, subject, and term are required' },
        { status: 400 }
      )
    }

    console.log(`Fetching existing marks for: ${className}, ${subject}, ${term}, ${academicYear}`)

    // Validate that the class exists
    const classExists = await prisma.class.findFirst({
      where: { name: className }
    })

    if (!classExists) {
      return NextResponse.json(
        { error: `Class '${className}' not found` },
        { status: 404 }
      )
    }

    // Validate that the subject exists for this class
    const subjectExists = await prisma.subject.findFirst({
      where: {
        name: subject,
        classId: classExists.id
      }
    })

    if (!subjectExists) {
      return NextResponse.json(
        { error: `Subject '${subject}' is not assigned to class '${className}'` },
        { status: 404 }
      )
    }

    // Fetch existing marks for the specified criteria
    const existingMarks = await prisma.mark.findMany({
      where: {
        className,
        subject,
        term,
        academicYear
      },
      include: {
        student: {
          select: {
            id: true,
            name: true,
            sid: true,
            className: true
          }
        }
      },
      orderBy: {
        student: {
          sid: 'asc'
        }
      }
    })

    console.log(`Found ${existingMarks.length} existing marks`)

    // Transform to match UI expectations
    const formattedMarks = existingMarks.map(mark => ({
      id: mark.id,
      studentId: mark.studentId,
      studentName: mark.student.name,
      studentSid: mark.student.sid,
      subject: mark.subject,
      marks: mark.marks,
      totalMarks: mark.totalMarks,
      className: mark.className,
      term: mark.term,
      academicYear: mark.academicYear,
      remarks: mark.remarks,
      createdAt: mark.createdAt,
      updatedAt: mark.updatedAt
    }))

    return NextResponse.json(formattedMarks)
  } catch (error) {
    console.error('Error fetching existing marks:', error)
    return NextResponse.json(
      { error: 'Failed to fetch existing marks' },
      { status: 500 }
    )
  }
}

export async function POST(request: Request) {
  try {
    // Verify authentication
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    }

    const decoded = await verifyJWT(token)
    const userId = decoded.id

    const data = await request.json()

    if (!Array.isArray(data) || data.length === 0) {
      return NextResponse.json(
        { error: 'Invalid input: expected an array of marks' },
        { status: 400 }
      )
    }

    // Validate each mark entry
    for (const mark of data) {
      if (!mark.studentId || !mark.subject || (!mark.className && !mark.class) || !mark.term || (mark.marks === undefined && mark.obtainedMarks === undefined)) {
        return NextResponse.json(
          { error: 'Missing required fields in one or more mark entries', details: JSON.stringify(mark) },
          { status: 400 }
        )
      }
    }

    console.log(`Processing ${data.length} mark entries for bulk operation`)

    // Process all marks in a transaction (create or update)
    const processedMarks = await prisma.$transaction(async (tx) => {
      const results = []

      for (const mark of data) {
        // Check if student exists
        const student = await tx.student.findUnique({
          where: { id: mark.studentId },
          select: { id: true, name: true, sid: true }
        })

        if (!student) {
          throw new Error(`Student with ID ${mark.studentId} not found`)
        }

        // Map fields to expected names
        const className = mark.className || mark.class
        const marks = mark.marks !== undefined ? mark.marks : mark.obtainedMarks
        const totalMarks = mark.totalMarks || 100
        const academicYear = mark.academicYear || '2023-2024'
        const remarks = mark.remarks || ''

        console.log(`Processing mark for student ${student.name}: ${marks}/${totalMarks}`)

        // Use upsert to create or update the mark
        const upsertedMark = await tx.mark.upsert({
          where: {
            studentId_subject_className_term_academicYear: {
              studentId: mark.studentId,
              subject: mark.subject,
              className: className,
              term: mark.term,
              academicYear: academicYear
            }
          },
          update: {
            marks: marks,
            totalMarks: totalMarks,
            remarks: remarks,
            updatedAt: new Date(),
            createdBy: userId // Track who last updated
          },
          create: {
            studentId: mark.studentId,
            subject: mark.subject,
            marks: marks,
            totalMarks: totalMarks,
            className: className,
            term: mark.term,
            academicYear: academicYear,
            remarks: remarks,
            createdBy: userId
          }
        })

        // Add formatted mark to results that match UI expectations
        results.push({
          id: upsertedMark.id,
          studentId: upsertedMark.studentId,
          studentName: student.name,
          studentSid: student.sid,
          subject: upsertedMark.subject,
          obtainedMarks: upsertedMark.marks, // Map marks to obtainedMarks for UI
          totalMarks: upsertedMark.totalMarks,
          class: upsertedMark.className, // Map className to class for UI
          term: upsertedMark.term,
          academicYear: upsertedMark.academicYear,
          remarks: upsertedMark.remarks,
          dateRecorded: upsertedMark.createdAt,
          recordedBy: 'Teacher',
          createdAt: upsertedMark.createdAt,
          updatedAt: upsertedMark.updatedAt
        })
      }

      return results
    })

    console.log(`Successfully processed ${processedMarks.length} marks`)
    return NextResponse.json({
      success: true,
      message: `Successfully processed ${processedMarks.length} marks`,
      marks: processedMarks
    })
  } catch (error) {
    console.error('Error creating bulk marks:', error)
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json(
      { error: 'Failed to create bulk marks', details: errorMessage },
      { status: 500 }
    )
  }
}

'use client'

import React, { useState, useEffect } from 'react'
import Sidebar from '../../components/Sidebar'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Label } from '../../components/ui/label'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../components/ui/table'
import { CreditCard, Plus, Pencil, Trash2, Loader2, CheckCircle, AlertCircle, X } from 'lucide-react'
import { useToast } from '../../components/ui/use-toast'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../../components/ui/dialog"
import dynamic from 'next/dynamic'

// Import Header component with no SSR to avoid hydration issues
const Header = dynamic(() => import('../../components/Header'), {
  ssr: false
})

interface FeeType {
  id: string
  name: string
  description: string
  amount: number
  frequency: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export default function FeeTypesPage() {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false)
  const [feeTypes, setFeeTypes] = useState<FeeType[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isFormVisible, setIsFormVisible] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [selectedFeeType, setSelectedFeeType] = useState<FeeType | null>(null)
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    amount: '',
    frequency: '',
    isActive: true
  })
  const { toast } = useToast()

  // Fetch fee types
  const fetchFeeTypes = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/accounting/fee-types')
      if (!response.ok) {
        throw new Error('Failed to fetch fee types')
      }
      const data = await response.json()
      setFeeTypes(data)
    } catch (error) {
      console.error('Error fetching fee types:', error)
      toast({
        title: 'Error',
        description: 'Failed to load fee types',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Load fee types on component mount
  useEffect(() => {
    fetchFeeTypes()
  }, [])

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  const toggleMobileSidebar = () => {
    setMobileSidebarOpen(!mobileSidebarOpen)
  }

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target
    setFormData({ ...formData, [id]: value })
  }

  // Handle select changes
  const handleSelectChange = (value: string, field: string) => {
    setFormData({ ...formData, [field]: value })
  }

  // Reset form
  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      amount: '',
      frequency: '',
      isActive: true
    })
    setSelectedFeeType(null)
  }

  // Show form for adding a new fee type
  const showAddForm = () => {
    resetForm()
    setIsFormVisible(true)
  }

  // Show form for editing a fee type
  const showEditForm = (feeType: FeeType) => {
    setSelectedFeeType(feeType)
    setFormData({
      name: feeType.name,
      description: feeType.description,
      amount: feeType.amount.toString(),
      frequency: feeType.frequency,
      isActive: feeType.isActive
    })
    setIsFormVisible(true)
  }

  // Show delete confirmation dialog
  const showDeleteDialog = (feeType: FeeType) => {
    setSelectedFeeType(feeType)
    setIsDeleteDialogOpen(true)
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validate form
    if (!formData.name || !formData.amount || !formData.frequency) {
      toast({
        title: 'Validation Error',
        description: 'Please fill in all required fields',
        variant: 'destructive',
      })
      return
    }

    setIsSubmitting(true)

    try {
      const url = selectedFeeType
        ? `/api/accounting/fee-types/${selectedFeeType.id}`
        : '/api/accounting/fee-types'

      const method = selectedFeeType ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name,
          description: formData.description,
          amount: parseFloat(formData.amount),
          frequency: formData.frequency,
          isActive: formData.isActive
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to save fee type')
      }

      toast({
        title: 'Success',
        description: selectedFeeType
          ? 'Fee type updated successfully'
          : 'Fee type created successfully',
      })

      // Refresh fee types list
      fetchFeeTypes()

      // Reset form and hide it
      resetForm()
      setIsFormVisible(false)
    } catch (error) {
      console.error('Error saving fee type:', error)
      toast({
        title: 'Error',
        description: error.message || 'Failed to save fee type',
        variant: 'destructive',
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle fee type deletion
  const handleDelete = async () => {
    if (!selectedFeeType) return

    setIsSubmitting(true)

    try {
      const response = await fetch(`/api/accounting/fee-types/${selectedFeeType.id}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete fee type')
      }

      toast({
        title: 'Success',
        description: 'Fee type deleted successfully',
      })

      // Refresh fee types list
      fetchFeeTypes()

      // Close dialog
      setIsDeleteDialogOpen(false)
      setSelectedFeeType(null)
    } catch (error) {
      console.error('Error deleting fee type:', error)
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete fee type',
        variant: 'destructive',
      })
    } finally {
      setIsSubmitting(false)
    }
  }



  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ET', {
      style: 'currency',
      currency: 'ETB',
      minimumFractionDigits: 2
    }).format(amount)
  }

  return (
    <div className="flex h-screen bg-gray-100 dark:bg-gray-900">
      <Sidebar
        isCollapsed={sidebarCollapsed}
        toggleSidebar={toggleSidebar}
        isMobileOpen={mobileSidebarOpen}
      />

      <main className={`main-content ${sidebarCollapsed ? 'sidebar-collapsed' : ''} w-full h-screen overflow-y-auto`}>
        <Header toggleMobileSidebar={toggleMobileSidebar} />

        <div className="container mx-auto px-4 py-6">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold text-gray-800 dark:text-white">
              Fee Types
            </h1>
            <Button
              className="bg-blue-600 hover:bg-blue-700"
              onClick={showAddForm}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add New Fee Type
            </Button>
          </div>

          {isFormVisible && (
            <Card className="mb-6">
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="text-lg">
                  {selectedFeeType ? 'Edit Fee Type' : 'Add New Fee Type'}
                </CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => setIsFormVisible(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Fee Name</Label>
                    <Input
                      id="name"
                      placeholder="e.g., Tuition Fee"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="amount">Amount</Label>
                    <Input
                      id="amount"
                      type="number"
                      placeholder="e.g., 5000"
                      value={formData.amount}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="frequency">Frequency</Label>
                    <Select
                      value={formData.frequency}
                      onValueChange={(value) => handleSelectChange(value, 'frequency')}
                    >
                      <SelectTrigger id="frequency">
                        <SelectValue placeholder="Select Frequency" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="monthly">Monthly</SelectItem>
                        <SelectItem value="quarterly">Quarterly</SelectItem>
                        <SelectItem value="semester">Semester</SelectItem>
                        <SelectItem value="yearly">Yearly</SelectItem>
                        <SelectItem value="one-time">One-time</SelectItem>
                        <SelectItem value="random">Random</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Input
                      id="description"
                      placeholder="Brief description of the fee"
                      value={formData.description}
                      onChange={handleInputChange}
                    />
                  </div>
                  <div className="md:col-span-2 flex justify-end space-x-2 mt-4">
                    <Button
                      variant="outline"
                      type="button"
                      onClick={() => setIsFormVisible(false)}
                      disabled={isSubmitting}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        <>
                          <CheckCircle className="h-4 w-4 mr-2" />
                          {selectedFeeType ? 'Update' : 'Save'} Fee Type
                        </>
                      )}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          )}

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Fee Types List</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center h-64">
                  <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
                  <span className="ml-2 text-lg">Loading fee types...</span>
                </div>
              ) : feeTypes.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-64 text-gray-500">
                  <CreditCard className="h-12 w-12 mb-2 opacity-20" />
                  <p>No fee types found</p>
                  <p className="text-sm">Click "Add New Fee Type" to create one</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Fee Name</TableHead>
                        <TableHead>Amount</TableHead>
                        <TableHead>Frequency</TableHead>
                        <TableHead>Description</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {feeTypes.map((fee) => (
                        <TableRow key={fee.id}>
                          <TableCell className="font-medium">{fee.name}</TableCell>
                          <TableCell>{formatCurrency(fee.amount)}</TableCell>
                          <TableCell className="capitalize">{fee.frequency}</TableCell>
                          <TableCell>{fee.description}</TableCell>
                          <TableCell>
                            <span className={`px-2 py-1 rounded-full text-xs ${fee.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                              {fee.isActive ? 'Active' : 'Inactive'}
                            </span>
                          </TableCell>
                          <TableCell className="text-right">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0 mr-1"
                              onClick={() => showEditForm(fee)}
                            >
                              <Pencil className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0 text-red-500"
                              onClick={() => showDeleteDialog(fee)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </main>

      {/* Overlay for mobile menu */}
      {mobileSidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 md:hidden z-40"
          onClick={toggleMobileSidebar}
        ></div>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the fee type "{selectedFeeType?.name}"? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

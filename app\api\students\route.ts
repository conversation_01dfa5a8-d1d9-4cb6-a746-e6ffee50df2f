import { NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'
import { formatStudentName } from '@/lib/utils'
import { prisma } from '@/lib/prisma'
import { cookies } from 'next/headers'
import { verifyJWT } from '@/lib/jwt'

// Helper function to generate a student SID
async function generateStudentSID(prisma: PrismaClient, className: string): Promise<string> {
  console.log(`Generating SID for class: ${className}`);

  // Get all students in the class, sorted by SID
  const classStudents = await prisma.student.findMany({
    where: { className },
    orderBy: { sid: 'desc' },
    take: 1,
  });

  // If no students exist in this class, start with 1
  if (classStudents.length === 0) {
    console.log(`No students found in class ${className}, starting with SID: ${className}1`);
    return `${className}1`;
  }

  // Get the highest SID
  const highestSid = classStudents[0].sid;
  console.log(`Highest existing SID: ${highestSid}`);

  // Extract the number part
  const match = highestSid.match(/\d+$/);
  if (!match) {
    console.log(`Could not extract number from SID: ${highestSid}, defaulting to 1`);
    return `${className}1`;
  }

  const highestNumber = parseInt(match[0], 10);
  const nextNumber = highestNumber + 1;
  console.log(`Next SID number: ${nextNumber}`);

  // Return the next SID in sequence
  return `${className}${nextNumber}`;
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const className = searchParams.get('class')

    // Filter students by class if provided
    const students = await prisma.student.findMany({
      where: className ? { className } : undefined,
      orderBy: [
        { className: 'asc' },
        { sid: 'asc' }
      ],
      select: {
        id: true,
        sid: true,
        name: true,
        className: true,
        fatherName: true,
        gfName: true,
        age: true,
        gender: true,
        academicYear: true,
        createdAt: true,
        updatedAt: true
        // photoUrl is excluded to avoid database schema issues
      }
    })

    console.log(`Found ${students.length} students${className ? ` in class ${className}` : ''}`)

    return NextResponse.json(students)
  } catch (error) {
    console.error('Failed to fetch students:', error)
    return NextResponse.json(
      { error: 'Failed to fetch students' },
      { status: 500 }
    )
  }
}

export async function POST(req: Request) {
  try {
    // Verify authentication and authorization
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    }

    const decoded = await verifyJWT(token)

    // Only allow SUPER_ADMIN, ADMIN, SUPERVISOR, and DATA_ENCODER to add students
    if (!['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'DATA_ENCODER'].includes(decoded.role)) {
      return NextResponse.json(
        { error: 'Forbidden - You do not have permission to add students' },
        { status: 403 }
      )
    }

    const data = await req.json()
    const { className, ...studentData } = data

    // Find the class and its current students
    const classRecord = await prisma.class.findFirst({
      where: { name: className },
      include: {
        students: {
          orderBy: { sid: 'desc' },
          take: 1,
          where: { className }
        }
      }
    })

    if (!classRecord) {
      return NextResponse.json(
        { error: 'Class not found' },
        { status: 404 }
      )
    }

    // Extract class prefix (e.g., "1A" from "1A1")
    const classPrefix = className.match(/^\d+[A-Z]+/)?.[0]
    if (!classPrefix) {
      return NextResponse.json(
        { error: 'Invalid class name format' },
        { status: 400 }
      )
    }

    // Get the highest student number for this class prefix across all students
    const existingStudents = await prisma.student.findMany({
      where: {
        sid: {
          startsWith: classPrefix
        }
      },
      select: {
        sid: true
      },
      orderBy: {
        sid: 'desc'
      }
    })

    console.log('Existing students with prefix', classPrefix, ':', existingStudents.map(s => s.sid))

    let nextNumber = 1
    if (existingStudents.length > 0) {
      // Extract numbers from all SIDs and find the highest
      const numbers = existingStudents
        .map(student => {
          const numberPart = student.sid.replace(classPrefix, '')
          return parseInt(numberPart)
        })
        .filter(num => !isNaN(num))
        .sort((a, b) => b - a) // Sort descending

      if (numbers.length > 0) {
        nextNumber = numbers[0] + 1
      }
    }

    // Generate new student ID (e.g., "1A1", "1A2", etc.)
    const newSid = `${classPrefix}${nextNumber}`
    console.log('Generated new SID:', newSid)

    // Double-check that this SID doesn't already exist
    const existingSid = await prisma.student.findUnique({
      where: { sid: newSid }
    })

    if (existingSid) {
      console.error('Generated SID already exists:', newSid)
      return NextResponse.json(
        { error: 'Failed to generate unique student ID. Please try again.' },
        { status: 500 }
      )
    }

    // Create the student (exclude photoUrl if it's causing issues)
    const { photoUrl, ...restStudentData } = studentData;
    const student = await prisma.student.create({
      data: {
        ...restStudentData,
        sid: newSid,
        className,
        age: parseInt(restStudentData.age),
        gender: restStudentData.gender.toLowerCase(),
        academicYear: restStudentData.academicYear || '2023-2024'
        // photoUrl is excluded to avoid database schema issues
      },
      select: {
        id: true,
        sid: true,
        name: true,
        className: true,
        fatherName: true,
        gfName: true,
        age: true,
        gender: true,
        academicYear: true,
        createdAt: true,
        updatedAt: true
      }
    })

    return NextResponse.json(student)
  } catch (error) {
    console.error('Failed to create student:', error)

    // Handle specific Prisma errors
    if (error instanceof Error) {
      // Handle unique constraint violation
      if (error.message.includes('Unique constraint failed') && error.message.includes('student_sid_key')) {
        return NextResponse.json(
          { error: 'Student ID already exists. Please try again or contact support.' },
          { status: 409 }
        )
      }

      // Handle other database errors
      if (error.message.includes('Prisma')) {
        return NextResponse.json(
          { error: 'Database error occurred while creating student.' },
          { status: 500 }
        )
      }
    }

    return NextResponse.json(
      { error: 'Failed to create student', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}



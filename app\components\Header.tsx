"use client"

import React, { useState, useEffect, useMemo } from 'react'
import { User, Settings, LogOut } from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu"
import { Button } from './ui/button'
import { ThemeSwitcher } from './ThemeSwitcher'
import { useAuth } from '../contexts/auth-context'
import { useRouter } from 'next/navigation'
import { useAppSettings } from '../contexts/app-settings-context'

interface HeaderProps {
  toggleMobileSidebar: () => void
}

// Wave Animation Component for School Name
const WaveText: React.FC<{ text: string; className?: string }> = ({ text, className = "" }) => {
  const [isVisible, setIsVisible] = useState(true)

  // Memoize the text splitting with proper word spacing
  const textArray = useMemo(() => {
    return text.split('').map((char, index) => ({
      char: char,
      index,
      isSpace: char === ' '
    }))
  }, [text])

  // Reduce motion for accessibility
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
    setIsVisible(!mediaQuery.matches)

    const handleChange = (e: MediaQueryListEvent) => {
      setIsVisible(!e.matches)
    }

    mediaQuery.addEventListener('change', handleChange)
    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [])

  if (!isVisible) {
    return (
      <span className={`font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent ${className}`} style={{ letterSpacing: '0.2em', wordSpacing: '0.8em' }}>
        {text}
      </span>
    )
  }

  return (
    <span className={`font-bold inline-flex ${className}`} role="text" aria-label={text} style={{ letterSpacing: '0.15em' }}>
      {textArray.map(({ char, index, isSpace }) => (
        <span
          key={index}
          className={isSpace ? "wave-space" : "wave-char inline-block"}
          style={{
            animationDelay: `${index * 0.1}s`,
            animationDuration: '3s',
            animationIterationCount: 'infinite',
            animationTimingFunction: 'ease-in-out',
            marginRight: isSpace ? '0.8em' : '0',
            width: isSpace ? '0.8em' : 'auto'
          }}
        >
          {isSpace ? '' : char}
        </span>
      ))}
    </span>
  )
}

const Header: React.FC<HeaderProps> = ({ toggleMobileSidebar }) => {
  const { user, logout, isAuthenticated } = useAuth()
  const router = useRouter()
  const { settings } = useAppSettings()

  const handleLogout = async () => {
    await logout()
    router.push('/login')
  }

  return (
    <header className="sticky top-0 z-50 w-full border-b backdrop-blur" style={{
      borderColor: 'var(--color-border)',
      backgroundColor: 'var(--color-surface)',
      color: 'var(--color-text)'
    }}>
      <div className="flex h-16 items-center justify-between px-4 md:px-6">
        {/* Left side with mobile menu */}
        <div className="flex items-center">
          {/* Mobile menu button */}
          <button
            className="md:hidden p-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 min-h-[44px] min-w-[44px] flex items-center justify-center"
            onClick={toggleMobileSidebar}
            aria-label="Open navigation menu"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6">
              <line x1="4" x2="20" y1="12" y2="12"></line>
              <line x1="4" x2="20" y1="6" y2="6"></line>
              <line x1="4" x2="20" y1="18" y2="18"></line>
            </svg>
          </button>
        </div>

        {/* Left-aligned school name with wave animation */}
        <div className="flex items-center justify-start ml-4 md:ml-6">
          {/* Desktop school name with wave animation */}
          <h1 className="hidden md:block text-3xl whitespace-nowrap">
            <WaveText
              text={settings.schoolName || "Jan School Management System"}
              className="text-3xl"
            />
          </h1>

          {/* Mobile school name with wave animation (shorter) */}
          <h1 className="md:hidden text-xl">
            <WaveText
              text={settings.schoolName && settings.schoolName.length > 15
                ? `${settings.schoolName.substring(0, 15)}...`
                : settings.schoolName || "Jan School"
              }
              className="text-xl"
            />
          </h1>
        </div>

        {/* Right side with user profile and theme toggle */}
        <div className="flex items-center gap-3">
          {/* Theme switcher */}
          <ThemeSwitcher />

          {/* User profile */}
          {isAuthenticated ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <button className="flex items-center gap-2 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200">
                  <div className="h-8 w-8 rounded-full bg-gradient-to-r from-indigo-500 to-purple-500 flex items-center justify-center text-white font-medium">
                    {user?.name?.charAt(0) || 'U'}
                  </div>
                  <span className="hidden md:inline text-sm font-medium text-gray-700 dark:text-gray-300">
                    {user?.name || 'User'}
                  </span>
                </button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56 mt-1 p-2">
                <div className="flex flex-col space-y-1 p-2 border-b border-gray-100 dark:border-gray-800">
                  <p className="text-sm font-medium">{user?.name || 'User'}</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">{user?.email || '<EMAIL>'}</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400 capitalize">{user?.role?.toLowerCase() || 'User'}</p>
                </div>
                <DropdownMenuItem className="cursor-pointer mt-1 focus:bg-indigo-50 dark:focus:bg-gray-700" onClick={() => router.push('/profile')}>
                  <User className="mr-2 h-4 w-4" />
                  <span>Profile Settings</span>
                </DropdownMenuItem>
                <DropdownMenuItem className="cursor-pointer focus:bg-indigo-50 dark:focus:bg-gray-700" onClick={() => router.push('/account')}>
                  <Settings className="mr-2 h-4 w-4" />
                  <span>Account Settings</span>
                </DropdownMenuItem>
                <DropdownMenuItem className="cursor-pointer focus:bg-indigo-50 dark:focus:bg-gray-700" onClick={handleLogout}>
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Logout</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push('/login')}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              Login
            </Button>
          )}
        </div>
      </div>
    </header>
  )
}

export default Header;

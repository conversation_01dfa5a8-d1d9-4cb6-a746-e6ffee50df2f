import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function fixSuperAdminPassword() {
  try {
    console.log('🔧 Fixing Super Admin password...')

    // Hash the correct password "admin@123"
    const correctPassword = 'admin@123'
    const hashedPassword = await bcrypt.hash(correctPassword, 10)
    
    console.log('✅ Password hashed successfully')

    // Update the super admin user
    const updatedUser = await prisma.user.upsert({
      where: {
        email: '<EMAIL>'
      },
      update: {
        password: hashedPassword,
        name: 'Super Admin',
        role: 'SUPER_ADMIN',
        status: 'active'
      },
      create: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Super Admin',
        role: 'SUPER_ADMIN',
        status: 'active'
      }
    })

    console.log('✅ Super Admin user updated:', {
      id: updatedUser.id,
      email: updatedUser.email,
      name: updatedUser.name,
      role: updatedUser.role
    })

    // Test the password
    const testResult = await bcrypt.compare(correctPassword, hashedPassword)
    console.log('✅ Password verification test:', testResult ? 'PASSED' : 'FAILED')

    console.log('🎉 Super Admin password fixed successfully!')
    console.log('📧 Email: <EMAIL>')
    console.log('🔑 Password: admin@123')

  } catch (error) {
    console.error('❌ Error fixing Super Admin password:', error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

fixSuperAdminPassword()

import { NextResponse } from 'next/server'
import { initializeDatabase, checkDatabaseHealth } from '@/lib/database-init'

export async function GET() {
  try {
    console.log('Database health check requested')
    
    const health = await checkDatabaseHealth()
    
    return NextResponse.json({
      status: health.isHealthy ? 'healthy' : 'unhealthy',
      missingTables: health.missingTables,
      errors: health.errors,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('Database health check failed:', error)
    return NextResponse.json(
      { 
        status: 'error',
        error: 'Failed to check database health',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function POST() {
  try {
    console.log('Database initialization requested')
    
    const success = await initializeDatabase()
    
    if (success) {
      return NextResponse.json({
        status: 'success',
        message: 'Database initialized successfully',
        timestamp: new Date().toISOString()
      })
    } else {
      return NextResponse.json(
        {
          status: 'error',
          message: 'Database initialization failed'
        },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Database initialization failed:', error)
    return NextResponse.json(
      { 
        status: 'error',
        error: 'Failed to initialize database',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

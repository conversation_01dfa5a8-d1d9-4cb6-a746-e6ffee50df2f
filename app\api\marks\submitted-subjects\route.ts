import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { cookies } from 'next/headers'
import { verifyJWT } from '@/lib/jwt'

// GET submitted subjects for a class and term
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const className = searchParams.get('className')
    const term = searchParams.get('term')
    const academicYear = searchParams.get('academicYear') || '2023-2024'

    if (!className || !term) {
      return NextResponse.json(
        { error: 'className and term are required' },
        { status: 400 }
      )
    }

    // Verify authentication
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    }

    const decoded = await verifyJWT(token)
    
    // Check if user has permission to view marks
    const allowedRoles = ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'TEACHER']
    if (!allowedRoles.includes(decoded.role)) {
      return NextResponse.json(
        { error: 'Forbidden - Insufficient permissions' },
        { status: 403 }
      )
    }

    // Get all unique subjects that have marks submitted for this class and term
    const submittedSubjects = await prisma.mark.findMany({
      where: {
        className,
        term
      },
      select: {
        subject: true,
      },
      distinct: ['subject']
    })

    // Get all subjects for this class from the class configuration
    const classData = await prisma.class.findUnique({
      where: { name: className },
      select: {
        subjects: true
      }
    })

    const allSubjects = classData?.subjects || []
    const submittedSubjectNames = submittedSubjects.map(s => s.subject.trim())

    // Create a map of subjects with their submission status
    // Use case-insensitive and trimmed comparison
    const subjectStatus = allSubjects.map(subjectObj => {
      // Handle both string and object formats
      const subjectName = typeof subjectObj === 'string' ? subjectObj : subjectObj.name
      const normalizedSubject = subjectName.trim()
      const isSubmitted = submittedSubjectNames.some(submitted =>
        submitted.toLowerCase() === normalizedSubject.toLowerCase()
      )
      return {
        subject: subjectName,
        isSubmitted,
        hasMarks: isSubmitted
      }
    })

    // Also get count of students who have marks for each subject
    const subjectCounts = await Promise.all(
      allSubjects.map(async (subjectObj) => {
        // Handle both string and object formats
        const subjectName = typeof subjectObj === 'string' ? subjectObj : subjectObj.name

        // Try to find marks with exact match first, then case-insensitive
        let count = await prisma.mark.count({
          where: {
            className,
            term,
            subject: subjectName.trim()
          }
        })

        // If no exact match, try case-insensitive search
        if (count === 0) {
          const allMarksForClassTerm = await prisma.mark.findMany({
            where: {
              className,
              term
            },
            select: {
              subject: true
            }
          })

          const matchingSubjects = allMarksForClassTerm.filter(mark =>
            mark.subject.toLowerCase().trim() === subjectName.toLowerCase().trim()
          )
          count = matchingSubjects.length
        }

        // Get total students in class for comparison
        const totalStudents = await prisma.student.count({
          where: { className }
        })

        return {
          subject: subjectName,
          marksCount: count,
          totalStudents,
          isComplete: count > 0 && count >= totalStudents * 0.8, // Consider complete if 80% or more students have marks
          completionPercentage: totalStudents > 0 ? Math.round((count / totalStudents) * 100) : 0
        }
      })
    )

    return NextResponse.json({
      className,
      term,
      academicYear,
      allSubjects,
      submittedSubjects: submittedSubjectNames,
      subjectStatus,
      subjectCounts,
      message: 'Subject submission status retrieved successfully'
    })
  } catch (error) {
    console.error('Error fetching submitted subjects:', error)
    return NextResponse.json(
      { error: 'Failed to fetch submitted subjects' },
      { status: 500 }
    )
  }
}

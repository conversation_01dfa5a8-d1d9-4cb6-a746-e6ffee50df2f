import { NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { verifyJWT } from '@/lib/jwt'

export async function GET() {
  try {
    const cookieStore = cookies()
    // Return all cookies for debugging
    const allCookies = cookieStore.getAll()

    // Check for NextAuth session token
    const nextAuthToken = cookieStore.get('next-auth.session-token')

    if (nextAuthToken) {
      return NextResponse.json({
        authenticated: true,
        message: 'NextAuth session token found',
        authType: 'next-auth',
        cookies: allCookies.map(c => ({ name: c.name, value: c.value ? c.value.substring(0, 10) + '...' : null }))
      })
    }

    // Check for our custom token
    const token = cookieStore.get('token')

    if (!token) {
      return NextResponse.json({
        authenticated: false,
        message: 'No authentication token found',
        cookies: allCookies.map(c => ({ name: c.name, value: c.value ? c.value.substring(0, 10) + '...' : null }))
      })
    }

    try {
      // Verify the token
      const decoded = await verifyJWT(token.value)

      return NextResponse.json({
        authenticated: true,
        message: 'Valid custom token',
        authType: 'custom-jwt',
        user: decoded,
        cookies: allCookies.map(c => ({ name: c.name, value: c.value ? c.value.substring(0, 10) + '...' : null }))
      })
    } catch (error) {
      return NextResponse.json({
        authenticated: false,
        message: 'Invalid token',
        error: error instanceof Error ? error.message : 'Unknown error',
        cookies: allCookies.map(c => ({ name: c.name, value: c.value ? c.value.substring(0, 10) + '...' : null }))
      })
    }
  } catch (error) {
    return NextResponse.json({
      authenticated: false,
      message: 'Error checking authentication',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}

import React from 'react'
import { LucideIcon } from 'lucide-react'

interface CustomSelectProps {
  label: string
  icon: LucideIcon
  value: string | string[]
  options: string[]
  placeholder?: string
  multiple?: boolean
  onChange: (value: string | string[]) => void
  className?: string
  disabled?: boolean
  required?: boolean
}

export function CustomSelect({
  label,
  icon: Icon,
  value,
  options,
  placeholder = 'Select',
  multiple = false,
  onChange,
  className = '',
  disabled = false,
  required = false
}: CustomSelectProps) {
  const [isOpen, setIsOpen] = React.useState(false)

  const handleToggle = (option: string) => {
    if (multiple) {
      const currentValue = value as string[]
      if (currentValue.includes(option)) {
        onChange(currentValue.filter(v => v !== option))
      } else {
        onChange([...currentValue, option])
      }
    } else {
      onChange(option)
      setIsOpen(false)
    }
  }

  const displayValue = React.useMemo(() => {
    if (multiple) {
      const count = (value as string[]).length
      return count > 0 ? `${count} selected` : placeholder
    }
    return value || placeholder
  }, [value, multiple, placeholder])

  return (
    <div className={className}>
      <label className="block text-sm font-medium text-gray-700 mb-1">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      <div className="relative">
        <button
          type="button"
          onClick={() => !disabled && setIsOpen(!isOpen)}
          className={`w-full p-2 border border-gray-300 rounded-md text-left flex items-center ${
            disabled ? 'bg-gray-50 text-gray-500 cursor-not-allowed' : 'bg-white hover:bg-gray-50'
          }`}
          disabled={disabled}
        >
          <Icon className="mr-2 h-4 w-4 text-gray-400" />
          <span className="flex-1">{displayValue}</span>
          <span className="ml-auto">▼</span>
        </button>

        {isOpen && !disabled && (
          <div className="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg p-2 max-h-56 overflow-y-auto">
            {options.map(option => (
              <label
                key={option}
                className="flex items-center p-2 hover:bg-gray-100 rounded cursor-pointer"
              >
                <input
                  type={multiple ? "checkbox" : "radio"}
                  checked={multiple 
                    ? (value as string[]).includes(option)
                    : value === option
                  }
                  onChange={() => handleToggle(option)}
                  className="mr-2"
                />
                {option}
              </label>
            ))}
          </div>
        )}
      </div>
    </div>
  )
} 
"use client"

import React, { useState } from 'react'
import { Button } from './ui/button'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/table'
import { Input } from './ui/input'
import { Search, Edit, Trash2, SlidersHorizontal, Plus, Users, BookOpen, UserCircle, Check } from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "./ui/dialog"
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "./ui/select"
import { Label } from "./ui/label"
import { capitalizeName } from "@/app/lib/utils"
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useAuth } from '../contexts/auth-context'
import { usePermissionDeniedDialog } from './PermissionDeniedDialog'
import { ConfirmDialog } from './ConfirmDialog'
import { useNotification } from '../hooks/useNotification'

interface Teacher {
  id: string
  name: string
  subject?: string
  role?: string
}

interface Class {
  id: string
  name: string
  hrTeacherId: string | null
  hrTeacher?: Teacher
  totalStudents: number
  totalSubjects: number
  createdAt: string
  updatedAt: string
}

/**
 * ============================================================================
 * STANDARDIZED CLASS MANAGEMENT SYSTEM
 * ============================================================================
 *
 * This component implements a UNIFIED approach for managing ALL classes in the system.
 * Every class (8A, 8B, 1A, 12F, and any future classes) follows the SAME implementation pattern.
 *
 * 🔧 IMPLEMENTATION PRINCIPLES:
 *
 * 1. UNIFIED EDIT FLOW: All classes use the same handleEdit function
 *    - No class-specific implementations
 *    - Consistent subject fetching via /api/classes/[id]/subjects
 *    - Standardized state management and UI updates
 *
 * 2. STANDARDIZED SUBJECT MANAGEMENT:
 *    - DELETE existing subjects → CREATE new selected subjects
 *    - Same API endpoints for all classes
 *    - Consistent error handling and logging
 *
 * 3. CONSISTENT DATA REFRESH:
 *    - Multiple refresh strategies ensure immediate UI updates
 *    - Same refresh pattern for all classes
 *
 * 4. UNIFIED UTILITIES:
 *    - fetchClassSubjects(): Works for ANY class
 *    - createSubjectsForClass(): Works for ANY class
 *    - updateSubjectCount(): Works for ANY class
 *
 * 📝 ADDING NEW CLASSES:
 *
 * To add new classes (e.g., Grade 13), simply:
 * 1. Add to classGroups array below
 * 2. No other code changes needed - everything is automatic
 *
 * ⚠️  IMPORTANT: DO NOT create class-specific implementations!
 *     All classes must use the unified functions to ensure consistency.
 *
 * ============================================================================
 */

// List of classes grouped by grade - ADD NEW CLASSES HERE
const classGroups = [
  { grade: 'Grade 1', classes: ['1A', '1B', '1C', '1D', '1E', '1F'] },
  { grade: 'Grade 2', classes: ['2A', '2B', '2C', '2D', '2E', '2F'] },
  { grade: 'Grade 3', classes: ['3A', '3B', '3C', '3D', '3E', '3F'] },
  { grade: 'Grade 4', classes: ['4A', '4B', '4C', '4D', '4E', '4F'] },
  { grade: 'Grade 5', classes: ['5A', '5B', '5C', '5D', '5E', '5F'] },
  { grade: 'Grade 6', classes: ['6A', '6B', '6C', '6D', '6E', '6F'] },
  { grade: 'Grade 7', classes: ['7A', '7B', '7C', '7D', '7E', '7F'] },
  { grade: 'Grade 8', classes: ['8A', '8B', '8C', '8D', '8E', '8F'] },
  { grade: 'Grade 9', classes: ['9A', '9B', '9C', '9D', '9E', '9F'] },
  { grade: 'Grade 10', classes: ['10A', '10B', '10C', '10D', '10E', '10F'] },
  { grade: 'Grade 11', classes: ['11A', '11B', '11C', '11D', '11E', '11F'] },
  { grade: 'Grade 12', classes: ['12A', '12B', '12C', '12D', '12E', '12F'] },
  // ADD NEW GRADES HERE: { grade: 'Grade 13', classes: ['13A', '13B', ...] },
]

// Flat list of all classes - AUTOMATICALLY GENERATED, DO NOT MODIFY
const classesList = classGroups.flatMap(group => group.classes)

// List of subjects - STANDARDIZED SUBJECT NAMES
// ⚠️  IMPORTANT: These names must match exactly what's stored in the database
// Do NOT use "Maths" - use "Mathematics" to match database
// Do NOT use "Science" - use "General Science" for elementary or specific sciences for higher grades
const subjectsList = [
  'Arabic', 'Quran', 'Islamic Education', 'Terbia', 'English', 'Somali', 'Amharic',
  'Mathematics', 'Chemistry', 'Biology', 'Physics', 'IT', 'Agriculture', 'Economics',
  'Geography', 'History', 'CTE', 'Citizenship', 'HPE', 'General Science', 'Social Science'
  // ADD NEW SUBJECTS HERE: 'New Subject Name',
]

export function Classes() {
  const queryClient = useQueryClient()
  const { user } = useAuth()
  const { showDialog, PermissionDialog } = usePermissionDeniedDialog()
  const { classNotifications } = useNotification()
  const [open, setOpen] = useState(false)
  const [isEditing, setIsEditing] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [classToDelete, setClassToDelete] = useState<{ id: string; name: string; totalStudents: number } | null>(null)


  const [selectedSubjects, setSelectedSubjects] = useState<string[]>([])
  const [newClass, setNewClass] = useState({
    id: '',
    name: '',
    totalStudents: 0,
    totalSubjects: 0
  })

  // Fetch classes with real-time data
  const { data: classes = [], isLoading, refetch: refetchClasses } = useQuery({
    queryKey: ['classes'],
    queryFn: async () => {
      const currentYear = new Date().getFullYear()
      const academicYear = `${currentYear}-${currentYear + 1}`
      const res = await fetch(`/api/classes?year=${academicYear}`)
      if (!res.ok) throw new Error('Failed to fetch classes')
      const data = await res.json()
      return Array.isArray(data) ? data : []
    }
  })

  // Create class mutation
  const createClassMutation = useMutation({
    mutationFn: async (newClass: Omit<Class, 'id' | 'createdAt' | 'updatedAt'>) => {
      console.log('Creating class with data:', newClass)
      const res = await fetch('/api/classes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newClass),
      })
      const data = await res.json()
      if (!res.ok) {
        throw new Error(data.error || 'Failed to create class')
      }
      return data
    },
    onSuccess: (data) => {
      // Note: Dialog closing, form reset, and notifications are handled in handleSubmit
      // to prevent duplicate notifications when using mutateAsync
      console.log(`[CREATE] Class mutation succeeded for: ${data.name || newClass.name}`)
    },
    onError: (error: Error) => {
      console.error('Error creating class:', error)

      // Show specific error messages
      if (error.message.includes('HR Teacher')) {
        classNotifications.validationError('Invalid HR Teacher selected. Please choose a valid teacher or leave it empty.')
      } else if (error.message.includes('already exists')) {
        classNotifications.validationError('A class with this name already exists. Please choose a different name.')
      } else {
        classNotifications.generalError('create class')
      }
    }
  })

  // Update class mutation
  const updateClassMutation = useMutation({
    mutationFn: async (updatedClass: Partial<Class> & { id: string }) => {
      const res = await fetch(`/api/classes/${updatedClass.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedClass),
      })
      const data = await res.json()
      if (!res.ok) {
        throw new Error(data.error || 'Failed to update class')
      }
      return data
    },
    onSuccess: (data) => {
      // Note: Dialog closing, form reset, and notifications are handled in handleSubmit
      // to prevent duplicate notifications when using mutateAsync
      console.log(`[UPDATE] Class mutation succeeded for: ${data.name || newClass.name}`)
    },
    onError: (error: Error) => {
      console.error('Error updating class:', error)
      classNotifications.generalError('update class')
    }
  })

  // Delete class mutation
  const deleteClassMutation = useMutation({
    mutationFn: async (id: string) => {
      try {
        const res = await fetch(`/api/classes/${id}`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
        })

        // Check if the response is ok first
        if (!res.ok) {
          // Try to get error message from response
          let errorMessage = 'Failed to delete class'
          try {
            const data = await res.json()
            errorMessage = data.error || errorMessage
          } catch (parseError) {
            console.error('Failed to parse error response:', parseError)
            errorMessage = `Server error: ${res.status} ${res.statusText}`
          }
          console.error('Server error when deleting class:', errorMessage)
          throw new Error(errorMessage)
        }

        const data = await res.json()
        return data
      } catch (error) {
        console.error('Error in deleteClassMutation:', error)

        // Handle different types of errors
        if (error instanceof TypeError && error.message.includes('fetch')) {
          throw new Error('Network error: Unable to connect to server. Please check your connection and try again.')
        }

        throw error
      }
    },
    onSuccess: () => {
      // Refresh the classes data to update the UI
      queryClient.invalidateQueries({ queryKey: ['classes'] })
      // Explicitly refetch to ensure we have the latest data
      refetchClasses()
      classNotifications.deleted(classToDelete?.name || 'Unknown Class')
    },
    onError: (error: Error) => {
      console.error('Error deleting class:', error)

      // Provide a more user-friendly error message
      if (error.message.includes('students')) {
        classNotifications.deleteError(classToDelete?.name || 'Unknown Class', 'Class has existing students')
      } else if (error.message.includes('Network error')) {
        classNotifications.networkError()
      } else if (error.message.includes('Unauthorized') || error.message.includes('Forbidden')) {
        classNotifications.permissionError()
      } else {
        classNotifications.generalError('delete class')
      }
    }
  })

  // Filter classes based on search
  const filteredClasses = Array.isArray(classes) ? classes.filter((cls: Class) =>
    cls && typeof cls.name === 'string' ? cls.name.toLowerCase().includes(searchTerm.toLowerCase()) : false
  ) : []

  const handleDelete = (id: string, className: string, totalStudents: number) => {
    if (totalStudents > 0) {
      classNotifications.deleteError(className, `Class has ${totalStudents} student(s). Please remove or transfer all students first.`)
      return
    }

    // Set the class to delete and open the confirmation dialog
    setClassToDelete({ id, name: className, totalStudents })
    setIsDeleteDialogOpen(true)
  }

  const confirmDeleteClass = () => {
    if (classToDelete) {
      deleteClassMutation.mutate(classToDelete.id)
      setIsDeleteDialogOpen(false)
      setClassToDelete(null)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setNewClass({
      ...newClass,
      [name]: value
    })
  }

  const handleSelectChange = (name: string, value: string) => {
    setNewClass({
      ...newClass,
      [name]: value
    })
  }

  /**
   * STANDARDIZED UTILITY: Fetch subjects for ANY class
   * This function works consistently for ALL classes (8A, 8B, 1A, 12F, etc.)
   *
   * @param classId - The unique ID of the class
   * @param className - Name of the class for logging
   * @returns Promise<string[]> - Array of subject names
   */
  const fetchClassSubjects = async (classId: string, className: string): Promise<string[]> => {
    try {
      console.log(`[${className}] Fetching subjects...`)

      // Use the standardized subjects endpoint for ALL classes
      const res = await fetch(`/api/classes/${classId}/subjects`, {
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!res.ok) {
        console.warn(`[${className}] Failed to fetch subjects: ${res.status} ${res.statusText}`)
        throw new Error('Failed to fetch subjects')
      }

      const subjects = await res.json()
      console.log(`[${className}] Raw subjects response:`, subjects)

      if (Array.isArray(subjects)) {
        // Extract subject names from the response (standardized for ALL classes)
        const subjectNames = subjects.map((subject: { name: string }) => {
          // Remove class name from subject if it's in parentheses
          let name = subject.name.split(' (')[0].trim()

          // Normalize subject names to match the form list
          // This ensures consistency between database and form
          if (name === 'Science') {
            name = 'General Science'  // Convert old "Science" to "General Science"
          }
          if (name === 'Maths') {
            name = 'Mathematics'  // Convert old "Maths" to "Mathematics"
          }

          return name
        })

        // Filter out any subjects that aren't in our standardized list
        const validSubjects = subjectNames.filter(name => subjectsList.includes(name))
        const invalidSubjects = subjectNames.filter(name => !subjectsList.includes(name))

        if (invalidSubjects.length > 0) {
          console.warn(`[${className}] Found invalid subjects (will be ignored):`, invalidSubjects)
        }

        console.log(`[${className}] Extracted valid subject names:`, validSubjects)
        console.log(`[${className}] Successfully loaded ${validSubjects.length} subjects`)
        return validSubjects
      } else {
        console.warn(`[${className}] Subjects response is not an array:`, subjects)
        return []
      }
    } catch (error) {
      console.error(`[${className}] Error fetching subjects:`, error)

      // Handle different types of errors
      if (error instanceof TypeError && error.message.includes('fetch')) {
        console.error(`[${className}] Network error when fetching subjects - server may be unreachable`)
        classNotifications.subjectsFetchError(className)
      } else {
        console.error(`[${className}] API error when fetching subjects:`, error)
      }

      return []
    }
  }

  /**
   * STANDARDIZED EDIT HANDLER: Works for ALL classes
   * This is the unified edit function that handles 8A, 8B, 1A, 12F, and ANY future class
   *
   * IMPLEMENTATION FLOW (same for ALL classes):
   * 1. Reset form state to prevent stale data
   * 2. Set basic class information
   * 3. Fetch current subjects using standardized API
   * 4. Update UI state
   * 5. Open edit dialog
   */
  const handleEdit = async (cls: Class) => {
    console.log(`[${cls.name}] Starting edit process (ID: ${cls.id})`)

    // STEP 1: Reset form state first to prevent stale data (SAME FOR ALL CLASSES)
    setSelectedSubjects([])

    // STEP 2: Set basic class information (SAME FOR ALL CLASSES)
    setNewClass({
      id: cls.id,
      name: cls.name,
      totalStudents: cls.totalStudents,
      totalSubjects: cls.totalSubjects
    })

    // STEP 3: Fetch current subjects using standardized function (SAME FOR ALL CLASSES)
    const subjectNames = await fetchClassSubjects(cls.id, cls.name)
    setSelectedSubjects(subjectNames)

    // STEP 4: Open edit dialog (SAME FOR ALL CLASSES)
    setIsEditing(true)
    setOpen(true)

    console.log(`[${cls.name}] Edit process completed`)
  }

  const resetForm = () => {
    console.log('Resetting form state')
    setNewClass({
      id: '',
      name: '',
      totalStudents: 0,
      totalSubjects: 0
    })
    setSelectedSubjects([])
    setIsEditing(false)
    console.log('Form state reset complete')
  }

  // Function to update student counts
  const updateStudentCounts = async () => {
    try {
      const res = await fetch('/api/classes/update-counts', {
        method: 'POST',
      })

      if (!res.ok) {
        throw new Error('Failed to update student counts')
      }

      // Refresh the classes data
      queryClient.invalidateQueries({ queryKey: ['classes'] })
      // Explicitly refetch to ensure we have the latest data
      refetchClasses()

      classNotifications.studentCountUpdated()
    } catch (error) {
      console.error('Error updating student counts:', error)
      classNotifications.generalError('update student counts')
    }
  }

  /**
   * STANDARDIZED UTILITY: Update subject count for ANY class
   * This function works consistently for ALL classes (8A, 8B, 1A, 12F, etc.)
   *
   * @param classId - The unique ID of the class
   * @param subjectCount - Number of subjects to set
   * @param className - Name of the class for logging
   * @returns Promise<boolean> - Success status
   */
  const updateSubjectCount = async (classId: string, subjectCount: number, className: string): Promise<boolean> => {
    try {
      console.log(`[${className}] Updating subject count to ${subjectCount}`)

      // First, get the current class data to ensure we have all required fields
      const getClassRes = await fetch(`/api/classes/${classId}`)
      if (!getClassRes.ok) {
        throw new Error('Failed to fetch class data')
      }

      const classData = await getClassRes.json()

      // Now update with the new subject count
      const res = await fetch(`/api/classes/${classId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: classId,
          name: classData.name || className, // Use the class name from the data or the provided name
          totalSubjects: subjectCount
        }),
      })

      if (!res.ok) {
        const errorData = await res.json()
        throw new Error(errorData.error || 'Failed to update subject count')
      }

      // Standardized data refresh for ALL classes
      queryClient.invalidateQueries({ queryKey: ['classes'] })
      await refetchClasses()

      console.log(`[${className}] Subject count updated successfully`)
      return true
    } catch (error) {
      console.error(`[${className}] Error updating subject count:`, error)
      return false
    }
  }

  /**
   * STANDARDIZED UTILITY: Create subjects for ANY class
   * This function works consistently for ALL classes (8A, 8B, 1A, 12F, etc.)
   *
   * @param classId - The unique ID of the class
   * @param subjectNames - Array of subject names to create
   * @param className - Name of the class for logging (optional)
   * @returns Promise<boolean> - Success status
   */
  const createSubjectsForClass = async (classId: string, subjectNames: string[], className?: string): Promise<boolean> => {
    try {
      const logPrefix = className ? `[${className}]` : `[Class ${classId}]`
      console.log(`${logPrefix} Creating ${subjectNames.length} subjects:`, subjectNames)

      // Validate that all subjects are in our standardized list
      const invalidSubjects = subjectNames.filter(name => !subjectsList.includes(name))
      if (invalidSubjects.length > 0) {
        console.error(`${logPrefix} Invalid subjects detected:`, invalidSubjects)
        console.error(`${logPrefix} Valid subjects are:`, subjectsList)
        return false
      }

      // Only create subjects that are explicitly selected
      if (subjectNames.length === 0) {
        console.log(`${logPrefix} No subjects to create`)
        return true
      }

      // Create each subject for the class
      const creationPromises = subjectNames.map(async (subjectName) => {
        console.log(`${logPrefix} Creating subject: ${subjectName}`)

        const res = await fetch('/api/subjects', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: subjectName,
            classId: classId
          }),
        })

        if (!res.ok) {
          const errorData = await res.json()

          // Handle specific error cases
          if (res.status === 409) {
            // Subject already exists - this is okay, just log it
            console.log(`${logPrefix} Subject ${subjectName} already exists:`, errorData.error)
            return true // Consider this a success since the subject exists
          } else {
            // Other errors are actual failures
            console.error(`${logPrefix} Failed to create subject ${subjectName}:`, errorData)
            return false
          }
        }

        console.log(`${logPrefix} Successfully created subject: ${subjectName}`)
        return true
      })

      const results = await Promise.all(creationPromises)
      const allSucceeded = results.every(result => result === true)

      if (allSucceeded) {
        console.log(`${logPrefix} Successfully created all ${subjectNames.length} subjects`)
      } else {
        console.warn(`${logPrefix} Some subjects could not be created`)
      }

      // Standardized data refresh for ALL classes
      queryClient.invalidateQueries({ queryKey: ['classes'] })
      await refetchClasses()

      return allSucceeded
    } catch (error) {
      const logPrefix = className ? `[${className}]` : `[Class ${classId}]`
      console.error(`${logPrefix} Error creating subjects:`, error)
      return false
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!newClass.name) {
      classNotifications.validationError('Please fill in all required fields')
      return
    }

    // Set totalSubjects based on selected subjects count
    const dataToSubmit = {
      ...newClass,
      totalSubjects: selectedSubjects.length
    }

    console.log('Submitting class data:', dataToSubmit)

    try {
      if (isEditing) {
        /**
         * STANDARDIZED EDIT FLOW: Works for ALL classes (8A, 8B, 1A, 12F, etc.)
         * This flow ensures consistent behavior across all class edits
         */
        console.log(`[${newClass.name}] Updating class with ${selectedSubjects.length} subjects:`, selectedSubjects)

        // STEP 1: Update the class basic information (SAME FOR ALL CLASSES)
        const updatedClassData = await updateClassMutation.mutateAsync(dataToSubmit)
        console.log(`[${newClass.name}] Class updated successfully:`, updatedClassData)

        // STEP 2: Update subjects if we're editing an existing class (SAME FOR ALL CLASSES)
        if (newClass.id) {
          try {
            console.log(`[${newClass.name}] Starting subject update process...`)

            // STEP 2a: Delete all existing subjects (STANDARDIZED FOR ALL CLASSES)
            const deleteRes = await fetch(`/api/classes/${newClass.id}/subjects`, {
              method: 'DELETE',
              headers: {
                'Content-Type': 'application/json',
              },
            })

            if (deleteRes.ok) {
              const deleteResult = await deleteRes.json()
              console.log(`[${newClass.name}] Successfully deleted ${deleteResult.deletedCount || 0} existing subjects`)
            } else {
              console.warn(`[${newClass.name}] Failed to delete existing subjects, but continuing with creation`)
            }

            // STEP 2b: Create new selected subjects (STANDARDIZED FOR ALL CLASSES)
            if (selectedSubjects.length > 0) {
              console.log(`[${newClass.name}] Creating ${selectedSubjects.length} new subjects...`)
              const subjectSuccess = await createSubjectsForClass(newClass.id, selectedSubjects, newClass.name)
              if (subjectSuccess) {
                console.log(`[${newClass.name}] All subjects created successfully`)
              } else {
                console.warn(`[${newClass.name}] Some subjects could not be created`)
              }
            } else {
              console.log(`[${newClass.name}] No subjects selected, skipping subject creation`)
            }

            // STEP 2c: Update the totalSubjects count (STANDARDIZED FOR ALL CLASSES)
            const success = await updateSubjectCount(newClass.id, selectedSubjects.length, newClass.name)
            if (!success) {
              console.warn(`[${newClass.name}] Failed to update subject count, but subjects were updated`)
            }
          } catch (subjectError) {
            console.error(`[${newClass.name}] Error updating subjects:`, subjectError)
            classNotifications.generalError('update class subjects')
          }
        }
      } else {
        /**
         * STANDARDIZED CREATE FLOW: Works for ALL new classes
         * This flow ensures consistent behavior when creating any new class
         */
        console.log(`[${newClass.name}] Creating new class with ${selectedSubjects.length} subjects:`, selectedSubjects)

        // STEP 1: Create the new class (SAME FOR ALL CLASSES)
        const newClassData = await createClassMutation.mutateAsync(dataToSubmit)
        console.log(`[${newClass.name}] New class created successfully:`, newClassData)

        // STEP 2: Create subjects for the new class (SAME FOR ALL CLASSES)
        if (newClassData && newClassData.id && selectedSubjects.length > 0) {
          const success = await createSubjectsForClass(newClassData.id, selectedSubjects, newClass.name)
          if (!success) {
            console.warn(`[${newClass.name}] Some subjects could not be created for the new class`)
          }
        }
      }

      /**
       * STANDARDIZED COMPLETION FLOW: Same for ALL classes
       * Ensures UI updates immediately for any class operation
       */
      console.log(`[${newClass.name}] Refreshing class data...`)
      queryClient.invalidateQueries({ queryKey: ['classes'] })
      await refetchClasses()

      // Additional refresh to ensure immediate UI update
      setTimeout(() => {
        refetchClasses()
      }, 100)

      // Close dialog and reset form (SAME FOR ALL CLASSES)
      setOpen(false)
      resetForm()

      // Show single success notification (SAME FOR ALL CLASSES)
      if (isEditing) {
        classNotifications.updated(newClass.name)
      } else {
        classNotifications.created(newClass.name)
      }

      console.log(`[${newClass.name}] Class form submission completed successfully`)
    } catch (error) {
      console.error(`[${newClass.name}] Error submitting class form:`, error)
      classNotifications.generalError(isEditing ? 'update class' : 'create class')
    }
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Classes</h2>
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Search className="h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search classes..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-64"
            />
          </div>
          <Button variant="outline" onClick={updateStudentCounts} className="mr-2">
            <SlidersHorizontal className="h-4 w-4 mr-2" />
            Update Student Counts
          </Button>
          <Button
            onClick={() => {
              if (!user || !['SUPER_ADMIN', 'ADMIN', 'DATA_ENCODER'].includes(user.role)) {
                showDialog(
                  'You do not have permission to add classes',
                  'Only Super Admin, Admin, and Data Encoder roles can add classes.'
                );
                return;
              }
              setOpen(true);
            }}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Class
          </Button>

          <Dialog open={open} onOpenChange={setOpen} key={`${isEditing ? 'edit' : 'create'}-${newClass.id || 'new'}`}>
            <DialogContent className="sm:max-w-[550px]" onInteractOutside={(e) => {
              // Prevent closing the dialog when interacting with select dropdown
              if (e.target.closest('[role="listbox"]')) {
                e.preventDefault();
              }
            }}>
              <DialogHeader>
                <DialogTitle>{isEditing ? 'Edit Class' : 'Add New Class'}</DialogTitle>
                <DialogDescription>
                  {isEditing ? 'Edit the class details and click save when you\'re done.' : 'Fill in the class details and click save when you\'re done.'}
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="grid gap-6 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="name" className="text-sm font-medium">
                      Class Name
                    </Label>
                    <div className="relative">
                      <Users className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Select
                        value={newClass.name}
                        onValueChange={(value) => handleSelectChange('name', value)}
                        required
                      >
                        <SelectTrigger className="pl-9">
                          <SelectValue placeholder="Select class" />
                        </SelectTrigger>
                        <SelectContent className="max-h-[300px] overflow-y-auto">
                          <div className="p-1 sticky top-0 bg-white z-10 border-b border-gray-100 flex items-center justify-center">
                            <p className="text-xs text-gray-500">Scroll to see more classes</p>
                          </div>
                          {classGroups.map((group) => (
                            <SelectGroup key={group.grade}>
                              <SelectLabel className="text-xs font-semibold">
                                {group.grade}
                              </SelectLabel>
                              {group.classes.map((cls) => (
                                <SelectItem key={cls} value={cls}>
                                  {cls}
                                </SelectItem>
                              ))}
                            </SelectGroup>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                </div>

                <div className="grid gap-2">
                  <Label className="text-sm font-medium">
                    Subjects
                  </Label>
                  <div className="border rounded-md p-4 max-h-[200px] overflow-y-auto">
                    <div className="grid grid-cols-2 gap-3">
                      {subjectsList.map((subject) => (
                        <div key={subject} className="flex items-center space-x-2">
                          <div className="flex h-5 items-center">
                            <input
                              type="checkbox"
                              id={`subject-${subject}`}
                              className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600"
                              onChange={(e) => {
                                const isChecked = e.target.checked
                                console.log(`Subject ${subject} ${isChecked ? 'checked' : 'unchecked'}`)

                                setSelectedSubjects(prevSubjects => {
                                  const currentSubjects = [...prevSubjects]
                                  console.log('Current subjects before change:', currentSubjects)

                                  if (isChecked && !currentSubjects.includes(subject)) {
                                    currentSubjects.push(subject)
                                    console.log(`Added ${subject}. New subjects:`, currentSubjects)
                                  } else if (!isChecked && currentSubjects.includes(subject)) {
                                    const index = currentSubjects.indexOf(subject)
                                    currentSubjects.splice(index, 1)
                                    console.log(`Removed ${subject}. New subjects:`, currentSubjects)
                                  }

                                  return currentSubjects
                                })
                              }}
                              checked={selectedSubjects.includes(subject)}
                            />
                          </div>
                          <label htmlFor={`subject-${subject}`} className="text-sm font-medium text-gray-700">
                            {subject}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>
                  <div className="flex items-center mt-1">
                    <BookOpen className="h-4 w-4 text-gray-400 mr-1" />
                    <p className="text-xs text-gray-500">
                      Selected subjects: <span className="font-medium text-indigo-600">{selectedSubjects.length}</span>
                      {selectedSubjects.length > 0 && (
                        <span className="ml-2 text-xs text-gray-400">
                          ({selectedSubjects.join(', ')})
                        </span>
                      )}
                    </p>
                  </div>
                </div>

                <DialogFooter>
                  <Button variant="outline" onClick={() => {
                    setOpen(false);
                    resetForm();
                  }} type="button">
                    Cancel
                  </Button>
                  <Button type="submit" disabled={createClassMutation.isPending || updateClassMutation.isPending}>
                    {createClassMutation.isPending || updateClassMutation.isPending ?
                      'Saving...' :
                      isEditing ? 'Update Class' : 'Add Class'
                    }
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Class Name</TableHead>
              <TableHead>HR Teacher</TableHead>
              <TableHead className="text-center">Total Students</TableHead>
              <TableHead className="text-center">Total Subjects</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-8">
                  Loading classes...
                </TableCell>
              </TableRow>
            ) : filteredClasses.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-8">
                  No classes found
                </TableCell>
              </TableRow>
            ) : (
              filteredClasses.map((cls: Class) => (
                <TableRow key={cls.id}>
                  <TableCell className="font-medium">{cls.name}</TableCell>
                  <TableCell>
                    {cls.hrTeacher ? (
                      <div className="flex items-center gap-2">
                        <UserCircle className="h-4 w-4 text-blue-500" />
                        <span>{cls.hrTeacher.name}</span>
                      </div>
                    ) : (
                      <span className="text-gray-500">Not assigned</span>
                    )}
                  </TableCell>
                  <TableCell className="text-center">{cls.totalStudents}</TableCell>
                  <TableCell className="text-center">{cls.totalSubjects}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => {
                          if (!user || !['SUPER_ADMIN', 'ADMIN', 'DATA_ENCODER'].includes(user.role)) {
                            showDialog(
                              'You do not have permission to edit classes',
                              'Only Super Admin, Admin, and Data Encoder roles can edit classes.'
                            );
                            return;
                          }
                          handleEdit(cls);
                        }}
                        title="Edit class"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => {
                          if (!user || !['SUPER_ADMIN', 'ADMIN', 'DATA_ENCODER'].includes(user.role)) {
                            showDialog(
                              'You do not have permission to delete classes',
                              'Only Super Admin, Admin, and Data Encoder roles can delete classes.'
                            );
                            return;
                          }
                          handleDelete(cls.id, cls.name, cls.totalStudents);
                        }}
                        title={cls.totalStudents > 0 ? "Cannot delete class with students" : "Delete class"}
                      >
                        <Trash2 className={`h-4 w-4 ${cls.totalStudents > 0 ? 'text-gray-300' : 'text-red-500'}`} />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        title="Delete Class"
        description={`Are you sure you want to delete class ${classToDelete?.name}?

This will permanently remove:
• The class record
• All subjects assigned to this class
• All teacher assignments for this class
• All class-teacher relationships

This action cannot be undone.`}
        onConfirm={confirmDeleteClass}
        onCancel={() => {
          setIsDeleteDialogOpen(false)
          setClassToDelete(null)
        }}
        confirmText="Delete Class"
        cancelText="Cancel"
        variant="destructive"
      />

      {/* Permission Denied Dialog */}
      <PermissionDialog />
    </div>
  )
}

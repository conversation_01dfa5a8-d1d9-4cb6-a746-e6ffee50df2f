"use client"

import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from './ui/button'
import { Edit, Trash2, Plus, FileText } from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "./ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select"
import { Label } from "./ui/label"
import { Textarea } from "./ui/textarea"
import { MarkForm } from './MarkForm'
import { BulkEntryForm } from './BulkEntryForm'
import { toast } from './ui/use-toast'
import { MarksDataTable } from './MarksDataTable'

// Mark interface definition
export interface Mark {
  id: string;
  studentId: string;
  studentName: string;
  class: string;
  subject: string;
  term: string;                // "First Semester" | "Second Semester" | "Annual"
  academicYear: string;        // e.g., "2023-2024"

  totalMarks: number;          // Usually 100
  obtainedMarks: number;       // Points scored
  grade: string;               // A+, A, B+, etc.
  remarks?: string;            // Teacher's comments (optional)
  dateRecorded: string;        // ISO date string
  recordedBy: string;          // Teacher who recorded the mark
}

// List of classes from Classes component
const classesList = [
  '1A', '1B', '1C', '1D', '2A', '2B', '2C', '2D', '3A', '3B', '3C', '3D',
  '4A', '4B', '4C', '4D', '5A', '5B', '5C', '5D', '6A', '6B', '6C', '6D',
  '7A', '7B', '7C', '7D', '8A', '8B', '8C', '8D', '9A', '9B', '9C', '9D',
  '10A', '10B', '10C', '10D', '11A', '11B', '11C', '11D', '12A', '12B', '12C', '12D'
]

// List of subjects from Classes component
const subjectsList = [
  'Arabic', 'Quran', 'Islamic Education', 'Terbia', 'English', 'Somali', 'Amharic',
  'Maths', 'Chemistry', 'Biology', 'Physics', 'IT', 'Agriculture', 'Economics',
  'Geography', 'History', 'CTE', 'Citizenship', 'HPE', 'General Science', 'Social Science'
]

export function MarkList() {
  // State variables
  const [marks, setMarks] = useState<Mark[]>([]);
  const [filteredMarks, setFilteredMarks] = useState<Mark[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedClass, setSelectedClass] = useState('all');
  const [selectedSubject, setSelectedSubject] = useState('all');
  const [selectedTerm, setSelectedTerm] = useState('all');
  const [selectedYear, setSelectedYear] = useState('2023-2024');
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isBulkEntryOpen, setIsBulkEntryOpen] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [currentMark, setCurrentMark] = useState<Mark | null>(null);

  // Add new state for loading and error handling
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Add function to handle data loading
  const loadMarks = async () => {
    setIsLoading(true)
    setError(null)
    try {
      // TODO: Replace with actual API call
      const response = await fetch('/api/marks');
      const data = await response.json();
      setMarks(data)
      setFilteredMarks(data)
    } catch (err) {
      setError('Failed to load marks')
      toast({
        title: "Error",
        description: "Failed to load marks. Please try again.",
        className: "bg-destructive text-destructive-foreground",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Load marks on component mount
  useEffect(() => {
    loadMarks()
  }, [])

  // Add refresh function
  const handleRefresh = () => {
    loadMarks()
  }

  // Filter marks based on selections
  useEffect(() => {
    let result = [...marks];

    if (selectedClass && selectedClass !== 'all') {
      result = result.filter(mark => mark.class === selectedClass);
    }

    if (selectedSubject && selectedSubject !== 'all') {
      result = result.filter(mark => mark.subject === selectedSubject);
    }

    if (selectedTerm && selectedTerm !== 'all') {
      result = result.filter(mark => mark.term === selectedTerm);
    }

    if (selectedYear) {
      result = result.filter(mark => mark.academicYear === selectedYear);
    }

    if (searchTerm) {
      result = result.filter(mark =>
        mark.studentName.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    setFilteredMarks(result);
  }, [marks, selectedClass, selectedSubject, selectedTerm, selectedYear, searchTerm]);

  // Calculate statistics
  const calculateStats = () => {
    if (filteredMarks.length === 0) return null;

    const scores = filteredMarks.map(mark => mark.obtainedMarks);
    const average = scores.reduce((a, b) => a + b, 0) / scores.length;
    const highest = Math.max(...scores);
    const lowest = Math.min(...scores);
    const highestStudent = filteredMarks.find(mark => mark.obtainedMarks === highest)?.studentName;
    const lowestStudent = filteredMarks.find(mark => mark.obtainedMarks === lowest)?.studentName;

    const gradeDistribution = {
      'A+': 0, 'A': 0, 'B+': 0, 'B': 0, 'C+': 0, 'C': 0, 'D': 0, 'F': 0
    };

    filteredMarks.forEach(mark => {
      if (gradeDistribution.hasOwnProperty(mark.grade)) {
        gradeDistribution[mark.grade as keyof typeof gradeDistribution]++;
      }
    });

    return { average, highest, lowest, highestStudent, lowestStudent, gradeDistribution };
  };

  // Calculate grade from mark
  const calculateGrade = (mark: number): string => {
    if (mark >= 90) return 'A+';
    if (mark >= 80) return 'A';
    if (mark >= 70) return 'B';
    if (mark >= 60) return 'C';
    if (mark >= 50) return 'D';
    return 'F';
  };

  // Add new mark
  const handleAddMark = async (mark: Omit<Mark, 'id' | 'dateRecorded' | 'grade'>) => {
    try {
      const grade = calculateGrade(mark.obtainedMarks);
      const newMark = {
        ...mark,
        grade,
        dateRecorded: new Date().toISOString()
      };

      // TODO: Replace with actual API call
      const response = await fetch('/api/marks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newMark),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.details || 'Failed to add mark');
      }

      const addedMark = await response.json();
      // Refresh the marks list to ensure we get the latest data
      await loadMarks();
      setIsAddDialogOpen(false);
      toast({
        title: "Success",
        description: "Mark added successfully.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add mark. Please try again.",
        className: "bg-destructive text-destructive-foreground",
      });
    }
  };

  // Edit existing mark
  const handleEditMark = async (updatedMark: Omit<Mark, 'grade'>) => {
    try {
      const grade = calculateGrade(updatedMark.obtainedMarks);
      const markWithGrade = { ...updatedMark, grade };

      // TODO: Replace with actual API call
      const response = await fetch(`/api/marks/${updatedMark.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(markWithGrade),
      });

      if (!response.ok) throw new Error('Failed to update mark');

      setMarks(marks.map(mark =>
        mark.id === updatedMark.id ? markWithGrade : mark
      ));
      setIsEditMode(false);
      setCurrentMark(null);
      setIsAddDialogOpen(false);
      toast({
        title: "Success",
        description: "Mark updated successfully.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update mark. Please try again.",
        className: "bg-destructive text-destructive-foreground",
      });
    }
  };

  // Delete mark
  const handleDeleteMark = async (id: string) => {
    try {
      // TODO: Replace with actual API call
      const response = await fetch(`/api/marks/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) throw new Error('Failed to delete mark');

      setMarks(marks.filter(mark => mark.id !== id));
      toast({
        title: "Success",
        description: "Mark deleted successfully.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete mark. Please try again.",
        className: "bg-destructive text-destructive-foreground",
      });
    }
  };

  // Start editing a mark
  const startEdit = (mark: Mark) => {
    setCurrentMark(mark);
    setIsEditMode(true);
    setIsAddDialogOpen(true);
  };

  // Handle bulk entry submission
  const handleBulkEntry = async (bulkMarks: Omit<Mark, 'id' | 'dateRecorded' | 'grade'>[]) => {
    try {
      // TODO: Replace with actual API call
      const response = await fetch('/api/marks/bulk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(bulkMarks),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.details || 'Failed to add bulk marks');
      }

      const addedMarks = await response.json();
      // Refresh the marks list to ensure we get the latest data
      await loadMarks();
      setIsBulkEntryOpen(false);
      toast({
        title: "Success",
        description: `Successfully added ${bulkMarks.length} marks.`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add bulk marks. Please try again.",
        className: "bg-destructive text-destructive-foreground",
      });
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">Mark List</h1>
          <p className="text-gray-600">Manage and view student marks</p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setIsBulkEntryOpen(true)}
            className="flex items-center"
          >
            <FileText className="mr-2 h-4 w-4" />
            Bulk Entry
          </Button>
          <Button
            onClick={() => {
              setIsEditMode(false);
              setCurrentMark(null);
              setIsAddDialogOpen(true);
            }}
            className="bg-indigo-600 hover:bg-indigo-700 flex items-center"
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Mark
          </Button>
        </div>
      </div>

      {/* Add loading and error states */}
      {isLoading && (
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
        </div>
      )}

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative">
          {error}
        </div>
      )}

      {/* Beautiful data table to display marks */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h2 className="text-lg font-semibold mb-4">Student Marks</h2>
        <MarksDataTable data={marks} isLoading={isLoading} />
      </div>

      {/* Add/Edit Mark Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{isEditMode ? 'Edit Mark' : 'Add New Mark'}</DialogTitle>
            <DialogDescription>
              {isEditMode
                ? 'Update the student\'s mark details'
                : 'Enter the student\'s mark details'
              }
            </DialogDescription>
          </DialogHeader>

          <MarkForm
            isEditMode={isEditMode}
            initialData={currentMark}
            onSubmit={isEditMode ?
              (mark: Omit<Mark, "id" | "dateRecorded" | "grade">) => handleEditMark({
                ...mark,
                id: currentMark?.id || '',
                dateRecorded: currentMark?.dateRecorded || new Date().toISOString()
              })
              : handleAddMark}
            onCancel={() => {
              setIsAddDialogOpen(false);
              setIsEditMode(false);
              setCurrentMark(null);
            }}
            calculateGrade={calculateGrade}
          />
        </DialogContent>
      </Dialog>

      {/* Bulk Entry Dialog */}
      <Dialog open={isBulkEntryOpen} onOpenChange={setIsBulkEntryOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Bulk Mark Entry</DialogTitle>
            <DialogDescription>
              Enter marks for multiple students at once
            </DialogDescription>
          </DialogHeader>

          <BulkEntryForm
            onSubmit={handleBulkEntry}
            onCancel={() => setIsBulkEntryOpen(false)}
            calculateGrade={calculateGrade}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}

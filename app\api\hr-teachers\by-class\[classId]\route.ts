import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET - Fetch HR teacher for a specific class
export async function GET(
  request: Request,
  { params }: { params: { classId: string } }
) {
  try {
    const { classId } = params
    
    console.log(`Fetching HR teacher for class: ${classId}`)

    // Fetch HR teacher for the specific class
    const hrTeacher = await prisma.hRTeacher.findUnique({
      where: { classId },
      include: {
        teacher: {
          select: {
            id: true,
            name: true,
            email: true,
            subject: true,
            mobile: true,
            fatherName: true,
            gender: true,
            createdAt: true,
            updatedAt: true
          }
        },
        class: {
          select: {
            id: true,
            name: true,
            totalStudents: true,
            totalSubjects: true
          }
        }
      }
    })

    if (!hrTeacher) {
      return NextResponse.json(
        { error: 'No HR teacher assigned to this class' },
        { status: 404 }
      )
    }

    console.log(`Found HR teacher: ${hrTeacher.teacher.name} for class: ${hrTeacher.class.name}`)

    return NextResponse.json(hrTeacher)
  } catch (error) {
    console.error('Error fetching HR teacher for class:', error)
    return NextResponse.json(
      { error: 'Failed to fetch HR teacher for class' },
      { status: 500 }
    )
  }
}

'use client'

import React, { useState, useEffect } from 'react'
import './journal-voucher.css'
import Sidebar from '../../components/Sidebar'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'
import { <PERSON><PERSON> } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Label } from '../../components/ui/label'
import { Textarea } from '../../components/ui/textarea'
import {
  FileText,
  DollarSign,
  CheckCircle2,
  AlertCircle,
  Loader2,
  Landmark,
  Calendar,
  Hash,
  CreditCard,
  FileBarChart,
  Search,
  Printer,
  Download
} from 'lucide-react'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "../../components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "../../components/ui/table"
import { useToast } from '../../components/ui/use-toast'
import Header from '../../components/Header'


// Account codes will be fetched from the API

// Define interface for voucher data
interface Voucher {
  id: string;
  voucherNo: string;
  date: string;
  paidTo: string;
  accountCode: string;
  amount: number;
  paymentMethod: string;
  chequeNo?: string;
  purpose: string;
  status: 'pending' | 'approved' | 'rejected';
  createdAt?: string;
  updatedAt?: string;
}

export default function JournalVoucherPage() {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false)
  const [date, setDate] = useState(new Date().toISOString().split('T')[0])
  const [paidTo, setPaidTo] = useState('')
  const [accountCode, setAccountCode] = useState('')
  const [amount, setAmount] = useState('')
  const [paymentMethod, setPaymentMethod] = useState('cash')
  const [chequeNo, setChequeNo] = useState('')
  const [purpose, setPurpose] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isHistoryOpen, setIsHistoryOpen] = useState(false)
  const [voucherList, setVoucherList] = useState<Voucher[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [accountCodes, setAccountCodes] = useState<{id: string, code: string, description: string}[]>([])
  const [isLoadingAccountCodes, setIsLoadingAccountCodes] = useState(false)
  const { toast } = useToast()

  // Toggle sidebar for desktop
  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  // Toggle sidebar for mobile
  const toggleMobileSidebar = () => {
    setMobileSidebarOpen(!mobileSidebarOpen)
  }

  // Fetch account codes from the API
  const fetchAccountCodes = async () => {
    setIsLoadingAccountCodes(true)
    try {
      const response = await fetch('/api/accounting/account-codes')

      if (!response.ok) {
        throw new Error('Failed to fetch account codes')
      }

      const data = await response.json()
      setAccountCodes(data)
    } catch (error) {
      console.error('Error fetching account codes:', error)
      toast({
        title: "Error",
        description: "Failed to load account codes",
        variant: "destructive",
      })

      // Use mock data as fallback
      setAccountCodes([
        { id: 'acc1', code: '5504', description: 'Tuition Revenue' },
        { id: 'acc2', code: '4211', description: 'School Supplies Expense' },
        { id: 'acc3', code: '6301', description: 'Salary Expense' },
        { id: 'acc4', code: '7102', description: 'Utilities Expense' },
        { id: 'acc5', code: '8005', description: 'Office Supplies' }
      ])
    } finally {
      setIsLoadingAccountCodes(false)
    }
  }

  // Fetch vouchers from the API
  const fetchVouchers = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/accounting/journal-vouchers')

      // Even if response is not OK, we'll try to parse it as JSON
      // The API is designed to return an empty array in case of errors
      const data = await response.json()

      if (Array.isArray(data)) {
        // Format the data to match our Voucher interface
        const formattedVouchers = data.map((voucher: any) => ({
          ...voucher,
          date: voucher.date ? new Date(voucher.date).toISOString().split('T')[0] : '',
        }))

        setVoucherList(formattedVouchers)
      } else if (data.error) {
        console.error('API returned an error:', data.error)
        toast({
          title: 'Error',
          description: data.error || 'Failed to load vouchers. Please try again.',
          variant: 'destructive',
        })
        // Set empty list to prevent UI from breaking
        setVoucherList([])
      } else {
        // Unexpected response format
        console.error('Unexpected API response format:', data)
        setVoucherList([])
      }
    } catch (error) {
      console.error('Error fetching vouchers:', error)
      toast({
        title: 'Error',
        description: 'Failed to load vouchers. Please try again.',
        variant: 'destructive',
      })
      // Set empty list to prevent UI from breaking
      setVoucherList([])
    } finally {
      setIsLoading(false)
    }
  }

  // Load account codes when the component mounts
  useEffect(() => {
    fetchAccountCodes()
  }, [])

  // Load vouchers when the component mounts or when the history is opened
  useEffect(() => {
    if (isHistoryOpen) {
      fetchVouchers()
    }
  }, [isHistoryOpen])

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validate form
    if (!date || !paidTo || !accountCode || !amount || !paymentMethod || !purpose) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      })
      return
    }

    if (paymentMethod === 'cheque' && !chequeNo) {
      toast({
        title: "Validation Error",
        description: "Please enter the Cheque Number",
        variant: "destructive",
      })
      return
    }

    // Start submission
    setIsSubmitting(true)

    try {
      // Prepare the data to send to the API
      const voucherData = {
        paidTo: paidTo,
        accountCode: accountCode,
        amount: amount,
        paymentMethod: paymentMethod,
        purpose: purpose,
        date: date
      }

      // Add payment method specific details
      if (paymentMethod === 'cheque') {
        voucherData.chequeNo = chequeNo
      }

      // Send the data to the API
      const response = await fetch('/api/accounting/journal-vouchers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(voucherData),
      })

      // Parse the response
      const responseData = await response.json()

      if (!response.ok) {
        throw new Error(responseData.error || 'Failed to create voucher')
      }

      // Get the created voucher from the response
      const newVoucher = responseData

      toast({
        title: "Journal Voucher Created",
        description: `Journal Voucher ${newVoucher.voucherNo} has been created successfully`,
        variant: "default",
      })

      // Refresh the voucher list if the history dialog is open
      if (isHistoryOpen) {
        fetchVouchers()
      }

      // Reset form
      setDate(new Date().toISOString().split('T')[0])
      setPaidTo('')
      setAccountCode('')
      setAmount('')
      setPaymentMethod('cash')
      setChequeNo('')
      setPurpose('')
    } catch (error) {
      console.error('Error creating journal voucher:', error)
      toast({
        title: "Error",
        description: "Failed to create journal voucher. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
      <Sidebar
        isCollapsed={sidebarCollapsed}
        toggleSidebar={toggleSidebar}
        isMobileOpen={mobileSidebarOpen}
      />

      <main className={`main-content ${sidebarCollapsed ? 'sidebar-collapsed' : ''} w-full h-screen overflow-y-auto`}>
        <Header toggleMobileSidebar={toggleMobileSidebar} />

        <div className="container mx-auto px-4 py-6">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold text-gray-800 dark:text-white">
              Journal Voucher
            </h1>
            <Button
              className="bg-blue-600 hover:bg-blue-700"
              onClick={() => setIsHistoryOpen(true)}
            >
              <FileBarChart className="h-4 w-4 mr-2" />
              View Voucher History
            </Button>
          </div>

          <Card className="shadow-lg border-0">
            <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border-b">
              <CardTitle className="flex items-center text-xl">
                <FileText className="mr-2 h-5 w-5 text-blue-600 dark:text-blue-400" />
                Create Journal Voucher
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="date" className="text-sm font-medium flex items-center">
                      <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                      Date
                    </Label>
                    <Input
                      id="date"
                      type="date"
                      value={date}
                      onChange={(e) => setDate(e.target.value)}
                      className="h-10"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="paid-to" className="text-sm font-medium flex items-center">
                      <Landmark className="h-4 w-4 mr-2 text-gray-500" />
                      Paid To
                    </Label>
                    <Input
                      id="paid-to"
                      placeholder="Enter recipient name"
                      value={paidTo}
                      onChange={(e) => setPaidTo(e.target.value.charAt(0).toUpperCase() + e.target.value.slice(1))}
                      className="h-10"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="account-code" className="text-sm font-medium flex items-center">
                      <Hash className="h-4 w-4 mr-2 text-gray-500" />
                      Account Code
                    </Label>
                    <Select
                      value={accountCode}
                      onValueChange={setAccountCode}
                    >
                      <SelectTrigger id="account-code" className="h-10">
                        {isLoadingAccountCodes ? (
                          <div className="flex items-center">
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            <span>Loading account codes...</span>
                          </div>
                        ) : (
                          <SelectValue placeholder="Select account code" />
                        )}
                      </SelectTrigger>
                      <SelectContent className="max-h-[200px] overflow-y-auto">
                        {accountCodes.map(code => (
                          <SelectItem key={code.id} value={code.code}>
                            {code.code} - {code.description}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="amount" className="text-sm font-medium flex items-center">
                      <DollarSign className="h-4 w-4 mr-2 text-gray-500" />
                      Amount
                    </Label>
                    <Input
                      id="amount"
                      type="number"
                      placeholder="Enter amount"
                      value={amount}
                      onChange={(e) => setAmount(e.target.value)}
                      className="h-10"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="payment-method" className="text-sm font-medium flex items-center">
                      <CreditCard className="h-4 w-4 mr-2 text-gray-500" />
                      Method of Payment
                    </Label>
                    <Select
                      value={paymentMethod}
                      onValueChange={setPaymentMethod}
                    >
                      <SelectTrigger id="payment-method" className="h-10">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="cash">Cash</SelectItem>
                        <SelectItem value="cheque">Cheque</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {paymentMethod === 'cheque' && (
                    <div className="space-y-2">
                      <Label htmlFor="cheque-no" className="text-sm font-medium flex items-center">
                        <FileText className="h-4 w-4 mr-2 text-gray-500" />
                        Cheque No.
                      </Label>
                      <Input
                        id="cheque-no"
                        placeholder="Enter cheque number"
                        value={chequeNo}
                        onChange={(e) => setChequeNo(e.target.value.replace(/[a-z]/g, match => match.toUpperCase()))}
                        className="h-10"
                        required
                      />
                    </div>
                  )}

                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="purpose" className="text-sm font-medium flex items-center">
                      <FileText className="h-4 w-4 mr-2 text-gray-500" />
                      Purpose
                    </Label>
                    <Textarea
                      id="purpose"
                      placeholder="e.g., Long Term Loan"
                      value={purpose}
                      onChange={(e) => setPurpose(e.target.value.charAt(0).toUpperCase() + e.target.value.slice(1))}
                      rows={3}
                      required
                    />
                  </div>
                </div>

                <div className="flex justify-end space-x-4 pt-4 border-t">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setDate(new Date().toISOString().split('T')[0])
                      setPaidTo('')
                      setAccountCode('')
                      setAmount('')
                      setPaymentMethod('cash')
                      setChequeNo('')
                      setPurpose('')
                    }}
                    disabled={isSubmitting}
                  >
                    <AlertCircle className="h-4 w-4 mr-2" />
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    className="bg-green-600 hover:bg-green-700"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Processing...
                      </>
                    ) : (
                      <>
                        <CheckCircle2 className="h-4 w-4 mr-2" />
                        Create Voucher
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </main>

      {/* Overlay for mobile menu */}
      {mobileSidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 md:hidden z-40"
          onClick={toggleMobileSidebar}
        ></div>
      )}

      {/* Voucher History Dialog */}
      <Dialog open={isHistoryOpen} onOpenChange={setIsHistoryOpen}>
        <DialogContent className="max-w-6xl w-[90vw] dialog-content">
          <DialogHeader>
            <DialogTitle className="text-xl flex items-center">
              <FileBarChart className="h-5 w-5 mr-2 text-blue-600" />
              Journal Voucher History
            </DialogTitle>
            <DialogDescription>
              View all journal vouchers and their details
            </DialogDescription>
          </DialogHeader>

          {/* Print-only title */}
          <div className="voucher-history-title hidden">
            Journal Voucher History
          </div>

          <div className="flex justify-between items-center mb-4 search-container print-export-buttons">
            <div className="relative w-64">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-500" />
              <Input
                placeholder="Search vouchers..."
                className="pl-8"
                onChange={(e) => {
                  const searchTerm = e.target.value.toLowerCase();
                  if (searchTerm) {
                    // First fetch all vouchers, then filter locally
                    setIsLoading(true);
                    fetchVouchers()
                      .then(() => {
                        setVoucherList(prev =>
                          prev.filter(voucher =>
                            (voucher.voucherNo && voucher.voucherNo.toLowerCase().includes(searchTerm)) ||
                            (voucher.paidTo && voucher.paidTo.toLowerCase().includes(searchTerm)) ||
                            (voucher.purpose && voucher.purpose.toLowerCase().includes(searchTerm))
                          )
                        );
                      })
                      .catch(error => {
                        console.error('Error during search:', error);
                        toast({
                          title: 'Search Error',
                          description: 'An error occurred while searching. Please try again.',
                          variant: 'destructive',
                        });
                      })
                      .finally(() => {
                        setIsLoading(false);
                      });
                  } else {
                    // If search term is empty, fetch all vouchers
                    fetchVouchers();
                  }
                }}
              />
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  // Create a safer print function that doesn't manipulate the current DOM
                  const printContent = () => {
                    // Open a new window for printing
                    const printWindow = window.open('', '_blank');

                    if (!printWindow) {
                      toast({
                        title: "Print Error",
                        description: "Unable to open print window. Please check your popup blocker settings.",
                        variant: "destructive",
                      });
                      return;
                    }

                    // Generate the HTML content for the print window
                    const printContent = `
                      <!DOCTYPE html>
                      <html>
                        <head>
                          <title>Journal Voucher History</title>
                          <meta charset="utf-8">
                          <meta name="viewport" content="width=device-width, initial-scale=1">
                          <style>
                            body {
                              font-family: Arial, sans-serif;
                              margin: 0;
                              padding: 20px;
                            }
                            h1 {
                              text-align: center;
                              font-size: 24px;
                              margin-bottom: 20px;
                            }
                            table {
                              width: 100%;
                              border-collapse: collapse;
                              margin-bottom: 20px;
                            }
                            th, td {
                              padding: 10px;
                              text-align: left;
                              border: 1px solid #ddd;
                            }
                            th {
                              background-color: #f3f4f6;
                              font-weight: bold;
                            }
                            .status-badge {
                              padding: 2px 6px;
                              border-radius: 4px;
                              font-size: 12px;
                            }
                            .status-approved {
                              background-color: #dcfce7;
                              color: #166534;
                            }
                            .status-rejected {
                              background-color: #fee2e2;
                              color: #991b1b;
                            }
                            .status-pending {
                              background-color: #fef9c3;
                              color: #854d0e;
                            }
                            .timestamp {
                              text-align: right;
                              margin-top: 20px;
                              font-size: 12px;
                            }
                            .capitalize {
                              text-transform: capitalize;
                            }
                          </style>
                        </head>
                        <body>
                          <h1>Journal Voucher History</h1>
                          <table>
                            <thead>
                              <tr>
                                <th style="width: 12%;">Voucher No.</th>
                                <th style="width: 10%;">Date</th>
                                <th style="width: 15%;">Paid To</th>
                                <th style="width: 10%;">Account Code</th>
                                <th style="width: 12%;">Amount</th>
                                <th style="width: 12%;">Payment Method</th>
                                <th style="width: 15%;">Reference</th>
                                <th style="width: 10%;">Status</th>
                              </tr>
                            </thead>
                            <tbody>
                              ${voucherList.map(voucher => `
                                <tr>
                                  <td>${voucher.voucherNo}</td>
                                  <td>${new Date(voucher.date).toLocaleDateString()}</td>
                                  <td>${voucher.paidTo}</td>
                                  <td>${voucher.accountCode}</td>
                                  <td>${voucher.amount.toLocaleString('en-US', { style: 'currency', currency: 'USD' })}</td>
                                  <td class="capitalize">${voucher.paymentMethod}</td>
                                  <td>${voucher.paymentMethod === 'cheque' ? voucher.chequeNo : '-'}</td>
                                  <td>
                                    <span class="status-badge status-${voucher.status}">
                                      ${voucher.status.charAt(0).toUpperCase() + voucher.status.slice(1)}
                                    </span>
                                  </td>
                                </tr>
                              `).join('')}
                            </tbody>
                          </table>
                          <div class="timestamp">
                            <p>Printed on: ${new Date().toLocaleString()}</p>
                          </div>
                          <script>
                            // Auto-print when loaded
                            window.onload = function() {
                              window.print();
                              // Close the window after printing (optional)
                              // setTimeout(function() { window.close(); }, 500);
                            };
                          </script>
                        </body>
                      </html>
                    `;

                    // Write the content to the new window
                    printWindow.document.open();
                    printWindow.document.write(printContent);
                    printWindow.document.close();
                  };

                  // Call the print function
                  printContent();
                }}
              >
                <Printer className="h-4 w-4 mr-1" />
                Print
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  // Simple CSV export
                  if (voucherList.length === 0) {
                    toast({
                      title: "No data to export",
                      description: "There are no vouchers to export.",
                      variant: "destructive",
                    });
                    return;
                  }

                  const headers = ["Voucher No.", "Date", "Paid To", "Account Code", "Amount", "Payment Method", "Reference", "Status"];
                  const csvData = voucherList.map(v => [
                    v.voucherNo,
                    new Date(v.date).toLocaleDateString(),
                    v.paidTo,
                    v.accountCode,
                    v.amount.toString(),
                    v.paymentMethod,
                    v.paymentMethod === 'cheque' ? v.chequeNo : '-',
                    v.status
                  ]);

                  const csvContent = [
                    headers.join(','),
                    ...csvData.map(row => row.join(','))
                  ].join('\n');

                  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                  const url = URL.createObjectURL(blob);
                  const link = document.createElement('a');
                  link.setAttribute('href', url);
                  link.setAttribute('download', `journal_vouchers_${new Date().toISOString().split('T')[0]}.csv`);
                  document.body.appendChild(link);
                  link.click();
                  document.body.removeChild(link);
                }}
              >
                <Download className="h-4 w-4 mr-1" />
                Export
              </Button>
            </div>
          </div>

          <div className="border rounded-md overflow-x-auto">
            {isLoading ? (
              <div className="flex items-center justify-center h-64">
                <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
                <span className="ml-2 text-lg">Loading vouchers...</span>
              </div>
            ) : voucherList.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-64 text-gray-500">
                <FileBarChart className="h-12 w-12 mb-2 opacity-20" />
                <p>No vouchers found</p>
                <p className="text-sm">Create a new voucher to see it here</p>
              </div>
            ) : (
              <div className="min-w-[1000px]">
                <Table className="w-full table-fixed border-separate border-spacing-0">
                  <TableHeader>
                    <TableRow className="bg-gray-50 dark:bg-gray-800">
                      <TableHead className="w-[110px] px-4">Voucher No.</TableHead>
                      <TableHead className="w-[90px] px-4">Date</TableHead>
                      <TableHead className="w-[140px] px-4">Paid To</TableHead>
                      <TableHead className="w-[90px] px-4">Account Code</TableHead>
                      <TableHead className="w-[110px] px-4">Amount</TableHead>
                      <TableHead className="w-[110px] px-4">Payment Method</TableHead>
                      <TableHead className="w-[140px] px-4">Reference</TableHead>
                      <TableHead className="w-[90px] px-4">Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {voucherList.map((voucher) => (
                      <TableRow key={voucher.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                        <TableCell className="font-medium w-[110px] px-4 truncate">{voucher.voucherNo}</TableCell>
                        <TableCell className="w-[90px] px-4">{new Date(voucher.date).toLocaleDateString()}</TableCell>
                        <TableCell className="w-[140px] px-4 truncate">{voucher.paidTo}</TableCell>
                        <TableCell className="w-[90px] px-4">{voucher.accountCode}</TableCell>
                        <TableCell className="w-[110px] px-4">{voucher.amount.toLocaleString('en-US', { style: 'currency', currency: 'USD' })}</TableCell>
                        <TableCell className="capitalize w-[110px] px-4">{voucher.paymentMethod}</TableCell>
                        <TableCell className="w-[140px] px-4">
                          {voucher.paymentMethod === 'cheque' ? voucher.chequeNo : '-'}
                        </TableCell>
                        <TableCell className="w-[90px] px-4">
                          <span className={`status-badge px-2 py-1 rounded-full text-xs font-medium ${
                            voucher.status === 'approved'
                              ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                              : voucher.status === 'rejected'
                              ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                              : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'
                          }`}>
                            {voucher.status.charAt(0).toUpperCase() + voucher.status.slice(1)}
                          </span>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}

import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

/**
 * This API route updates the user roles in the database to match the required roles:
 * 'SUPER_ADMIN', 'ADMIN', 'SUPERVISOR', 'ACCOUNTANT', 'TEACHER'
 * 
 * It will:
 * 1. Map existing user roles to the new roles
 * 2. Update all users with the new roles
 */
export async function GET() {
  try {
    console.log('Starting user role update process')
    
    // Step 1: Get all users
    const users = await prisma.user.findMany()
    console.log(`Found ${users.length} users`)
    
    // Step 2: Define the valid roles
    const validRoles = [
      'SUPER_ADMIN',
      'ADMIN',
      'SUPERVISOR',
      'ACCOUNTANT',
      'TEACHER'
    ]
    
    // Step 3: Map old roles to new roles
    const roleMapping = {
      'ADMIN': 'SUPER_ADMIN',         // Map existing ADMIN to SUPER_ADMIN
      'USER': 'TEACHER',              // Map existing USER to TEACHER
      'STAFF': 'ACCOUNTANT',          // Map existing STAFF to ACCOUNTANT
      'HR_TEACHER': 'TEACHER',        // Map existing HR_TEACHER to TEACHER
      'SUBJECT_TEACHER': 'TEACHER',   // Map existing SUBJECT_TEACHER to TEACHER
      'Administrator': 'SUPER_ADMIN', // Map existing Administrator to SUPER_ADMIN
      'HR Teacher': 'TEACHER',        // Map existing HR Teacher to TEACHER
      'Subject Teacher': 'TEACHER'    // Map existing Subject Teacher to TEACHER
    }
    
    // Step 4: Update users with invalid roles
    const updatedUsers = []
    for (const user of users) {
      const currentRole = user.role
      
      // Check if current role is valid
      if (!validRoles.includes(currentRole)) {
        // Map to new role or default to TEACHER
        const newRole = roleMapping[currentRole] || 'TEACHER'
        
        const updatedUser = await prisma.user.update({
          where: { id: user.id },
          data: { role: newRole }
        })
        
        updatedUsers.push({
          id: updatedUser.id,
          email: updatedUser.email,
          oldRole: currentRole,
          newRole: updatedUser.role
        })
        
        console.log(`Updated user ${user.email} role from ${currentRole} to ${newRole}`)
      }
    }
    
    return NextResponse.json({
      success: true,
      message: 'User roles updated successfully',
      usersUpdated: updatedUsers.length,
      updatedUsers
    })
    
  } catch (error) {
    console.error('Error updating user roles:', error)
    return NextResponse.json(
      { error: 'Failed to update user roles', details: error.message },
      { status: 500 }
    )
  }
}

import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { startOfWeek, endOfWeek, startOfMonth, endOfMonth, format, parseISO } from 'date-fns'

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url)
  const className = searchParams.get('class')
  const reportType = searchParams.get('type') // daily, weekly, monthly, semester
  const date = searchParams.get('date')
  const week = searchParams.get('week')
  const month = searchParams.get('month')
  const semester = searchParams.get('semester')
  const academicYear = searchParams.get('academicYear') || '2024-2025' // Default to current academic year

  if (!className) {
    return NextResponse.json(
      { error: 'Class name is required' },
      { status: 400 }
    )
  }

  try {

    let attendanceRecords;
    let dateRange = {};
    let startDateValue = '';
    let endDateValue = '';

    // Get students in the class for calculating total expected attendance
    const students = await prisma.student.findMany({
      where: {
        className,
      },
      select: {
        id: true,
        name: true,
      },
    })

    // If no students found, return empty report
    if (students.length === 0) {
      return NextResponse.json({
        className,
        reportType,
        dateRange: {},
        summary: {
          totalStudents: 0,
          totalRecords: 0,
          presentCount: 0,
          absentCount: 0,
          permissionCount: 0,
          attendanceRate: 0,
        },
        dailyBreakdown: [],
        studentBreakdown: [],
      })
    }

    const totalStudents = students.length

    switch (reportType) {
      case 'daily':
        if (!date) {
          return NextResponse.json(
            { error: 'Date is required for daily report' },
            { status: 400 }
          )
        }

        attendanceRecords = await prisma.attendance.findMany({
          where: {
            className,
            date,
          },
          include: {
            student: {
              select: {
                name: true,
              },
            },
          },
        })

        dateRange = { start: date, end: date }
        break

      case 'weekly':
        if (!week) {
          return NextResponse.json(
            { error: 'Week range is required for weekly report' },
            { status: 400 }
          )
        }

        // Parse week range (format: "2023-01-01_2023-01-07")
        const weekParts = week.split('_')
        if (weekParts.length !== 2) {
          return NextResponse.json(
            { error: 'Invalid week format. Expected format: YYYY-MM-DD_YYYY-MM-DD' },
            { status: 400 }
          )
        }

        const [startDateStr, endDateStr] = weekParts

        attendanceRecords = await prisma.attendance.findMany({
          where: {
            className,
            date: {
              gte: startDateStr,
              lte: endDateStr,
            },
          },
          include: {
            student: {
              select: {
                name: true,
              },
            },
          },
        })

        dateRange = { start: startDateStr, end: endDateStr }
        break

      case 'monthly':
        if (!month) {
          return NextResponse.json(
            { error: 'Month is required for monthly report' },
            { status: 400 }
          )
        }

        // Parse month (format: "2023-01")
        const monthParts = month.split('-')
        if (monthParts.length !== 2 || !/^\d{4}-\d{2}$/.test(month)) {
          return NextResponse.json(
            { error: 'Invalid month format. Expected format: YYYY-MM' },
            { status: 400 }
          )
        }

        const [year, monthNum] = monthParts
        startDateValue = format(startOfMonth(new Date(parseInt(year), parseInt(monthNum) - 1, 1)), 'yyyy-MM-dd')
        endDateValue = format(endOfMonth(new Date(parseInt(year), parseInt(monthNum) - 1, 1)), 'yyyy-MM-dd')

        attendanceRecords = await prisma.attendance.findMany({
          where: {
            className,
            date: {
              gte: startDateValue,
              lte: endDateValue,
            },
          },
          include: {
            student: {
              select: {
                name: true,
              },
            },
          },
        })

        dateRange = { start: startDateValue, end: endDateValue }
        break

      case 'semester':
        if (!semester) {
          return NextResponse.json(
            { error: 'Semester is required for semester report' },
            { status: 400 }
          )
        }

        // Define semester date ranges based on the academic calendar
        // First semester: September 1 to January 30
        // Second semester: February 1 to June 30
        const academicStartYear = parseInt(academicYear.split('-')[0])

        if (semester === 'first') {
          // First semester: September 1 to January 30 (next year)
          startDateValue = `${academicStartYear}-09-01`
          endDateValue = `${academicStartYear + 1}-01-30`
        } else if (semester === 'second') {
          // Second semester: February 1 to June 30 (next year)
          startDateValue = `${academicStartYear + 1}-02-01`
          endDateValue = `${academicStartYear + 1}-06-30`
        } else {
          return NextResponse.json(
            { error: 'Invalid semester value. Use "first" or "second"' },
            { status: 400 }
          )
        }

        attendanceRecords = await prisma.attendance.findMany({
          where: {
            className,
            date: {
              gte: startDateValue,
              lte: endDateValue,
            },
          },
          include: {
            student: {
              select: {
                name: true,
              },
            },
          },
        })

        dateRange = { start: startDateValue, end: endDateValue }
        break

      default:
        return NextResponse.json(
          { error: 'Invalid report type' },
          { status: 400 }
        )
    }

    // Check if we have any attendance records
    if (!attendanceRecords || attendanceRecords.length === 0) {
      return NextResponse.json({
        className,
        reportType,
        dateRange,
        summary: {
          totalStudents,
          totalRecords: 0,
          presentCount: 0,
          absentCount: 0,
          permissionCount: 0,
          attendanceRate: 0,
        },
        dailyBreakdown: [],
        studentBreakdown: [],
      })
    }

    // Calculate statistics
    const totalRecords = attendanceRecords.length
    const presentCount = attendanceRecords.filter(record => record.status === 'present').length
    const absentCount = attendanceRecords.filter(record => record.status === 'absent').length
    const permissionCount = attendanceRecords.filter(record => record.status === 'permission').length

    // Group by date for daily breakdown
    const dailyBreakdown = attendanceRecords.reduce((acc, record) => {
      if (!acc[record.date]) {
        acc[record.date] = {
          date: record.date,
          total: 0,
          present: 0,
          absent: 0,
          permission: 0,
        }
      }

      acc[record.date].total++

      if (record.status === 'present') {
        acc[record.date].present++
      } else if (record.status === 'absent') {
        acc[record.date].absent++
      } else if (record.status === 'permission') {
        acc[record.date].permission++
      }

      return acc
    }, {})

    // Group by student for individual breakdown
    const studentBreakdown = attendanceRecords.reduce((acc, record) => {
      const studentId = record.studentId

      if (!acc[studentId]) {
        acc[studentId] = {
          studentId,
          studentName: record.student.name,
          total: 0,
          present: 0,
          absent: 0,
          permission: 0,
          dates: {},
        }
      }

      acc[studentId].total++

      if (record.status === 'present') {
        acc[studentId].present++
      } else if (record.status === 'absent') {
        acc[studentId].absent++
      } else if (record.status === 'permission') {
        acc[studentId].permission++
      }

      // Track status by date
      acc[studentId].dates[record.date] = record.status

      return acc
    }, {})

    // Calculate attendance rate
    const attendanceRate = totalRecords > 0
      ? Math.round((presentCount / totalRecords) * 100)
      : 0

    return NextResponse.json({
      className,
      reportType,
      dateRange,
      summary: {
        totalStudents,
        totalRecords,
        presentCount,
        absentCount,
        permissionCount,
        attendanceRate,
      },
      dailyBreakdown: Object.values(dailyBreakdown),
      studentBreakdown: Object.values(studentBreakdown),
    })
  } catch (error) {
    console.error('Error generating attendance report:', error)
    return NextResponse.json(
      { error: 'Failed to generate attendance report' },
      { status: 500 }
    )
  }
}

import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { cookies } from 'next/headers'
import { verifyJWT } from '@/lib/jwt'

// GET test endpoint for parent functionality
export async function GET(request: Request) {
  try {
    console.log('Parent test API called')
    
    // Verify authentication
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    if (!token) {
      console.log('No token found')
      return NextResponse.json({
        error: 'No token found',
        authenticated: false
      })
    }

    const decoded = await verifyJWT(token)
    console.log('Decoded token:', { id: decoded.id, role: decoded.role, email: decoded.email })
    
    // Check if user has PARENT role
    const isParent = decoded.role === 'PARENT'
    
    // Get all parent-student relationships for this user
    let relationships = []
    if (isParent) {
      relationships = await prisma.parentStudent.findMany({
        where: { parentId: decoded.id },
        include: {
          student: {
            select: {
              id: true,
              sid: true,
              name: true,
              className: true
            }
          },
          parent: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      })
    }

    // Get all students for reference
    const allStudents = await prisma.student.findMany({
      select: {
        id: true,
        sid: true,
        name: true,
        className: true
      },
      take: 5 // Just first 5 for testing
    })

    // Get all parent users
    const parentUsers = await prisma.user.findMany({
      where: { role: 'PARENT' },
      select: {
        id: true,
        name: true,
        email: true,
        role: true
      }
    })

    return NextResponse.json({
      authenticated: true,
      user: {
        id: decoded.id,
        email: decoded.email,
        role: decoded.role
      },
      isParent,
      relationships,
      relationshipCount: relationships.length,
      allStudents,
      parentUsers,
      message: 'Parent test API working'
    })
  } catch (error) {
    console.error('Error in parent test API:', error)
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Unknown error',
      authenticated: false
    })
  }
}

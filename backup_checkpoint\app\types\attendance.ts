export type AttendanceStatus = 'present' | 'absent' | 'permission'

export interface Student {
  id: string
  name: string
  className: string
}

export interface AttendanceRecord {
  id: string
  date: string
  studentId: string
  className: string
  status: AttendanceStatus
  createdAt: string
  updatedAt: string
}

export interface WeekRange {
  label: string
  startDate: Date
  endDate: Date
}

export interface AttendanceStats {
  total: number
  present: number
  absent: number
  permission: number
  presentPercentage: number
  absentPercentage: number
  permissionPercentage: number
}
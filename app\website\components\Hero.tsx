"use client";

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { Button } from '@/app/components/ui/button';
import { useRouter } from 'next/navigation';
import { useAppSettings } from '@/app/contexts/app-settings-context';

// Define the slide interface
interface HeroSlide {
  id: string;
  title: string;
  subtitle: string;
  imageUrl: string;
  ctaText: string;
  ctaLink: string;
  isActive: boolean;
  order: number;
}

// Default fallback image URL
const FALLBACK_IMAGE_URL = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9IiMwMDdiZmYiLz48dGV4dCB4PSI1MCUiIHk9IjUwJSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjI0IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkhlcm8gSW1hZ2U8L3RleHQ+PC9zdmc+';

// Legacy fallback slides (replaced by dynamic ones in component)
const fallbackSlides = [
  {
    id: '1',
    title: 'Welcome to Alfalah Islamic School',
    subtitle: 'Nurturing minds, enriching souls, building futures',
    imageUrl: '/images/banner.jpg',
    ctaText: 'Apply Now',
    ctaLink: '/dashboard',
    isActive: true,
    order: 0
  },
  {
    id: '2',
    title: 'Academic Excellence',
    subtitle: 'Providing quality education with Islamic values since 1995',
    imageUrl: '/images/h_slide.jpg',
    ctaText: 'Learn More',
    ctaLink: '/website/academics',
    isActive: true,
    order: 1
  },
  {
    id: '3',
    title: 'Academic Excellence',
    subtitle: 'Providing quality education with Islamic values since 1995',
    imageUrl: '/images/h_slide2.jpg',
    ctaText: 'Learn More',
    ctaLink: '/website/academics',
    isActive: true,
    order: 1
  },
  {
    id: '4',
    title: 'Academic Excellence',
    subtitle: 'Providing quality education with Islamic values since 1995',
    imageUrl: '/images/banner2.jpg',
    ctaText: 'Learn More',
    ctaLink: '/website/academics',
    isActive: true,
    order: 1
  },
  {
    id: '5',
    title: 'Academic Excellence',
    subtitle: 'Providing quality education with Islamic values since 1995',
    imageUrl: '/images/h_slide3.jpg',
    ctaText: 'Learn More',
    ctaLink: '/website/academics',
    isActive: true,
    order: 1
  },
  {
    id: '6',
    title: 'Join Our Community',
    subtitle: 'A supportive environment for students to thrive',
    imageUrl: '/images/h_slide4.jpg',
    ctaText: 'Contact Us',
    ctaLink: '/website/contact',
    isActive: true,
    order: 2
  }
];

export default function Hero() {
  const { settings } = useAppSettings();
  const [slides, setSlides] = useState<HeroSlide[]>([]);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  // Create dynamic fallback slides based on settings
  const getDynamicFallbackSlides = () => [
    {
      id: '1',
      title: `Welcome to ${settings.schoolName}`,
      subtitle: 'Nurturing minds, enriching souls, building futures',
      imageUrl: '/images/banner.jpg',
      ctaText: 'Apply Now',
      ctaLink: '/dashboard',
      isActive: true,
      order: 0
    },
    {
      id: '2',
      title: 'Academic Excellence',
      subtitle: 'Providing quality education with Islamic values since 1995',
      imageUrl: '/images/h_slide2.jpg',
      ctaText: 'Learn More',
      ctaLink: '/website/academics',
      isActive: true,
      order: 1
    },
    {
      id: '4',
      title: 'Academic Excellence',
      subtitle: 'Providing quality education with Islamic values since 1995',
      imageUrl: '/images/banner2.jpg',
      ctaText: 'Learn More',
      ctaLink: '/website/academics',
      isActive: true,
      order: 1
    },
    {
      id: '5',
      title: 'Academic Excellence',
      subtitle: 'Providing quality education with Islamic values since 1995',
      imageUrl: '/images/h_slide3.jpg',
      ctaText: 'Learn More',
      ctaLink: '/website/academics',
      isActive: true,
      order: 1
    },
    {
      id: '6',
      title: 'Join Our Community',
      subtitle: 'A supportive environment for students to thrive',
      imageUrl: '/images/h_slide4.jpg',
      ctaText: 'Contact Us',
      ctaLink: '/website/contact',
      isActive: true,
      order: 2
    }
  ];

  // Initialize slides with dynamic fallback and fetch from API
  useEffect(() => {
    const dynamicFallbackSlides = getDynamicFallbackSlides();
    setSlides(dynamicFallbackSlides);

    const fetchSlides = async () => {
      try {
        const response = await fetch('/api/website-management/hero-slides');
        if (response.ok) {
          const data = await response.json();
          // Filter only active slides
          const activeSlides = data.filter((slide: HeroSlide) => slide.isActive);
          if (activeSlides.length > 0) {
            // Ensure all slides have valid image URLs
            const processedSlides = activeSlides.map(slide => ({
              ...slide,
              imageUrl: slide.imageUrl && slide.imageUrl.trim() !== '' ? slide.imageUrl : FALLBACK_IMAGE_URL
            }));
            setSlides(processedSlides);
          }
        }
      } catch (error) {
        console.error('Error fetching hero slides:', error);
        // Keep the dynamic fallback slides on error
      } finally {
        setIsLoading(false);
      }
    };

    fetchSlides();
  }, [settings.schoolName]); // Re-run when school name changes

  // Auto-advance slides
  useEffect(() => {
    if (slides.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev === slides.length - 1 ? 0 : prev + 1));
    }, 5000);

    return () => clearInterval(interval);
  }, [slides.length]);

  // Handle CTA button click
  const handleCtaClick = (link: string) => {
    router.push(link);
  };

  return (
    <div className="relative h-screen w-full overflow-hidden">
      {/* Slides */}
      {slides.map((slide, index) => (
        <motion.div
          key={slide.id}
          className="absolute inset-0"
          initial={{ opacity: 0 }}
          animate={{
            opacity: currentSlide === index ? 1 : 0,
            zIndex: currentSlide === index ? 10 : 0,
          }}
          transition={{ duration: 1 }}
        >
          {/* Background Image */}
          <div className="absolute inset-0 bg-black/50 z-10" />
          <Image
            src={slide.imageUrl || FALLBACK_IMAGE_URL}
            alt={slide.title}
            fill
            className="object-cover"
            priority={index === 0}
          />

          {/* Content */}
          <div className="absolute inset-0 z-20 flex items-center justify-center">
            <div className="container mx-auto px-4 text-center text-white">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{
                  opacity: currentSlide === index ? 1 : 0,
                  y: currentSlide === index ? 0 : 20,
                }}
                transition={{ duration: 0.8, delay: 0.3 }}
                className="max-w-3xl mx-auto"
              >
                <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-4">{slide.title}</h1>
                <p className="text-xl md:text-2xl mb-8 text-gray-200">{slide.subtitle}</p>
                <Button
                  onClick={() => handleCtaClick(slide.ctaLink)}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 text-lg rounded-md"
                >
                  {slide.ctaText}
                </Button>
              </motion.div>
            </div>
          </div>
        </motion.div>
      ))}

      {/* Slide Indicators */}
      <div className="absolute bottom-10 left-0 right-0 z-30 flex justify-center space-x-2">
        {slides.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentSlide(index)}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              currentSlide === index ? 'bg-white w-8' : 'bg-white/50'
            }`}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div>
    </div>
  );
}

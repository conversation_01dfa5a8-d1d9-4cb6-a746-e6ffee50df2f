'use client'

import { useAuth } from '@/app/contexts/auth-context'
import { useQuery } from '@tanstack/react-query'
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/ui/card'

export default function ParentDebugPage() {
  const { user } = useAuth()

  // Test API call
  const { data: apiTest, error: apiError } = useQuery({
    queryKey: ['parent-debug'],
    queryFn: async () => {
      const res = await fetch('/api/parent/children')
      const data = await res.json()
      return { status: res.status, data }
    },
    enabled: !!user
  })

  // Test parent functionality
  const { data: parentTest } = useQuery({
    queryKey: ['parent-test'],
    queryFn: async () => {
      const res = await fetch('/api/parent/test')
      return res.json()
    },
    enabled: !!user
  })

  // Test parent-student relationships
  const { data: relationships } = useQuery({
    queryKey: ['parent-relationships'],
    queryFn: async () => {
      const res = await fetch('/api/parent-students')
      if (res.ok) {
        return res.json()
      }
      return { error: 'Failed to fetch', status: res.status }
    },
    enabled: !!user
  })

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Parent Debug Information</h1>
      
      <Card>
        <CardHeader>
          <CardTitle>Current User</CardTitle>
        </CardHeader>
        <CardContent>
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
            {JSON.stringify(user, null, 2)}
          </pre>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Parent Test - /api/parent/test</CardTitle>
        </CardHeader>
        <CardContent>
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
            {JSON.stringify(parentTest, null, 2)}
          </pre>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>API Test - /api/parent/children</CardTitle>
        </CardHeader>
        <CardContent>
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
            {JSON.stringify(apiTest, null, 2)}
          </pre>
          {apiError && (
            <div className="mt-4 p-4 bg-red-100 rounded">
              <p className="text-red-700">Error: {apiError.message}</p>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Parent-Student Relationships</CardTitle>
        </CardHeader>
        <CardContent>
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
            {JSON.stringify(relationships, null, 2)}
          </pre>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Browser Cookies</CardTitle>
        </CardHeader>
        <CardContent>
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
            {typeof document !== 'undefined' ? document.cookie : 'Server-side rendering'}
          </pre>
        </CardContent>
      </Card>
    </div>
  )
}

# Fixing the "too much recursion" Error in Radix UI Dialog Components

This document explains the issue and how to fix the "too much recursion" error that occurs with Radix UI dialog components.

## The Problem

The error occurs due to conflicting versions of `@radix-ui/react-focus-scope` being used by different Radix UI components. This creates an infinite loop in the focus management system when multiple dialogs or components with focus trapping are used together.

Error stack trace:
```
Uncaught InternalError: too much recursion
    focus webpack-internal:///(app-pages-browser)/./node_modules/@radix-ui/react-dialog/node_modules/@radix-ui/react-focus-scope/dist/index.mjs:235
    handleFocusIn2 webpack-internal:///(app-pages-browser)/./node_modules/@radix-ui/react-dialog/node_modules/@radix-ui/react-focus-scope/dist/index.mjs:50
    focus webpack-internal:///(app-pages-browser)/./node_modules/@radix-ui/react-focus-scope/dist/index.mjs:235
    handleFocusOut2 webpack-internal:///(app-pages-browser)/./node_modules/@radix-ui/react-focus-scope/dist/index.mjs:59
    focus webpack-internal:///(app-pages-browser)/./node_modules/@radix-ui/react-dialog/node_modules/@radix-ui/react-focus-scope/dist/index.mjs:235
    handleFocusIn2 webpack-internal:///(app-pages-browser)/./node_modules/@radix-ui/react-dialog/node_modules/@radix-ui/react-focus-scope/dist/index.mjs:50
    focus webpack-internal:///(app-pages-browser)/./node_modules/@radix-ui/react-focus-scope/dist/index.mjs:235
```

## The Solution

We've implemented several fixes to resolve this issue:

1. Created a `RecursionSafeDialog.tsx` component that doesn't use Radix UI's focus trapping
2. Updated the `SafeDialog.tsx` component to use our new implementation
3. Enhanced the `useDialogState` hook to better manage dialog state transitions
4. Added package resolution settings to ensure consistent versions of Radix UI packages

## How to Use the Fix

### Option 1: Use RecursionSafeDialog

Replace problematic Radix UI Dialog components with our custom `RecursionSafeDialog`:

```tsx
import { RecursionSafeDialog } from './components/RecursionSafeDialog';

function MyComponent() {
  const [open, setOpen] = useState(false);
  
  return (
    <RecursionSafeDialog
      open={open}
      onOpenChange={setOpen}
      title="My Dialog"
      description="This dialog won't cause recursion issues"
    >
      Dialog content goes here
    </RecursionSafeDialog>
  );
}
```

### Option 2: Use SafeDialog

Use our updated `SafeDialog` component which wraps `RecursionSafeDialog`:

```tsx
import { SafeDialog } from './components/SafeDialog';

function MyComponent() {
  const [open, setOpen] = useState(false);
  
  return (
    <SafeDialog
      open={open}
      onOpenChange={setOpen}
      title="My Dialog"
      description="This dialog won't cause recursion issues"
    >
      Dialog content goes here
    </SafeDialog>
  );
}
```

### Option 3: Use useDialogState Hook

Use our enhanced `useDialogState` hook to manage dialog state:

```tsx
import { useDialogState } from './hooks/useDialogState';
import { RecursionSafeDialog } from './components/RecursionSafeDialog';

function MyComponent() {
  const dialogState = useDialogState();
  
  return (
    <>
      <button onClick={dialogState.open}>Open Dialog</button>
      
      <RecursionSafeDialog
        open={dialogState.isOpen}
        onOpenChange={dialogState.handleOpenChange}
        title="My Dialog"
      >
        Dialog content
        <button onClick={dialogState.close}>Close</button>
      </RecursionSafeDialog>
    </>
  );
}
```

## Package Resolution

We've added package resolution settings to ensure consistent versions of Radix UI packages:

1. Added `.npmrc` file with:
```
public-hoist-pattern[]=*@radix-ui/*
shamefully-hoist=true
```

2. Added resolutions field to `package.json`:
```json
"resolutions": {
  "@radix-ui/react-focus-scope": "1.1.6",
  "@radix-ui/react-dialog": "1.1.13"
}
```

After making these changes, run:
```
npm install
```

This will ensure all components use the same version of the Radix UI packages, preventing version conflicts that lead to recursion issues.

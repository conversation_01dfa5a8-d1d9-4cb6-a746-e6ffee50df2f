"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { FaCalendarAlt, FaClock, FaArrowRight } from 'react-icons/fa';
import { Button } from '@/app/components/ui/button';
import { useRouter } from 'next/navigation';

// Define the news/event interface
interface NewsEvent {
  id: string;
  title: string;
  content: string;
  excerpt: string;
  imageUrl: string;
  date: string;
  category: 'news' | 'event';
  tags: string | null;
  isActive: boolean;
}

// Fallback news data
const fallbackNewsItems = [
  {
    id: '1',
    title: 'New Science Lab Opening',
    excerpt: 'Alfalah Islamic School opens a new state-of-the-art science laboratory to enhance STEM education.',
    content: 'We are excited to announce the opening of our new state-of-the-art science laboratory.',
    imageUrl: '/images/banner5.jpg',
    date: '2023-09-15',
    category: 'news',
    tags: 'science,laboratory,stem',
    isActive: true
  },
  {
    id: '2',
    title: 'Annual Quran Competition Results',
    excerpt: 'Students showcase their Quran memorization and recitation skills in our annual competition.',
    content: 'Congratulations to all participants in our Annual Quran Competition!',
    imageUrl: '/images/banner6.jpg',
    date: '2023-08-20',
    category: 'news',
    tags: 'quran,competition,achievement',
    isActive: true
  },
  {
    id: '3',
    title: 'School Receives Excellence Award',
    excerpt: 'Our school has been recognized for excellence in Islamic education.',
    content: 'We are proud to announce that our school has received the Excellence in Islamic Education Award.',
    imageUrl: '/images/logo.png',
    date: '2023-07-15',
    category: 'news',
    tags: 'award,achievement,recognition',
    isActive: true
  }
];

// Fallback events data
const fallbackEvents = [
  {
    id: '4',
    title: 'Back to School Night',
    excerpt: 'Parents are invited to meet teachers and learn about the curriculum for the new school year.',
    content: 'Join us for our Back to School Night on September 5th at 6:00 PM.',
    imageUrl: '/images/banner.jpg',
    date: '2023-09-05',
    category: 'event',
    tags: 'parents,teachers,curriculum',
    isActive: true
  },
  {
    id: '5',
    title: 'Upcoming Field Trip to Science Museum',
    excerpt: 'Students will explore interactive exhibits at the Science Museum to enhance their learning.',
    content: 'Our 5th and 6th grade students will be visiting the Science Museum on October 10th.',
    imageUrl: '/images/banner2.jpg',
    date: '2023-10-10',
    category: 'event',
    tags: 'field trip,science,museum',
    isActive: true
  },
  {
    id: '6',
    title: 'Parent-Teacher Conference',
    excerpt: 'Schedule your meeting with teachers to discuss your child\'s progress.',
    content: 'Parent-Teacher conferences will be held on October 20th.',
    imageUrl: '/images/banner3.jpg',
    date: '2023-10-20',
    category: 'event',
    tags: 'parents,teachers,conference',
    isActive: true
  },
  {
    id: '7',
    title: 'Annual Sports Day',
    excerpt: 'Join us for a day of sports and fun activities.',
    content: 'Our Annual Sports Day will be held on November 5th.',
    imageUrl: '/images/banner4.jpg',
    date: '2023-11-05',
    category: 'event',
    tags: 'sports,activities,competition',
    isActive: true
  }
];

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.5 },
  },
};

// Format date
const formatDate = (dateString: string) => {
  const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'long', day: 'numeric' };
  return new Date(dateString).toLocaleDateString('en-US', options);
};

export default function NewsEvents() {
  const router = useRouter();
  const [newsItems, setNewsItems] = useState<NewsEvent[]>(fallbackNewsItems);
  const [upcomingEvents, setUpcomingEvents] = useState<NewsEvent[]>(fallbackEvents);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch news and events from API
  useEffect(() => {
    const fetchNewsEvents = async () => {
      try {
        // Fetch news
        const newsResponse = await fetch('/api/website-management/news-events?category=news');
        // Fetch events
        const eventsResponse = await fetch('/api/website-management/news-events?category=event');

        if (newsResponse.ok && eventsResponse.ok) {
          const newsData = await newsResponse.json();
          const eventsData = await eventsResponse.json();

          // Filter active items and sort by date (newest first for news, upcoming first for events)
          const activeNews = newsData
            .filter((item: NewsEvent) => item.isActive)
            .sort((a: NewsEvent, b: NewsEvent) => new Date(b.date).getTime() - new Date(a.date).getTime())
            .slice(0, 3); // Limit to 3 news items

          const activeEvents = eventsData
            .filter((item: NewsEvent) => item.isActive)
            .sort((a: NewsEvent, b: NewsEvent) => new Date(a.date).getTime() - new Date(b.date).getTime())
            .slice(0, 4); // Limit to 4 events

          if (activeNews.length > 0) {
            setNewsItems(activeNews);
          }

          if (activeEvents.length > 0) {
            setUpcomingEvents(activeEvents);
          }
        }
      } catch (error) {
        console.error('Error fetching news and events:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchNewsEvents();
  }, []);

  // Extract time from event content (simple heuristic)
  const extractTimeFromContent = (content: string): string => {
    // Look for time patterns like "X:XX PM" or "X:XX AM"
    const timeRegex = /\b(\d{1,2}:\d{2}\s*(?:AM|PM|am|pm))\s*-\s*(\d{1,2}:\d{2}\s*(?:AM|PM|am|pm))\b/;
    const match = content.match(timeRegex);

    if (match) {
      return match[0];
    }

    // Default time if not found
    return "Time TBA";
  };

  // Extract location from content (simple heuristic)
  const extractLocationFromContent = (content: string): string => {
    // Look for location indicators
    if (content.includes("location:") || content.includes("Location:")) {
      const locationRegex = /(?:location:|Location:)\s*([^\.]+)/;
      const match = content.match(locationRegex);
      if (match) {
        return match[1].trim();
      }
    }

    // Default location if not found
    return "School Campus";
  };

  // Show loading state
  if (isLoading) {
    return (
      <section className="py-16 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4 flex justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 bg-white dark:bg-gray-900">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">News & Upcoming Events</h2>
          <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Stay updated with the latest news and events happening at Alfalah Islamic School.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* News Section */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="lg:col-span-2 space-y-8"
          >
            <div className="flex items-center justify-between">
              <h3 className="text-2xl font-semibold text-gray-900 dark:text-white">Latest News</h3>
              <Link href="/website/news-events" className="text-blue-600 dark:text-blue-400 flex items-center hover:underline">
                View All <FaArrowRight className="ml-1 text-sm" />
              </Link>
            </div>

            <div className="space-y-6">
              {newsItems.map((item) => (
                <motion.div
                  key={item.id}
                  variants={itemVariants}
                  className="bg-gray-50 dark:bg-gray-800 rounded-lg overflow-hidden shadow-md flex flex-col md:flex-row"
                >
                  <div className="relative w-full md:w-1/3 h-48 md:h-auto">
                    <Image
                      src={item.imageUrl}
                      alt={item.title}
                      fill
                      className="object-cover"
                    />
                    <div className="absolute top-2 left-2 bg-blue-600 text-white text-xs px-2 py-1 rounded">
                      News
                    </div>
                  </div>
                  <div className="p-4 md:p-6 flex-1">
                    <div className="flex items-center text-gray-500 dark:text-gray-400 text-sm mb-2">
                      <FaCalendarAlt className="mr-1" />
                      <span>{formatDate(item.date)}</span>
                    </div>
                    <h4 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">{item.title}</h4>
                    <p className="text-gray-600 dark:text-gray-300 mb-4">{item.excerpt}</p>
                    <Link href={`/website/news-events/${item.id}`} className="text-blue-600 dark:text-blue-400 hover:underline">
                      Read More →
                    </Link>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Events Section */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="space-y-8"
          >
            <div className="flex items-center justify-between">
              <h3 className="text-2xl font-semibold text-gray-900 dark:text-white">Upcoming Events</h3>
              <Link href="/website/news-events#events" className="text-blue-600 dark:text-blue-400 flex items-center hover:underline">
                View All <FaArrowRight className="ml-1 text-sm" />
              </Link>
            </div>

            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg shadow-md p-6">
              <div className="space-y-4">
                {upcomingEvents.map((event) => (
                  <motion.div
                    key={event.id}
                    variants={itemVariants}
                    className="border-b border-gray-200 dark:border-gray-700 last:border-0 pb-4 last:pb-0"
                  >
                    <h4 className="text-lg font-semibold mb-2 text-gray-900 dark:text-white">{event.title}</h4>
                    <div className="flex items-center text-gray-600 dark:text-gray-300 text-sm mb-1">
                      <FaCalendarAlt className="mr-2 text-blue-600 dark:text-blue-400" />
                      <span>{formatDate(event.date)}</span>
                    </div>
                    <div className="flex items-center text-gray-600 dark:text-gray-300 text-sm mb-1">
                      <FaClock className="mr-2 text-blue-600 dark:text-blue-400" />
                      <span>{extractTimeFromContent(event.content)}</span>
                    </div>
                    <div className="text-gray-600 dark:text-gray-300 text-sm">
                      <span className="font-medium">Location:</span> {extractLocationFromContent(event.content)}
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>

            <div className="text-center pt-4">
              <Button
                onClick={() => router.push('/website/news-events')}
                variant="outline"
                className="w-full"
              >
                View Full Calendar
              </Button>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}

import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET all testimonials
export async function GET() {
  try {
    // Check if prisma is available
    if (!prisma) {
      console.error('Prisma client is not available')
      // Return mock data for development
      return NextResponse.json([
        {
          id: '1',
          name: '<PERSON>',
          role: 'Parent',
          content: 'Alfalah Islamic School has been a blessing for our family. The teachers are dedicated and caring, and the Islamic environment has helped our children grow both academically and spiritually.',
          imageUrl: '/images/portrait.jpg',
          rating: 5,
          isActive: true,
          order: 0,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: '2',
          name: '<PERSON><PERSON>',
          role: 'Alumni',
          content: 'My years at Alfalah Islamic School prepared me well for university and beyond. The strong foundation in both academic subjects and Islamic studies has been invaluable in my personal and professional life.',
          imageUrl: '/images/portrait.jpg',
          rating: 5,
          isActive: true,
          order: 1,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ])
    }

    const testimonials = await prisma.testimonial.findMany({
      orderBy: {
        order: 'asc'
      }
    })

    return NextResponse.json(testimonials)
  } catch (error) {
    console.error('Error fetching testimonials:', error)

    // Return mock data for development
    return NextResponse.json([
      {
        id: '1',
        name: 'Ahmed Hassan',
        role: 'Parent',
        content: 'Alfalah Islamic School has been a blessing for our family. The teachers are dedicated and caring, and the Islamic environment has helped our children grow both academically and spiritually.',
        imageUrl: '/images/portrait.jpg',
        rating: 5,
        isActive: true,
        order: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '2',
        name: 'Fatima Khan',
        role: 'Alumni',
        content: 'My years at Alfalah Islamic School prepared me well for university and beyond. The strong foundation in both academic subjects and Islamic studies has been invaluable in my personal and professional life.',
        imageUrl: '/images/portrait.jpg',
        rating: 5,
        isActive: true,
        order: 1,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ])
  }
}

// POST a new testimonial
export async function POST(request: Request) {
  try {
    const data = await request.json()

    // Validate required fields
    if (!data.name || !data.role || !data.content) {
      return NextResponse.json({
        error: 'Name, role, and content are required'
      }, { status: 400 })
    }

    // Validate rating if provided
    if (data.rating !== undefined) {
      const rating = Number(data.rating)
      if (isNaN(rating) || rating < 1 || rating > 5) {
        return NextResponse.json({
          error: 'Rating must be a number between 1 and 5'
        }, { status: 400 })
      }
    }

    // Get the highest order value to place the new testimonial at the end
    const highestOrder = await prisma.testimonial.findFirst({
      orderBy: {
        order: 'desc'
      },
      select: {
        order: true
      }
    })

    const newOrder = highestOrder ? highestOrder.order + 1 : 0

    // Create the new testimonial
    const testimonial = await prisma.testimonial.create({
      data: {
        name: data.name,
        role: data.role,
        content: data.content,
        imageUrl: data.imageUrl || null,
        rating: data.rating !== undefined ? Number(data.rating) : 5,
        isActive: data.isActive !== undefined ? data.isActive : true,
        order: newOrder
      }
    })

    return NextResponse.json(testimonial)
  } catch (error) {
    console.error('Error creating testimonial:', error)
    return NextResponse.json({
      error: error.message || 'Failed to create testimonial'
    }, { status: 500 })
  }
}

import { NextResponse } from 'next/server'
import { verifyJWT } from '@/lib/jwt'
import { cookies } from 'next/headers'

// GET all subjects
export async function GET() {
  try {
    console.log('Fetching all subjects')
    
    // Verify authentication
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value
    
    if (!token) {
      console.error('No authentication token found')
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    }
    
    try {
      // Verify the token
      const decoded = await verifyJWT(token)
      
      // Mock data for subjects
      const subjects = [
        { id: '1', name: 'Mathematics', code: 'MATH' },
        { id: '2', name: 'Physics', code: 'PHYS' },
        { id: '3', name: 'Chemistry', code: 'CHEM' },
        { id: '4', name: 'Biology', code: 'BIO' },
        { id: '5', name: 'English', code: 'ENG' },
        { id: '6', name: 'Literature', code: 'L<PERSON>' },
        { id: '7', name: 'History', code: 'HIST' },
        { id: '8', name: 'Geography', code: 'GEO' },
        { id: '9', name: 'Physical Education', code: 'PE' },
        { id: '10', name: 'Computer Science', code: 'CS' }
      ]
      
      console.log(`Returning ${subjects.length} subjects`)
      return NextResponse.json(subjects)
    } catch (jwtError) {
      console.error('JWT verification error:', jwtError)
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      )
    }
  } catch (error) {
    console.error('Error fetching subjects:', error)
    return NextResponse.json(
      { error: 'Failed to fetch subjects' },
      { status: 500 }
    )
  }
}

import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { cookies } from 'next/headers'
import { verifyJWT } from '@/lib/jwt'

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    console.log(`GET request for class ID: ${params.id}`)

    // Test database connection
    try {
      await prisma.$queryRaw`SELECT 1`
    } catch (dbError) {
      console.error('GET class: Database connection failed:', dbError)
      return NextResponse.json(
        { error: 'Database connection failed' },
        { status: 500 }
      )
    }

    const cls = await prisma.class.findUnique({
      where: {
        id: params.id,
      },
      include: {
        students: true,
        subjects: true,
      },
    })

    if (!cls) {
      console.error(`GET class: Class with ID ${params.id} not found`)
      return NextResponse.json(
        { error: 'Class not found' },
        { status: 404 }
      )
    }

    console.log(`GET class: Successfully fetched class ${cls.name}`)
    return NextResponse.json(cls)
  } catch (error) {
    console.error('Error fetching class:', error)

    // Provide more specific error messages
    if (error instanceof Error) {
      if (error.message.includes('Invalid `prisma.class.findUnique()`')) {
        return NextResponse.json(
          { error: 'Invalid class ID format' },
          { status: 400 }
        )
      }
    }

    return NextResponse.json(
      { error: 'Failed to fetch class' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Verify authentication and authorization
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    }

    const decoded = await verifyJWT(token)

    // Only allow SUPER_ADMIN, ADMIN, and DATA_ENCODER to edit classes
    if (!['SUPER_ADMIN', 'ADMIN', 'DATA_ENCODER'].includes(decoded.role)) {
      return NextResponse.json(
        { error: 'Forbidden - You do not have permission to edit classes' },
        { status: 403 }
      )
    }

    const data = await request.json()

    // Validate required fields
    if (!data.name) {
      return NextResponse.json({ error: 'Class name is required' }, { status: 400 })
    }

    // Check if class exists
    const existingClass = await prisma.class.findUnique({
      where: { id: params.id }
    })

    if (!existingClass) {
      return NextResponse.json({ error: 'Class not found' }, { status: 404 })
    }

    // Check if new name is already taken by another class
    if (data.name !== existingClass.name) {
      const nameExists = await prisma.class.findUnique({
        where: { name: data.name }
      })
      if (nameExists) {
        return NextResponse.json({ error: 'Class with this name already exists' }, { status: 400 })
      }
    }

    const updatedClass = await prisma.class.update({
      where: {
        id: params.id,
      },
      data: {
        name: data.name,
        // hrTeacherId: data.hrTeacherId === 'none' ? null : data.hrTeacherId, // Commented out as hrTeacher relationship is disabled
        totalSubjects: data.totalSubjects !== undefined ? data.totalSubjects : undefined,
        totalStudents: data.totalStudents !== undefined ? data.totalStudents : undefined,
      },
    })
    return NextResponse.json(updatedClass)
  } catch (error) {
    console.error('Error updating class:', error)
    return NextResponse.json(
      { error: 'Failed to update class' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    console.log(`DELETE request for class ID: ${params.id}`)

    // Verify authentication and authorization
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    if (!token) {
      console.error('DELETE class: No authentication token provided')
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    }

    let decoded
    try {
      decoded = await verifyJWT(token)
      console.log(`DELETE class: User ${decoded.email} (${decoded.role}) attempting to delete class`)
    } catch (jwtError) {
      console.error('DELETE class: JWT verification failed:', jwtError)
      return NextResponse.json(
        { error: 'Unauthorized - Invalid token' },
        { status: 401 }
      )
    }

    // Only allow SUPER_ADMIN, ADMIN, and DATA_ENCODER to delete classes
    if (!['SUPER_ADMIN', 'ADMIN', 'DATA_ENCODER'].includes(decoded.role)) {
      console.error(`DELETE class: User ${decoded.email} with role ${decoded.role} not authorized`)
      return NextResponse.json(
        { error: 'Forbidden - You do not have permission to delete classes' },
        { status: 403 }
      )
    }

    // Test database connection
    try {
      await prisma.$queryRaw`SELECT 1`
    } catch (dbError) {
      console.error('DELETE class: Database connection failed:', dbError)
      return NextResponse.json(
        { error: 'Database connection failed' },
        { status: 500 }
      )
    }

    // Check if class exists and get all related data
    const existingClass = await prisma.class.findUnique({
      where: { id: params.id },
      include: {
        students: true,
        subjects: true,
        classTeachers: true,
        teacherAssignments: true
      }
    })

    if (!existingClass) {
      console.error(`DELETE class: Class with ID ${params.id} not found`)
      return NextResponse.json({ error: 'Class not found' }, { status: 404 })
    }

    console.log(`DELETE class: Found class ${existingClass.name} with:`)
    console.log(`- ${existingClass.students.length} students`)
    console.log(`- ${existingClass.subjects.length} subjects`)
    console.log(`- ${existingClass.classTeachers.length} class teachers`)
    console.log(`- ${existingClass.teacherAssignments.length} teacher assignments`)

    // Check if class has students - this is the only blocker
    if (existingClass.students.length > 0) {
      console.error(`DELETE class: Class ${existingClass.name} has ${existingClass.students.length} students`)
      return NextResponse.json(
        { error: 'Cannot delete class with existing students. Please remove or transfer students first.' },
        { status: 400 }
      )
    }

    // Perform cascade deletion in the correct order
    console.log(`DELETE class: Starting cascade deletion for class ${existingClass.name}`)

    try {
      // Use a transaction to ensure all deletions succeed or fail together
      await prisma.$transaction(async (tx) => {
        // 1. Delete teacher assignments
        if (existingClass.teacherAssignments.length > 0) {
          console.log(`Deleting ${existingClass.teacherAssignments.length} teacher assignments...`)
          await tx.teacherAssignment.deleteMany({
            where: { classId: params.id }
          })
        }

        // 2. Delete class teachers
        if (existingClass.classTeachers.length > 0) {
          console.log(`Deleting ${existingClass.classTeachers.length} class teachers...`)
          await tx.classTeacher.deleteMany({
            where: { classId: params.id }
          })
        }

        // 3. Delete subject teachers for subjects in this class
        if (existingClass.subjects.length > 0) {
          console.log(`Deleting subject teachers for ${existingClass.subjects.length} subjects...`)
          for (const subject of existingClass.subjects) {
            await tx.subjectTeacher.deleteMany({
              where: { subjectId: subject.id }
            })
          }
        }

        // 4. Delete subjects
        if (existingClass.subjects.length > 0) {
          console.log(`Deleting ${existingClass.subjects.length} subjects...`)
          await tx.subject.deleteMany({
            where: { classId: params.id }
          })
        }

        // 5. Delete any teacher permissions for this class
        console.log(`Deleting teacher permissions for class...`)
        await tx.teacherPermission.deleteMany({
          where: { classId: params.id }
        })

        // 6. Finally, delete the class itself
        console.log(`Deleting the class record...`)
        await tx.class.delete({
          where: { id: params.id }
        })
      })
    } catch (transactionError) {
      console.error('Transaction failed during cascade deletion:', transactionError)
      throw new Error('Failed to delete class and related data')
    }

    console.log(`DELETE class: Successfully deleted class ${existingClass.name}`)
    return NextResponse.json({ message: 'Class deleted successfully' })
  } catch (error) {
    console.error('Error deleting class:', error)

    // Provide more specific error messages
    if (error instanceof Error) {
      if (error.message.includes('Foreign key constraint')) {
        return NextResponse.json(
          { error: 'Cannot delete class due to existing references. Please remove all related data first.' },
          { status: 400 }
        )
      }
      if (error.message.includes('Record to delete does not exist')) {
        return NextResponse.json(
          { error: 'Class not found' },
          { status: 404 }
        )
      }
    }

    return NextResponse.json(
      { error: 'Failed to delete class' },
      { status: 500 }
    )
  }
}

'use client'

import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/ui/card'
import { Button } from '@/app/components/ui/button'
import { Badge } from '@/app/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/ui/select'
import { Progress } from '@/app/components/ui/progress'
import { BookOpen, Users, CheckCircle, Clock, XCircle, BarChart3, TrendingUp } from 'lucide-react'
import { ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, Cell } from 'recharts'

interface MarkProgressData {
  className: string
  totalStudents: number
  subjects: {
    name: string
    submittedCount: number
    status: 'not_started' | 'in_progress' | 'completed'
    completionPercentage: number
    lastUpdated?: string
    lastUpdatedBy?: {
      name: string
      email: string
      role: string
    }
    teachers: {
      id: string
      name: string
      email: string
      role: string
    }[]
    teacherCount: number
  }[]
  overallProgress: number
}

export default function MarkProgress() {
  const [selectedClass, setSelectedClass] = useState<string>('all')
  const [selectedTerm, setSelectedTerm] = useState<string>('First Semester')

  // Fetch classes
  const { data: classes } = useQuery({
    queryKey: ['classes'],
    queryFn: async () => {
      const res = await fetch('/api/classes')
      if (!res.ok) throw new Error('Failed to fetch classes')
      return res.json()
    }
  })

  // Fetch mark progress data
  const { data: markProgressData, isLoading } = useQuery({
    queryKey: ['mark-progress', selectedClass, selectedTerm],
    queryFn: async () => {
      const params = new URLSearchParams({
        className: selectedClass,
        term: selectedTerm
      })
      const res = await fetch(`/api/progress/marks?${params}`)
      if (!res.ok) throw new Error('Failed to fetch mark progress')
      return res.json()
    }
  })

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return '#10b981' // green-500
      case 'in_progress': return '#f59e0b' // yellow-500
      case 'not_started': return '#ef4444' // red-500
      default: return '#6b7280' // gray-500
    }
  }

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="w-4 h-4 text-green-600" />
      case 'in_progress': return <Clock className="w-4 h-4 text-yellow-600" />
      case 'not_started': return <XCircle className="w-4 h-4 text-red-600" />
      default: return null
    }
  }

  // Prepare chart data
  const prepareChartData = () => {
    if (!markProgressData?.data) return []
    
    if (selectedClass === 'all') {
      // Show all classes overview
      return markProgressData.data.map((classData: MarkProgressData) => ({
        className: classData.className,
        totalStudents: classData.totalStudents,
        completed: classData.subjects.filter(s => s.status === 'completed').length,
        inProgress: classData.subjects.filter(s => s.status === 'in_progress').length,
        notStarted: classData.subjects.filter(s => s.status === 'not_started').length,
        overallProgress: classData.overallProgress
      }))
    } else {
      // Show subjects for selected class
      const classData = markProgressData.data.find((c: MarkProgressData) => c.className === selectedClass)
      if (!classData) return []
      
      return classData.subjects.map(subject => ({
        subject: subject.name,
        totalStudents: classData.totalStudents,
        submittedCount: subject.submittedCount,
        completionPercentage: subject.completionPercentage,
        status: subject.status
      }))
    }
  }

  const chartData = prepareChartData()

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-500">Loading mark progress...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Filters */}
      <div className="flex flex-wrap gap-4">
        <div className="flex items-center gap-2">
          <label className="text-sm font-medium">Term:</label>
          <Select value={selectedTerm} onValueChange={setSelectedTerm}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="First Semester">First Semester</SelectItem>
              <SelectItem value="Second Semester">Second Semester</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center gap-2">
          <label className="text-sm font-medium">Class:</label>
          <Select value={selectedClass} onValueChange={setSelectedClass}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Classes</SelectItem>
              {classes?.map((cls: any) => (
                <SelectItem key={cls.id} value={cls.name}>
                  {cls.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="w-5 h-5" />
            {selectedClass === 'all' ? 'Class Overview' : `${selectedClass} - Subject Progress`}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey={selectedClass === 'all' ? 'className' : 'subject'} 
                  angle={-45}
                  textAnchor="end"
                  height={80}
                />
                <YAxis />
                <Tooltip 
                  formatter={(value, name) => [value, name]}
                  labelFormatter={(label) => selectedClass === 'all' ? `Class ${label}` : `Subject: ${label}`}
                />
                <Legend />
                
                {selectedClass === 'all' ? (
                  <>
                    <Bar dataKey="completed" stackId="a" fill="#10b981" name="Completed" />
                    <Bar dataKey="inProgress" stackId="a" fill="#f59e0b" name="In Progress" />
                    <Bar dataKey="notStarted" stackId="a" fill="#ef4444" name="Not Started" />
                  </>
                ) : (
                  <Bar dataKey="submittedCount" fill="#3b82f6" name="Students with Marks">
                    {chartData.map((entry: any, index: number) => (
                      <Cell key={`cell-${index}`} fill={getStatusColor(entry.status)} />
                    ))}
                  </Bar>
                )}
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Progress Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {markProgressData?.data?.map((classData: MarkProgressData) => {
          if (selectedClass !== 'all' && selectedClass !== classData.className) return null
          
          return (
            <Card key={classData.className} className="border-l-4 border-l-blue-500">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">Class {classData.className}</CardTitle>
                  <Badge variant="outline" className="flex items-center gap-1">
                    <Users className="w-3 h-3" />
                    {classData.totalStudents} students
                  </Badge>
                </div>
                <div className="flex items-center gap-2">
                  <Progress value={classData.overallProgress} className="flex-1" />
                  <span className="text-sm font-medium">{classData.overallProgress}%</span>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {classData.subjects.map((subject) => (
                    <div key={subject.name} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center gap-3">
                        {getStatusIcon(subject.status)}
                        <div className="flex-1">
                          <p className="font-medium text-sm">{subject.name}</p>
                          <p className="text-xs text-muted-foreground">
                            {subject.submittedCount}/{classData.totalStudents} students
                          </p>
                          {subject.lastUpdatedBy && (
                            <p className="text-xs text-blue-600 mt-1">
                              Last updated by: {subject.lastUpdatedBy.name} ({subject.lastUpdatedBy.role})
                            </p>
                          )}
                          {subject.teachers && subject.teachers.length > 0 && (
                            <div className="mt-1">
                              <p className="text-xs text-gray-600">
                                Data encoders ({subject.teacherCount}): {' '}
                                {subject.teachers.slice(0, 2).map(teacher => teacher.name).join(', ')}
                                {subject.teachers.length > 2 && ` +${subject.teachers.length - 2} more`}
                              </p>
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="text-right">
                        <Badge
                          variant={subject.status === 'completed' ? 'default' :
                                  subject.status === 'in_progress' ? 'secondary' : 'destructive'}
                          className="text-xs"
                        >
                          {subject.completionPercentage}%
                        </Badge>
                        {subject.lastUpdated && (
                          <p className="text-xs text-muted-foreground mt-1">
                            Updated {new Date(subject.lastUpdated).toLocaleDateString()}
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Summary Stats */}
      {selectedClass === 'all' && markProgressData?.summary && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5" />
              Overall Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {markProgressData.summary.completedSubjects}
                </div>
                <p className="text-sm text-muted-foreground">Completed Subjects</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">
                  {markProgressData.summary.inProgressSubjects}
                </div>
                <p className="text-sm text-muted-foreground">In Progress</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">
                  {markProgressData.summary.notStartedSubjects}
                </div>
                <p className="text-sm text-muted-foreground">Not Started</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {markProgressData.summary.overallCompletion}%
                </div>
                <p className="text-sm text-muted-foreground">Overall Progress</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

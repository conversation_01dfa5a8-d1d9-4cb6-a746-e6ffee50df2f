import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { cookies } from 'next/headers'
import { verifyJWT } from '@/lib/jwt'

// GET attendance data for parent's children
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const studentId = searchParams.get('studentId')
    const period = searchParams.get('period') || 'monthly' // daily, weekly, monthly, semester
    const date = searchParams.get('date') // specific date for daily view
    const month = searchParams.get('month') // specific month for monthly view
    const year = searchParams.get('year') || new Date().getFullYear().toString()
    const startDate = searchParams.get('startDate') // for weekly view
    const endDate = searchParams.get('endDate') // for weekly view

    // Verify authentication
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    }

    const decoded = await verifyJWT(token)
    
    // Only PARENT role can access this endpoint
    if (decoded.role !== 'PARENT') {
      return NextResponse.json(
        { error: 'Forbidden - Only parents can access this endpoint' },
        { status: 403 }
      )
    }

    const parentId = decoded.id

    // If studentId is provided, verify the parent has access to this student
    if (studentId) {
      const relationship = await prisma.parentStudent.findUnique({
        where: {
          parentId_studentId: {
            parentId,
            studentId
          }
        }
      })

      if (!relationship) {
        return NextResponse.json(
          { error: 'Forbidden - You do not have access to this student' },
          { status: 403 }
        )
      }
    }

    // Get all children if no specific student is requested
    let studentIds: string[] = []
    if (studentId) {
      studentIds = [studentId]
    } else {
      const relationships = await prisma.parentStudent.findMany({
        where: { parentId },
        select: { studentId: true }
      })
      studentIds = relationships.map(rel => rel.studentId)
    }

    if (studentIds.length === 0) {
      return NextResponse.json({
        attendance: [],
        students: [],
        message: 'No children found for this parent'
      })
    }

    // Build date filter based on period
    let dateFilter: any = {}
    const currentDate = new Date()

    switch (period) {
      case 'daily':
        if (date) {
          dateFilter.date = date
        } else {
          // Today's date
          const today = currentDate.toISOString().split('T')[0]
          dateFilter.date = today
        }
        break

      case 'weekly':
        if (startDate && endDate) {
          dateFilter.date = {
            gte: startDate,
            lte: endDate
          }
        } else {
          // Current week
          const startOfWeek = new Date(currentDate)
          startOfWeek.setDate(currentDate.getDate() - currentDate.getDay())
          const endOfWeek = new Date(startOfWeek)
          endOfWeek.setDate(startOfWeek.getDate() + 6)

          dateFilter.date = {
            gte: startOfWeek.toISOString().split('T')[0],
            lte: endOfWeek.toISOString().split('T')[0]
          }
        }
        break

      case 'monthly':
        const targetMonth = month || (currentDate.getMonth() + 1).toString().padStart(2, '0')
        const paddedMonth = targetMonth.padStart(2, '0')

        // Calculate the first and last day of the month
        const firstDay = `${year}-${paddedMonth}-01`
        const lastDay = new Date(parseInt(year), parseInt(paddedMonth), 0).getDate()
        const lastDayFormatted = `${year}-${paddedMonth}-${lastDay.toString().padStart(2, '0')}`

        dateFilter.date = {
          gte: firstDay,
          lte: lastDayFormatted
        }
        break

      case 'semester':
        // Determine current semester based on academic calendar
        // First semester: September-January, Second semester: February-June
        const currentMonth = currentDate.getMonth() + 1
        const semester = (currentMonth >= 9 || currentMonth <= 1) ? 1 : 2

        if (semester === 1) {
          // First semester: September 1st to January 30th (next year)
          dateFilter.date = {
            gte: `${year}-09-01`,
            lte: `${year + 1}-01-30`
          }
        } else {
          // Second semester: February 1st to June 30th (current year)
          dateFilter.date = {
            gte: `${year}-02-01`,
            lte: `${year}-06-30`
          }
        }
        break
    }

    // Fetch attendance data
    const attendance = await prisma.attendance.findMany({
      where: {
        studentId: { in: studentIds },
        ...dateFilter
      },
      include: {
        student: {
          select: {
            id: true,
            sid: true,
            name: true,
            className: true
          }
        }
      },
      orderBy: [
        { date: 'desc' },
        { student: { name: 'asc' } }
      ]
    })

    // Get student details
    const students = await prisma.student.findMany({
      where: { id: { in: studentIds } },
      select: {
        id: true,
        sid: true,
        name: true,
        className: true,
        photoUrl: true
      }
    })

    // Calculate statistics
    const stats = studentIds.map(sId => {
      const studentAttendance = attendance.filter(a => a.studentId === sId)
      const total = studentAttendance.length
      const present = studentAttendance.filter(a => a.status === 'present').length
      const absent = studentAttendance.filter(a => a.status === 'absent').length
      const permission = studentAttendance.filter(a => a.status === 'permission').length
      
      return {
        studentId: sId,
        total,
        present,
        absent,
        permission,
        presentPercentage: total > 0 ? Math.round((present / total) * 100) : 0,
        absentPercentage: total > 0 ? Math.round((absent / total) * 100) : 0,
        permissionPercentage: total > 0 ? Math.round((permission / total) * 100) : 0
      }
    })

    return NextResponse.json({
      attendance,
      students,
      stats,
      period,
      dateFilter,
      message: 'Attendance data retrieved successfully'
    })
  } catch (error) {
    console.error('Error fetching attendance data:', error)
    return NextResponse.json(
      { error: 'Failed to fetch attendance data' },
      { status: 500 }
    )
  }
}

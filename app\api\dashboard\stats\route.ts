import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { format } from 'date-fns';

export async function GET() {
  try {
    // Get today's date in YYYY-MM-DD format
    const today = format(new Date(), 'yyyy-MM-dd');

    // Get total students count
    const totalStudents = await prisma.student.count();

    // Get attendance for today
    const todayAttendance = await prisma.attendance.findMany({
      where: {
        date: today,
      },
    });

    // Calculate present and absent students
    const presentStudents = todayAttendance.filter(a => a.status === 'present').length;
    const absentStudents = todayAttendance.filter(a => a.status === 'absent').length;
    const permissionStudents = todayAttendance.filter(a => a.status === 'permission').length;

    // If no attendance records for today, estimate based on average attendance rate
    let estimatedPresent = presentStudents;
    let estimatedAbsent = absentStudents;
    let estimatedPermission = permissionStudents;

    if (todayAttendance.length === 0) {
      // Estimate 85% present, 10% absent, 5% permission if no data
      estimatedPresent = Math.round(totalStudents * 0.85);
      estimatedAbsent = Math.round(totalStudents * 0.1);
      estimatedPermission = totalStudents - estimatedPresent - estimatedAbsent;
    }

    // Get recent marks (last 30 entries)
    const recentMarks = await prisma.mark.findMany({
      take: 30,
      orderBy: {
        createdAt: 'desc',
      },
      include: {
        student: {
          select: {
            name: true,
            gender: true,
          },
        },
      },
    });

    // Calculate top performers and failing students
    const passingThreshold = 50; // Marks below 50 are considered failing

    // Group marks by student
    const studentMarks = recentMarks.reduce((acc, mark) => {
      if (!acc[mark.studentId]) {
        acc[mark.studentId] = {
          studentId: mark.studentId,
          studentName: mark.student?.name || 'Unknown',
          marks: [],
          average: 0,
        };
      }
      acc[mark.studentId].marks.push(mark.marks);
      return acc;
    }, {} as Record<string, { studentId: string; studentName: string; marks: number[]; average: number }>);

    // Calculate average for each student
    Object.values(studentMarks).forEach(student => {
      student.average = student.marks.reduce((sum, mark) => sum + mark, 0) / student.marks.length;
    });

    // Get top performers and failing students
    const topPerformers = Object.values(studentMarks)
      .filter(student => student.average >= 80)
      .sort((a, b) => b.average - a.average)
      .slice(0, 5);

    const failingStudents = Object.values(studentMarks)
      .filter(student => student.average < passingThreshold)
      .sort((a, b) => a.average - b.average)
      .slice(0, 5);

    // Get upcoming events from academic calendar until June 30
    const today_date = new Date();
    const endOfAcademicYear = new Date(today_date.getFullYear(), 5, 30); // June 30

    // If today is after June 30 of current year, use June 30 of next year
    if (today_date > endOfAcademicYear) {
      endOfAcademicYear.setFullYear(today_date.getFullYear() + 1);
    }

    // Try to get calendar events if the model exists
    let upcomingEvents = [];
    try {
      // Try to get events from news_event table first (academic events)
      try {
        const academicEvents = await prisma.news_event.findMany({
          where: {
            date: {
              gte: today_date.toISOString(),
              lte: endOfAcademicYear.toISOString(),
            },
            category: 'academic',
            isActive: true,
          },
          orderBy: {
            date: 'asc',
          },
        });

        // Map to the expected format
        upcomingEvents = academicEvents.map(event => ({
          id: event.id,
          title: event.title,
          date: event.date,
          category: 'academic',
        }));
      } catch (error) {
        console.log('News events not available or error:', error);
      }

      // If no events found, try calendarEvent model
      if (upcomingEvents.length === 0 && prisma.calendarEvent) {
        upcomingEvents = await prisma.calendarEvent.findMany({
          where: {
            date: {
              gte: today_date.toISOString(),
              lte: endOfAcademicYear.toISOString(),
            },
            isActive: true,
          },
          orderBy: {
            date: 'asc',
          },
        });
      }
    } catch (error) {
      console.log('Calendar events not available in database schema:', error);
    }

    // Use mock data if no events found
    if (upcomingEvents.length === 0) {
      // Create mock events spread out until June 30
      const mockEvents = [];
      const currentYear = today_date.getFullYear();
      const months = ['January', 'February', 'March', 'April', 'May', 'June'];

      // Create events for each month until June
      months.forEach((month, index) => {
        // Skip months that are already past
        if (today_date.getMonth() > index) return;

        // Add exam event
        mockEvents.push({
          id: `exam-${index}`,
          title: `${month} Exams`,
          date: new Date(currentYear, index, 15).toISOString(),
          category: 'exam',
        });

        // Add academic event
        mockEvents.push({
          id: `academic-${index}`,
          title: `${month} Academic Meeting`,
          date: new Date(currentYear, index, 5).toISOString(),
          category: 'academic',
        });

        // Add special event
        if (index % 2 === 0) {
          mockEvents.push({
            id: `special-${index}`,
            title: `${month} Special Event`,
            date: new Date(currentYear, index, 25).toISOString(),
            category: 'special',
          });
        }
      });

      // Filter out past events
      upcomingEvents = mockEvents.filter(event => new Date(event.date) >= today_date)
        .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
    }

    return NextResponse.json({
      totalStudents,
      attendance: {
        present: presentStudents || estimatedPresent,
        absent: absentStudents || estimatedAbsent,
        permission: permissionStudents || estimatedPermission,
        total: totalStudents,
        hasRealData: todayAttendance.length > 0,
      },
      performance: {
        recentMarks: recentMarks.slice(0, 10),
        topPerformers,
        failingStudents,
        averageScore: recentMarks.length > 0
          ? Math.round(recentMarks.reduce((sum, mark) => sum + mark.marks, 0) / recentMarks.length)
          : 0,
      },
      events: upcomingEvents,
    });
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch dashboard stats' },
      { status: 500 }
    );
  }
}

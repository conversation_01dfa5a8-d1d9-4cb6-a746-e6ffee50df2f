"use client";

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { FaQuoteLeft, FaQuoteRight, FaStar } from 'react-icons/fa';

// Testimonial interface
interface Testimonial {
  id: string;
  name: string;
  role: string;
  content: string;
  imageUrl: string | null;
  rating: number;
  isActive: boolean;
  order: number;
}

export default function Testimonials() {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);
  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch testimonials from API
  useEffect(() => {
    const fetchTestimonials = async () => {
      try {
        const response = await fetch('/api/website-management/testimonials');

        if (!response.ok) {
          throw new Error('Failed to fetch testimonials');
        }

        const data = await response.json();

        // Filter active testimonials and sort by order
        const activeTestimonials = data
          .filter((testimonial: Testimonial) => testimonial.isActive)
          .sort((a: Testimonial, b: Testimonial) => a.order - b.order);

        setTestimonials(activeTestimonials);
        setIsLoading(false);
      } catch (err) {
        console.error('Error fetching testimonials:', err);
        setError('Failed to load testimonials');
        setIsLoading(false);

        // Fallback to sample data if API fails
        setTestimonials([
          {
            id: '1',
            name: 'Ahmed Hassan',
            role: 'Parent',
            content: 'Alfalah Islamic School has been a blessing for our family. The teachers are dedicated and caring, and the Islamic environment has helped our children grow both academically and spiritually.',
            imageUrl: '/images/portrait.jpg',
            rating: 5,
            isActive: true,
            order: 0
          },
          {
            id: '2',
            name: 'Fatima Khan',
            role: 'Alumni',
            content: 'My years at Alfalah Islamic School prepared me well for university and beyond. The strong foundation in both academic subjects and Islamic studies has been invaluable in my personal and professional life.',
            imageUrl: '/images/portrait.jpg',
            rating: 5,
            isActive: true,
            order: 1
          }
        ]);
      }
    };

    fetchTestimonials();
  }, []);

  // Auto-advance testimonials
  useEffect(() => {
    if (testimonials.length === 0) return;

    const interval = setInterval(() => {
      setCurrentTestimonial((prev) => (prev === testimonials.length - 1 ? 0 : prev + 1));
    }, 8000);

    return () => clearInterval(interval);
  }, [testimonials.length]);

  return (
    <section className="py-20 bg-gradient-to-br from-blue-900 to-indigo-900 text-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{ backgroundImage: 'url(/images/pattern.svg)', backgroundSize: '20px' }}></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl font-bold mb-4">What Our Community Says</h2>
          <p className="text-blue-200 max-w-2xl mx-auto">
            Hear from our parents, students, and alumni about their experiences at Alfalah Islamic School.
          </p>
        </motion.div>

        <div className="max-w-4xl mx-auto">
          {isLoading ? (
            <div className="flex justify-center items-center h-[300px]">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white"></div>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <p className="text-red-300">{error}</p>
            </div>
          ) : testimonials.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-blue-200">No testimonials available at the moment.</p>
            </div>
          ) : (
            <>
              {/* Testimonial Carousel */}
              <div className="relative h-[400px] md:h-[300px]">
                {testimonials.map((testimonial, index) => (
                  <motion.div
                    key={testimonial.id}
                    className="absolute inset-0 flex flex-col items-center text-center"
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{
                      opacity: currentTestimonial === index ? 1 : 0,
                      scale: currentTestimonial === index ? 1 : 0.9,
                      zIndex: currentTestimonial === index ? 10 : 0,
                    }}
                    transition={{ duration: 0.7 }}
                  >
                    <div className="relative w-20 h-20 mb-6 rounded-full overflow-hidden border-4 border-blue-400">
                      {testimonial.imageUrl ? (
                        <Image
                          src={testimonial.imageUrl}
                          alt={testimonial.name}
                          fill
                          className="object-cover"
                        />
                      ) : (
                        <div className="w-full h-full bg-blue-800 flex items-center justify-center">
                          <span className="text-2xl font-bold text-white">
                            {testimonial.name.charAt(0)}
                          </span>
                        </div>
                      )}
                    </div>

                    <div className="flex justify-center mb-4">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <FaStar key={i} className="text-yellow-400 mx-0.5" />
                      ))}
                    </div>

                    <div className="relative mb-6 px-8">
                      <FaQuoteLeft className="absolute top-0 left-0 text-blue-400 opacity-50 text-xl" />
                      <p className="text-lg md:text-xl italic text-blue-100">
                        {testimonial.content}
                      </p>
                      <FaQuoteRight className="absolute bottom-0 right-0 text-blue-400 opacity-50 text-xl" />
                    </div>

                    <div>
                      <h4 className="text-xl font-semibold">{testimonial.name}</h4>
                      <p className="text-blue-300">{testimonial.role}</p>
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* Testimonial Indicators */}
              <div className="flex justify-center space-x-2 mt-8">
                {testimonials.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentTestimonial(index)}
                    className={`w-3 h-3 rounded-full transition-all duration-300 ${
                      currentTestimonial === index ? 'bg-white w-8' : 'bg-blue-400/50'
                    }`}
                    aria-label={`Go to testimonial ${index + 1}`}
                  />
                ))}
              </div>
            </>
          )}
        </div>
      </div>
    </section>
  );
}

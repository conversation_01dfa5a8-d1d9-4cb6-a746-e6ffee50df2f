import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { verifyJWT } from '@/lib/jwt'
import { cookies } from 'next/headers'

// GET a single role by ID
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    console.log(`Fetching role with ID: ${params.id}`)

    // Fetch the role with its permissions
    const role = await prisma.role.findUnique({
      where: { id: params.id },
      include: {
        permissions: {
          include: {
            permission: true
          }
        }
      }
    })

    if (!role) {
      console.error(`Role with ID ${params.id} not found`)
      return NextResponse.json(
        { error: 'Role not found' },
        { status: 404 }
      )
    }

    // Format the response
    const formattedRole = {
      id: role.id,
      name: role.name,
      description: role.description,
      permissions: role.permissions.map(rp => rp.permission.name),
      systemDefined: role.systemDefined,
      createdAt: role.createdAt,
      updatedAt: role.updatedAt
    }

    console.log(`Found role: ${role.name}`)
    return NextResponse.json(formattedRole)
  } catch (error) {
    console.error('Error fetching role:', error)
    return NextResponse.json(
      { error: 'Failed to fetch role' },
      { status: 500 }
    )
  }
}

// PUT to update a role
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    console.log(`Updating role with ID: ${params.id}`)

    // Verify authentication
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    if (!token) {
      console.error('No authentication token found')
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    }

    try {
      // Verify the token
      const decoded = await verifyJWT(token)

      // Only allow admins to update roles
      if (decoded.role !== 'ADMIN') {
        console.error('Unauthorized role update attempt', {
          requesterId: decoded.id,
          requesterRole: decoded.role
        })
        return NextResponse.json(
          { error: 'Forbidden - Only administrators can update roles' },
          { status: 403 }
        )
      }

      // Get the request body
      const data = await request.json()

      // Validate the role data
      if (!data.name || !data.description) {
        console.error('Invalid role data:', data)
        return NextResponse.json(
          { error: 'Role name and description are required' },
          { status: 400 }
        )
      }

      if (!Array.isArray(data.permissions) || data.permissions.length === 0) {
        console.error('Invalid permissions:', data.permissions)
        return NextResponse.json(
          { error: 'At least one permission is required' },
          { status: 400 }
        )
      }

      // Check if the role exists
      const existingRole = await prisma.role.findUnique({
        where: { id: params.id }
      })

      if (!existingRole) {
        console.error(`Role with ID ${params.id} not found`)
        return NextResponse.json(
          { error: 'Role not found' },
          { status: 404 }
        )
      }

      // Check if the role is system-defined
      if (existingRole.systemDefined) {
        console.error(`Cannot update system-defined role: ${params.id}`)
        return NextResponse.json(
          { error: 'Cannot update system-defined roles' },
          { status: 400 }
        )
      }

      // Check if another role with the same name exists (if name is being changed)
      if (data.name !== existingRole.name) {
        const duplicateRole = await prisma.role.findUnique({
          where: { name: data.name }
        })

        if (duplicateRole) {
          console.error(`Role with name ${data.name} already exists`)
          return NextResponse.json(
            { error: 'A role with this name already exists' },
            { status: 400 }
          )
        }
      }

      // Get the permission IDs for the given permission names
      const permissions = await prisma.permission.findMany({
        where: {
          name: {
            in: data.permissions
          }
        }
      })

      // Check if all requested permissions exist
      if (permissions.length !== data.permissions.length) {
        const foundPermissionNames = permissions.map(p => p.name)
        const missingPermissions = data.permissions.filter(p => !foundPermissionNames.includes(p))

        console.error(`Some permissions were not found: ${missingPermissions.join(', ')}`)
        return NextResponse.json(
          { error: `Some permissions were not found: ${missingPermissions.join(', ')}` },
          { status: 400 }
        )
      }

      // Update the role
      const updatedRole = await prisma.role.update({
        where: { id: params.id },
        data: {
          name: data.name,
          description: data.description,
          updatedAt: new Date()
        }
      })

      // Delete existing role permissions
      await prisma.rolePermission.deleteMany({
        where: { roleId: params.id }
      })

      // Assign new permissions to the role
      await Promise.all(
        permissions.map(permission =>
          prisma.rolePermission.create({
            data: {
              roleId: updatedRole.id,
              permissionId: permission.id
            }
          })
        )
      )

      // Fetch the updated role with its permissions
      const updatedRoleWithPermissions = await prisma.role.findUnique({
        where: { id: updatedRole.id },
        include: {
          permissions: {
            include: {
              permission: true
            }
          }
        }
      })

      // Format the response
      const formattedRole = {
        id: updatedRoleWithPermissions.id,
        name: updatedRoleWithPermissions.name,
        description: updatedRoleWithPermissions.description,
        permissions: updatedRoleWithPermissions.permissions.map(rp => rp.permission.name),
        systemDefined: updatedRoleWithPermissions.systemDefined,
        createdAt: updatedRoleWithPermissions.createdAt,
        updatedAt: updatedRoleWithPermissions.updatedAt
      }

      console.log('Role updated:', formattedRole)
      return NextResponse.json({
        message: 'Role updated successfully',
        role: formattedRole
      })

    } catch (jwtError) {
      console.error('JWT verification error:', jwtError)
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      )
    }
  } catch (error) {
    console.error('Error updating role:', error)
    return NextResponse.json(
      { error: 'Failed to update role' },
      { status: 500 }
    )
  }
}

// DELETE a role
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    console.log(`Deleting role with ID: ${params.id}`)

    // Verify authentication
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    if (!token) {
      console.error('No authentication token found')
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    }

    try {
      // Verify the token
      const decoded = await verifyJWT(token)

      // Only allow admins to delete roles
      if (decoded.role !== 'ADMIN') {
        console.error('Unauthorized role deletion attempt', {
          requesterId: decoded.id,
          requesterRole: decoded.role
        })
        return NextResponse.json(
          { error: 'Forbidden - Only administrators can delete roles' },
          { status: 403 }
        )
      }

      // Check if the role exists
      const existingRole = await prisma.role.findUnique({
        where: { id: params.id }
      })

      if (!existingRole) {
        console.error(`Role with ID ${params.id} not found`)
        return NextResponse.json(
          { error: 'Role not found' },
          { status: 404 }
        )
      }

      // Check if the role is system-defined
      if (existingRole.systemDefined) {
        console.error(`Cannot delete system-defined role: ${params.id}`)
        return NextResponse.json(
          { error: 'Cannot delete system-defined roles' },
          { status: 400 }
        )
      }

      // Delete the role (this will cascade delete the role permissions due to the onDelete: Cascade setting)
      await prisma.role.delete({
        where: { id: params.id }
      })

      console.log(`Role with ID ${params.id} deleted`)
      return NextResponse.json({
        message: 'Role deleted successfully'
      })

    } catch (jwtError) {
      console.error('JWT verification error:', jwtError)
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      )
    }
  } catch (error) {
    console.error('Error deleting role:', error)
    return NextResponse.json(
      { error: 'Failed to delete role' },
      { status: 500 }
    )
  }
}

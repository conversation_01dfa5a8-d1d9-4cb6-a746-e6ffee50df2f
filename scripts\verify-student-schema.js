// Script to verify the student table schema
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    // Execute a raw query to get the table schema
    const result = await prisma.$queryRaw`DESCRIBE student`;
    console.log('Student table schema:');
    result.forEach(column => {
      console.log(`${column.Field}: ${column.Type} ${column.Null === 'YES' ? '(nullable)' : '(required)'}`);
    });
    
    // Check if photoUrl column exists
    const photoUrlColumn = result.find(column => column.Field === 'photoUrl');
    if (photoUrlColumn) {
      console.log('\nphotoUrl column exists in the student table!');
    } else {
      console.log('\nphotoUrl column does NOT exist in the student table!');
    }
    
    // Try to query a student with the photoUrl field
    console.log('\nTrying to query a student with photoUrl field...');
    const student = await prisma.student.findFirst({
      select: {
        id: true,
        name: true,
        photoUrl: true
      }
    });
    console.log('Query successful!');
    console.log('Sample student:', student);
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();

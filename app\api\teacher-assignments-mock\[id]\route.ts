import { NextResponse } from 'next/server'
import { verifyJWT } from '@/lib/jwt'
import { cookies } from 'next/headers'

// GET a single teacher assignment by ID
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    console.log(`Fetching teacher assignment with ID: ${params.id}`)

    // Skip authentication checks for development
    if (process.env.NODE_ENV !== 'production') {
      console.log('Development mode - bypassing authentication checks');

      // Mock data for teacher assignments
      const assignments = [
        {
          id: '1',
          teacherId: '1',
          teacherName: '<PERSON>',
          classId: '1',
          className: '10A',
          subjectId: null,
          subjectName: null,
          isHRTeacher: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: '2',
          teacherId: '1',
          teacherName: '<PERSON>',
          classId: null,
          className: null,
          subjectId: '1',
          subjectName: 'Mathematics',
          isHRTeacher: false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ];

      const assignment = assignments.find(a => a.id === params.id);

      if (!assignment) {
        console.error(`Teacher assignment with ID ${params.id} not found`);
        return NextResponse.json(
          { error: 'Teacher assignment not found' },
          { status: 404 }
        );
      }

      console.log(`Found teacher assignment: ${assignment.id}`);
      return NextResponse.json(assignment);
    }

    try {
      // This code will run in production mode
      // Verify authentication
      const cookieStore = cookies();
      const token = cookieStore.get('token')?.value;

      if (!token) {
        console.error('No authentication token found');
        return NextResponse.json(
          { error: 'Unauthorized - Authentication required' },
          { status: 401 }
        );
      }

      // Verify the token
      const decoded = await verifyJWT(token);

      // Mock data for teacher assignments
      const assignments = [
        // Same assignments as above
      ];

      const assignment = assignments.find(a => a.id === params.id);

      if (!assignment) {
        console.error(`Teacher assignment with ID ${params.id} not found`);
        return NextResponse.json(
          { error: 'Teacher assignment not found' },
          { status: 404 }
        );
      }

      console.log(`Found teacher assignment: ${assignment.id}`);
      return NextResponse.json(assignment);
    } catch (jwtError) {
      console.error('JWT verification error:', jwtError)
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      )
    }
  } catch (error) {
    console.error('Error fetching teacher assignment:', error)
    return NextResponse.json(
      { error: 'Failed to fetch teacher assignment' },
      { status: 500 }
    )
  }
}

// DELETE a teacher assignment
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    console.log(`Deleting teacher assignment with ID: ${params.id}`)

    // Skip authentication checks for development
    if (process.env.NODE_ENV !== 'production') {
      console.log('Development mode - bypassing authentication checks');

      // In a real implementation, we would check if the assignment exists and delete it

      console.log(`Teacher assignment with ID ${params.id} deleted`);
      return NextResponse.json({
        message: 'Teacher assignment deleted successfully'
      });
    }

    try {
      // This code will run in production mode
      // Verify authentication
      const cookieStore = cookies();
      const token = cookieStore.get('token')?.value;

      if (!token) {
        console.error('No authentication token found');
        return NextResponse.json(
          { error: 'Unauthorized - Authentication required' },
          { status: 401 }
        );
      }

      // Verify the token
      const decoded = await verifyJWT(token);

      // Allow admins and HR teachers to delete teacher assignments
      if (decoded.role !== 'ADMIN' && decoded.role !== 'HR_TEACHER') {
        console.error('Unauthorized teacher assignment deletion attempt', {
          requesterId: decoded.id,
          requesterRole: decoded.role
        });
        return NextResponse.json(
          { error: 'Forbidden - Only administrators and HR teachers can delete teacher assignments' },
          { status: 403 }
        );
      }

      // In a real implementation, we would check if the assignment exists and delete it

      console.log(`Teacher assignment with ID ${params.id} deleted`);
      return NextResponse.json({
        message: 'Teacher assignment deleted successfully'
      });
    } catch (jwtError) {
      console.error('JWT verification error:', jwtError)
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      )
    }
  } catch (error) {
    console.error('Error deleting teacher assignment:', error)
    return NextResponse.json(
      { error: 'Failed to delete teacher assignment' },
      { status: 500 }
    )
  }
}

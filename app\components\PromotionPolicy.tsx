"use client"

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from './ui/button'
import { Input } from './ui/input'
import { Label } from './ui/label'
import { Switch } from './ui/switch'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Badge } from './ui/badge'
import { Separator } from './ui/separator'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from './ui/tabs'
import { 
  Save, 
  AlertCircle, 
  CheckCircle, 
  GraduationCap, 
  Users,
  BookOpen,
  TrendingUp,
  Info,
  School,
  Settings
} from 'lucide-react'
import { useToast } from './ui/use-toast'
import { BasicInformation } from './BasicInformation'

interface PromotionPolicyData {
  id?: string
  passingScore: number
  maxFailedSubjects: number
  applyToAllClasses: boolean
  isActive?: boolean
  description?: string
  createdBy?: string
  createdAt?: string
  updatedAt?: string
}

export function PromotionPolicy() {
  const { toast } = useToast()
  const [policy, setPolicy] = useState<PromotionPolicyData>({
    passingScore: 50,
    maxFailedSubjects: 2,
    applyToAllClasses: true,
    description: ''
  })
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)

  // Fetch current promotion policy
  useEffect(() => {
    fetchPromotionPolicy()
  }, [])

  const fetchPromotionPolicy = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/promotion-policy')
      if (!response.ok) throw new Error('Failed to fetch promotion policy')
      
      const data = await response.json()
      setPolicy(data)
      setHasChanges(false)
    } catch (error) {
      console.error('Error fetching promotion policy:', error)
      toast({
        title: "Error",
        description: "Failed to load promotion policy. Using default values.",
        className: "bg-destructive text-destructive-foreground",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleInputChange = (field: keyof PromotionPolicyData, value: any) => {
    setPolicy(prev => ({
      ...prev,
      [field]: value
    }))
    setHasChanges(true)
  }

  const handleSave = async () => {
    try {
      setIsSaving(true)
      
      const method = policy.id ? 'PUT' : 'POST'
      const response = await fetch('/api/promotion-policy', {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...policy,
          createdBy: 'admin' // TODO: Get from auth context
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to save promotion policy')
      }

      const savedPolicy = await response.json()
      setPolicy(savedPolicy)
      setHasChanges(false)

      toast({
        title: "Success",
        description: "Promotion policy saved successfully.",
        className: "bg-green-600 text-white",
      })
    } catch (error) {
      console.error('Error saving promotion policy:', error)
      toast({
        title: "Error",
        description: error.message || "Failed to save promotion policy.",
        className: "bg-destructive text-destructive-foreground",
      })
    } finally {
      setIsSaving(false)
    }
  }

  const getPromotionPreview = () => {
    const detentionThreshold = policy.maxFailedSubjects + 1
    return {
      promoted: `Students failing ${policy.maxFailedSubjects} or fewer subjects (scoring >=${policy.passingScore}%)`,
      detained: `Students failing ${detentionThreshold} or more subjects (scoring <${policy.passingScore}%)`
    }
  }

  const preview = getPromotionPreview()

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-2/3 mb-8"></div>
          <div className="space-y-4">
            <div className="h-32 bg-gray-200 rounded"></div>
            <div className="h-32 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold flex items-center gap-2">
          <Settings className="h-5 w-5 text-indigo-600" />
          Settings
        </h2>
        <p className="text-gray-500 text-sm mt-1">
          Configure application settings and policies
        </p>
      </div>

      <Tabs defaultValue="basic-info" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="basic-info" className="flex items-center gap-2">
            <School className="h-4 w-4" />
            Basic Information
          </TabsTrigger>
          <TabsTrigger value="promotion-policy" className="flex items-center gap-2">
            <GraduationCap className="h-4 w-4" />
            Promotion Policy
          </TabsTrigger>
        </TabsList>

        <TabsContent value="basic-info" className="mt-6">
          <BasicInformation />
        </TabsContent>

        <TabsContent value="promotion-policy" className="mt-6">
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <GraduationCap className="h-5 w-5 text-indigo-600" />
                Promotion Policy
              </h3>
              <p className="text-gray-500 text-sm mt-1">
                Configure the criteria for student promotion and detention
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Configuration Panel */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Settings className="h-5 w-5" />
                    Policy Configuration
                  </CardTitle>
                  <CardDescription>
                    Set the criteria for student promotion and detention
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Passing Score */}
                  <div className="space-y-2">
                    <Label htmlFor="passingScore" className="text-sm font-medium">
                      Minimum Passing Score (%)
                    </Label>
                    <Input
                      id="passingScore"
                      type="number"
                      min="0"
                      max="100"
                      value={policy.passingScore}
                      onChange={(e) => handleInputChange('passingScore', parseInt(e.target.value) || 0)}
                      className="w-full"
                    />
                    <p className="text-xs text-gray-500">
                      Students must score at least this percentage to pass a subject
                    </p>
                  </div>

                  {/* Max Failed Subjects */}
                  <div className="space-y-2">
                    <Label htmlFor="maxFailedSubjects" className="text-sm font-medium">
                      Maximum Failed Subjects for Promotion
                    </Label>
                    <Input
                      id="maxFailedSubjects"
                      type="number"
                      min="0"
                      max="10"
                      value={policy.maxFailedSubjects}
                      onChange={(e) => handleInputChange('maxFailedSubjects', parseInt(e.target.value) || 0)}
                      className="w-full"
                    />
                    <p className="text-xs text-gray-500">
                      Students can fail this many subjects and still be promoted
                    </p>
                  </div>

                  {/* Apply to All Classes */}
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label className="text-sm font-medium">
                        Apply to All Classes
                      </Label>
                      <p className="text-xs text-gray-500">
                        Use this policy for all classes in the school
                      </p>
                    </div>
                    <Switch
                      checked={policy.applyToAllClasses}
                      onCheckedChange={(checked) => handleInputChange('applyToAllClasses', checked)}
                    />
                  </div>

                  {/* Description */}
                  <div className="space-y-2">
                    <Label htmlFor="description" className="text-sm font-medium">
                      Policy Description (Optional)
                    </Label>
                    <Input
                      id="description"
                      type="text"
                      value={policy.description || ''}
                      onChange={(e) => handleInputChange('description', e.target.value)}
                      placeholder="Brief description of this policy..."
                      className="w-full"
                    />
                  </div>

                  <Separator />

                  {/* Save Button */}
                  <Button 
                    onClick={handleSave} 
                    disabled={!hasChanges || isSaving}
                    className="w-full bg-indigo-600 hover:bg-indigo-700"
                  >
                    <Save className="h-4 w-4 mr-2" />
                    {isSaving ? 'Saving...' : 'Save Policy'}
                  </Button>
                </CardContent>
              </Card>

              {/* Preview Panel */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <TrendingUp className="h-5 w-5" />
                    Policy Preview
                  </CardTitle>
                  <CardDescription>
                    See how this policy will affect student promotion
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Current Settings Summary */}
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span>Passing Score:</span>
                      <Badge variant="outline" className="text-blue-700 border-blue-300">
                        {policy.passingScore}%
                      </Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Max Failures Allowed:</span>
                      <Badge variant="outline" className="text-blue-700 border-blue-300">
                        {policy.maxFailedSubjects} subjects
                      </Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Detention Threshold:</span>
                      <Badge variant="outline" className="text-red-700 border-red-300">
                        {policy.maxFailedSubjects + 1}+ failed subjects
                      </Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Scope:</span>
                      <Badge variant="outline" className="text-blue-700 border-blue-300">
                        {policy.applyToAllClasses ? 'All Classes' : 'Specific Classes'}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Status Indicator */}
            {hasChanges && (
              <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-center gap-2">
                  <AlertCircle className="h-4 w-4 text-yellow-600" />
                  <span className="text-sm font-medium text-yellow-800">
                    Unsaved Changes
                  </span>
                </div>
                <p className="text-xs text-yellow-700 mt-1">
                  Save your changes to apply this policy to report cards
                </p>
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

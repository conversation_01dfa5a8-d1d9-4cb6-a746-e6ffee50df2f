'use client'

import React, { useState } from 'react'
import Sidebar from '../components/Sidebar'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card'
import { DollarSign, CreditCard, Receipt, FileSpreadsheet, Printer } from 'lucide-react'
import dynamic from 'next/dynamic'
import Link from 'next/link'

// Import Header component with no SSR to avoid hydration issues
const Header = dynamic(() => import('../components/Header'), {
  ssr: false
})

export default function AccountingPage() {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false)

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  const toggleMobileSidebar = () => {
    setMobileSidebarOpen(!mobileSidebarOpen)
  }

  return (
    <div className="flex h-screen bg-gray-100 dark:bg-gray-900">
      <Sidebar
        isCollapsed={sidebarCollapsed}
        toggleSidebar={toggleSidebar}
        isMobileOpen={mobileSidebarOpen}
      />

      <main className={`main-content ${sidebarCollapsed ? 'sidebar-collapsed' : ''} w-full h-screen overflow-y-auto`}>
        <Header toggleMobileSidebar={toggleMobileSidebar} />

        <div className="container mx-auto px-4 py-6">
          <h1 className="text-2xl font-bold mb-6 text-gray-800 dark:text-white">
            Accounting Management
          </h1>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Link href="/accounting/fee-types">
              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
                  <CardTitle className="text-sm font-medium">Fee Types</CardTitle>
                  <CreditCard className="h-4 w-4 text-blue-500" />
                </CardHeader>
                <CardContent>
                  <p className="text-xs text-gray-500">Manage different types of fees and their configurations</p>
                </CardContent>
              </Card>
            </Link>

            <Link href="/accounting/fee-invoice">
              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
                  <CardTitle className="text-sm font-medium">Fee Invoice</CardTitle>
                  <Receipt className="h-4 w-4 text-green-500" />
                </CardHeader>
                <CardContent>
                  <p className="text-xs text-gray-500">Create and manage individual student fee invoices</p>
                </CardContent>
              </Card>
            </Link>

            <Link href="/accounting/invoices-report">
              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
                  <CardTitle className="text-sm font-medium">Invoices Report</CardTitle>
                  <FileSpreadsheet className="h-4 w-4 text-purple-500" />
                </CardHeader>
                <CardContent>
                  <p className="text-xs text-gray-500">Generate comprehensive reports on fee collection status</p>
                </CardContent>
              </Card>
            </Link>


          </div>
        </div>
      </main>

      {/* Overlay for mobile menu */}
      {mobileSidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 md:hidden z-40"
          onClick={toggleMobileSidebar}
        ></div>
      )}
    </div>
  )
}

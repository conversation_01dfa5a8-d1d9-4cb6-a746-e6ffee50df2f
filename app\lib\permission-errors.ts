/**
 * Utility functions for handling permission errors consistently across the application
 */

import { NextResponse } from 'next/server'

/**
 * Creates a standardized permission denied response for API routes
 *
 * @param action The action that was attempted (e.g., 'view', 'edit', 'add', 'delete')
 * @param resource The resource that was being accessed (e.g., 'marks', 'attendance')
 * @param context Additional context (e.g., class name)
 * @returns NextResponse with appropriate error message and status code
 */
export function createPermissionDeniedResponse(
  action: string,
  resource: string,
  context?: string
): NextResponse {
  const contextStr = context ? ` for ${context}` : ''
  const message = `You do not have permission to ${action} ${resource}${contextStr}`

  return NextResponse.json(
    { error: message },
    { status: 403 }
  )
}

/**
 * Standard permission error messages to ensure consistency across the application
 */
export const PermissionErrors = {
  VIEW_MARKS: 'You do not have permission to view marks for this class',
  EDIT_MARKS: 'You do not have permission to edit marks for this class',
  ADD_MARKS: 'You do not have permission to add marks for this class',
  DELETE_MARKS: 'You do not have permission to delete marks for this class',

  VIEW_ATTENDANCE: 'You do not have permission to view attendance for this class',
  TAKE_ATTENDANCE: 'You do not have permission to take attendance for this class',
  EDIT_ATTENDANCE: 'You do not have permission to edit attendance for this class',

  // Financial permissions
  VIEW_FINANCES: 'You do not have permission to view financial information',
  EDIT_FINANCES: 'You do not have permission to edit financial information',
  CREATE_INVOICE: 'You do not have permission to create invoices',
  EDIT_INVOICE: 'You do not have permission to edit invoices',
  DELETE_INVOICE: 'You do not have permission to delete invoices',

  // Report card permissions
  VIEW_REPORT_CARDS: 'You do not have permission to view report cards',
  GENERATE_REPORT_CARDS: 'You do not have permission to generate report cards',

  // User management permissions
  MANAGE_USERS: 'You do not have permission to manage users',
  ASSIGN_ROLES: 'You do not have permission to assign roles',

  // Add more standard error messages as needed
}

/**
 * Helper function to check if an error is a permission error
 *
 * @param error The error to check
 * @returns True if the error is a permission error, false otherwise
 */
export function isPermissionError(error: any): boolean {
  // Check if it's an Error object with a message
  const errorMessage = error instanceof Error
    ? error.message
    : typeof error === 'string'
      ? error
      : error?.error || '';

  // Check if it's a permission error
  return (
    errorMessage.includes('permission') ||
    errorMessage.includes('Access Denied') ||
    errorMessage.includes('Forbidden') ||
    (error?.status === 403)
  );
}

/**
 * Helper function to extract action and resource from a permission error message
 *
 * @param errorMessage The error message to parse
 * @returns An object with action, resource, and context properties
 */
export function parsePermissionError(errorMessage: string): {
  action: string;
  resource: string;
  context?: string;
} {
  const permissionRegex = /permission to ([\w]+) ([\w\s]+)(?: for (.+))?/i;
  const matches = errorMessage.match(permissionRegex);

  if (matches) {
    const [, action, resource, context] = matches;
    return { action, resource, context };
  }

  return {
    action: 'access',
    resource: 'feature'
  };
}

/**
 * Client-side helper to handle API permission errors
 *
 * @param error The error from the API
 * @param showPermissionDialog Function to show the permission dialog
 * @returns True if the error was handled, false otherwise
 */
export function handleApiPermissionError(
  error: any,
  showPermissionDialog: (options: any) => void
): boolean {
  if (!isPermissionError(error)) {
    return false;
  }

  const errorMessage = error instanceof Error
    ? error.message
    : typeof error === 'string'
      ? error
      : error?.error || 'You do not have permission to access this feature';

  const { action, resource, context } = parsePermissionError(errorMessage);

  showPermissionDialog({
    message: errorMessage,
    title: 'Permission Denied',
    action,
    resource: context ? `${resource} for ${context}` : resource
  });

  return true;
}

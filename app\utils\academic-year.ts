/**
 * Utility functions for academic year management
 */

export interface AcademicYearOption {
  value: string
  label: string
}

/**
 * Generate academic year options for a given range
 * @param startYear - Starting year (default: current year - 2)
 * @param count - Number of years to generate (default: 10)
 * @returns Array of academic year options
 */
export function generateAcademicYearOptions(
  startYear?: number,
  count: number = 10
): AcademicYearOption[] {
  const currentYear = new Date().getFullYear()
  const baseYear = startYear || (currentYear - 2)
  
  const options: AcademicYearOption[] = []
  
  for (let i = 0; i < count; i++) {
    const year = baseYear + i
    const nextYear = year + 1
    const academicYear = `${year}-${nextYear}`
    
    options.push({
      value: academicYear,
      label: academicYear
    })
  }
  
  return options
}

/**
 * Get the current academic year based on the current date
 * Academic year typically starts in August/September
 * @param startMonth - Month when academic year starts (default: 8 for August)
 * @returns Current academic year string
 */
export function getCurrentAcademicYear(startMonth: number = 8): string {
  const now = new Date()
  const currentYear = now.getFullYear()
  const currentMonth = now.getMonth() + 1 // getMonth() returns 0-11
  
  if (currentMonth >= startMonth) {
    // We're in the first part of the academic year
    return `${currentYear}-${currentYear + 1}`
  } else {
    // We're in the second part of the academic year
    return `${currentYear - 1}-${currentYear}`
  }
}

/**
 * Parse academic year string to get start and end years
 * @param academicYear - Academic year string (e.g., "2024-2025")
 * @returns Object with startYear and endYear
 */
export function parseAcademicYear(academicYear: string): { startYear: number; endYear: number } {
  const [startYear, endYear] = academicYear.split('-').map(Number)
  return { startYear, endYear }
}

/**
 * Validate academic year format
 * @param academicYear - Academic year string to validate
 * @returns Boolean indicating if the format is valid
 */
export function isValidAcademicYear(academicYear: string): boolean {
  const pattern = /^\d{4}-\d{4}$/
  if (!pattern.test(academicYear)) {
    return false
  }
  
  const { startYear, endYear } = parseAcademicYear(academicYear)
  return endYear === startYear + 1
}

/**
 * Get academic year display name with additional context
 * @param academicYear - Academic year string
 * @returns Formatted display name
 */
export function getAcademicYearDisplayName(academicYear: string): string {
  if (!isValidAcademicYear(academicYear)) {
    return academicYear
  }
  
  const { startYear, endYear } = parseAcademicYear(academicYear)
  const currentAcademicYear = getCurrentAcademicYear()
  
  if (academicYear === currentAcademicYear) {
    return `${academicYear} (Current)`
  }
  
  return academicYear
}

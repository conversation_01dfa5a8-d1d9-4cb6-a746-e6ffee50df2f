import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET all bank payment vouchers
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const paidTo = searchParams.get('paidTo')
    const paymentMethod = searchParams.get('paymentMethod')

    // Build filter conditions
    const where: any = {}

    if (status) {
      where.status = status
    }

    if (paidTo) {
      where.paidTo = {
        contains: paidTo
      }
    }

    if (paymentMethod) {
      where.paymentMethod = paymentMethod
    }

    // Fetch vouchers with filters
    const vouchers = await prisma.bankPaymentVoucher.findMany({
      where,
      orderBy: {
        date: 'desc'
      }
    })

    return NextResponse.json(vouchers)
  } catch (error) {
    console.error('Error fetching bank payment vouchers:', error)
    return NextResponse.json(
      { error: 'Failed to fetch bank payment vouchers' },
      { status: 500 }
    )
  }
}

// CREATE a new bank payment voucher
export async function POST(request: Request) {
  try {
    const data = await request.json()

    // Validate required fields
    if (!data.paidTo || !data.amount || !data.paymentMethod || !data.purpose) {
      return NextResponse.json(
        { error: 'Paid to, amount, payment method, and purpose are required' },
        { status: 400 }
      )
    }

    // Generate a unique voucher number
    const date = new Date()
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')

    // Count existing vouchers to generate sequential number
    const voucherCount = await prisma.bankPaymentVoucher.count()
    const sequentialNumber = String(voucherCount + 1).padStart(3, '0')

    const voucherNo = `BPV-${year}${month}-${sequentialNumber}`

    // Create the voucher
    let voucher;
    try {
      // Log the data being sent to the database
      console.log('Creating voucher with data:', JSON.stringify({
        voucherNo,
        date: data.date ? new Date(data.date) : new Date(),
        paidTo: data.paidTo,
        amount: parseFloat(data.amount),
        paymentMethod: data.paymentMethod,
        chequeNo: data.paymentMethod === 'cheque' ? data.chequeNo : null,
        accountNo: data.paymentMethod === 'transfer' ? data.accountNo : null,
        purpose: data.purpose,
        accountCode: data.accountCode || null
      }));

      // Create voucher data object including accountCode
      const voucherData: any = {
        voucherNo,
        date: data.date ? new Date(data.date) : new Date(),
        paidTo: data.paidTo,
        amount: parseFloat(data.amount),
        paymentMethod: data.paymentMethod,
        chequeNo: data.paymentMethod === 'cheque' ? data.chequeNo : null,
        accountNo: data.paymentMethod === 'transfer' ? data.accountNo : null,
        purpose: data.purpose,
        status: 'pending'
      }

      // Add accountCode if provided
      if (data.accountCode) {
        // The column exists now, so we can add it directly
        voucherData.accountCode = data.accountCode;
        console.log('Adding account code to voucher data:', data.accountCode);
      }

      // Try to create the voucher
      voucher = await prisma.bankPaymentVoucher.create({
        data: voucherData
      })

      console.log('Voucher created successfully:', voucher);
    } catch (error) {
      console.error('Error creating voucher:', error);

      // Provide more detailed error information
      let errorMessage = 'Failed to create voucher. Database error.';
      let errorDetails = null;

      if (error instanceof Error) {
        errorMessage = error.message;
        errorDetails = error.stack;
      }

      return NextResponse.json(
        {
          error: errorMessage,
          details: errorDetails,
          data: data // Include the data that was sent to help with debugging
        },
        { status: 500 }
      )
    }

    return NextResponse.json(voucher, { status: 201 })
  } catch (error) {
    console.error('Error creating bank payment voucher:', error)

    // Provide more detailed error information
    let errorMessage = 'Failed to create bank payment voucher';
    let errorDetails = null;

    if (error instanceof Error) {
      errorMessage = error.message;
      errorDetails = error.stack;
    }

    return NextResponse.json(
      {
        error: errorMessage,
        details: errorDetails,
        data: data // Include the data that was sent to help with debugging
      },
      { status: 500 }
    )
  }
}

'use client'

import React, { useState, useEffect } from 'react'
import dynamic from 'next/dynamic'
import Sidebar from '../components/Sidebar'
import { Card } from '../components/ui/card'
import { Globe, Image, Bell, Link2, Newspaper, MessageSquare, MousePointer, CalendarCheck as CalendarIcon } from 'lucide-react'
import { useQuery, QueryClient, QueryClientProvider } from '@tanstack/react-query'
import Link from 'next/link'

// Create a client
const queryClient = new QueryClient()

// Import Header component with no SSR to avoid hydration issues
const Header = dynamic(() => import('../components/Header'), {
  ssr: false
})

// Type definition for website stats
interface WebsiteStats {
  status: string;
  counts: {
    heroSlides: number;
    announcements: number;
    quickLinks: number;
    featuredContent: number;
    newsEvents: number;
    academicCalendar: number;
    testimonials: number;
    callToAction: number;
  }
}

function WebsiteManagement() {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false)
  const [isMounted, setIsMounted] = useState(false)

  // Fetch website stats from the API
  const { data: websiteStats, isLoading } = useQuery<WebsiteStats>({
    queryKey: ['websiteStats'],
    queryFn: async () => {
      try {
        const res = await fetch('/api/website-management/stats')
        if (!res.ok) {
          throw new Error('Failed to fetch website stats')
        }
        return res.json()
      } catch (error) {
        // If API doesn't exist yet, return mock data
        return {
          status: 'success',
          counts: {
            heroSlides: 3,
            announcements: 3,
            quickLinks: 6,
            featuredContent: 1,
            newsEvents: 6,
            academicCalendar: 4,
            testimonials: 4,
            callToAction: 1
          }
        }
      }
    },
    staleTime: 60000, // 1 minute
    refetchOnWindowFocus: false,
  })

  useEffect(() => {
    setIsMounted(true)
  }, [])

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  const toggleMobileSidebar = () => {
    setMobileSidebarOpen(!mobileSidebarOpen)
  }

  // Only show loading while mounting
  if (!isMounted || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-600"></div>
      </div>
    )
  }

  // Website management sections
  const sections = [
    {
      title: 'Hero Slides',
      description: 'Manage banner slides on the homepage',
      icon: <Image className="h-10 w-10 text-blue-500" />,
      count: websiteStats?.counts.heroSlides || 0,
      link: '/website-management/hero-slides',
      color: 'from-blue-400 to-blue-600'
    },
    {
      title: 'Announcements',
      description: 'Manage important announcements',
      icon: <Bell className="h-10 w-10 text-red-500" />,
      count: websiteStats?.counts.announcements || 0,
      link: '/website-management/announcements',
      color: 'from-red-400 to-red-600'
    },
    {
      title: 'Quick Links',
      description: 'Manage quick access links',
      icon: <Link2 className="h-10 w-10 text-green-500" />,
      count: websiteStats?.counts.quickLinks || 0,
      link: '/website-management/quick-links',
      color: 'from-green-400 to-green-600'
    },
    {
      title: 'Featured Content',
      description: 'Manage featured section content',
      icon: <Image className="h-10 w-10 text-purple-500" />,
      count: websiteStats?.counts.featuredContent || 0,
      link: '/website-management/featured-content',
      color: 'from-purple-400 to-purple-600'
    },
    {
      title: 'News & Events',
      description: 'Manage news articles and events',
      icon: <Newspaper className="h-10 w-10 text-amber-500" />,
      count: websiteStats?.counts.newsEvents || 0,
      link: '/website-management/news-events',
      color: 'from-amber-400 to-amber-600'
    },
    {
      title: 'Academic Calendar',
      description: 'Manage academic calendar events',
      icon: <CalendarIcon className="h-10 w-10 text-blue-500" />,
      count: websiteStats?.counts.academicCalendar || 0,
      link: '/website-management/academic-calendar',
      color: 'from-blue-500 to-blue-700'
    },
    {
      title: 'Testimonials',
      description: 'Manage student and parent testimonials',
      icon: <MessageSquare className="h-10 w-10 text-teal-500" />,
      count: websiteStats?.counts.testimonials || 0,
      link: '/website-management/testimonials',
      color: 'from-teal-400 to-teal-600'
    },
    {
      title: 'Call to Action',
      description: 'Manage call-to-action section',
      icon: <MousePointer className="h-10 w-10 text-indigo-500" />,
      count: websiteStats?.counts.callToAction || 0,
      link: '/website-management/call-to-action',
      color: 'from-indigo-400 to-indigo-600'
    }
  ]

  return (
    <div className="flex h-screen bg-gray-100 dark:bg-gray-900">
      <Sidebar
        isCollapsed={sidebarCollapsed}
        toggleSidebar={toggleSidebar}
        isMobileOpen={mobileSidebarOpen}
      />

      <main className={`main-content ${sidebarCollapsed ? 'sidebar-collapsed' : ''} w-full h-screen overflow-y-auto`}>
        <Header toggleMobileSidebar={toggleMobileSidebar} />

        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-2xl font-semibold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent inline-block">
              Website Content Management
            </h1>
            <Link href="/website" target="_blank" className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 flex items-center">
              <Globe className="h-4 w-4 mr-1" />
              <span>View Website</span>
            </Link>
          </div>

          <p className="text-gray-600 dark:text-gray-300 mb-8">
            Manage all content displayed on your school website. Changes will be reflected immediately on the website.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {sections.map((section) => (
              <Link href={section.link} key={section.title}>
                <Card className="overflow-hidden border-0 shadow-lg hover:shadow-2xl transition-all duration-300 dark:bg-gray-800 dark:border-gray-700 transform hover:-translate-y-2 hover:scale-105 dashboard-card-float hover-glow">
                  <div className="relative">
                    <div className={`absolute top-0 left-0 w-full h-1 bg-gradient-to-r ${section.color}`}></div>
                    <div className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">{section.title}</p>
                          <h3 className="text-3xl font-bold text-gray-900 dark:text-white animate-fadeIn">
                            {section.count}
                          </h3>
                          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{section.description}</p>
                        </div>
                        <div className="bg-gray-100 dark:bg-gray-700 p-3 rounded-full">
                          {section.icon}
                        </div>
                      </div>
                    </div>
                  </div>
                </Card>
              </Link>
            ))}
          </div>
        </div>
      </main>

      {/* Overlay for mobile menu */}
      {mobileSidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 md:hidden z-40"
          onClick={toggleMobileSidebar}
        ></div>
      )}
    </div>
  )
}

// Export a wrapper component that provides the QueryClient
export default function WebsiteManagementPage() {
  return (
    <QueryClientProvider client={queryClient}>
      <WebsiteManagement />
    </QueryClientProvider>
  )
}

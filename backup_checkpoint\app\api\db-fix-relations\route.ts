import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

export async function GET() {
  let prisma: PrismaClient | null = null;
  
  try {
    console.log('Fixing database relations...');
    console.log('Database URL:', process.env.DATABASE_URL);
    
    // Create a new PrismaClient instance specifically for this operation
    prisma = new PrismaClient({
      log: ['query', 'info', 'warn', 'error'],
      datasources: {
        db: {
          url: process.env.DATABASE_URL
        }
      }
    });
    
    // Try to connect to the database
    console.log('Connecting to database...');
    await prisma.$connect();
    console.log('Database connection successful');
    
    // First, check if the database tables exist by trying to count records
    try {
      // Get all students
      const students = await prisma.student.findMany();
      console.log(`Found ${students.length} students`);
      
      // Get all classes
      const classes = await prisma.class.findMany();
      console.log(`Found ${classes.length} classes`);
      
      if (students.length > 0 && classes.length === 0) {
        console.log('Students exist but no classes found. Creating classes based on student data...');
        
        // Extract unique class names from students
        const uniqueClassNames = [...new Set(students.map(student => student.className))];
        console.log(`Found ${uniqueClassNames.length} unique class names: ${uniqueClassNames.join(', ')}`);
        
        // Create classes for each unique class name
        for (const className of uniqueClassNames) {
          if (!className) {
            console.log('Skipping empty class name');
            continue;
          }
          
          // Create the class
          const newClass = await prisma.class.create({
            data: {
              name: className,
              totalStudents: 0,
              totalSubjects: 6,
              hrTeacherId: null
            }
          });
          
          console.log(`Created class ${className} with ID ${newClass.id}`);
        }
        
        console.log('All classes created successfully');
      }
      
      // Update class student counts
      console.log('Updating class student counts...');
      
      const updatedClasses = await prisma.class.findMany();
      
      for (const classItem of updatedClasses) {
        const studentCount = await prisma.student.count({
          where: { className: classItem.name }
        });
        
        await prisma.class.update({
          where: { id: classItem.id },
          data: { totalStudents: studentCount }
        });
        
        console.log(`Updated class ${classItem.name} with ${studentCount} students`);
      }
      
      // Return success response
      return NextResponse.json({
        status: 'success',
        message: 'Database relations fixed successfully',
        data: {
          classes: await prisma.class.count(),
          students: await prisma.student.count()
        }
      });
    } catch (tableError) {
      console.error('Error accessing database tables:', tableError);
      
      // This might be because the database tables don't exist yet
      return NextResponse.json({
        status: 'error',
        message: 'Database tables not found',
        error: tableError instanceof Error ? tableError.message : 'Unknown error',
        suggestion: 'You may need to run prisma migrate or prisma db push to create the database tables first.'
      }, { status: 500 });
    }
  } catch (error) {
    console.error('Error fixing database relations:', error);
    
    // Return detailed error information
    return NextResponse.json({
      status: 'error',
      message: 'Failed to fix database relations',
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      databaseUrl: process.env.DATABASE_URL ? '***' + process.env.DATABASE_URL.substring(process.env.DATABASE_URL.indexOf('@')) : 'Not set',
      suggestion: 'Please check your database connection string and ensure the database server is running.'
    }, { status: 500 });
  } finally {
    // Clean up the connection
    if (prisma) {
      await prisma.$disconnect().catch(e => console.error('Error disconnecting from database:', e));
    }
  }
}

'use client'

import React from 'react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import DashboardLayout from '../components/DashboardLayout'
import { NewRoleManagement } from '../components/RoleManagement/NewRoleManagement'
import RoleGuard from '../components/RoleGuard'
import { AlertCircle } from 'lucide-react'

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
})

// Access denied component
const AccessDenied = () => (
  <div className="flex items-center justify-center h-full">
    <div className="text-center">
      <h1 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h1>
      <p className="text-gray-600">You don't have permission to access this page.</p>
    </div>
  </div>
)

export default function RolesPage() {
  return (
    <QueryClientProvider client={queryClient}>
      <DashboardLayout>
        <RoleGuard
          roles="SUPER_ADMIN"
          redirectOnAccessDenied={true}
          fallback={<AccessDenied />}
        >
          <div className="space-y-8">
            {/* Enhanced Header */}
            <div className="relative overflow-hidden rounded-3xl bg-gradient-to-r from-amber-600 via-orange-600 to-red-600 p-8 text-white shadow-2xl">
              <div className="absolute inset-0 bg-black/10"></div>
              <div className="relative flex items-center justify-between">
                <div>
                  <h1 className="text-4xl font-bold tracking-tight mb-2 bg-gradient-to-r from-white to-amber-100 bg-clip-text text-transparent">
                    Role Management
                  </h1>
                  <p className="text-amber-100 text-lg">
                    Manage user roles, permissions, and access control across the system
                  </p>
                </div>
                <div className="hidden md:flex items-center gap-4">
                  <div className="h-16 w-16 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center">
                    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                    </svg>
                  </div>
                </div>
              </div>
              {/* Decorative elements */}
              <div className="absolute -top-8 -right-8 w-32 h-32 bg-white/10 rounded-full blur-xl"></div>
              <div className="absolute -bottom-12 -left-12 w-40 h-40 bg-white/5 rounded-full blur-2xl"></div>
            </div>

            {/* Role Management Component */}
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border-0 overflow-hidden">
              <NewRoleManagement />
            </div>
          </div>
        </RoleGuard>
      </DashboardLayout>
    </QueryClientProvider>
  )
}
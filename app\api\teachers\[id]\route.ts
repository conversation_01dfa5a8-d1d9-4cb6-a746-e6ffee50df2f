import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { formatTeacherName } from '@/app/utils/formatters'
import { cookies } from 'next/headers'
import { verifyJWT } from '@/lib/jwt'

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const teacher = await prisma.teacher.findUnique({
      where: {
        id: params.id,
      },
    })

    if (!teacher) {
      return NextResponse.json(
        { error: 'Teacher not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(teacher)
  } catch (error) {
    console.error('Error fetching teacher:', error)
    return NextResponse.json(
      { error: 'Failed to fetch teacher' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Verify authentication and authorization
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    }

    const decoded = await verifyJWT(token)

    // Only allow SUPER_ADMIN, ADMIN, and DATA_ENCODER to edit teachers
    if (!['SUPER_ADMIN', 'ADMIN', 'DATA_ENCODER'].includes(decoded.role)) {
      return NextResponse.json(
        { error: 'Forbidden - You do not have permission to edit teachers' },
        { status: 403 }
      )
    }

    const data = await request.json()
    const updatedTeacher = await prisma.teacher.update({
      where: {
        id: params.id,
      },
      data: {
        name: formatTeacherName(data.name),
        fatherName: formatTeacherName(data.fatherName),
        gender: data.gender,
        email: data.email,
        subject: data.subject,
        mobile: data.mobile,
      },
    })
    return NextResponse.json(updatedTeacher)
  } catch (error) {
    console.error('Error updating teacher:', error)
    return NextResponse.json(
      { error: 'Failed to update teacher' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Verify authentication and authorization
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    }

    const decoded = await verifyJWT(token)

    // Only allow SUPER_ADMIN, ADMIN, and DATA_ENCODER to delete teachers
    if (!['SUPER_ADMIN', 'ADMIN', 'DATA_ENCODER'].includes(decoded.role)) {
      return NextResponse.json(
        { error: 'Forbidden - You do not have permission to delete teachers' },
        { status: 403 }
      )
    }
    await prisma.teacher.delete({
      where: {
        id: params.id,
      },
    })
    return NextResponse.json({ message: 'Teacher deleted successfully' })
  } catch (error) {
    console.error('Error deleting teacher:', error)
    return NextResponse.json(
      { error: 'Failed to delete teacher' },
      { status: 500 }
    )
  }
}

'use client'

import React from 'react'
import { useAuth } from '../contexts/auth-context'
import { Button } from '../components/ui/button'
import { useRouter } from 'next/navigation'

export default function AuthTestPage() {
  const { user, isAuthenticated, isLoading, logout } = useAuth()
  const router = useRouter()

  const handleLogin = () => {
    router.push('/login')
  }

  const handleLogout = async () => {
    await logout()
    router.push('/login')
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-4">
      <div className="w-full max-w-md bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold mb-6 text-center">Authentication Test</h1>
        
        <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-md">
          <h2 className="text-lg font-semibold mb-2">Authentication Status</h2>
          <p className="mb-1">
            <span className="font-medium">Status:</span>{' '}
            <span className={isAuthenticated ? 'text-green-600' : 'text-red-600'}>
              {isAuthenticated ? 'Authenticated' : 'Not Authenticated'}
            </span>
          </p>
          
          {isAuthenticated && user && (
            <div className="mt-3">
              <p className="mb-1"><span className="font-medium">Name:</span> {user.name}</p>
              <p className="mb-1"><span className="font-medium">Email:</span> {user.email}</p>
              <p className="mb-1"><span className="font-medium">Role:</span> {user.role}</p>
            </div>
          )}
        </div>
        
        <div className="flex justify-center">
          {isAuthenticated ? (
            <Button 
              onClick={handleLogout}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              Logout
            </Button>
          ) : (
            <Button 
              onClick={handleLogin}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              Login
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}

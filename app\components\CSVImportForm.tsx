"use client"

import React, { useState, useRef, useCallback } from 'react'
import { But<PERSON> } from './ui/button'
import { Label } from "./ui/label"
import { Input } from "./ui/input"
import { Textarea } from "./ui/textarea"
import { Upload, FileText, AlertCircle, CheckCircle, Loader2, Download } from 'lucide-react'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select"
import { DialogFooter } from "./ui/dialog"
import { Mark } from './MarkList'
import { useQuery } from '@tanstack/react-query'
import { Alert, AlertDescription, AlertTitle } from "./ui/alert"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/table'
import Papa from 'papaparse'

// Academic terms and exam types (same thing in this case)
const terms = ['First Semester', 'Second Semester']

// Fixed academic years from 2020-2021 to 2027-2028
const academicYears = [
  '2020-2021',
  '2021-2022',
  '2022-2023',
  '2023-2024',
  '2024-2025',
  '2025-2026',
  '2026-2027',
  '2027-2028'
]

interface CSVImportFormProps {
  onSubmit: (marks: Omit<Mark, 'id' | 'dateRecorded' | 'grade'>[]) => void
  onCancel: () => void
  calculateGrade: (mark: number) => string
}

export function CSVImportForm({
  onSubmit,
  onCancel,
  calculateGrade
}: CSVImportFormProps) {
  // Form settings
  const [formSettings, setFormSettings] = useState({
    class: '',
    subject: '',
    term: 'First Semester',
    academicYear: '2024-2025',
    totalMarks: 100
  })

  // CSV import states
  const [file, setFile] = useState<File | null>(null)
  const [csvData, setCSVData] = useState<any[]>([])
  const [mappings, setMappings] = useState({
    studentId: 'none',
    studentName: 'none',
    obtainedMarks: 'no_headers', // Use a non-empty default value
    subject: 'default',
    term: 'default',
    academicYear: 'default'
  })
  const [headers, setHeaders] = useState<string[]>([])
  const [previewData, setPreviewData] = useState<any[]>([])
  const [importStep, setImportStep] = useState<'upload' | 'mapping' | 'preview'>('upload')
  const [importError, setImportError] = useState<string | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)

  const fileInputRef = useRef<HTMLInputElement>(null)

  // Fetch classes from the database
  const { data: classes, isLoading: isLoadingClasses } = useQuery({
    queryKey: ['classes'],
    queryFn: async () => {
      const res = await fetch('/api/classes')
      if (!res.ok) throw new Error('Failed to fetch classes')
      return res.json()
    }
  })

  // Fetch subjects based on selected class
  const { data: subjects, isLoading: isLoadingSubjects } = useQuery({
    queryKey: ['subjects', formSettings.class],
    queryFn: async () => {
      if (!formSettings.class) return []
      const res = await fetch(`/api/subjects?className=${formSettings.class}`)
      if (!res.ok) throw new Error('Failed to fetch subjects')
      return res.json()
    },
    enabled: !!formSettings.class
  })

  // Fetch students based on selected class
  const { data: students, isLoading: isLoadingStudents } = useQuery({
    queryKey: ['students', formSettings.class],
    queryFn: async () => {
      if (!formSettings.class) return []
      const res = await fetch(`/api/students?class=${formSettings.class}`)
      if (!res.ok) throw new Error('Failed to fetch students')
      return res.json()
    },
    enabled: !!formSettings.class
  })

  // Handle form setting changes
  const handleSettingChange = (name: string, value: string | number) => {
    setFormSettings({
      ...formSettings,
      [name]: value
    })
  }

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setImportError(null)
    const selectedFile = e.target.files?.[0]
    if (selectedFile) {
      if (selectedFile.type !== 'text/csv' && !selectedFile.name.endsWith('.csv')) {
        setImportError('Please upload a CSV file')
        setFile(null)
        return
      }
      setFile(selectedFile)
    }
  }

  // Parse CSV file
  const parseCSV = useCallback(() => {
    if (!file) return

    setIsProcessing(true)
    setImportError(null)

    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      complete: (results) => {
        if (results.errors.length > 0) {
          setImportError(`Error parsing CSV: ${results.errors[0].message}`)
          setIsProcessing(false)
          return
        }

        if (results.data.length === 0) {
          setImportError('The CSV file is empty')
          setIsProcessing(false)
          return
        }

        // Get headers from the first row
        const headers = Object.keys(results.data[0])
        if (headers.length < 2) {
          setImportError('CSV must contain at least student ID/name and marks columns')
          setIsProcessing(false)
          return
        }

        setHeaders(headers)
        setCSVData(results.data)
        setImportStep('mapping')
        setIsProcessing(false)
      },
      error: (error) => {
        setImportError(`Error parsing CSV: ${error.message}`)
        setIsProcessing(false)
      }
    })
  }, [file])

  // Handle mapping changes
  const handleMappingChange = (field: string, value: string) => {
    setMappings({
      ...mappings,
      [field]: value
    })
  }

  // Validate mappings and proceed to preview
  const validateAndPreview = () => {
    setImportError(null)

    // Check if all required mappings are set
    if ((!mappings.studentId || mappings.studentId === 'none') &&
        (!mappings.studentName || mappings.studentName === 'none')) {
      setImportError('You must map either Student ID or Student Name')
      return
    }

    if (!mappings.obtainedMarks || mappings.obtainedMarks === 'no_headers') {
      setImportError('You must map the Obtained Marks column')
      return
    }

    // Generate preview data
    try {
      const preview = csvData.map((row) => {
        let studentId = ''
        let studentName = ''

        // Find student by ID or name
        if (mappings.studentId && mappings.studentId !== 'none' && row[mappings.studentId]) {
          const student = students?.find((s: any) =>
            s.id === row[mappings.studentId] ||
            s.sid === row[mappings.studentId]
          )
          if (student) {
            studentId = student.id
            studentName = student.name
          }
        }

        // If no student found by ID, try by name
        if (!studentId && mappings.studentName && mappings.studentName !== 'none' && row[mappings.studentName]) {
          const student = students?.find((s: any) =>
            s.name.toLowerCase() === row[mappings.studentName].toLowerCase()
          )
          if (student) {
            studentId = student.id
            studentName = student.name
          }
        }

        // Get obtained marks
        const obtainedMarks = parseFloat(row[mappings.obtainedMarks]) || 0

        // Get subject, term, and academic year from CSV if mapped, otherwise use form settings
        const subject = mappings.subject && mappings.subject !== 'default' && row[mappings.subject]
          ? row[mappings.subject]
          : formSettings.subject

        const term = mappings.term && mappings.term !== 'default' && row[mappings.term]
          ? row[mappings.term]
          : formSettings.term

        const academicYear = mappings.academicYear && mappings.academicYear !== 'default' && row[mappings.academicYear]
          ? row[mappings.academicYear]
          : formSettings.academicYear

        return {
          studentId,
          studentName,
          obtainedMarks,
          subject,
          term,
          academicYear,
          originalData: row
        }
      })

      // Filter out rows where student wasn't found
      const validRows = preview.filter(row => row.studentId && row.studentName)

      if (validRows.length === 0) {
        setImportError('No valid student records found in CSV. Please check your mappings and ensure student IDs or names match the database.')
        return
      }

      setPreviewData(validRows)
      setImportStep('preview')
    } catch (error) {
      setImportError('Error processing CSV data. Please check your file format.')
    }
  }

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (importStep !== 'preview') {
      return
    }

    // Create mark objects for each student
    const marks = previewData.map(student => ({
      studentId: student.studentId,
      studentName: student.studentName,
      class: formSettings.class,
      subject: student.subject || formSettings.subject,
      term: student.term || formSettings.term,
      academicYear: student.academicYear || formSettings.academicYear,
      totalMarks: formSettings.totalMarks,
      obtainedMarks: student.obtainedMarks,
      remarks: '',
      recordedBy: 'Current Teacher'
    }))

    onSubmit(marks)
  }

  // Download sample CSV template
  const downloadTemplate = () => {
    const headers = ['StudentID', 'StudentName', 'Subject', 'Term', 'AcademicYear', 'ObtainedMarks']
    const sampleData = [
      ['1A001', 'John Doe', 'Maths', 'First Semester', '2023-2024', '85'],
      ['1A002', 'Jane Smith', 'English', 'First Semester', '2023-2024', '92'],
      ['1A003', 'Alex Johnson', 'Science', 'First Semester', '2023-2024', '78']
    ]

    const csvContent = [
      headers.join(','),
      ...sampleData.map(row => row.join(','))
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', 'marks_template.csv')
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  return (
    <form onSubmit={handleSubmit} className="grid gap-6 py-4">
      {/* Form Settings */}
      <div className="grid grid-cols-3 gap-4 pb-4 border-b">
        {/* Class */}
        <div className="grid gap-2">
          <Label htmlFor="class" className="text-sm font-medium">
            Class
          </Label>
          <Select
            value={formSettings.class}
            onValueChange={(value) => handleSettingChange('class', value)}
            required
          >
            <SelectTrigger>
              {isLoadingClasses ? (
                <div className="flex items-center">
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Loading classes...
                </div>
              ) : (
                <SelectValue placeholder="Select Class" />
              )}
            </SelectTrigger>
            <SelectContent>
              {classes && classes.length > 0 ? (
                classes.map((cls: { id: string, name: string }) => (
                  <SelectItem key={cls.id} value={cls.name}>
                    {cls.name}
                  </SelectItem>
                ))
              ) : (
                <div className="p-2 text-center text-sm text-gray-500">
                  No classes found
                </div>
              )}
            </SelectContent>
          </Select>
        </div>

        {/* Subject */}
        <div className="grid gap-2">
          <Label htmlFor="subject" className="text-sm font-medium">
            Subject
          </Label>
          <Select
            value={formSettings.subject}
            onValueChange={(value) => handleSettingChange('subject', value)}
            disabled={!formSettings.class || isLoadingSubjects}
            required
          >
            <SelectTrigger>
              {isLoadingSubjects ? (
                <div className="flex items-center">
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Loading subjects...
                </div>
              ) : (
                <SelectValue placeholder="Select Subject" />
              )}
            </SelectTrigger>
            <SelectContent>
              {subjects && subjects.length > 0 ? (
                subjects.map((subject: { id: string, name: string }) => (
                  <SelectItem key={subject.id} value={subject.name}>
                    {subject.name}
                  </SelectItem>
                ))
              ) : (
                <div className="p-2 text-center text-sm text-gray-500">
                  {formSettings.class ? 'No subjects found for this class' : 'Select a class first'}
                </div>
              )}
            </SelectContent>
          </Select>
        </div>

        {/* Term */}
        <div className="grid gap-2">
          <Label htmlFor="term" className="text-sm font-medium">
            Term Semester
          </Label>
          <Select
            value={formSettings.term}
            onValueChange={(value) => handleSettingChange('term', value)}
            required
          >
            <SelectTrigger>
              <SelectValue placeholder="Select term" />
            </SelectTrigger>
            <SelectContent>
              {terms.map(term => (
                <SelectItem key={term} value={term}>
                  {term}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Academic Year */}
        <div className="grid gap-2">
          <Label htmlFor="academicYear" className="text-sm font-medium">
            Academic Year
          </Label>
          <Select
            value={formSettings.academicYear}
            onValueChange={(value) => handleSettingChange('academicYear', value)}
            required
          >
            <SelectTrigger>
              <SelectValue placeholder="Select academic year" />
            </SelectTrigger>
            <SelectContent>
              {academicYears.map(year => (
                <SelectItem key={year} value={year}>
                  {year}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Total Marks */}
        <div className="grid gap-2">
          <Label htmlFor="totalMarks" className="text-sm font-medium">
            Total Marks
          </Label>
          <Input
            id="totalMarks"
            type="number"
            min="1"
            max="1000"
            value={formSettings.totalMarks}
            onChange={(e) => handleSettingChange('totalMarks', parseInt(e.target.value) || 100)}
            required
          />
        </div>
      </div>

      {/* Import Steps */}
      {importStep === 'upload' && (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">Upload CSV File</h3>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={downloadTemplate}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Download Template
            </Button>
          </div>

          <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
            <input
              type="file"
              accept=".csv"
              onChange={handleFileChange}
              className="hidden"
              ref={fileInputRef}
            />

            {file ? (
              <div className="space-y-4">
                <div className="flex items-center justify-center gap-2 text-green-600">
                  <CheckCircle className="h-5 w-5" />
                  <span className="font-medium">{file.name}</span>
                </div>
                <Button
                  type="button"
                  onClick={parseCSV}
                  disabled={isProcessing}
                  className="bg-indigo-600 hover:bg-indigo-700"
                >
                  {isProcessing ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <FileText className="mr-2 h-4 w-4" />
                      Parse CSV
                    </>
                  )}
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                <Upload className="h-12 w-12 mx-auto text-gray-400" />
                <p className="text-gray-600">Drag and drop your CSV file here, or click to browse</p>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => fileInputRef.current?.click()}
                >
                  Browse Files
                </Button>
              </div>
            )}
          </div>

          {importError && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{importError}</AlertDescription>
            </Alert>
          )}

          <div className="bg-blue-50 border-l-4 border-blue-500 p-4 text-blue-700 rounded-md">
            <h4 className="font-medium mb-1">CSV Format Requirements:</h4>
            <ul className="list-disc list-inside text-sm space-y-1">
              <li>File must be in CSV format</li>
              <li>Must include columns for student ID or name and marks</li>
              <li>Optional columns: Subject, Term Semester, Academic Year</li>
              <li>First row should contain column headers</li>
              <li>Student IDs should match the system IDs</li>
              <li>Marks should be numeric values</li>
              <li>Term values should be "First Semester" or "Second Semester"</li>
              <li>Academic Year format should be "YYYY-YYYY" (e.g., "2023-2024")</li>
            </ul>
          </div>
        </div>
      )}

      {importStep === 'mapping' && (
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Map CSV Columns</h3>

          <div className="grid grid-cols-3 gap-4">
            <div className="grid gap-2">
              <Label htmlFor="studentIdMapping" className="text-sm font-medium">
                Student ID Column
              </Label>
              <Select
                value={mappings.studentId}
                onValueChange={(value) => handleMappingChange('studentId', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select column" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">None</SelectItem>
                  {headers.map(header => (
                    <SelectItem key={header} value={header}>
                      {header}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="studentNameMapping" className="text-sm font-medium">
                Student Name Column
              </Label>
              <Select
                value={mappings.studentName}
                onValueChange={(value) => handleMappingChange('studentName', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select column" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">None</SelectItem>
                  {headers.map(header => (
                    <SelectItem key={header} value={header}>
                      {header}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="obtainedMarksMapping" className="text-sm font-medium">
                Obtained Marks Column
              </Label>
              <Select
                value={mappings.obtainedMarks}
                onValueChange={(value) => handleMappingChange('obtainedMarks', value)}
                required
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select column" />
                </SelectTrigger>
                <SelectContent>
                  {headers.length > 0 ? (
                    headers.map(header => (
                      <SelectItem key={header} value={header}>
                        {header}
                      </SelectItem>
                    ))
                  ) : (
                    <SelectItem value="no_headers" disabled>
                      No columns found
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="subjectMapping" className="text-sm font-medium">
                Subject Column
              </Label>
              <Select
                value={mappings.subject}
                onValueChange={(value) => handleMappingChange('subject', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select column" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="default">None (Use form setting)</SelectItem>
                  {headers.map(header => (
                    <SelectItem key={header} value={header}>
                      {header}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="termMapping" className="text-sm font-medium">
                Term Semester Column
              </Label>
              <Select
                value={mappings.term}
                onValueChange={(value) => handleMappingChange('term', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select column" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="default">None (Use form setting)</SelectItem>
                  {headers.map(header => (
                    <SelectItem key={header} value={header}>
                      {header}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="academicYearMapping" className="text-sm font-medium">
                Academic Year Column
              </Label>
              <Select
                value={mappings.academicYear}
                onValueChange={(value) => handleMappingChange('academicYear', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select column" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="default">None (Use form setting)</SelectItem>
                  {headers.map(header => (
                    <SelectItem key={header} value={header}>
                      {header}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {importError && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{importError}</AlertDescription>
            </Alert>
          )}

          <div className="bg-gray-50 border rounded-md p-4">
            <h4 className="font-medium mb-2">CSV Preview</h4>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    {headers.map(header => (
                      <TableHead key={header}>{header}</TableHead>
                    ))}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {csvData.slice(0, 5).map((row, index) => (
                    <TableRow key={index}>
                      {headers.map(header => (
                        <TableCell key={header}>{row[header]}</TableCell>
                      ))}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              {csvData.length > 5 && (
                <p className="text-xs text-gray-500 mt-2 text-right">
                  Showing 5 of {csvData.length} rows
                </p>
              )}
            </div>
          </div>

          <div className="flex justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setImportStep('upload')
                setFile(null)
                setCSVData([])
                setHeaders([])
                setMappings({
                  studentId: 'none',
                  studentName: 'none',
                  obtainedMarks: 'no_headers',
                  subject: 'default',
                  term: 'default',
                  academicYear: 'default'
                })
              }}
            >
              Back
            </Button>
            <Button
              type="button"
              onClick={validateAndPreview}
              disabled={
                (!mappings.studentId || mappings.studentId === 'none') &&
                (!mappings.studentName || mappings.studentName === 'none') ||
                !mappings.obtainedMarks ||
                mappings.obtainedMarks === 'no_headers'
              }
            >
              Continue
            </Button>
          </div>
        </div>
      )}

      {importStep === 'preview' && (
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Preview and Confirm</h3>

          <div className="bg-gray-50 border rounded-md p-4">
            <h4 className="font-medium mb-2">Import Summary</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p><span className="font-medium">Class:</span> {formSettings.class}</p>
                <p><span className="font-medium">Subject:</span> {formSettings.subject}</p>
                <p><span className="font-medium">Term:</span> {formSettings.term}</p>
              </div>
              <div>
                <p><span className="font-medium">Academic Year:</span> {formSettings.academicYear}</p>
                <p><span className="font-medium">Total Marks:</span> {formSettings.totalMarks}</p>
                <p><span className="font-medium">Records to Import:</span> {previewData.length}</p>
              </div>
            </div>
          </div>

          <div className="border rounded-md overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">#</TableHead>
                  <TableHead>Student</TableHead>
                  <TableHead>Subject</TableHead>
                  <TableHead>Term</TableHead>
                  <TableHead>Academic Year</TableHead>
                  <TableHead>Obtained Marks</TableHead>
                  <TableHead>Grade</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {previewData.map((student, index) => (
                  <TableRow key={index}>
                    <TableCell>{index + 1}</TableCell>
                    <TableCell>{student.studentName}</TableCell>
                    <TableCell>{student.subject || formSettings.subject}</TableCell>
                    <TableCell>{student.term || formSettings.term}</TableCell>
                    <TableCell>{student.academicYear || formSettings.academicYear}</TableCell>
                    <TableCell>{student.obtainedMarks}</TableCell>
                    <TableCell>{calculateGrade(student.obtainedMarks)}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          <div className="flex justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={() => setImportStep('mapping')}
            >
              Back
            </Button>
            <Button
              type="submit"
              disabled={previewData.length === 0 || !formSettings.class || !formSettings.subject}
              className="bg-indigo-600 hover:bg-indigo-700"
            >
              Import Marks
            </Button>
          </div>
        </div>
      )}

      <DialogFooter>
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
      </DialogFooter>
    </form>
  )
}

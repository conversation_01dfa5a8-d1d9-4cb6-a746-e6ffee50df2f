import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// Mock data for fee types (used as fallback)
const mockFeeTypes = [
  {
    id: 'fee1',
    name: '<PERSON><PERSON> Fee (Monthly)',
    description: 'Monthly tuition fee',
    amount: 1500,
    frequency: 'monthly',
    isActive: true,
    createdAt: new Date('2023-09-01T00:00:00Z'),
    updatedAt: new Date('2023-09-01T00:00:00Z')
  },
  {
    id: 'fee2',
    name: 'Registration Fee',
    description: 'One-time registration fee',
    amount: 2000,
    frequency: 'one-time',
    isActive: true,
    createdAt: new Date('2023-09-01T00:00:00Z'),
    updatedAt: new Date('2023-09-01T00:00:00Z')
  },
  {
    id: 'fee3',
    name: 'Exam <PERSON>e',
    description: 'Fee for exams',
    amount: 1800,
    frequency: 'semester',
    isActive: true,
    createdAt: new Date('2023-09-01T00:00:00Z'),
    updatedAt: new Date('2023-09-01T00:00:00Z')
  },
  {
    id: 'fee4',
    name: 'Library Fee',
    description: 'Annual library fee',
    amount: 500,
    frequency: 'yearly',
    isActive: true,
    createdAt: new Date('2023-09-01T00:00:00Z'),
    updatedAt: new Date('2023-09-01T00:00:00Z')
  }
];

// Helper function to check if a model exists in Prisma
function isModelAvailable(modelName: string): boolean {
  return Object.prototype.hasOwnProperty.call(prisma, modelName);
}

// GET all fee types
export async function GET() {
  try {
    // Check if FeeType model is available
    if (!isModelAvailable('feeType')) {
      console.warn('FeeType model is not available in Prisma client. Using mock data.');
      return NextResponse.json(mockFeeTypes);
    }

    try {
      const feeTypes = await prisma.feeType.findMany({
        orderBy: {
          name: 'asc',
        },
      })

      return NextResponse.json(feeTypes)
    } catch (dbError) {
      console.error('Database error fetching fee types:', dbError);

      // If there's a database error, fall back to mock data
      console.warn('Falling back to mock fee types data');
      return NextResponse.json(mockFeeTypes);
    }
  } catch (error) {
    console.error('Error fetching fee types:', error)

    // Return mock data as a fallback
    console.warn('Error in fee types API, returning mock data');
    return NextResponse.json(mockFeeTypes);
  }
}

// CREATE a new fee type
export async function POST(request: Request) {
  try {
    const data = await request.json()

    // Validate required fields
    if (!data.name || !data.amount || !data.frequency) {
      return NextResponse.json(
        { error: 'Name, amount, and frequency are required' },
        { status: 400 }
      )
    }

    // Check if FeeType model is available
    if (!isModelAvailable('feeType')) {
      console.warn('FeeType model is not available in Prisma client. Using mock data.');

      // Generate a mock fee type response
      const mockFeeType = {
        id: `mock-${Date.now()}`,
        name: data.name,
        description: data.description || '',
        amount: parseFloat(data.amount),
        frequency: data.frequency,
        isActive: data.isActive !== undefined ? data.isActive : true,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      return NextResponse.json(mockFeeType, { status: 201 });
    }

    try {
      // Check if fee type with the same name already exists
      const existingFeeType = await prisma.feeType.findUnique({
        where: {
          name: data.name,
        },
      })

      if (existingFeeType) {
        return NextResponse.json(
          { error: 'A fee type with this name already exists' },
          { status: 409 }
        )
      }

      // Create the new fee type
      const feeType = await prisma.feeType.create({
        data: {
          name: data.name,
          description: data.description || '',
          amount: parseFloat(data.amount),
          frequency: data.frequency,
          isActive: data.isActive !== undefined ? data.isActive : true,
        },
      })

      return NextResponse.json(feeType, { status: 201 })
    } catch (dbError) {
      console.error('Database error creating fee type:', dbError);

      // Generate a mock fee type response as fallback
      const mockFeeType = {
        id: `mock-${Date.now()}`,
        name: data.name,
        description: data.description || '',
        amount: parseFloat(data.amount),
        frequency: data.frequency,
        isActive: data.isActive !== undefined ? data.isActive : true,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      return NextResponse.json(mockFeeType, { status: 201 });
    }
  } catch (error) {
    console.error('Error creating fee type:', error)
    return NextResponse.json(
      { error: 'Failed to create fee type' },
      { status: 500 }
    )
  }
}

'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { themes, ThemeColor, ThemeMode, ThemeColors } from '../config/themes';

interface ThemeContextType {
  currentTheme: ThemeColors;
  themeColor: ThemeColor;
  themeMode: ThemeMode;
  setThemeColor: (color: ThemeColor) => void;
  setThemeMode: (mode: ThemeMode) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [themeColor, setThemeColor] = useState<ThemeColor>('red');
  const [themeMode, setThemeMode] = useState<ThemeMode>('light');
  const currentTheme = themes[themeColor][themeMode];

  useEffect(() => {
    // Load saved theme preferences from localStorage
    const savedColor = localStorage.getItem('themeColor') as ThemeColor;
    const savedMode = localStorage.getItem('themeMode') as ThemeMode;
    
    if (savedColor) setThemeColor(savedColor);
    if (savedMode) setThemeMode(savedMode);
  }, []);

  const handleThemeColorChange = (color: ThemeColor) => {
    setThemeColor(color);
    localStorage.setItem('themeColor', color);
  };

  const handleThemeModeChange = (mode: ThemeMode) => {
    setThemeMode(mode);
    localStorage.setItem('themeMode', mode);
  };

  useEffect(() => {
    // Apply theme colors to CSS variables
    const root = document.documentElement;
    Object.entries(currentTheme).forEach(([key, value]) => {
      root.style.setProperty(`--${key}`, value as string);
    });
  }, [currentTheme]);

  return (
    <ThemeContext.Provider value={{ currentTheme, themeColor, themeMode, setThemeColor, setThemeMode }}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}; 
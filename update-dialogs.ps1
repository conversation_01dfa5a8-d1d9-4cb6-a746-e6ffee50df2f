# PowerShell script to update Dialog components to RecursionSafeDialog in all website-management pages

Write-Host "Starting to update Dialog components to RecursionSafeDialog..." -ForegroundColor Green

# Define the directory to search
$directory = "app\website-management"

# Get all TypeScript/TSX files in the directory
$files = Get-ChildItem -Path $directory -Recurse -Include "*.tsx", "*.ts"

# Counter for modified files
$modifiedFiles = 0

foreach ($file in $files) {
    $content = Get-Content -Path $file.FullName -Raw
    $originalContent = $content

    # Check if the file imports Dialog from ui/dialog
    if ($content -match "Dialog.*from.*components/ui/dialog") {
        Write-Host "Processing file: $($file.FullName)" -ForegroundColor Yellow

        # Add RecursionSafeDialog import
        $newImport = "import { RecursionSafeDialog, RecursionSafeDialogContent, RecursionSafeDialogHeader, RecursionSafeDialogTitle, RecursionSafeDialogDescription, RecursionSafeDialogFooter } from '../../components/RecursionSafeDialog'"
        $content = $content -replace "(import.*from.*components/ui/dialog.*)", "`$1`n$newImport"

        # Replace Dialog opening tag
        $content = $content -replace "<Dialog\s+open=\{([^}]+)\}\s+onOpenChange=\{([^}]+)\}>", "<RecursionSafeDialog open={`$1} onOpenChange={`$2}>"

        # Replace DialogContent
        $content = $content -replace "<DialogContent\s+className=[`"]([^`"]+)[`"]>", "<RecursionSafeDialogContent>"
        $content = $content -replace "<DialogContent>", "<RecursionSafeDialogContent>"

        # Replace DialogHeader
        $content = $content -replace "<DialogHeader>", "<RecursionSafeDialogHeader>"
        $content = $content -replace "</DialogHeader>", "</RecursionSafeDialogHeader>"

        # Replace DialogTitle
        $content = $content -replace "<DialogTitle>", "<RecursionSafeDialogTitle>"
        $content = $content -replace "</DialogTitle>", "</RecursionSafeDialogTitle>"

        # Replace DialogDescription
        $content = $content -replace "<DialogDescription>", "<RecursionSafeDialogDescription>"
        $content = $content -replace "</DialogDescription>", "</RecursionSafeDialogDescription>"

        # Replace DialogFooter
        $content = $content -replace "<DialogFooter>", "<RecursionSafeDialogFooter>"
        $content = $content -replace "</DialogFooter>", "</RecursionSafeDialogFooter>"

        # Replace closing tags
        $content = $content -replace "</DialogContent>", "</RecursionSafeDialogContent>"
        $content = $content -replace "</Dialog>", "</RecursionSafeDialog>"

        # If content was modified, save the file
        if ($content -ne $originalContent) {
            Set-Content -Path $file.FullName -Value $content
            Write-Host "Updated file: $($file.FullName)" -ForegroundColor Green
            $modifiedFiles++
        } else {
            Write-Host "No changes needed for: $($file.FullName)" -ForegroundColor Cyan
        }
    }
}

Write-Host "Completed updating Dialog components to RecursionSafeDialog." -ForegroundColor Green
Write-Host "Modified $modifiedFiles files." -ForegroundColor Green
Write-Host "Please review the changes and test the application." -ForegroundColor Yellow

"use client";

import React from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { FaCheckCircle, FaTrophy, FaHistory, FaUsers } from 'react-icons/fa';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.5 },
  },
};

export default function AboutPage() {
  return (
    <div className="min-h-screen pt-20">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-blue-900 to-indigo-900 text-white">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{ backgroundImage: 'url(/images/pattern.svg)', backgroundSize: '20px' }}></div>
        </div>
        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center max-w-3xl mx-auto"
          >
            <h1 className="text-4xl md:text-5xl font-bold mb-6">About Alfalah Islamic School</h1>
            <p className="text-xl text-blue-100">
              Providing quality education with Islamic values since 1995. Our mission is to nurture academic excellence and moral character.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Mission & Vision Section */}
      <section id="mission-vision" className="py-16 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.7 }}
              viewport={{ once: true }}
            >
              <div className="relative h-[400px] rounded-lg overflow-hidden shadow-xl">
                <Image
                  src="/images/portrait.jpg" // Replace with actual image
                  alt="Students at Alfalah Islamic School"
                  fill
                  className="object-cover"
                />
              </div>
            </motion.div>

            <motion.div
              variants={containerVariants}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              className="space-y-8"
            >
              <motion.div variants={itemVariants}>
                <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Our Mission</h2>
                <div className="w-20 h-1 bg-blue-600 rounded-full mb-4"></div>
                <p className="text-gray-600 dark:text-gray-300">
                  To provide a comprehensive education that nurtures academic excellence, Islamic values, and moral character, preparing students to be successful contributors to society and exemplary Muslims.
                </p>
              </motion.div>

              <motion.div variants={itemVariants}>
                <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Our Vision</h2>
                <div className="w-20 h-1 bg-blue-600 rounded-full mb-4"></div>
                <p className="text-gray-600 dark:text-gray-300">
                  To be a leading educational institution that empowers students with knowledge, faith, and character, enabling them to excel in this world and the hereafter.
                </p>
              </motion.div>

              <motion.div variants={itemVariants} className="space-y-4">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Our Core Values</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  <div className="flex items-start">
                    <FaCheckCircle className="text-blue-600 mt-1 mr-2 flex-shrink-0" />
                    <span className="text-gray-600 dark:text-gray-300">Excellence in Education</span>
                  </div>
                  <div className="flex items-start">
                    <FaCheckCircle className="text-blue-600 mt-1 mr-2 flex-shrink-0" />
                    <span className="text-gray-600 dark:text-gray-300">Islamic Principles</span>
                  </div>
                  <div className="flex items-start">
                    <FaCheckCircle className="text-blue-600 mt-1 mr-2 flex-shrink-0" />
                    <span className="text-gray-600 dark:text-gray-300">Character Development</span>
                  </div>
                  <div className="flex items-start">
                    <FaCheckCircle className="text-blue-600 mt-1 mr-2 flex-shrink-0" />
                    <span className="text-gray-600 dark:text-gray-300">Community Service</span>
                  </div>
                  <div className="flex items-start">
                    <FaCheckCircle className="text-blue-600 mt-1 mr-2 flex-shrink-0" />
                    <span className="text-gray-600 dark:text-gray-300">Respect & Responsibility</span>
                  </div>
                  <div className="flex items-start">
                    <FaCheckCircle className="text-blue-600 mt-1 mr-2 flex-shrink-0" />
                    <span className="text-gray-600 dark:text-gray-300">Continuous Improvement</span>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* History Section */}
      <section id="history" className="py-16 bg-gray-50 dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Our History</h2>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              From humble beginnings to a thriving educational institution, the journey of Alfalah Islamic School spans over 25 years of dedication to excellence.
            </p>
          </motion.div>

          <div className="max-w-4xl mx-auto">
            <div className="relative">
              {/* Timeline Line */}
              <div className="absolute left-0 md:left-1/2 transform md:-translate-x-1/2 h-full w-1 bg-blue-600"></div>

              {/* Timeline Items */}
              <motion.div
                variants={containerVariants}
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
                className="space-y-12"
              >
                {/* 1995 */}
                <motion.div variants={itemVariants} className="relative flex flex-col md:flex-row items-center">
                  <div className="flex-1 md:text-right md:pr-8 order-2 md:order-1">
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">1995: Foundation</h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      Alfalah Islamic School was founded with just 50 students and 5 teachers, operating from a small rented building.
                    </p>
                  </div>
                  <div className="order-1 md:order-2 mb-4 md:mb-0">
                    <div className="w-10 h-10 rounded-full bg-blue-600 flex items-center justify-center text-white z-10 relative">
                      <FaHistory />
                    </div>
                  </div>
                  <div className="flex-1 md:pl-8 order-3"></div>
                </motion.div>

                {/* 2000 */}
                <motion.div variants={itemVariants} className="relative flex flex-col md:flex-row items-center">
                  <div className="flex-1 md:pr-8 order-2 md:order-1"></div>
                  <div className="order-1 md:order-2 mb-4 md:mb-0">
                    <div className="w-10 h-10 rounded-full bg-blue-600 flex items-center justify-center text-white z-10 relative">
                      <FaHistory />
                    </div>
                  </div>
                  <div className="flex-1 md:pl-8 order-3 md:text-left">
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">2000: Expansion</h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      The school moved to its current campus, expanding to accommodate 200 students with improved facilities.
                    </p>
                  </div>
                </motion.div>

                {/* 2010 */}
                <motion.div variants={itemVariants} className="relative flex flex-col md:flex-row items-center">
                  <div className="flex-1 md:text-right md:pr-8 order-2 md:order-1">
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">2010: Accreditation</h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      Alfalah Islamic School received full accreditation and recognition for its academic excellence and Islamic education.
                    </p>
                  </div>
                  <div className="order-1 md:order-2 mb-4 md:mb-0">
                    <div className="w-10 h-10 rounded-full bg-blue-600 flex items-center justify-center text-white z-10 relative">
                      <FaHistory />
                    </div>
                  </div>
                  <div className="flex-1 md:pl-8 order-3"></div>
                </motion.div>

                {/* 2020 */}
                <motion.div variants={itemVariants} className="relative flex flex-col md:flex-row items-center">
                  <div className="flex-1 md:pr-8 order-2 md:order-1"></div>
                  <div className="order-1 md:order-2 mb-4 md:mb-0">
                    <div className="w-10 h-10 rounded-full bg-blue-600 flex items-center justify-center text-white z-10 relative">
                      <FaHistory />
                    </div>
                  </div>
                  <div className="flex-1 md:pl-8 order-3 md:text-left">
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">2020: Modern Era</h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      The school implemented advanced technology and expanded its curriculum to include STEM programs while maintaining its Islamic foundation.
                    </p>
                  </div>
                </motion.div>

                {/* Today */}
                <motion.div variants={itemVariants} className="relative flex flex-col md:flex-row items-center">
                  <div className="flex-1 md:text-right md:pr-8 order-2 md:order-1">
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">Today</h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      With over 500 students and 50 staff members, Alfalah Islamic School continues to grow and excel, providing quality education with Islamic values.
                    </p>
                  </div>
                  <div className="order-1 md:order-2 mb-4 md:mb-0">
                    <div className="w-10 h-10 rounded-full bg-blue-600 flex items-center justify-center text-white z-10 relative">
                      <FaHistory />
                    </div>
                  </div>
                  <div className="flex-1 md:pl-8 order-3"></div>
                </motion.div>
              </motion.div>
            </div>
          </div>
        </div>
      </section>

      {/* Achievements Section */}
      <section id="achievements" className="py-16 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Achievements & Accreditations</h2>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Our commitment to excellence has been recognized through various achievements and accreditations.
            </p>
          </motion.div>

          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          >
            {/* Achievement Cards */}
            <motion.div variants={itemVariants} className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <div className="flex items-center mb-4">
                <FaTrophy className="text-amber-500 text-3xl mr-4" />
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Academic Excellence</h3>
              </div>
              <p className="text-gray-600 dark:text-gray-300">
                Consistently ranked among the top 10% of schools in standardized test scores for the past 5 years.
              </p>
            </motion.div>

            <motion.div variants={itemVariants} className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <div className="flex items-center mb-4">
                <FaTrophy className="text-amber-500 text-3xl mr-4" />
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">National Recognition</h3>
              </div>
              <p className="text-gray-600 dark:text-gray-300">
                Received the National School of Excellence Award in 2022 for outstanding educational programs.
              </p>
            </motion.div>

            <motion.div variants={itemVariants} className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <div className="flex items-center mb-4">
                <FaTrophy className="text-amber-500 text-3xl mr-4" />
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">STEM Achievement</h3>
              </div>
              <p className="text-gray-600 dark:text-gray-300">
                Our students have won regional and national competitions in robotics, mathematics, and science.
              </p>
            </motion.div>

            <motion.div variants={itemVariants} className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <div className="flex items-center mb-4">
                <FaTrophy className="text-amber-500 text-3xl mr-4" />
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Quranic Excellence</h3>
              </div>
              <p className="text-gray-600 dark:text-gray-300">
                Our students have consistently placed in the top positions in national Quran memorization and recitation competitions.
              </p>
            </motion.div>

            <motion.div variants={itemVariants} className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <div className="flex items-center mb-4">
                <FaTrophy className="text-amber-500 text-3xl mr-4" />
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Sports Championships</h3>
              </div>
              <p className="text-gray-600 dark:text-gray-300">
                Our sports teams have won multiple regional championships in soccer, basketball, and track and field.
              </p>
            </motion.div>

            <motion.div variants={itemVariants} className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <div className="flex items-center mb-4">
                <FaTrophy className="text-amber-500 text-3xl mr-4" />
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Community Service</h3>
              </div>
              <p className="text-gray-600 dark:text-gray-300">
                Recognized for outstanding community service initiatives, with students contributing over 5,000 volunteer hours annually.
              </p>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Leadership Section */}
      <section id="leadership" className="py-16 bg-gray-50 dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Our Leadership</h2>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Meet the dedicated individuals who guide our school's vision and mission.
            </p>
          </motion.div>

          <div className="max-w-4xl mx-auto">
            {/* Headmaster's Message */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7 }}
              viewport={{ once: true }}
              className="bg-white dark:bg-gray-900 rounded-lg shadow-md overflow-hidden mb-12"
            >
              <div className="grid grid-cols-1 md:grid-cols-3">
                <div className="relative h-64 md:h-auto">
                  <Image
                    src="/images/portrait.jpg" // Replace with actual headmaster image
                    alt="Headmaster"
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="p-6 md:col-span-2">
                  <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Headmaster's Message</h3>
                  <h4 className="text-lg font-semibold text-blue-600 dark:text-blue-400 mb-4">Dr. Abdullah Rahman</h4>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    "At Alfalah Islamic School, we are committed to providing an educational environment that nurtures both academic excellence and Islamic values. Our goal is to prepare students not only for success in this world but also for the hereafter.
                  </p>
                  <p className="text-gray-600 dark:text-gray-300">
                    We believe that every child has unique talents and abilities, and our dedicated staff works tirelessly to help each student reach their full potential. We invite you to join our school community and experience the difference that a balanced education can make in your child's life."
                  </p>
                </div>
              </div>
            </motion.div>

            {/* Board of Governors */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7 }}
              viewport={{ once: true }}
              className="space-y-6"
            >
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white text-center mb-6">Board of Governors</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* Board Member Cards */}
                <div className="bg-white dark:bg-gray-900 rounded-lg shadow-md overflow-hidden">
                  <div className="relative h-48">
                    <Image
                      src="/images/portrait.jpg" // Replace with actual board member image
                      alt="Board Member"
                      fill
                      className="object-cover"
                    />
                  </div>
                  <div className="p-4 text-center">
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white">Dr. Yusuf Ali</h4>
                    <p className="text-blue-600 dark:text-blue-400 text-sm mb-2">Chairman</p>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">Professor of Islamic Studies</p>
                  </div>
                </div>

                <div className="bg-white dark:bg-gray-900 rounded-lg shadow-md overflow-hidden">
                  <div className="relative h-48">
                    <Image
                      src="/images/portrait.jpg" // Replace with actual board member image
                      alt="Board Member"
                      fill
                      className="object-cover"
                    />
                  </div>
                  <div className="p-4 text-center">
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white">Dr. Aisha Khan</h4>
                    <p className="text-blue-600 dark:text-blue-400 text-sm mb-2">Vice Chair</p>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">Educational Consultant</p>
                  </div>
                </div>

                <div className="bg-white dark:bg-gray-900 rounded-lg shadow-md overflow-hidden">
                  <div className="relative h-48">
                    <Image
                      src="/images/portrait.jpg" // Replace with actual board member image
                      alt="Board Member"
                      fill
                      className="object-cover"
                    />
                  </div>
                  <div className="p-4 text-center">
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white">Mr. Ibrahim Hassan</h4>
                    <p className="text-blue-600 dark:text-blue-400 text-sm mb-2">Treasurer</p>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">Business Executive</p>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  );
}

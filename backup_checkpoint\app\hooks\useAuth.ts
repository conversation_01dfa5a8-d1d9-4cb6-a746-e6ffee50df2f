import { useToast } from '../../components/ui/use-toast'

export function useAuth() {
  const { toast } = useToast()

  const logout = async () => {
    try {
      const res = await fetch('/api/auth/logout', {
        method: 'POST',
      })

      if (!res.ok) {
        throw new Error('Logout failed')
      }

      toast({
        title: "Success",
        description: "Logged out successfully",
      })

      // Redirect to login
      window.location.href = '/login'

    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to logout",
        variant: "destructive"
      })
    }
  }

  return { logout }
}



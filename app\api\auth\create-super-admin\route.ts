import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { cookies } from 'next/headers'
import { verifyJWT } from '@/lib/jwt'

/**
 * This API route creates a Super Admin account with the specified credentials
 * or returns the existing Super Admin account if one already exists.
 *
 * This route is now protected and can only be accessed by an existing Super Admin.
 */
export async function POST(request: Request) {
  try {
    console.log('Checking authorization for Super Admin creation...')

    // Check if the request is from an existing Super Admin
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    // If there's no token, this is unauthorized
    if (!token) {
      console.log('No authentication token found')
      return NextResponse.json(
        { error: 'Unauthorized. Only existing Super Admins can create new Super Admin accounts.' },
        { status: 401 }
      )
    }

    try {
      // Verify the token
      const jwtSecret = process.env.JWT_SECRET || 'school_management_secure_jwt_secret_key_2023'
      const decoded = verifyJWT(token) as {
        id: string
        email: string
        role: string
        name: string
      }

      // Check if the user is a Super Admin
      if (decoded.role.toLowerCase() !== 'super_admin') {
        console.log('User is not a Super Admin')
        return NextResponse.json(
          { error: 'Unauthorized. Only existing Super Admins can create new Super Admin accounts.' },
          { status: 403 }
        )
      }
    } catch (error) {
      console.log('Invalid token')
      return NextResponse.json(
        { error: 'Unauthorized. Invalid authentication token.' },
        { status: 401 }
      )
    }

    console.log('Authorization successful. Creating Super Admin account...')

    // Get the request body
    const body = await request.json()
    const { username, password } = body

    // Validate input
    if (!username || !password) {
      console.log('Missing required fields')
      return NextResponse.json(
        { error: 'Username and password are required' },
        { status: 400 }
      )
    }

    // Validate password length
    if (password.length < 8) {
      console.log('Password too short')
      return NextResponse.json(
        { error: 'Password must be at least 8 characters long' },
        { status: 400 }
      )
    }

    // Check if a Super Admin already exists
    const existingSuperAdmin = await prisma.user.findFirst({
      where: { role: 'SUPER_ADMIN' },
    })

    // If a Super Admin exists, check if it's the one we're trying to create
    if (existingSuperAdmin) {
      const existingWithSameEmail = await prisma.user.findUnique({
        where: { email: username },
      })

      if (existingWithSameEmail && existingWithSameEmail.role === 'SUPER_ADMIN') {
        console.log('Super Admin with this username already exists')

        // Generate JWT token for the existing Super Admin
        const jwtSecret = process.env.JWT_SECRET || 'school_management_secure_jwt_secret_key_2023'
        const existingToken = jwt.sign(
          {
            id: existingWithSameEmail.id,
            email: existingWithSameEmail.email,
            role: existingWithSameEmail.role,
            name: existingWithSameEmail.name
          },
          jwtSecret,
          { expiresIn: '24h' }
        )

        // Create response with token
        const response = NextResponse.json({
          status: 'success',
          message: 'Super Admin already exists',
          user: {
            id: existingWithSameEmail.id,
            email: existingWithSameEmail.email,
            name: existingWithSameEmail.name,
            role: existingWithSameEmail.role
          }
        })

        // Set the token cookie
        response.cookies.set('token', existingToken, {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax',
          maxAge: 86400, // 24 hours
          path: '/'
        })

        // Also set a non-httpOnly cookie for client-side access
        response.cookies.set('auth_status', 'logged_in', {
          httpOnly: false,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax',
          maxAge: 86400,
          path: '/'
        })

        return response
      } else {
        console.log('A different Super Admin already exists')
        return NextResponse.json(
          { error: 'A Super Admin account already exists. Only one Super Admin account is allowed.' },
          { status: 400 }
        )
      }
    }

    // Hash the password
    console.log('Hashing password')
    const hashedPassword = await bcrypt.hash(password, 10)

    // Create the Super Admin user
    console.log('Creating new Super Admin user')
    const superAdmin = await prisma.user.create({
      data: {
        name: 'Super Admin',
        email: username,
        password: hashedPassword,
        role: 'SUPER_ADMIN',
        status: 'active',
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
      },
    })

    console.log('Super Admin created successfully:', superAdmin)

    // Generate JWT token for the new Super Admin
    const jwtSecret = process.env.JWT_SECRET || 'school_management_secure_jwt_secret_key_2023'
    const newToken = jwt.sign(
      {
        id: superAdmin.id,
        email: superAdmin.email,
        role: superAdmin.role,
        name: superAdmin.name
      },
      jwtSecret,
      { expiresIn: '24h' }
    )

    // Create response with token
    const response = NextResponse.json({
      status: 'success',
      message: 'Super Admin created successfully',
      user: superAdmin
    })

    // Set the token cookie
    response.cookies.set('token', newToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 86400, // 24 hours
      path: '/'
    })

    // Also set a non-httpOnly cookie for client-side access
    response.cookies.set('auth_status', 'logged_in', {
      httpOnly: false,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 86400,
      path: '/'
    })

    return response
  } catch (error) {
    console.error('Error creating Super Admin:', error)

    // Check for specific Prisma errors
    if (error instanceof Error) {
      // Check for unique constraint violation
      if (error.message.includes('Unique constraint failed')) {
        return NextResponse.json(
          { error: 'Email is already registered' },
          { status: 400 }
        )
      }

      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to create Super Admin' },
      { status: 500 }
    )
  }
}

import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  combineTeachers, 
  filterTeachers, 
  type TeacherFromTable, 
  type UserWithTeacherRole 
} from '@/app/utils/teacher-utils'

/**
 * GET - Fetch teachers from both Teacher table and Users table (with teacher role)
 * Query parameters:
 * - search: Filter teachers by name, email, subject, or mobile
 * - includeAssigned: Include teachers already assigned (default: true)
 */
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const searchTerm = searchParams.get('search') || ''
    const includeAssigned = searchParams.get('includeAssigned') !== 'false'

    console.log('Fetching unified teachers list...')
    console.log('Search term:', searchTerm)
    console.log('Include assigned:', includeAssigned)

    // Fetch teachers from Teacher table
    const teachersFromTable = await prisma.teacher.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        subject: true,
        mobile: true,
        fatherName: true,
        gender: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: {
        name: 'asc',
      },
    }) as TeacherFromTable[]

    // Fetch users with teacher role
    const usersWithTeacherRole = await prisma.user.findMany({
      where: {
        role: 'TEACHER',
        status: 'active', // Only include active users
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        phone: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: {
        name: 'asc',
      },
    }) as UserWithTeacherRole[]

    console.log(`Found ${teachersFromTable.length} teachers from Teacher table`)
    console.log(`Found ${usersWithTeacherRole.length} users with teacher role`)

    // Combine and deduplicate teachers
    let unifiedTeachers = combineTeachers(teachersFromTable, usersWithTeacherRole)

    // Apply search filter if provided
    if (searchTerm) {
      unifiedTeachers = filterTeachers(unifiedTeachers, searchTerm)
    }

    console.log(`Returning ${unifiedTeachers.length} unified teachers`)

    return NextResponse.json({
      teachers: unifiedTeachers,
      meta: {
        total: unifiedTeachers.length,
        fromTeacherTable: teachersFromTable.length,
        fromUserTable: usersWithTeacherRole.length,
        searchTerm: searchTerm || null,
      }
    })
  } catch (error) {
    console.error('Error fetching unified teachers:', error)
    return NextResponse.json(
      { 
        error: 'Failed to fetch teachers',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

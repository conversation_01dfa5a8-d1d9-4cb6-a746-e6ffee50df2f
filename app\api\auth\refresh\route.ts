import { NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { verifyJWT } from '@/lib/jwt'
import jwt from 'jsonwebtoken'
import { prisma } from '@/lib/prisma'

export async function POST() {
  try {
    console.log('Token refresh request received')
    
    // Get the token from cookies
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value
    
    console.log('Token found:', !!token)

    if (!token) {
      console.error('No token found in cookies')
      return NextResponse.json(
        { error: 'No token found' },
        { status: 401 }
      )
    }

    try {
      // Verify the token - this will throw an error if the token is invalid
      // But we'll try to decode it anyway to get the user info
      let decoded: any
      
      try {
        // First try to verify normally
        decoded = await verifyJWT(token)
        console.log('Token is still valid, no need to refresh')
        
        // If verification succeeds, token is still valid, so just return success
        return NextResponse.json({ 
          message: 'Token is still valid',
          refreshed: false
        })
      } catch (verifyError) {
        console.log('Token verification failed, attempting to decode anyway:', verifyError)
        
        // If verification fails, try to decode without verification
        // to extract the user ID for refreshing
        try {
          const jwtSecret = process.env.JWT_SECRET || 'school_management_secure_jwt_secret_key_2023'
          decoded = jwt.decode(token)
          
          if (!decoded || !decoded.id) {
            console.error('Failed to decode token or missing user ID')
            throw new Error('Invalid token format')
          }
          
          console.log('Successfully decoded expired token for user ID:', decoded.id)
        } catch (decodeError) {
          console.error('Failed to decode token:', decodeError)
          throw new Error('Invalid token format')
        }
      }

      // Get the user from the database
      const user = await prisma.user.findUnique({
        where: { id: decoded.id },
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
        },
      })

      if (!user) {
        console.error('User not found:', decoded.id)
        return NextResponse.json(
          { error: 'User not found' },
          { status: 404 }
        )
      }

      console.log('User found, generating new token for:', user.email)

      // Generate a new token
      const jwtSecret = process.env.JWT_SECRET || 'school_management_secure_jwt_secret_key_2023'
      const newToken = jwt.sign(
        {
          id: user.id,
          email: user.email,
          role: user.role
        },
        jwtSecret,
        { expiresIn: '24h' }
      )

      // Create response with the new token
      const response = NextResponse.json({
        message: 'Token refreshed successfully',
        refreshed: true
      })

      // Set the new token cookie
      response.cookies.set('token', newToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 86400, // 24 hours
        path: '/'
      })

      console.log('New token set in cookie')
      return response
    } catch (error) {
      console.error('Error refreshing token:', error)
      return NextResponse.json(
        { error: 'Failed to refresh token' },
        { status: 401 }
      )
    }
  } catch (error) {
    console.error('Unexpected error in token refresh:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

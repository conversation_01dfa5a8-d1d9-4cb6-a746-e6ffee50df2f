'use client'

import React, { useState, useEffect } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import {
  Table, TableBody, TableCell, TableHead,
  TableHeader, TableRow
} from '@/app/components/ui/table'
import { Button } from '@/app/components/ui/button'
import { Input } from '@/app/components/ui/input'
import {
  RecursionSafeDialog,
  RecursionSafeDialogContent,
  RecursionSafeDialogHeader,
  RecursionSafeDialogTitle,
  RecursionSafeDialogDescription,
  RecursionSafeDialogFooter
} from '@/app/components/RecursionSafeDialog'
import {
  Select, SelectContent, SelectItem, SelectTrigger,
  SelectValue
} from '@/app/components/ui/select'
import { Label } from '@/app/components/ui/label'
import { Badge } from '@/app/components/ui/badge'
import {
  Card, CardContent, CardDescription, CardFooter,
  CardHeader, CardTitle
} from '@/app/components/ui/card'
import {
  Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger
} from '@/app/components/ui/tabs'
import {
  DropdownMenu, DropdownMenuContent, DropdownMenuItem,
  DropdownMenuTrigger
} from '@/app/components/ui/dropdown-menu'
import {
  Plus, MoreHorizontal, RefreshCw, Edit, Trash2,
  Search, UserCheck, Users, GraduationCap, BookOpen,
  XCircle, UserX
} from 'lucide-react'
import { AssignHRTeacherDialog } from './AssignHRTeacherDialog'
import { AssignTeacherPermissionsDialog } from './AssignTeacherPermissionsDialog'

// Types
interface Teacher {
  id: string
  name: string
  email: string
  subject: string
  mobile?: string
  fatherName?: string
  gender?: string
  subjects: string[]
  isHRTeacher: boolean
  hrClass?: string
  role?: string
}

interface Class {
  id: string
  name: string
  hrTeacherId?: string
  hrTeacherName?: string
  totalStudents: number
}

interface Subject {
  id: string
  name: string
  code: string
}

export function TeacherAssignmentsTab() {
  const queryClient = useQueryClient()
  const [searchTerm, setSearchTerm] = useState('')
  const [activeTab, setActiveTab] = useState('hr-teachers')
  const [isAssignHRDialogOpen, setIsAssignHRDialogOpen] = useState(false)
  const [isAssignSubjectDialogOpen, setIsAssignSubjectDialogOpen] = useState(false)
  const [selectedTeacher, setSelectedTeacher] = useState<Teacher | null>(null)
  const [selectedClass, setSelectedClass] = useState<string>('')
  const [selectedSubjects, setSelectedSubjects] = useState<string[]>([])
  const [isEditingHRTeacher, setIsEditingHRTeacher] = useState(false)
  const [currentHRTeacher, setCurrentHRTeacher] = useState<string | null>(null)
  const [subjectSearchTerm, setSubjectSearchTerm] = useState('')
  const [teacherSearchTerm, setTeacherSearchTerm] = useState('')

  // Teacher permissions state
  const [isAssignPermissionsDialogOpen, setIsAssignPermissionsDialogOpen] = useState(false)

  // Fetch teachers (for Subject Teachers and Teacher Permissions tabs)
  const {
    data: teachers = [],
    isLoading: isLoadingTeachers,
    error: teachersError,
    refetch: refetchTeachers
  } = useQuery({
    queryKey: ['teachers'],
    queryFn: async () => {
      try {
        // Use the unified teachers API that searches both Teacher and User tables
        const res = await fetch('/api/teachers/unified')
        if (!res.ok) throw new Error('Failed to fetch teachers')
        const response = await res.json()
        const teachersData = response.teachers || []

        console.log(`Fetched ${teachersData.length} unified teachers`)

        // Fetch subject assignments for each teacher
        const teachersWithSubjects = await Promise.all(
          teachersData.map(async (teacher: any) => {
            try {
              const subjectsRes = await fetch(`/api/teachers/subject-assignments?teacherId=${teacher.id}`)
              let subjectAssignments = []
              if (subjectsRes.ok) {
                subjectAssignments = await subjectsRes.json()
              }

              return {
                id: teacher.id,
                name: teacher.name,
                email: teacher.email,
                subject: teacher.subject,
                mobile: teacher.mobile,
                fatherName: teacher.fatherName,
                gender: teacher.gender,
                source: teacher.source,
                subjects: subjectAssignments.map((sa: any) => sa.subject.name),
                isHRTeacher: false, // Will be determined by checking classes
                hrClass: undefined,
                // Add display name with source indicator
                displayName: teacher.source === 'user' ? `${teacher.name} (User)` : teacher.name
              }
            } catch (error) {
              console.error(`Error fetching subjects for teacher ${teacher.id}:`, error)
              return {
                id: teacher.id,
                name: teacher.name,
                email: teacher.email,
                subject: teacher.subject,
                mobile: teacher.mobile,
                fatherName: teacher.fatherName,
                gender: teacher.gender,
                source: teacher.source,
                subjects: [],
                isHRTeacher: false,
                hrClass: undefined,
                // Add display name with source indicator
                displayName: teacher.source === 'user' ? `${teacher.name} (User)` : teacher.name
              }
            }
          })
        )

        return teachersWithSubjects
      } catch (error) {
        console.error('Error fetching teachers:', error)
        throw new Error('Failed to fetch teachers from database')
      }
    }
  })

  // Fetch classes with HR teacher information
  const {
    data: classes = [],
    isLoading: isLoadingClasses,
    refetch: refetchClasses
  } = useQuery({
    queryKey: ['classes-with-hr'],
    queryFn: async () => {
      const res = await fetch('/api/hr-teachers/available-classes')
      if (!res.ok) throw new Error('Failed to fetch classes')
      const data = await res.json()
      console.log('Fetched classes data:', data)
      return data
    }
  })

  // Fetch HR teacher assignments (using classes with HR teacher info)
  const {
    data: hrTeachers = [],
    isLoading: isLoadingHRTeachers,
    refetch: refetchHRTeachers
  } = useQuery({
    queryKey: ['hr-teachers'],
    queryFn: async () => {
      try {
        // Fetch classes with their HR teacher information
        const res = await fetch('/api/hr-teachers/available-classes')
        if (!res.ok) throw new Error('Failed to fetch HR teachers')
        const classesData = await res.json()

        // Filter only classes that have HR teachers assigned
        const classesWithHR = classesData.filter((cls: any) => cls.hrTeacherId && cls.hrTeacher)

        // Transform to match the expected format
        return classesWithHR.map((cls: any) => ({
          classId: cls.id,
          teacherId: cls.hrTeacherId,
          teacher: cls.hrTeacher,
          class: {
            id: cls.id,
            name: cls.name,
            totalStudents: cls.totalStudents
          }
        }))
      } catch (error) {
        console.error('Error fetching HR teachers:', error)
        return []
      }
    }
  })

  // Fetch teacher permissions
  const {
    data: teacherPermissionsData = { permissions: [], total: 0 },
    isLoading: isLoadingPermissions,
    error: permissionsError,
    refetch: refetchPermissions
  } = useQuery({
    queryKey: ['teacher-permissions'],
    queryFn: async () => {
      try {
        const response = await fetch('/api/teacher-permissions')
        if (!response.ok) {
          throw new Error('Failed to fetch teacher permissions')
        }
        const data = await response.json()
        console.log("Fetched teacher permissions:", data);
        return data
      } catch (error) {
        console.error("Error fetching teacher permissions:", error);
        return { permissions: [], total: 0 };
      }
    },
    // Initialize with empty data structure
    initialData: { permissions: [], total: 0 }
  })

  // Extract permissions array for easier use
  const teacherPermissions = teacherPermissionsData.permissions || []

  // Fetch subjects from the database
  const {
    data: subjects = [],
    isLoading: isLoadingSubjects,
    error: subjectsError
  } = useQuery({
    queryKey: ['subjects'],
    queryFn: async () => {
      try {
        const res = await fetch('/api/subjects')
        if (!res.ok) throw new Error('Failed to fetch subjects')

        const subjectsData = await res.json()

        // Transform the data to match the expected Subject interface
        return subjectsData.map((subject: any) => ({
          id: subject.id,
          name: subject.name,
          code: subject.code || '' // Some subjects might not have a code
        }))
      } catch (error) {
        console.error('Error fetching subjects:', error)
        throw new Error('Failed to fetch subjects from database')
      }
    }
  })

  // Assign HR Teacher mutation
  const assignHRTeacher = useMutation({
    mutationFn: async ({ teacherId, classId, isUpdate = false }: { teacherId: string, classId: string, isUpdate?: boolean }) => {
      console.log(`${isUpdate ? 'Updating' : 'Assigning'} teacher ${teacherId} as HR for class ${classId}`)

      // Use the new HR teachers management API
      const response = await fetch('/api/hr-teachers/manage', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          teacherId,
          classId
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to assign HR teacher')
      }

      return response.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teachers'] })
      queryClient.invalidateQueries({ queryKey: ['classes'] })
      queryClient.invalidateQueries({ queryKey: ['hr-teachers'] })
      queryClient.invalidateQueries({ queryKey: ['teacher-assignments'] })
      refetchHRTeachers() // Refetch HR teachers data
      setIsAssignHRDialogOpen(false)
      setSelectedTeacher(null)
      setSelectedClass('')
      setIsEditingHRTeacher(false)
      setCurrentHRTeacher(null)
    }
  })

  // Remove HR Teacher mutation
  const removeHRTeacher = useMutation({
    mutationFn: async (classId: string) => {
      console.log(`Removing HR teacher from class ${classId}`)

      const response = await fetch('/api/hr-teachers/assign', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ classId }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to remove HR teacher')
      }

      return response.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teachers'] })
      queryClient.invalidateQueries({ queryKey: ['classes'] })
      queryClient.invalidateQueries({ queryKey: ['hr-teachers'] })
      queryClient.invalidateQueries({ queryKey: ['teacher-assignments'] })
      refetchHRTeachers() // Refetch HR teachers data

      // Show success message
      alert('HR teacher removed successfully!')
    },
    onError: (error: Error) => {
      alert(`Failed to remove HR teacher: ${error.message}`)
    }
  })

  // Assign Subjects mutation
  const assignSubjects = useMutation({
    mutationFn: async ({ teacherId, subjects }: { teacherId: string, subjects: string[] }) => {
      console.log(`Assigning subjects ${subjects.join(', ')} to teacher ${teacherId}`)

      try {
        // Use the new subject assignments API
        const response = await fetch('/api/teachers/subject-assignments', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            teacherId,
            subjects
          })
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Failed to assign subjects')
        }

        const result = await response.json()
        return result
      } catch (error) {
        console.error('Error in assignSubjects mutation:', error)
        throw error
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teachers'] })
      queryClient.invalidateQueries({ queryKey: ['teacher-assignments'] })
      setIsAssignSubjectDialogOpen(false)
      setSelectedTeacher(null)
      setSelectedSubjects([])
    },
    onError: (error) => {
      console.error('Subject assignment failed:', error)
      alert(`Failed to assign subjects: ${error.message}`)
    }
  })



  // Filter HR teachers based on search term (for HR Teachers tab)
  const filteredHRTeachers = hrTeachers.filter((teacher: any) => {
    const searchLower = searchTerm.toLowerCase()
    return (
      teacher.teacher?.name.toLowerCase().includes(searchLower) ||
      teacher.teacher?.email.toLowerCase().includes(searchLower) ||
      (teacher.class?.name && teacher.class.name.toLowerCase().includes(searchLower))
    )
  })

  // Filter regular teachers based on search term (for Subject Teachers and Teacher Permissions tabs)
  const filteredTeachers = teachers.filter((teacher: Teacher) => {
    const searchLower = searchTerm.toLowerCase()
    return (
      teacher.name.toLowerCase().includes(searchLower) ||
      teacher.email.toLowerCase().includes(searchLower) ||
      teacher.subject.toLowerCase().includes(searchLower)
    )
  })

  // Classes already include HR teacher information from the API
  const classesWithHRInfo = classes

  // Get classes without HR teachers
  const classesWithoutHR = classesWithHRInfo.filter((cls: any) => !cls.hrTeacherId)



  // Handle HR teacher assignment
  const handleAssignHRTeacher = () => {
    if (!selectedTeacher || !selectedClass) {
      alert('Please select a teacher and a class')
      return
    }

    assignHRTeacher.mutate({
      teacherId: selectedTeacher.id,
      classId: selectedClass,
      isUpdate: isEditingHRTeacher
    })
  }

  // Handle subject assignment
  const handleAssignSubjects = () => {
    if (!selectedTeacher || selectedSubjects.length === 0) {
      alert('Please select a teacher and at least one subject')
      return
    }

    assignSubjects.mutate({
      teacherId: selectedTeacher.id,
      subjects: selectedSubjects
    })
  }



  // Get class name by ID - used for displaying class names in the UI
  const getClassName = (classId: string): string => {
    const cls = classes.find((c: any) => c.id === classId)
    return cls ? cls.name : 'Unknown Class'
  }

  return (
    <div className="space-y-4">
      <Tabs
        defaultValue="hr-teachers"
        value={activeTab}
        onValueChange={setActiveTab}
        className="w-full"
      >
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="hr-teachers">HR Teachers</TabsTrigger>
          <TabsTrigger value="subject-teachers">Subject Teachers</TabsTrigger>
          <TabsTrigger value="teacher-permissions">Teacher Permissions</TabsTrigger>
        </TabsList>

        {/* HR Teachers Tab */}
        <TabsContent value="hr-teachers" className="mt-4 space-y-4">
          {/* Header and actions */}
          <div className="flex justify-between items-center">
            <div className="relative w-64">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
              <Input
                type="text"
                placeholder="Search teachers..."
                className="pl-9"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => refetchHRTeachers()}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
              <Button
                size="sm"
                onClick={() => setIsAssignHRDialogOpen(true)}
                disabled={classes.length === 0}
              >
                <UserCheck className="h-4 w-4 mr-2" />
                Assign HR Teacher
              </Button>
            </div>
          </div>

          {/* Classes grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {isLoadingClasses ? (
              <Card className="col-span-full h-40 flex items-center justify-center">
                <RefreshCw className="h-5 w-5 animate-spin text-gray-400 mr-2" />
                <span>Loading classes...</span>
              </Card>
            ) : classesWithHRInfo.length === 0 ? (
              <Card className="col-span-full h-40 flex items-center justify-center">
                <span className="text-gray-500">No classes found</span>
              </Card>
            ) : (
              classesWithHRInfo.map((cls: any) => (
                <Card key={cls.id}>
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <CardTitle className="text-lg">Class {cls.name}</CardTitle>
                      <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                        {cls.totalStudents} Students
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="pb-2">
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <GraduationCap className="h-5 w-5 text-gray-500" />
                        <div className="flex-1">
                          <div className="text-sm font-medium">HR Teacher:</div>
                          {cls.hrTeacher ? (
                            <div className="space-y-1">
                              <div className="text-sm font-medium text-blue-600">{cls.hrTeacher.name}</div>
                              <div className="text-xs text-gray-500 flex items-center gap-2">
                                <span>📚 {cls.hrTeacher.subject}</span>
                                <span>✉️ {cls.hrTeacher.email}</span>
                              </div>
                              {cls.hrTeacher.mobile && (
                                <div className="text-xs text-gray-400">📞 {cls.hrTeacher.mobile}</div>
                              )}
                            </div>
                          ) : (
                            <div className="text-sm text-gray-500 italic">Not assigned</div>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="pt-2">
                    {!cls.hrTeacherId ? (
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full"
                        onClick={() => {
                          setSelectedClass(cls.id)
                          setIsEditingHRTeacher(false)
                          setCurrentHRTeacher(null)
                          setIsAssignHRDialogOpen(true)
                        }}
                      >
                        <UserCheck className="h-4 w-4 mr-2" />
                        Assign HR Teacher
                      </Button>
                    ) : (
                      <div className="w-full space-y-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="w-full"
                          onClick={() => {
                            setSelectedClass(cls.id)
                            setIsEditingHRTeacher(true)
                            setCurrentHRTeacher(cls.hrTeacherId || null)
                            setIsAssignHRDialogOpen(true)
                          }}
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4 mr-2">
                            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                          </svg>
                          Change HR Teacher
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="w-full text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200"
                          onClick={() => {
                            if (confirm(`Are you sure you want to remove the HR teacher from class ${cls.name}?`)) {
                              removeHRTeacher.mutate(cls.id)
                            }
                          }}
                          disabled={removeHRTeacher.isPending}
                        >
                          {removeHRTeacher.isPending ? (
                            <>
                              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                              Removing...
                            </>
                          ) : (
                            <>
                              <UserX className="h-4 w-4 mr-2" />
                              Remove HR Teacher
                            </>
                          )}
                        </Button>
                      </div>
                    )}
                  </CardFooter>
                </Card>
              ))
            )}
          </div>
        </TabsContent>

        {/* Subject Teachers Tab */}
        <TabsContent value="subject-teachers" className="mt-4 space-y-4">
          {/* Header and actions */}
          <div className="flex justify-between items-center">
            <div className="relative w-64">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
              <Input
                type="text"
                placeholder="Search teachers..."
                className="pl-9"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => refetchTeachers()}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
              <Button
                size="sm"
                onClick={() => setIsAssignSubjectDialogOpen(true)}
              >
                <BookOpen className="h-4 w-4 mr-2" />
                Assign Subjects
              </Button>
            </div>
          </div>

          {/* Teachers table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Teacher</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Subjects</TableHead>
                  <TableHead>HR Class</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoadingTeachers ? (
                  <TableRow>
                    <TableCell colSpan={5} className="h-24 text-center">
                      <div className="flex justify-center items-center">
                        <RefreshCw className="h-5 w-5 animate-spin text-gray-400 mr-2" />
                        <span>Loading teachers...</span>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : teachersError ? (
                  <TableRow>
                    <TableCell colSpan={5} className="h-24 text-center">
                      <div className="flex flex-col items-center justify-center text-red-500">
                        <XCircle className="h-5 w-5 mb-1" />
                        <span>Error loading teachers</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => refetchTeachers()}
                          className="mt-2"
                        >
                          Try Again
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : filteredTeachers.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className="h-24 text-center text-gray-500">
                      No teachers found
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredTeachers.map((teacher: Teacher) => (
                    <TableRow key={teacher.id}>
                      <TableCell className="font-medium">{teacher.displayName || teacher.name}</TableCell>
                      <TableCell>{teacher.email}</TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {teacher.subjects && teacher.subjects.length > 0 ? (
                            teacher.subjects.map((subject) => (
                              <Badge key={subject} variant="secondary" className="text-xs">
                                {subject}
                              </Badge>
                            ))
                          ) : (
                            <span className="text-gray-500 text-sm">No subjects assigned</span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        {teacher.isHRTeacher && teacher.hrClass ? (
                          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                            {getClassName(teacher.hrClass)}
                          </Badge>
                        ) : (
                          <span className="text-gray-500 text-sm">-</span>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">Open menu</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              onClick={() => {
                                setSelectedTeacher(teacher)
                                setIsAssignSubjectDialogOpen(true)
                              }}
                            >
                              <BookOpen className="h-4 w-4 mr-2" />
                              Assign Subjects
                            </DropdownMenuItem>
                            {!teacher.isHRTeacher && classesWithoutHR.length > 0 && (
                              <DropdownMenuItem
                                onClick={() => {
                                  setSelectedTeacher(teacher)
                                  setIsEditingHRTeacher(false)
                                  setCurrentHRTeacher(null)
                                  setIsAssignHRDialogOpen(true)
                                }}
                              >
                                <UserCheck className="h-4 w-4 mr-2" />
                                Assign as HR Teacher
                              </DropdownMenuItem>
                            )}
                            {teacher.isHRTeacher && teacher.hrClass && (
                              <DropdownMenuItem
                                onClick={() => {
                                  setSelectedTeacher(teacher)
                                  setSelectedClass(teacher.hrClass || '')
                                  setIsEditingHRTeacher(true)
                                  setCurrentHRTeacher(teacher.id)
                                  setIsAssignHRDialogOpen(true)
                                }}
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4 mr-2">
                                  <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                                  <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                                </svg>
                                Change HR Class
                              </DropdownMenuItem>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </TabsContent>

        {/* Teacher Permissions Tab */}
        <TabsContent value="teacher-permissions" className="mt-4 space-y-4">
          {/* Header and actions */}
          <div className="flex justify-between items-center">
            <div className="relative w-64">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
              <Input
                type="text"
                placeholder="Search teachers..."
                className="pl-9"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => refetchTeachers()}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
              <Button
                size="sm"
                onClick={() => setIsAssignPermissionsDialogOpen(true)}
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4 mr-2">
                  <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                </svg>
                Assign Permissions
              </Button>
            </div>
          </div>

          {/* Teachers with permissions table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Teacher</TableHead>
                  <TableHead>Class</TableHead>
                  <TableHead>Attendance</TableHead>
                  <TableHead>Marks</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoadingTeachers ? (
                  <TableRow>
                    <TableCell colSpan={5} className="h-24 text-center">
                      <div className="flex justify-center items-center">
                        <RefreshCw className="h-5 w-5 animate-spin text-gray-400 mr-2" />
                        <span>Loading teachers...</span>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : teachersError ? (
                  <TableRow>
                    <TableCell colSpan={5} className="h-24 text-center">
                      <div className="flex flex-col items-center justify-center text-red-500">
                        <XCircle className="h-5 w-5 mb-1" />
                        <span>Error loading teachers</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => refetchTeachers()}
                          className="mt-2"
                        >
                          Try Again
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : filteredTeachers.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className="h-24 text-center text-gray-500">
                      No teachers found
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredTeachers.map((teacher: Teacher) => {
                    // Find permissions for this teacher
                    const teacherPerms = teacherPermissions.filter((perm: any) => perm.teacherId === teacher.id)

                    return (
                      <TableRow key={teacher.id}>
                        <TableCell className="font-medium">{teacher.displayName || teacher.name}</TableCell>
                        <TableCell>
                          <div className="flex flex-wrap gap-1">
                            {teacherPerms.map((perm: any) => {
                              return (
                                <Badge key={perm.id} variant="outline" className="text-xs">
                                  {perm.class ? perm.class.name : perm.classId}
                                </Badge>
                              )
                            })}
                            {teacherPerms.length === 0 && (
                              <span className="text-gray-500 text-sm">No classes assigned</span>
                            )}
                          </div>
                        </TableCell>
                      <TableCell>
                        <div className="flex flex-col gap-1">
                          {teacherPerms.map((perm: any) => (
                            <div key={`att-${perm.id}`} className="flex flex-col gap-1">
                              <Badge variant="secondary" className="text-xs">
                                Can View: {perm.canViewAttendance ? 'Yes' : 'No'}
                              </Badge>
                              <Badge variant="secondary" className="text-xs">
                                Can Take: {perm.canTakeAttendance ? 'Yes' : 'No'}
                              </Badge>
                            </div>
                          ))}
                          {teacherPerms.length === 0 && (
                            <span className="text-gray-500 text-sm">No permissions</span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col gap-1">
                          {teacherPerms.map((perm: any) => (
                            <div key={`mark-${perm.id}`} className="flex flex-col gap-1">
                              <Badge variant="secondary" className="text-xs">
                                Can Add: {perm.canAddMarks ? 'Yes' : 'No'}
                              </Badge>
                              <Badge variant="secondary" className="text-xs">
                                Can Edit: {perm.canEditMarks ? 'Yes' : 'No'}
                              </Badge>
                            </div>
                          ))}
                          {teacherPerms.length === 0 && (
                            <span className="text-gray-500 text-sm">No permissions</span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">Open menu</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              onClick={() => {
                                setIsAssignPermissionsDialogOpen(true)
                              }}
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4 mr-2">
                                <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                              </svg>
                              Manage Permissions
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                    )
                  })
                )}
              </TableBody>
            </Table>
          </div>
        </TabsContent>
      </Tabs>

      {/* Assign HR Teacher Dialog - New Clean Implementation */}
      <AssignHRTeacherDialog
        open={isAssignHRDialogOpen}
        onOpenChange={setIsAssignHRDialogOpen}
        onSuccess={() => {
          // Refetch classes and HR teachers data after successful assignment
          refetchClasses()
          refetchHRTeachers()
        }}
      />

      {/* Assign Subjects Dialog */}
      <RecursionSafeDialog open={isAssignSubjectDialogOpen} onOpenChange={setIsAssignSubjectDialogOpen}>
        <RecursionSafeDialogHeader>
          <RecursionSafeDialogTitle>Assign Subjects</RecursionSafeDialogTitle>
          <RecursionSafeDialogDescription>
            Assign subjects to a teacher
          </RecursionSafeDialogDescription>
        </RecursionSafeDialogHeader>
        <RecursionSafeDialogContent>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="teacher" className="text-right">
                Teacher
              </Label>
              <div className="col-span-3">
                <Select
                  value={selectedTeacher?.id || ''}
                  onValueChange={(value) => {
                    const teacher = teachers.find((t: Teacher) => t.id === value)
                    setSelectedTeacher(teacher || null)
                    if (teacher) {
                      setSelectedSubjects(teacher.subjects || [])
                    }
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a teacher" />
                  </SelectTrigger>
                  <SelectContent>
                    {isLoadingTeachers ? (
                      <div className="flex items-center justify-center p-2">
                        <RefreshCw className="h-4 w-4 animate-spin text-gray-400 mr-2" />
                        <span className="text-sm">Loading teachers...</span>
                      </div>
                    ) : teachersError ? (
                      <div className="flex items-center justify-center p-2 text-red-500">
                        <span className="text-sm">Error loading teachers</span>
                      </div>
                    ) : teachers.length === 0 ? (
                      <div className="p-2 text-center text-gray-500">No teachers found</div>
                    ) : (
                      teachers.map((teacher: Teacher) => (
                        <SelectItem key={teacher.id} value={teacher.id}>
                          {teacher.displayName || teacher.name}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-4 items-start gap-4">
              <Label className="text-right pt-2">
                Subjects
              </Label>
              <div className="col-span-3">
                {isLoadingSubjects ? (
                  <div className="flex items-center">
                    <RefreshCw className="h-4 w-4 animate-spin text-gray-400 mr-2" />
                    <span className="text-sm">Loading subjects...</span>
                  </div>
                ) : subjectsError ? (
                  <div className="flex items-center text-red-500">
                    <span className="text-sm">Error loading subjects</span>
                  </div>
                ) : subjects.length === 0 ? (
                  <div className="text-sm text-gray-500">No subjects available</div>
                ) : (
                  <>
                    <div className="mb-3">
                      <div className="relative mb-2">
                        <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-500" />
                        <Input
                          type="text"
                          placeholder="Search subjects..."
                          className="pl-8"
                          value={subjectSearchTerm}
                          onChange={(e) => setSubjectSearchTerm(e.target.value)}
                        />
                      </div>
                      <div className="flex justify-between mb-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const filteredSubjectIds = subjects
                              .filter((subject: Subject) =>
                                subject.name.toLowerCase().includes(subjectSearchTerm.toLowerCase())
                              )
                              .map((subject: Subject) => subject.id);
                            // Use Array.from instead of spread operator with Set
                            setSelectedSubjects(Array.from(new Set([...selectedSubjects, ...filteredSubjectIds])));
                          }}
                        >
                          Select All {subjectSearchTerm && 'Filtered'}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            if (subjectSearchTerm) {
                              // Clear only filtered subjects
                              const filteredSubjectNames = subjects
                                .filter((subject: Subject) =>
                                  subject.name.toLowerCase().includes(subjectSearchTerm.toLowerCase())
                                )
                                .map((subject: Subject) => subject.name);
                              setSelectedSubjects(selectedSubjects.filter(name => !filteredSubjectNames.includes(name)));
                            } else {
                              // Clear all subjects
                              setSelectedSubjects([]);
                            }
                          }}
                        >
                          Clear {subjectSearchTerm ? 'Filtered' : 'All'}
                        </Button>
                      </div>
                    </div>
                    <div className="max-h-60 overflow-y-auto pr-2">
                      <div className="grid grid-cols-2 gap-2">
                        {subjects
                          .filter((subject: Subject) =>
                            subject.name.toLowerCase().includes(subjectSearchTerm.toLowerCase())
                          )
                          .map((subject: Subject) => (
                        <div key={subject.id} className="flex items-center space-x-2 bg-gray-50 p-2 rounded-md hover:bg-gray-100">
                          <input
                            type="checkbox"
                            id={`subject-${subject.id}`}
                            checked={selectedSubjects.includes(subject.id)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedSubjects([...selectedSubjects, subject.id])
                              } else {
                                setSelectedSubjects(selectedSubjects.filter(s => s !== subject.id))
                              }
                            }}
                            className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600"
                          />
                          <Label htmlFor={`subject-${subject.id}`} className="text-sm truncate">
                            {subject.name} {subject.code && <span className="text-gray-500">({subject.code})</span>}
                          </Label>
                        </div>
                      ))}
                      </div>
                    </div>
                    {subjectSearchTerm && subjects.filter((subject: Subject) =>
                      subject.name.toLowerCase().includes(subjectSearchTerm.toLowerCase())
                    ).length === 0 && (
                      <div className="text-sm text-gray-500 text-center py-2">
                        No subjects match your search
                      </div>
                    )}
                  </>
                )}

                <div className="text-sm text-gray-500 mt-4 pt-2 border-t">
                  Selected: {selectedSubjects.length} subject{selectedSubjects.length !== 1 ? 's' : ''}
                </div>
              </div>
            </div>
          </div>

          <RecursionSafeDialogFooter>
            <Button variant="outline" onClick={() => setIsAssignSubjectDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleAssignSubjects}
              disabled={assignSubjects.isPending || !selectedTeacher || selectedSubjects.length === 0}
            >
              {assignSubjects.isPending ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Assigning...
                </>
              ) : 'Assign Subjects'}
            </Button>
          </RecursionSafeDialogFooter>
        </RecursionSafeDialogContent>
      </RecursionSafeDialog>

      {/* Assign Teacher Permissions Dialog - New Clean Implementation */}
      <AssignTeacherPermissionsDialog
        open={isAssignPermissionsDialogOpen}
        onOpenChange={setIsAssignPermissionsDialogOpen}
        onSuccess={() => {
          // Refetch all related data after successful assignment
          refetchPermissions()
          refetchTeachers()
        }}
      />
    </div>
  )
}



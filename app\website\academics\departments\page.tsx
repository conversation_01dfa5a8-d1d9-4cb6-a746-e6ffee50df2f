"use client";

import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { FaChevronDown, FaChevronUp, FaUserTie, FaEnvelope, FaPhone } from 'react-icons/fa';
import { Button } from '@/app/components/ui/button';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.5 },
  },
};

// Department data
const departments = [
  {
    id: 1,
    name: 'Islamic Studies Department',
    description: 'The Islamic Studies Department is the cornerstone of our educational mission, providing comprehensive instruction in Quran, Hadith, Fiqh, and Islamic history. Our dedicated scholars and teachers guide students in developing a deep understanding of their faith and how to apply Islamic principles in their daily lives.',
    image: '/images/portrait.jpg', // Replace with actual department image
    head: {
      name: 'Dr. <PERSON>',
      title: 'Department Head',
      image: '/images/portrait.jpg', // Replace with actual image
      bio: 'Dr. <PERSON> holds a PhD in Islamic Studies from Al-Azhar University and has over 15 years of experience in Islamic education. He specializes in Quranic exegesis and contemporary Islamic thought.',
      email: '<EMAIL>',
      phone: '(************* ext. 101',
    },
    courses: [
      'Quran Memorization and Tajweed',
      'Tafsir (Quranic Interpretation)',
      'Hadith Studies',
      'Fiqh (Islamic Jurisprudence)',
      'Islamic History and Civilization',
      'Islamic Ethics and Character Development',
      'Comparative Religion',
    ],
    achievements: [
      'Over 50 students completed full Quran memorization in the past 5 years',
      'Regional champions in the National Islamic Knowledge Competition for 3 consecutive years',
      'Published a comprehensive Islamic Studies curriculum adopted by several Islamic schools',
    ],
  },
  {
    id: 2,
    name: 'Language Arts Department',
    description: 'The Language Arts Department fosters excellence in reading, writing, speaking, and listening skills. Our comprehensive program develops critical thinking, analytical abilities, and creative expression through the study of literature, grammar, composition, and public speaking.',
    image: '/images/portrait.jpg', // Replace with actual department image
    head: {
      name: 'Ms. Aisha Khan',
      title: 'Department Head',
      image: '/images/portrait.jpg', // Replace with actual image
      bio: 'Ms. Aisha Khan holds a Master\'s degree in English Literature from Columbia University and has been teaching for over 12 years. She specializes in creative writing and contemporary literature.',
      email: '<EMAIL>',
      phone: '(************* ext. 102',
    },
    courses: [
      'Reading and Comprehension',
      'Creative Writing',
      'Grammar and Composition',
      'Literature Studies',
      'Public Speaking and Debate',
      'Journalism',
      'English as a Second Language (ESL)',
    ],
    achievements: [
      'Students consistently score in the top 10% nationally on standardized English tests',
      'School literary magazine won state recognition for excellence',
      'Debate team reached national finals in the Middle School Debate Championship',
    ],
  },
  {
    id: 3,
    name: 'Mathematics Department',
    description: 'The Mathematics Department provides a rigorous and comprehensive mathematics education that develops problem-solving skills, logical reasoning, and mathematical fluency. Our curriculum progresses from foundational arithmetic to advanced mathematics, preparing students for success in college and careers.',
    image: '/images/portrait.jpg', // Replace with actual department image
    head: {
      name: 'Mr. Ibrahim Hassan',
      title: 'Department Head',
      image: '/images/portrait.jpg', // Replace with actual image
      bio: 'Mr. Ibrahim Hassan holds a Master\'s degree in Mathematics from MIT and has been teaching for 15 years. He specializes in applied mathematics and has published several papers on mathematics education.',
      email: '<EMAIL>',
      phone: '(************* ext. 103',
    },
    courses: [
      'Elementary Mathematics',
      'Pre-Algebra',
      'Algebra I and II',
      'Geometry',
      'Pre-Calculus',
      'Calculus',
      'Statistics and Probability',
    ],
    achievements: [
      'Math team placed 2nd in the State Mathematics Competition',
      '85% of students score above grade level on standardized math assessments',
      'Implemented innovative math curriculum that integrates real-world applications',
    ],
  },
  {
    id: 4,
    name: 'Science Department',
    description: 'The Science Department inspires scientific curiosity and inquiry through hands-on experimentation, research, and discovery. Our comprehensive curriculum covers biology, chemistry, physics, and environmental science, emphasizing both theoretical knowledge and practical applications.',
    image: '/images/portrait.jpg', // Replace with actual department image
    head: {
      name: 'Dr. Fatima Ali',
      title: 'Department Head',
      image: '/images/portrait.jpg', // Replace with actual image
      bio: 'Dr. Fatima Ali holds a PhD in Biochemistry from Stanford University and has 10 years of teaching experience. She previously worked as a research scientist and brings real-world expertise to the classroom.',
      email: '<EMAIL>',
      phone: '(************* ext. 104',
    },
    courses: [
      'General Science',
      'Biology',
      'Chemistry',
      'Physics',
      'Environmental Science',
      'Anatomy and Physiology',
      'Science Research Methods',
    ],
    achievements: [
      'Science Fair projects have won regional and state recognition',
      'Established partnership with local university for advanced lab experiences',
      'Implemented state-of-the-art STEM lab with modern equipment and technology',
    ],
  },
  {
    id: 5,
    name: 'Social Studies Department',
    description: 'The Social Studies Department explores history, geography, civics, and cultures, helping students understand their place in the world and develop global citizenship. Our curriculum emphasizes critical thinking, research skills, and multicultural perspectives.',
    image: '/images/portrait.jpg', // Replace with actual department image
    head: {
      name: 'Mr. Yusuf Rahman',
      title: 'Department Head',
      image: '/images/portrait.jpg', // Replace with actual image
      bio: 'Mr. Yusuf Rahman holds a Master\'s degree in History from Georgetown University and has been teaching for 12 years. He specializes in Islamic world history and international relations.',
      email: '<EMAIL>',
      phone: '(************* ext. 105',
    },
    courses: [
      'World History',
      'U.S. History',
      'Islamic World History',
      'Geography',
      'Civics and Government',
      'Economics',
      'Current Events',
    ],
    achievements: [
      'Developed comprehensive Islamic world history curriculum',
      'Students consistently perform above average on AP History exams',
      'Annual International Cultural Fair recognized for excellence in multicultural education',
    ],
  },
];

export default function DepartmentsPage() {
  const [expandedDept, setExpandedDept] = useState<number | null>(null);

  const toggleDepartment = (id: number) => {
    if (expandedDept === id) {
      setExpandedDept(null);
    } else {
      setExpandedDept(id);
    }
  };

  return (
    <div className="min-h-screen pt-20">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-blue-900 to-indigo-900 text-white">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{ backgroundImage: 'url(/images/pattern.svg)', backgroundSize: '20px' }}></div>
        </div>
        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center max-w-3xl mx-auto"
          >
            <h1 className="text-4xl md:text-5xl font-bold mb-6">Academic Departments</h1>
            <p className="text-xl text-blue-100">
              Explore our specialized academic departments, each dedicated to excellence in their field of study.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Introduction Section */}
      <section className="py-16 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="max-w-3xl mx-auto text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Departmental Excellence</h2>
            <p className="text-gray-600 dark:text-gray-300">
              At Alfalah Islamic School, our academic departments are staffed by highly qualified educators who are experts in their fields. Each department offers a comprehensive curriculum that meets or exceeds national standards while integrating Islamic perspectives.
            </p>
            <p className="text-gray-600 dark:text-gray-300 mt-4">
              Our departmental structure allows for specialized instruction, collaborative teaching, and continuous curriculum development to ensure the highest quality education for our students.
            </p>
          </motion.div>

          <div className="flex justify-center space-x-4 mb-12">
            <Link href="/website/academics/curriculum">
              <Button variant="outline" className="flex items-center gap-2">
                View Curriculum
              </Button>
            </Link>
            <Link href="/website/academics/calendar">
              <Button variant="outline" className="flex items-center gap-2">
                Academic Calendar
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Departments Section */}
      <section className="py-16 bg-gray-50 dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="space-y-8"
          >
            {departments.map((dept) => (
              <motion.div
                key={dept.id}
                variants={itemVariants}
                className="bg-white dark:bg-gray-900 rounded-lg shadow-md overflow-hidden"
              >
                <div 
                  className="p-6 cursor-pointer"
                  onClick={() => toggleDepartment(dept.id)}
                >
                  <div className="flex justify-between items-center">
                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white">{dept.name}</h3>
                    <div className="text-blue-600 dark:text-blue-400">
                      {expandedDept === dept.id ? <FaChevronUp /> : <FaChevronDown />}
                    </div>
                  </div>
                  <p className="text-gray-600 dark:text-gray-300 mt-2">{dept.description}</p>
                </div>

                {expandedDept === dept.id && (
                  <div className="px-6 pb-6 pt-2">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                      {/* Department Head */}
                      <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                        <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Department Head</h4>
                        <div className="flex items-center mb-4">
                          <div className="relative w-16 h-16 rounded-full overflow-hidden mr-4">
                            <Image
                              src={dept.head.image}
                              alt={dept.head.name}
                              fill
                              className="object-cover"
                            />
                          </div>
                          <div>
                            <h5 className="font-semibold text-gray-900 dark:text-white">{dept.head.name}</h5>
                            <p className="text-sm text-gray-600 dark:text-gray-400">{dept.head.title}</p>
                          </div>
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">{dept.head.bio}</p>
                        <div className="space-y-2">
                          <div className="flex items-center text-sm">
                            <FaEnvelope className="text-blue-600 mr-2" />
                            <span className="text-gray-600 dark:text-gray-400">{dept.head.email}</span>
                          </div>
                          <div className="flex items-center text-sm">
                            <FaPhone className="text-blue-600 mr-2" />
                            <span className="text-gray-600 dark:text-gray-400">{dept.head.phone}</span>
                          </div>
                        </div>
                      </div>

                      {/* Courses */}
                      <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                        <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Courses Offered</h4>
                        <ul className="space-y-2">
                          {dept.courses.map((course, index) => (
                            <li key={index} className="flex items-start">
                              <span className="text-blue-600 mr-2">•</span>
                              <span className="text-gray-600 dark:text-gray-400">{course}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      {/* Achievements */}
                      <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                        <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Achievements</h4>
                        <ul className="space-y-2">
                          {dept.achievements.map((achievement, index) => (
                            <li key={index} className="flex items-start">
                              <span className="text-green-600 mr-2">✓</span>
                              <span className="text-gray-600 dark:text-gray-400">{achievement}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                )}
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-12 bg-blue-600">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="text-white mb-6 md:mb-0">
              <h2 className="text-2xl font-bold mb-2">Interested in learning more?</h2>
              <p className="text-blue-100">Contact our academic departments or schedule a visit to see our classrooms in action.</p>
            </div>
            <div className="flex flex-col sm:flex-row gap-4">
              <Link href="/website/contact">
                <Button className="bg-white text-blue-600 hover:bg-blue-50">
                  Contact Us
                </Button>
              </Link>
              <Link href="/website/admissions">
                <Button variant="outline" className="border-white text-white hover:bg-blue-700">
                  Schedule a Visit
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

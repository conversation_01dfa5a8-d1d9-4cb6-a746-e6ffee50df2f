import { NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { prisma } from '@/lib/prisma'
import { verifyJWT } from '@/lib/jwt'
import bcrypt from 'bcryptjs'

export async function PUT(request: Request) {
  try {
    console.log('Password update request received')

    // Get the token from cookies
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    console.log('Token found:', !!token)

    if (!token) {
      console.error('No token found in cookies')
      return NextResponse.json(
        { error: 'Unauthorized - No token found' },
        { status: 401 }
      )
    }

    try {
      // Verify the token
      const decoded = await verifyJWT(token)
      console.log('Token verified, user ID:', decoded.id)

      // Get the user ID from the token
      const userId = decoded.id

      // Get the request body
      const body = await request.json()
      const { currentPassword, newPassword } = body
      console.log('Request body received (passwords hidden for security)')

      // Validate the input
      if (!currentPassword || !newPassword) {
        console.error('Missing required password fields')
        return NextResponse.json(
          { error: 'Current password and new password are required' },
          { status: 400 }
        )
      }

      if (newPassword.length < 8) {
        console.error('New password too short')
        return NextResponse.json(
          { error: 'New password must be at least 8 characters long' },
          { status: 400 }
        )
      }

      // Get the user with the password
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          password: true,
        },
      })

      if (!user) {
        console.error('User not found:', userId)
        return NextResponse.json(
          { error: 'User not found' },
          { status: 404 }
        )
      }

      // Verify the current password
      const isPasswordValid = await bcrypt.compare(currentPassword, user.password)
      console.log('Current password valid:', isPasswordValid)

      if (!isPasswordValid) {
        return NextResponse.json(
          { error: 'Current password is incorrect' },
          { status: 400 }
        )
      }

      // Hash the new password
      const hashedPassword = await bcrypt.hash(newPassword, 10)
      console.log('New password hashed successfully')

      // Update the user's password
      await prisma.user.update({
        where: { id: userId },
        data: {
          password: hashedPassword,
        },
      })

      console.log('Password updated successfully for user:', userId)

      return NextResponse.json({
        message: 'Password updated successfully',
      })
    } catch (jwtError) {
      console.error('JWT verification error:', jwtError)
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      )
    }
  } catch (error) {
    console.error('Error updating password:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to update password' },
      { status: 500 }
    )
  }
}

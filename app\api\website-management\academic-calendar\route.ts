import { NextRequest, NextResponse } from 'next/server';

// Define interfaces for different types of calendar content
interface CalendarEvent {
  id: string;
  title: string;
  date: string;
  category: 'academic' | 'holiday' | 'exam' | 'staff' | 'student' | 'parent' | 'special';
  description: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface CalendarSettings {
  id: string;
  academicYear: string;
  currentSemester: number;
  updatedAt: string;
}

interface SemesterData {
  id: string;
  name: string;
  events: {
    date: string;
    event: string;
    category: string;
  }[];
}

// Mock data for calendar settings
let calendarSettings: CalendarSettings = {
  id: 'settings-1',
  academicYear: '2023-2024',
  currentSemester: 1,
  updatedAt: new Date().toISOString()
};

// Mock data for semesters
let semestersData: SemesterData[] = [
  {
    id: 'sem-1',
    name: 'First Semester',
    events: [
      { date: "August 15, 2023", event: "Teacher Orientation & Preparation", category: "staff" },
      { date: "August 20, 2023", event: "New Student Orientation", category: "student" },
      { date: "August 21, 2023", event: "First Day of School", category: "academic" },
      { date: "September 4, 2023", event: "Labor Day (No School)", category: "holiday" },
      { date: "September 15, 2023", event: "Back to School Night", category: "parent" },
      { date: "October 9-13, 2023", event: "Fall Break (No School)", category: "holiday" },
      { date: "October 20, 2023", event: "End of First Quarter", category: "academic" },
      { date: "October 27, 2023", event: "Parent-Teacher Conferences", category: "parent" },
      { date: "November 10, 2023", event: "Veterans Day Observed (No School)", category: "holiday" },
      { date: "November 22-24, 2023", event: "Thanksgiving Break (No School)", category: "holiday" },
      { date: "December 11-15, 2023", event: "First Semester Final Exams", category: "academic" },
      { date: "December 15, 2023", event: "End of First Semester", category: "academic" },
      { date: "December 18, 2023 - January 5, 2024", event: "Winter Break (No School)", category: "holiday" }
    ]
  },
  {
    id: 'sem-2',
    name: 'Second Semester',
    events: [
      { date: "January 8, 2024", event: "Teacher Professional Development Day (No Students)", category: "staff" },
      { date: "January 9, 2024", event: "Second Semester Begins", category: "academic" },
      { date: "January 15, 2024", event: "Martin Luther King Jr. Day (No School)", category: "holiday" },
      { date: "February 19, 2024", event: "Presidents' Day (No School)", category: "holiday" },
      { date: "March 8, 2024", event: "End of Third Quarter", category: "academic" },
      { date: "March 15, 2024", event: "Parent-Teacher Conferences", category: "parent" },
      { date: "March 25-29, 2024", event: "Spring Break (No School)", category: "holiday" },
      { date: "April 19, 2024", event: "Teacher Professional Development Day (No Students)", category: "staff" },
      { date: "May 20-24, 2024", event: "Second Semester Final Exams", category: "academic" },
      { date: "May 24, 2024", event: "Last Day of School", category: "academic" },
      { date: "May 27, 2024", event: "Memorial Day (No School)", category: "holiday" },
      { date: "May 30, 2024", event: "Graduation Ceremony", category: "special" },
      { date: "May 31, 2024", event: "Teacher Check-Out Day", category: "staff" }
    ]
  }
];

// Mock data for important dates
let importantDates = [
  { id: 'imp-1', date: "August 21, 2023", event: "First Day of School", category: "academic" },
  { id: 'imp-2', date: "October 20, 2023", event: "End of First Quarter", category: "academic" },
  { id: 'imp-3', date: "December 15, 2023", event: "End of First Semester", category: "academic" },
  { id: 'imp-4', date: "January 9, 2024", event: "Second Semester Begins", category: "academic" },
  { id: 'imp-5', date: "March 8, 2024", event: "End of Third Quarter", category: "academic" },
  { id: 'imp-6', date: "May 24, 2024", event: "Last Day of School", category: "academic" },
  { id: 'imp-7', date: "May 30, 2024", event: "Graduation Ceremony", category: "special" }
];

// Mock data for holidays
let holidays = [
  { id: 'hol-1', date: "September 4, 2023", event: "Labor Day (No School)", category: "holiday" },
  { id: 'hol-2', date: "October 9-13, 2023", event: "Fall Break (No School)", category: "holiday" },
  { id: 'hol-3', date: "November 10, 2023", event: "Veterans Day Observed (No School)", category: "holiday" },
  { id: 'hol-4', date: "November 22-24, 2023", event: "Thanksgiving Break (No School)", category: "holiday" },
  { id: 'hol-5', date: "December 18, 2023 - January 5, 2024", event: "Winter Break (No School)", category: "holiday" },
  { id: 'hol-6', date: "January 15, 2024", event: "Martin Luther King Jr. Day (No School)", category: "holiday" },
  { id: 'hol-7', date: "February 19, 2024", event: "Presidents' Day (No School)", category: "holiday" },
  { id: 'hol-8', date: "March 25-29, 2024", event: "Spring Break (No School)", category: "holiday" },
  { id: 'hol-9', date: "May 27, 2024", event: "Memorial Day (No School)", category: "holiday" }
];

// Mock data for parent-teacher conferences
let parentTeacherConferences = [
  { id: 'ptc-1', date: "October 27, 2023", event: "Fall Parent-Teacher Conferences", category: "parent" },
  { id: 'ptc-2', date: "March 15, 2024", event: "Spring Parent-Teacher Conferences", category: "parent" }
];

// Mock data for exam schedule
let examSchedule = [
  { id: 'exam-1', date: "December 11-15, 2023", event: "First Semester Final Exams", category: "academic" },
  { id: 'exam-2', date: "May 20-24, 2024", event: "Second Semester Final Exams", category: "academic" }
];

// Mock data for individual calendar events
let calendarEvents = [
  {
    id: '1',
    title: 'First Day of School',
    date: new Date('2023-08-21').toISOString(),
    category: 'academic',
    description: 'Welcome back to school! First day of the academic year.',
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: '2',
    title: 'Fall Break',
    date: new Date('2023-10-09').toISOString(),
    category: 'holiday',
    description: 'Fall Break (No School) from October 9-13, 2023',
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: '3',
    title: 'Mid-Term Exams',
    date: new Date('2023-11-15').toISOString(),
    category: 'exam',
    description: 'Mid-term examinations for all grades',
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: '4',
    title: 'Teacher Orientation',
    date: new Date('2023-08-15').toISOString(),
    category: 'staff',
    description: 'Orientation and preparation day for all teaching staff',
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

export async function GET(request: NextRequest) {
  try {
    // Get the query parameters
    const { searchParams } = new URL(request.url);
    const dataType = searchParams.get('dataType');
    const category = searchParams.get('category');

    // Return different data based on the dataType parameter
    switch (dataType) {
      case 'settings':
        return NextResponse.json(calendarSettings);

      case 'semesters':
        return NextResponse.json(semestersData);

      case 'importantDates':
        return NextResponse.json(importantDates);

      case 'holidays':
        return NextResponse.json(holidays);

      case 'parentTeacherConferences':
        return NextResponse.json(parentTeacherConferences);

      case 'examSchedule':
        return NextResponse.json(examSchedule);

      case 'events':
        // Filter events by category if provided
        const filteredEvents = category
          ? calendarEvents.filter(event => event.category === category)
          : calendarEvents;
        return NextResponse.json(filteredEvents);

      case 'all':
        // Return all data
        return NextResponse.json({
          settings: calendarSettings,
          semesters: semestersData,
          importantDates: importantDates,
          holidays: holidays,
          parentTeacherConferences: parentTeacherConferences,
          examSchedule: examSchedule,
          events: calendarEvents
        });

      default:
        // Default to returning individual events
        const defaultFilteredEvents = category
          ? calendarEvents.filter(event => event.category === category)
          : calendarEvents;
        return NextResponse.json(defaultFilteredEvents);
    }
  } catch (error) {
    console.error('Error in GET academic-calendar:', error);
    return NextResponse.json({ error: 'Failed to fetch calendar data', details: error instanceof Error ? error.message : String(error) }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const dataType = searchParams.get('dataType');
    const data = await request.json();
    console.log(`Received POST data for ${dataType}:`, data);

    switch (dataType) {
      case 'settings':
        // Update calendar settings
        calendarSettings = {
          ...calendarSettings,
          ...data,
          updatedAt: new Date().toISOString()
        };
        return NextResponse.json(calendarSettings, { status: 200 });

      case 'semesters':
        // Add or update a semester
        if (data.id) {
          // Update existing semester
          const semesterIndex = semestersData.findIndex(s => s.id === data.id);
          if (semesterIndex !== -1) {
            semestersData[semesterIndex] = {
              ...semestersData[semesterIndex],
              ...data
            };
            return NextResponse.json(semestersData[semesterIndex], { status: 200 });
          }
        }

        // Add new semester
        const newSemester = {
          id: data.id || `sem-${Date.now()}`,
          name: data.name,
          events: data.events || []
        };
        semestersData.push(newSemester);
        return NextResponse.json(newSemester, { status: 201 });

      case 'semesterEvent':
        // Add an event to a semester
        if (!data.semesterId || !data.event) {
          return NextResponse.json(
            { error: 'Semester ID and event data are required' },
            { status: 400 }
          );
        }

        const semester = semestersData.find(s => s.id === data.semesterId);
        if (!semester) {
          return NextResponse.json(
            { error: 'Semester not found' },
            { status: 404 }
          );
        }

        semester.events.push(data.event);
        return NextResponse.json(semester, { status: 200 });

      case 'importantDates':
        // Add a new important date
        const newImportantDate = {
          id: data.id || `imp-${Date.now()}`,
          date: data.date,
          event: data.event,
          category: data.category || 'academic'
        };
        importantDates.push(newImportantDate);
        return NextResponse.json(newImportantDate, { status: 201 });

      case 'holidays':
        // Add a new holiday
        const newHoliday = {
          id: data.id || `hol-${Date.now()}`,
          date: data.date,
          event: data.event,
          category: 'holiday'
        };
        holidays.push(newHoliday);
        return NextResponse.json(newHoliday, { status: 201 });

      case 'parentTeacherConferences':
        // Add a new parent-teacher conference
        const newConference = {
          id: data.id || `ptc-${Date.now()}`,
          date: data.date,
          event: data.event,
          category: 'parent'
        };
        parentTeacherConferences.push(newConference);
        return NextResponse.json(newConference, { status: 201 });

      case 'examSchedule':
        // Add a new exam
        const newExam = {
          id: data.id || `exam-${Date.now()}`,
          date: data.date,
          event: data.event,
          category: 'academic'
        };
        examSchedule.push(newExam);
        return NextResponse.json(newExam, { status: 201 });

      case 'events':
      default:
        // Create a new calendar event
        const newEvent = {
          id: data.id || Date.now().toString(),
          title: data.title,
          date: data.date,
          category: data.category,
          description: data.description || '',
          isActive: data.isActive !== undefined ? data.isActive : true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        console.log('Created new event:', newEvent);

        // Add the new event to our array
        calendarEvents.push(newEvent);
        console.log('Total events after adding:', calendarEvents.length);

        // Return the new event
        return NextResponse.json(newEvent, { status: 201 });
    }
  } catch (error) {
    console.error('Error creating calendar data:', error);
    return NextResponse.json(
      { error: 'Failed to create calendar data', details: error instanceof Error ? error.message : String(error) },
      { status: 400 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const dataType = searchParams.get('dataType');
    const data = await request.json();
    console.log(`Received PUT data for ${dataType}:`, data);

    if (!data.id) {
      return NextResponse.json(
        { error: 'ID is required' },
        { status: 400 }
      );
    }

    switch (dataType) {
      case 'settings':
        // Update calendar settings
        calendarSettings = {
          ...calendarSettings,
          ...data,
          updatedAt: new Date().toISOString()
        };
        return NextResponse.json(calendarSettings);

      case 'semesters':
        // Update a semester
        const semesterIndex = semestersData.findIndex(s => s.id === data.id);
        if (semesterIndex === -1) {
          return NextResponse.json(
            { error: 'Semester not found' },
            { status: 404 }
          );
        }

        semestersData[semesterIndex] = {
          ...semestersData[semesterIndex],
          ...data
        };
        return NextResponse.json(semestersData[semesterIndex]);

      case 'semesterEvent':
        // Update an event in a semester
        if (!data.semesterId || !data.eventIndex || data.eventIndex < 0) {
          return NextResponse.json(
            { error: 'Semester ID and valid event index are required' },
            { status: 400 }
          );
        }

        const semester = semestersData.find(s => s.id === data.semesterId);
        if (!semester) {
          return NextResponse.json(
            { error: 'Semester not found' },
            { status: 404 }
          );
        }

        if (data.eventIndex >= semester.events.length) {
          return NextResponse.json(
            { error: 'Event index out of bounds' },
            { status: 400 }
          );
        }

        semester.events[data.eventIndex] = {
          ...semester.events[data.eventIndex],
          ...data.event
        };
        return NextResponse.json(semester);

      case 'importantDates':
        // Update an important date
        const importantDateIndex = importantDates.findIndex(d => d.id === data.id);
        if (importantDateIndex === -1) {
          return NextResponse.json(
            { error: 'Important date not found' },
            { status: 404 }
          );
        }

        importantDates[importantDateIndex] = {
          ...importantDates[importantDateIndex],
          ...data
        };
        return NextResponse.json(importantDates[importantDateIndex]);

      case 'holidays':
        // Update a holiday
        const holidayIndex = holidays.findIndex(h => h.id === data.id);
        if (holidayIndex === -1) {
          return NextResponse.json(
            { error: 'Holiday not found' },
            { status: 404 }
          );
        }

        holidays[holidayIndex] = {
          ...holidays[holidayIndex],
          ...data,
          category: 'holiday' // Ensure category remains 'holiday'
        };
        return NextResponse.json(holidays[holidayIndex]);

      case 'parentTeacherConferences':
        // Update a parent-teacher conference
        const conferenceIndex = parentTeacherConferences.findIndex(c => c.id === data.id);
        if (conferenceIndex === -1) {
          return NextResponse.json(
            { error: 'Conference not found' },
            { status: 404 }
          );
        }

        parentTeacherConferences[conferenceIndex] = {
          ...parentTeacherConferences[conferenceIndex],
          ...data,
          category: 'parent' // Ensure category remains 'parent'
        };
        return NextResponse.json(parentTeacherConferences[conferenceIndex]);

      case 'examSchedule':
        // Update an exam
        const examIndex = examSchedule.findIndex(e => e.id === data.id);
        if (examIndex === -1) {
          return NextResponse.json(
            { error: 'Exam not found' },
            { status: 404 }
          );
        }

        examSchedule[examIndex] = {
          ...examSchedule[examIndex],
          ...data,
          category: 'academic' // Ensure category remains 'academic'
        };
        return NextResponse.json(examSchedule[examIndex]);

      case 'events':
      default:
        // Update a calendar event
        const eventIndex = calendarEvents.findIndex(event => event.id === data.id);
        console.log('Event index:', eventIndex);

        if (eventIndex === -1) {
          return NextResponse.json(
            { error: 'Event not found' },
            { status: 404 }
          );
        }

        // Update the event
        const updatedEvent = {
          ...calendarEvents[eventIndex],
          ...data,
          updatedAt: new Date().toISOString()
        };

        // Replace the old event with the updated one
        calendarEvents[eventIndex] = updatedEvent;
        console.log('Updated event:', updatedEvent);

        // Return the updated event
        return NextResponse.json(updatedEvent);
    }
  } catch (error) {
    console.error('Error updating calendar data:', error);
    return NextResponse.json(
      { error: 'Failed to update calendar data', details: error instanceof Error ? error.message : String(error) },
      { status: 400 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const dataType = searchParams.get('dataType');
    const id = searchParams.get('id');
    console.log(`Received DELETE request for ${dataType} with ID:`, id);

    // Skip ID check for semesterEvent as it uses semesterId and eventIndex instead
    if (!id && dataType !== 'semesterEvent') {
      return NextResponse.json(
        { error: 'ID is required' },
        { status: 400 }
      );
    }

    switch (dataType) {
      case 'semesters':
        // Delete a semester
        const semesterIndex = semestersData.findIndex(s => s.id === id);
        if (semesterIndex === -1) {
          return NextResponse.json(
            { error: 'Semester not found' },
            { status: 404 }
          );
        }

        const deletedSemester = semestersData[semesterIndex];
        semestersData = semestersData.filter(s => s.id !== id);
        return NextResponse.json({ success: true, id, deletedItem: deletedSemester });

      case 'semesterEvent':
        // Delete an event from a semester
        const semesterId = searchParams.get('semesterId');
        const eventIndex = searchParams.get('eventIndex');

        console.log('Deleting semester event:', { semesterId, eventIndex });

        if (!semesterId || eventIndex === null || eventIndex === undefined) {
          return NextResponse.json(
            { error: 'Semester ID and event index are required' },
            { status: 400 }
          );
        }

        const semester = semestersData.find(s => s.id === semesterId);
        if (!semester) {
          return NextResponse.json(
            { error: 'Semester not found' },
            { status: 404 }
          );
        }

        const eventIndexNum = parseInt(eventIndex, 10);
        console.log('Event index as number:', eventIndexNum);
        console.log('Semester events length:', semester.events.length);

        if (isNaN(eventIndexNum) || eventIndexNum < 0 || eventIndexNum >= semester.events.length) {
          return NextResponse.json(
            { error: `Invalid event index: ${eventIndexNum}. Valid range is 0-${semester.events.length - 1}` },
            { status: 400 }
          );
        }

        const deletedEvent = semester.events[eventIndexNum];
        console.log('Event to delete:', deletedEvent);

        semester.events = semester.events.filter((_, i) => i !== eventIndexNum);
        console.log('Remaining events:', semester.events);

        return NextResponse.json({
          success: true,
          semesterId,
          eventIndex,
          deletedItem: deletedEvent
        });

      case 'importantDates':
        // Delete an important date
        const importantDateIndex = importantDates.findIndex(d => d.id === id);
        if (importantDateIndex === -1) {
          return NextResponse.json(
            { error: 'Important date not found' },
            { status: 404 }
          );
        }

        const deletedImportantDate = importantDates[importantDateIndex];
        importantDates = importantDates.filter(d => d.id !== id);
        return NextResponse.json({ success: true, id, deletedItem: deletedImportantDate });

      case 'holidays':
        // Delete a holiday
        const holidayIndex = holidays.findIndex(h => h.id === id);
        if (holidayIndex === -1) {
          return NextResponse.json(
            { error: 'Holiday not found' },
            { status: 404 }
          );
        }

        const deletedHoliday = holidays[holidayIndex];
        holidays = holidays.filter(h => h.id !== id);
        return NextResponse.json({ success: true, id, deletedItem: deletedHoliday });

      case 'parentTeacherConferences':
        // Delete a parent-teacher conference
        const conferenceIndex = parentTeacherConferences.findIndex(c => c.id === id);
        if (conferenceIndex === -1) {
          return NextResponse.json(
            { error: 'Conference not found' },
            { status: 404 }
          );
        }

        const deletedConference = parentTeacherConferences[conferenceIndex];
        parentTeacherConferences = parentTeacherConferences.filter(c => c.id !== id);
        return NextResponse.json({ success: true, id, deletedItem: deletedConference });

      case 'examSchedule':
        // Delete an exam
        const examIndex = examSchedule.findIndex(e => e.id === id);
        if (examIndex === -1) {
          return NextResponse.json(
            { error: 'Exam not found' },
            { status: 404 }
          );
        }

        const deletedExam = examSchedule[examIndex];
        examSchedule = examSchedule.filter(e => e.id !== id);
        return NextResponse.json({ success: true, id, deletedItem: deletedExam });

      case 'events':
      default:
        // Delete a calendar event
        const calendarEventIndex = calendarEvents.findIndex(event => event.id === id);
        console.log('Event index:', calendarEventIndex);

        if (calendarEventIndex === -1) {
          return NextResponse.json(
            { error: 'Event not found' },
            { status: 404 }
          );
        }

        // Get the event before removing it
        const eventToDelete = calendarEvents[calendarEventIndex];
        console.log('Deleting event:', eventToDelete);

        // Remove the event from the array
        calendarEvents = calendarEvents.filter(event => event.id !== id);
        console.log('Total events after deletion:', calendarEvents.length);

        // Return success
        return NextResponse.json({ success: true, id, deletedItem: eventToDelete });
    }
  } catch (error) {
    console.error('Error deleting calendar data:', error);
    return NextResponse.json(
      { error: 'Failed to delete calendar data', details: error instanceof Error ? error.message : String(error) },
      { status: 400 }
    );
  }
}

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
 
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
 
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
 
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
 
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
 
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
 
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
 
    --radius: 0.5rem;
  }
 
  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
 
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
 
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
 
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
 
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
 
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
 
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
 
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
 
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}
 
@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Sidebar Styles */
.sidebar {
  @apply fixed top-0 left-0 h-screen w-64 bg-gray-900 text-white transition-all duration-300 ease-in-out z-50;
}

.sidebar.collapsed {
  @apply w-20;
}

.sidebar-item {
  @apply flex items-center px-4 py-3 text-gray-300 hover:bg-gray-800 cursor-pointer transition-colors duration-200;
}

.sidebar-item.active {
  @apply bg-gray-800 text-white;
}

.sidebar-icon {
  @apply mr-3 flex-shrink-0;
}

.sidebar-text {
  @apply text-sm font-medium;
}

/* Mobile Sidebar */
@media (max-width: 768px) {
  .sidebar {
    @apply -translate-x-full;
  }

  .sidebar.mobile-open {
    @apply translate-x-0;
  }
}

/* Main Content */
.main-content {
  @apply ml-64 transition-all duration-300 ease-in-out;
}

.main-content.sidebar-collapsed {
  @apply ml-20;
}

@media (max-width: 768px) {
  .main-content {
    @apply ml-0;
  }
}

/* Dashboard Layout */
.dashboard-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6;
}

.dashboard-card {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow-md p-6;
}

/* Form Styles */
.form-container {
  @apply max-w-4xl mx-auto p-6;
}

.form-section {
  @apply space-y-4 bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6;
}

.form-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-4;
}

/* Table Styles */
.table-container {
  @apply w-full overflow-x-auto bg-white dark:bg-gray-800 rounded-lg shadow-md;
}

.data-table {
  @apply min-w-full divide-y divide-gray-200 dark:divide-gray-700;
}

.table-header {
  @apply bg-gray-50 dark:bg-gray-700;
}

.table-row {
  @apply hover:bg-gray-50 dark:hover:bg-gray-700;
}

.table-cell {
  @apply px-6 py-4 whitespace-nowrap text-sm;
}

/* Button Styles */
.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200;
}

.btn-secondary {
  @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-md transition-colors duration-200;
}

.btn-danger {
  @apply bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200;
}

/* Loading Spinner */
.loading-spinner {
  @apply animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600;
} 
'use client';

import React from 'react';
import {
  RecursionSafeDialog,
  RecursionSafeDialogHeader,
  RecursionSafeDialogTitle,
  RecursionSafeDialogDescription,
  RecursionSafeDialogFooter,
  RecursionSafeDialogContent
} from './RecursionSafeDialog';

// This is a wrapper that uses our RecursionSafeDialog to prevent focus recursion issues
export function SafeDialog({
  open,
  onOpenChange,
  children,
  title,
  description,
  footer,
  maxWidth = 'max-w-lg',
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  children: React.ReactNode;
  title?: React.ReactNode;
  description?: React.ReactNode;
  footer?: React.ReactNode;
  maxWidth?: string;
}) {
  return (
    <RecursionSafeDialog
      open={open}
      onOpenChange={onOpenChange}
      maxWidth={maxWidth}
    >
      {title || description ? (
        <RecursionSafeDialogHeader>
          {title && <RecursionSafeDialogTitle>{title}</RecursionSafeDialogTitle>}
          {description && <RecursionSafeDialogDescription>{description}</RecursionSafeDialogDescription>}
        </RecursionSafeDialogHeader>
      ) : null}

      <RecursionSafeDialogContent>
        {children}
      </RecursionSafeDialogContent>

      {footer && (
        <RecursionSafeDialogFooter>
          {footer}
        </RecursionSafeDialogFooter>
      )}
    </RecursionSafeDialog>
  );
}

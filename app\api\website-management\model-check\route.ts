import { NextResponse } from 'next/server';
import { prisma, isUsingMockPrisma, isModelAvailable } from '@/lib/prisma';

export async function GET() {
  try {
    // Check if we're using the mock client
    const usingMock = isUsingMockPrisma();
    
    // Check the availability of website-related models
    const modelStatus = {
      heroSlide: isModelAvailable('heroSlide'),
      announcement: isModelAvailable('announcement'),
      newsEvent: isModelAvailable('newsEvent'),
      testimonial: isModelAvailable('testimonial'),
      quickLink: isModelAvailable('quickLink'),
      featuredContent: isModelAvailable('featuredContent'),
      callToAction: isModelAvailable('callToAction'),
    };
    
    // Check if any models are missing
    const missingModels = Object.entries(modelStatus)
      .filter(([_, available]) => !available)
      .map(([model]) => model);
    
    // Check if Prisma client has the expected properties
    const prismaInfo = {
      isPrismaObject: !!prisma && typeof prisma === 'object',
      hasConnectMethod: !!prisma && typeof prisma.$connect === 'function',
      hasDisconnectMethod: !!prisma && typeof prisma.$disconnect === 'function',
      availableModels: Object.keys(prisma || {}).filter(key => 
        !key.startsWith('$') && 
        typeof (prisma as any)[key] === 'object' &&
        typeof (prisma as any)[key].findFirst === 'function'
      ),
    };
    
    return NextResponse.json({
      status: missingModels.length === 0 ? 'ok' : 'missing_models',
      usingMockClient: usingMock,
      modelStatus,
      missingModels,
      prismaInfo,
      message: missingModels.length === 0 
        ? 'All website-related models are available' 
        : `Missing models: ${missingModels.join(', ')}`,
      suggestion: missingModels.length > 0 
        ? 'Try running "npx prisma generate" and "npx prisma db push" to update the Prisma client and database schema' 
        : undefined,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error checking model availability:', error);
    
    return NextResponse.json({
      status: 'error',
      message: 'Failed to check model availability',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// DELETE teacher permission by ID
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id
    console.log(`Deleting teacher permission with ID: ${id}`)

    // Check if the permission exists
    const permission = await prisma.teacherPermission.findUnique({
      where: { id },
      include: {
        // Note: We'll fetch teacher and class details separately since they're not in relations
      }
    })

    if (!permission) {
      return NextResponse.json(
        { error: 'Teacher permission not found' },
        { status: 404 }
      )
    }

    // Get teacher and class details for logging
    const [teacher, classInfo] = await Promise.all([
      prisma.teacher.findUnique({
        where: { id: permission.teacherId },
        select: { name: true }
      }),
      prisma.class.findUnique({
        where: { id: permission.classId },
        select: { name: true }
      })
    ])

    // Delete the permission
    await prisma.teacherPermission.delete({
      where: { id }
    })

    console.log(`Deleted teacher permission for ${teacher?.name || 'Unknown Teacher'} in class ${classInfo?.name || 'Unknown Class'}`)
    
    return NextResponse.json({
      message: 'Teacher permission deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting teacher permission:', error)
    return NextResponse.json(
      { error: 'Failed to delete teacher permission' },
      { status: 500 }
    )
  }
}

// GET specific teacher permission by ID
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id
    console.log(`Fetching teacher permission with ID: ${id}`)

    const permission = await prisma.teacherPermission.findUnique({
      where: { id },
      select: {
        id: true,
        teacherId: true,
        classId: true,
        canViewAttendance: true,
        canTakeAttendance: true,
        canAddMarks: true,
        canEditMarks: true,
        createdAt: true,
        updatedAt: true,
      }
    })

    if (!permission) {
      return NextResponse.json(
        { error: 'Teacher permission not found' },
        { status: 404 }
      )
    }

    // Get teacher and class details
    const [teacher, classInfo] = await Promise.all([
      prisma.teacher.findUnique({
        where: { id: permission.teacherId },
        select: { id: true, name: true, email: true, subject: true }
      }),
      prisma.class.findUnique({
        where: { id: permission.classId },
        select: { id: true, name: true }
      })
    ])

    const permissionWithDetails = {
      ...permission,
      teacher,
      class: classInfo
    }

    return NextResponse.json(permissionWithDetails)
  } catch (error) {
    console.error('Error fetching teacher permission:', error)
    return NextResponse.json(
      { error: 'Failed to fetch teacher permission' },
      { status: 500 }
    )
  }
}

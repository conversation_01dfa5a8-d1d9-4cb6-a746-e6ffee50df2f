import { NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { verifyJWT } from '@/lib/jwt'

export async function POST() {
  try {
    // Get the token from cookies
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    // Create the response
    const response = NextResponse.json(
      { message: 'Logged out successfully' },
      { status: 200 }
    )

    // Delete the token cookie
    response.cookies.delete('token')

    // Also delete the auth_status cookie if it exists
    response.cookies.delete('auth_status')

    return response
  } catch (error) {
    console.error('Error during logout:', error)
    return NextResponse.json(
      { error: 'Logout failed' },
      { status: 500 }
    )
  }
}
"use client"

import React, { useState, useMemo } from 'react'
import { ResponsiveTable, ResponsiveTableColumn } from '../ui/responsive-table'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar'
import { Edit, Trash2, Eye, Star } from 'lucide-react'

// Sample data interface
interface Student {
  id: string
  name: string
  email: string
  grade: string
  status: 'active' | 'inactive' | 'suspended'
  gpa: number
  enrollmentDate: string
  avatar?: string
  subjects: string[]
  phone: string
  address: string
}

// Generate sample data
const generateSampleData = (count: number): Student[] => {
  const statuses: Student['status'][] = ['active', 'inactive', 'suspended']
  const grades = ['A', 'B', 'C', 'D', 'F']
  const subjects = ['Math', 'Science', 'English', 'History', 'Art', 'PE', 'Music']
  
  return Array.from({ length: count }, (_, i) => ({
    id: `student-${i + 1}`,
    name: `Student ${i + 1}`,
    email: `student${i + 1}@school.edu`,
    grade: grades[Math.floor(Math.random() * grades.length)],
    status: statuses[Math.floor(Math.random() * statuses.length)],
    gpa: Math.round((Math.random() * 3 + 1) * 100) / 100,
    enrollmentDate: new Date(2020 + Math.floor(Math.random() * 4), Math.floor(Math.random() * 12), Math.floor(Math.random() * 28)).toISOString().split('T')[0],
    avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${i}`,
    subjects: subjects.slice(0, Math.floor(Math.random() * 4) + 2),
    phone: `+1 (555) ${String(Math.floor(Math.random() * 900) + 100)}-${String(Math.floor(Math.random() * 9000) + 1000)}`,
    address: `${Math.floor(Math.random() * 9999) + 1} Main St, City, State ${String(Math.floor(Math.random() * 90000) + 10000)}`
  }))
}

export function ResponsiveTableExample() {
  const [data, setData] = useState<Student[]>(() => generateSampleData(50))
  const [isLoading, setIsLoading] = useState(false)

  // Define columns with priorities for mobile-first design
  const columns: ResponsiveTableColumn<Student>[] = useMemo(() => [
    {
      id: 'avatar',
      header: '',
      priority: 'high',
      minWidth: 60,
      maxWidth: 60,
      cell: (student) => (
        <Avatar className="h-8 w-8">
          <AvatarImage src={student.avatar} alt={student.name} />
          <AvatarFallback>{student.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
        </Avatar>
      )
    },
    {
      id: 'name',
      header: 'Name',
      accessorKey: 'name',
      priority: 'high',
      sortable: true,
      filterable: true,
      minWidth: 150,
      mobileLabel: 'Student Name'
    },
    {
      id: 'email',
      header: 'Email',
      accessorKey: 'email',
      priority: 'medium',
      sortable: true,
      filterable: true,
      minWidth: 200,
      cell: (student) => (
        <a href={`mailto:${student.email}`} className="text-blue-600 hover:underline">
          {student.email}
        </a>
      )
    },
    {
      id: 'status',
      header: 'Status',
      accessorKey: 'status',
      priority: 'high',
      sortable: true,
      filterable: true,
      minWidth: 100,
      cell: (student) => (
        <Badge variant={
          student.status === 'active' ? 'default' :
          student.status === 'inactive' ? 'secondary' : 'destructive'
        }>
          {student.status}
        </Badge>
      )
    },
    {
      id: 'grade',
      header: 'Grade',
      accessorKey: 'grade',
      priority: 'medium',
      sortable: true,
      minWidth: 80,
      cell: (student) => (
        <div className="flex items-center gap-1">
          {student.grade}
          {student.grade === 'A' && <Star className="h-3 w-3 text-yellow-500" />}
        </div>
      )
    },
    {
      id: 'gpa',
      header: 'GPA',
      accessorKey: 'gpa',
      priority: 'medium',
      sortable: true,
      minWidth: 80,
      cell: (student) => (
        <span className={`font-medium ${
          student.gpa >= 3.5 ? 'text-green-600' :
          student.gpa >= 2.5 ? 'text-yellow-600' : 'text-red-600'
        }`}>
          {student.gpa.toFixed(2)}
        </span>
      )
    },
    {
      id: 'enrollmentDate',
      header: 'Enrollment Date',
      accessorKey: 'enrollmentDate',
      priority: 'low',
      sortable: true,
      minWidth: 120,
      mobileLabel: 'Enrolled',
      cell: (student) => new Date(student.enrollmentDate).toLocaleDateString()
    },
    {
      id: 'subjects',
      header: 'Subjects',
      priority: 'low',
      minWidth: 200,
      cell: (student) => (
        <div className="flex flex-wrap gap-1">
          {student.subjects.slice(0, 2).map(subject => (
            <Badge key={subject} variant="outline" className="text-xs">
              {subject}
            </Badge>
          ))}
          {student.subjects.length > 2 && (
            <Badge variant="outline" className="text-xs">
              +{student.subjects.length - 2}
            </Badge>
          )}
        </div>
      )
    },
    {
      id: 'phone',
      header: 'Phone',
      accessorKey: 'phone',
      priority: 'low',
      minWidth: 140,
      cell: (student) => (
        <a href={`tel:${student.phone}`} className="text-blue-600 hover:underline">
          {student.phone}
        </a>
      )
    },
    {
      id: 'address',
      header: 'Address',
      accessorKey: 'address',
      priority: 'low',
      minWidth: 200,
      maxWidth: 250,
      cell: (student) => (
        <span className="text-sm text-gray-600 truncate" title={student.address}>
          {student.address}
        </span>
      )
    }
  ], [])

  // Handle actions
  const handleRowClick = (student: Student) => {
    console.log('Row clicked:', student)
  }

  const handleEdit = (student: Student) => {
    console.log('Edit student:', student)
    // Implement edit logic
  }

  const handleDelete = (student: Student) => {
    console.log('Delete student:', student)
    setData(prev => prev.filter(s => s.id !== student.id))
  }

  const handleSwipeLeft = (student: Student) => {
    console.log('Swipe left (edit):', student)
    handleEdit(student)
  }

  const handleSwipeRight = (student: Student) => {
    console.log('Swipe right (delete):', student)
    handleDelete(student)
  }

  const handleRefresh = async () => {
    setIsLoading(true)
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    setData(generateSampleData(50))
    setIsLoading(false)
  }

  const handleLoadMore = async () => {
    // Simulate loading more data
    await new Promise(resolve => setTimeout(resolve, 500))
    const newData = generateSampleData(20)
    setData(prev => [...prev, ...newData])
  }

  return (
    <div className="space-y-6 p-6">
      <div className="space-y-2">
        <h1 className="text-2xl font-bold">Responsive Table Example</h1>
        <p className="text-gray-600">
          This table demonstrates mobile-first responsive design with touch interactions.
          Try resizing your browser or viewing on different devices.
        </p>
      </div>

      <div className="space-y-4">
        <div className="flex gap-2 flex-wrap">
          <Button onClick={handleRefresh} disabled={isLoading}>
            Refresh Data
          </Button>
          <Button 
            variant="outline" 
            onClick={() => setData(prev => [...prev, ...generateSampleData(10)])}
          >
            Add More Data
          </Button>
          <Button 
            variant="outline" 
            onClick={() => setData(generateSampleData(5))}
          >
            Reset to 5 Items
          </Button>
        </div>

        <ResponsiveTable
          data={data}
          columns={columns}
          isLoading={isLoading}
          onRowClick={handleRowClick}
          onRowEdit={handleEdit}
          onRowDelete={handleDelete}
          onRowSwipeLeft={handleSwipeLeft}
          onRowSwipeRight={handleSwipeRight}
          onRefresh={handleRefresh}
          onLoadMore={handleLoadMore}
          hasMore={data.length < 200}
          searchable={true}
          filterable={true}
          sortable={true}
          pagination={true}
          pageSize={15}
          stickyHeader={true}
          virtualScrolling={data.length > 50}
          virtualRowHeight={60}
          virtualContainerHeight={500}
          enableSwipeActions={true}
          enablePullToRefresh={true}
          enableDragScroll={true}
          emptyMessage="No students found"
          loadingMessage="Loading students..."
          className="border rounded-lg shadow-sm"
        />
      </div>

      <div className="text-sm text-gray-500 space-y-1">
        <p><strong>Mobile:</strong> Cards with swipe actions (left = edit, right = delete)</p>
        <p><strong>Tablet:</strong> Horizontal scrolling table with touch support</p>
        <p><strong>Desktop:</strong> Full table with all features</p>
        <p><strong>Touch:</strong> Pull down to refresh, long press for details</p>
      </div>
    </div>
  )
}

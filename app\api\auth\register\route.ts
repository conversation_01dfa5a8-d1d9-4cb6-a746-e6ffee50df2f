import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'
import { formatUserName } from '@/app/utils/formatters'

export async function POST(request: Request) {
  try {
    console.log('Registration request received')

    const body = await request.json()
    const { name, email, password } = body

    console.log('Registration data:', { name, email, password: '********' })

    // Validate input
    if (!name || !email || !password) {
      console.log('Missing required fields')
      return NextResponse.json(
        { error: 'Name, email, and password are required' },
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      console.log('Invalid email format:', email)
      return NextResponse.json(
        { error: 'Please enter a valid email address' },
        { status: 400 }
      )
    }

    // Validate password length
    if (password.length < 8) {
      console.log('Password too short')
      return NextResponse.json(
        { error: 'Password must be at least 8 characters long' },
        { status: 400 }
      )
    }

    // Check if email is already taken
    console.log('Checking if email is already registered:', email)
    const existingUser = await prisma.user.findUnique({
      where: { email },
    })

    if (existingUser) {
      console.log('Email already registered:', email)
      return NextResponse.json(
        { error: 'Email is already registered' },
        { status: 400 }
      )
    }

    // Hash the password
    console.log('Hashing password')
    const hashedPassword = await bcrypt.hash(password, 10)

    // Format the name
    const formattedName = formatUserName(name)
    console.log('Formatted name:', formattedName)

    // Create the user with formatted name - set as inactive and pending role
    console.log('Creating new user with inactive status')
    const user = await prisma.user.create({
      data: {
        name: formattedName,
        email,
        password: hashedPassword,
        role: 'TEACHER', // Default role will be assigned by admin
        status: 'inactive', // Set as inactive until approved by admin
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        status: true,
      },
    })

    console.log('User created successfully:', user)
    return NextResponse.json({
      message: 'Registration successful! Your account is pending approval. Please contact the administrator.',
      user,
    })
  } catch (error) {
    console.error('Error registering user:', error)

    // Check for specific Prisma errors
    if (error instanceof Error) {
      // Check for unique constraint violation
      if (error.message.includes('Unique constraint failed')) {
        return NextResponse.json(
          { error: 'Email is already registered' },
          { status: 400 }
        )
      }

      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to register user' },
      { status: 500 }
    )
  }
}

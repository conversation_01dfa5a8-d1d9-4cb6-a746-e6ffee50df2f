import { NextResponse } from 'next/server';
import { prisma, isUsingMockPrisma, checkDatabaseConnection } from '@/lib/prisma';

export async function GET() {
  try {
    // Check if we're using the mock client
    const usingMock = isUsingMockPrisma();

    // Check database connection
    const connectionStatus = await checkDatabaseConnection();

    // Get counts safely
    const getCounts = async () => {
      try {
        // Check if prisma is properly initialized
        if (!prisma || typeof prisma !== 'object') {
          return { error: 'Prisma client is not properly initialized' };
        }

        // Helper function to safely get count
        const safeCount = async (model: string, countFn: () => Promise<number>) => {
          try {
            if (typeof countFn !== 'function') {
              return { error: `${model} count method not available` };
            }
            return await countFn();
          } catch (e) {
            return { error: e instanceof Error ? e.message : 'Unknown error' };
          }
        };

        return {
          heroSlides: await safeCount('heroSlide', () =>
            prisma.heroSlide && typeof prisma.heroSlide.count === 'function' ?
            prisma.heroSlide.count() : Promise.reject('Count method not available')),

          announcements: await safeCount('announcement', () =>
            prisma.announcement && typeof prisma.announcement.count === 'function' ?
            prisma.announcement.count() : Promise.reject('Count method not available')),

          newsEvents: await safeCount('newsEvent', () =>
            prisma.newsEvent && typeof prisma.newsEvent.count === 'function' ?
            prisma.newsEvent.count() : Promise.reject('Count method not available')),

          testimonials: await safeCount('testimonial', () =>
            prisma.testimonial && typeof prisma.testimonial.count === 'function' ?
            prisma.testimonial.count() : Promise.reject('Count method not available')),

          quickLinks: await safeCount('quickLink', () =>
            prisma.quickLink && typeof prisma.quickLink.count === 'function' ?
            prisma.quickLink.count() : Promise.reject('Count method not available')),

          featuredContents: await safeCount('featuredContent', () =>
            prisma.featuredContent && typeof prisma.featuredContent.count === 'function' ?
            prisma.featuredContent.count() : Promise.reject('Count method not available')),

          callToActions: await safeCount('callToAction', () =>
            prisma.callToAction && typeof prisma.callToAction.count === 'function' ?
            prisma.callToAction.count() : Promise.reject('Count method not available')),
        };
      } catch (countError) {
        console.error('Error getting counts:', countError);
        return {
          error: countError instanceof Error ? countError.message : 'Error getting counts'
        };
      }
    };

    // Get samples safely
    const getSamples = async () => {
      try {
        // Check if prisma is properly initialized
        if (!prisma || typeof prisma !== 'object') {
          return { error: 'Prisma client is not properly initialized' };
        }

        // Helper function to safely get a sample
        const safeFindFirst = async (model: string, findFn: () => Promise<any>) => {
          try {
            if (typeof findFn !== 'function') {
              return { error: `${model} findFirst method not available` };
            }
            return await findFn();
          } catch (e) {
            return { error: e instanceof Error ? e.message : 'Unknown error' };
          }
        };

        return {
          heroSlide: await safeFindFirst('heroSlide', () =>
            prisma.heroSlide && typeof prisma.heroSlide.findFirst === 'function' ?
            prisma.heroSlide.findFirst() : Promise.reject('FindFirst method not available')),

          announcement: await safeFindFirst('announcement', () =>
            prisma.announcement && typeof prisma.announcement.findFirst === 'function' ?
            prisma.announcement.findFirst() : Promise.reject('FindFirst method not available')),

          newsEvent: await safeFindFirst('newsEvent', () =>
            prisma.newsEvent && typeof prisma.newsEvent.findFirst === 'function' ?
            prisma.newsEvent.findFirst() : Promise.reject('FindFirst method not available')),

          testimonial: await safeFindFirst('testimonial', () =>
            prisma.testimonial && typeof prisma.testimonial.findFirst === 'function' ?
            prisma.testimonial.findFirst() : Promise.reject('FindFirst method not available')),

          quickLink: await safeFindFirst('quickLink', () =>
            prisma.quickLink && typeof prisma.quickLink.findFirst === 'function' ?
            prisma.quickLink.findFirst() : Promise.reject('FindFirst method not available')),

          featuredContent: await safeFindFirst('featuredContent', () =>
            prisma.featuredContent && typeof prisma.featuredContent.findFirst === 'function' ?
            prisma.featuredContent.findFirst() : Promise.reject('FindFirst method not available')),

          callToAction: await safeFindFirst('callToAction', () =>
            prisma.callToAction && typeof prisma.callToAction.findFirst === 'function' ?
            prisma.callToAction.findFirst() : Promise.reject('FindFirst method not available')),
        };
      } catch (sampleError) {
        console.error('Error getting samples:', sampleError);
        return {
          error: sampleError instanceof Error ? sampleError.message : 'Error getting samples'
        };
      }
    };

    // Get the data
    const counts = await getCounts();
    const samples = await getSamples();

    return NextResponse.json({
      status: connectionStatus.connected ? 'connected' : 'disconnected',
      usingMockClient: usingMock,
      message: usingMock
        ? 'Using mock Prisma client for website models'
        : 'Using real Prisma client with database connection for website models',
      connectionStatus,
      counts,
      samples,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error testing website models:', error);

    // Provide a safe response even if everything fails
    return NextResponse.json({
      status: 'error',
      message: 'Failed to test website models',
      error: error instanceof Error ? error.message : 'Unknown error',
      suggestion: 'Please check your database connection and ensure the MySQL server is running.',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

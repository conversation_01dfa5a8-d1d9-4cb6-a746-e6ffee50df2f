import { prisma } from '@/lib/prisma'

/**
 * Check if a teacher has permission to view attendance for a specific class
 */
export async function canTeacherViewAttendance(teacherId: string, classId: string): Promise<boolean> {
  try {
    const permission = await prisma.teacherPermission.findUnique({
      where: {
        teacherId_classId: {
          teacherId,
          classId
        }
      }
    })

    return permission?.canViewAttendance || false
  } catch (error) {
    console.error('Error checking teacher attendance view permission:', error)
    return false
  }
}

/**
 * Check if a teacher has permission to take/update attendance for a specific class
 */
export async function canTeacherTakeAttendance(teacherId: string, classId: string): Promise<boolean> {
  try {
    const permission = await prisma.teacherPermission.findUnique({
      where: {
        teacherId_classId: {
          teacherId,
          classId
        }
      }
    })

    return permission?.canTakeAttendance || false
  } catch (error) {
    console.error('Error checking teacher attendance take permission:', error)
    return false
  }
}

/**
 * Check if a teacher has permission to add marks for a specific class
 */
export async function canTeacherAddMarks(teacherId: string, classId: string): Promise<boolean> {
  try {
    const permission = await prisma.teacherPermission.findUnique({
      where: {
        teacherId_classId: {
          teacherId,
          classId
        }
      }
    })

    return permission?.canAddMarks || false
  } catch (error) {
    console.error('Error checking teacher marks add permission:', error)
    return false
  }
}

/**
 * Check if a teacher has permission to edit marks for a specific class
 */
export async function canTeacherEditMarks(teacherId: string, classId: string): Promise<boolean> {
  try {
    const permission = await prisma.teacherPermission.findUnique({
      where: {
        teacherId_classId: {
          teacherId,
          classId
        }
      }
    })

    return permission?.canEditMarks || false
  } catch (error) {
    console.error('Error checking teacher marks edit permission:', error)
    return false
  }
}

/**
 * Get all permissions for a teacher
 */
export async function getTeacherPermissions(teacherId: string) {
  try {
    const permissions = await prisma.teacherPermission.findMany({
      where: {
        teacherId
      },
      include: {
        class: {
          select: {
            id: true,
            name: true
          }
        }
      }
    })

    return permissions
  } catch (error) {
    console.error('Error fetching teacher permissions:', error)
    return []
  }
}

/**
 * Check if a teacher has any permission for a specific class
 */
export async function hasTeacherClassPermission(teacherId: string, classId: string): Promise<boolean> {
  try {
    const permission = await prisma.teacherPermission.findUnique({
      where: {
        teacherId_classId: {
          teacherId,
          classId
        }
      }
    })

    return !!permission && (
      permission.canViewAttendance ||
      permission.canTakeAttendance ||
      permission.canAddMarks ||
      permission.canEditMarks
    )
  } catch (error) {
    console.error('Error checking teacher class permission:', error)
    return false
  }
}

/* Wave Animation Background Styles */
.login-page-container {
  position: relative;
  overflow: hidden;
  background: linear-gradient(to bottom, #ffffff 0%, #990000 100%);
  animation: gradient 15s ease infinite;
  background-size: 400% 400%;
  background-attachment: fixed;
}

/* Dark mode background */
.dark .login-page-container {
  background: linear-gradient(to bottom, #1a1a1a 0%, #660000 100%);
}

@keyframes gradient {
  0% {
    background-position: 0% 0%;
  }
  50% {
    background-position: 100% 100%;
  }
  100% {
    background-position: 0% 0%;
  }
}

.wave-container {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.wave {
  background: rgb(255 255 255 / 35%);
  border-radius: 1000% 1000% 0 0;
  position: fixed;
  width: 200%;
  height: 12em;
  animation: wave 10s -3s linear infinite;
  transform: translate3d(0, 0, 0);
  opacity: 0.8;
  bottom: 0;
  left: 0;
  z-index: 0;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
}

/* Dark mode waves */
.dark .wave {
  background: rgb(255 255 255 / 15%);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.05);
}

.wave:nth-of-type(2) {
  bottom: -1.25em;
  animation: wave 18s linear reverse infinite;
  opacity: 0.8;
}

.wave:nth-of-type(3) {
  bottom: -2.5em;
  animation: wave 20s -1s reverse infinite;
  opacity: 0.9;
}

@keyframes wave {
  2% {
    transform: translateX(1);
  }
  25% {
    transform: translateX(-25%);
  }
  50% {
    transform: translateX(-50%);
  }
  75% {
    transform: translateX(-25%);
  }
  100% {
    transform: translateX(1);
  }
}

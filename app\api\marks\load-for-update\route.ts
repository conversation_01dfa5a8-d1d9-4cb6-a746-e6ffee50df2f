import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const className = searchParams.get('className')
    const subject = searchParams.get('subject')
    const term = searchParams.get('term')
    const academicYear = searchParams.get('academicYear')

    // Validate required parameters
    if (!className || !subject || !term || !academicYear) {
      return NextResponse.json(
        { error: 'Class name, subject, term, and academic year are required' },
        { status: 400 }
      )
    }

    console.log(`Loading marks for update: ${className} - ${subject} - ${term} - ${academicYear}`)

    // Get existing marks for the specified criteria
    const marks = await prisma.mark.findMany({
      where: {
        className: className,
        subject: subject,
        term: term,
        academicYear: academicYear,
      },
      include: {
        student: {
          select: {
            id: true,
            name: true,
            sid: true,
          },
        },
      },
      orderBy: [
        {
          student: {
            sid: 'asc',
          },
        },
        {
          student: {
            name: 'asc',
          },
        },
      ],
    })

    if (marks.length === 0) {
      return NextResponse.json(
        { error: `No marks found for ${subject} in ${className} for ${term} ${academicYear}` },
        { status: 404 }
      )
    }

    // Format the marks data for the frontend
    const formattedMarks = marks.map(mark => ({
      id: mark.id,
      studentId: mark.studentId,
      studentName: mark.student.name,
      studentSid: mark.student.sid,
      marks: mark.marks,
      totalMarks: mark.totalMarks,
      className: mark.className,
      subject: mark.subject,
      term: mark.term,
      academicYear: mark.academicYear,
      remarks: mark.remarks,
      createdAt: mark.createdAt,
      updatedAt: mark.updatedAt,
    }))

    console.log(`Found ${formattedMarks.length} marks for update`)

    return NextResponse.json(formattedMarks)
  } catch (error) {
    console.error('Error loading marks for update:', error)
    return NextResponse.json(
      { error: 'Failed to load marks for update' },
      { status: 500 }
    )
  }
}

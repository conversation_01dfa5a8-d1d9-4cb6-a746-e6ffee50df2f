'use client'

import { useState, useEffect } from 'react'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/app/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/app/components/ui/select'
import { Button } from '@/app/components/ui/button'
import { Label } from '@/app/components/ui/label'
import { RefreshCw } from 'lucide-react'

interface Teacher {
  id: string
  name: string
  email: string
  subject?: string
  mobile?: string
  source?: 'teacher' | 'user'
}

interface Class {
  id: string
  name: string
  totalStudents?: number
  totalSubjects?: number
}

interface TeacherPermission {
  id: string
  teacherId: string
  classId: string
  canViewAttendance: boolean
  canTakeAttendance: boolean
  canAddMarks: boolean
  canEditMarks: boolean
  teacher?: Teacher
  class?: Class
}

interface AssignTeacherPermissionsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: () => void
}

export function AssignTeacherPermissionsDialog({
  open,
  onOpenChange,
  onSuccess
}: AssignTeacherPermissionsDialogProps) {
  const queryClient = useQueryClient()
  
  // State
  const [selectedTeacher, setSelectedTeacher] = useState<string>('')
  const [selectedClasses, setSelectedClasses] = useState<string[]>([])
  const [canViewAttendance, setCanViewAttendance] = useState(false)
  const [canTakeAttendance, setCanTakeAttendance] = useState(false)
  const [canAddMarks, setCanAddMarks] = useState(false)
  const [canEditMarks, setCanEditMarks] = useState(false)
  const [classSearchTerm, setClassSearchTerm] = useState('')

  // Fetch teachers from both Teacher table and User table
  const {
    data: teachers = [],
    isLoading: isLoadingTeachers,
    error: teachersError
  } = useQuery({
    queryKey: ['teachers-for-permissions'],
    queryFn: async () => {
      const res = await fetch('/api/teachers/unified')
      if (!res.ok) throw new Error('Failed to fetch teachers')
      const response = await res.json()
      return response.teachers || []
    },
    enabled: open // Only fetch when dialog is open
  })

  // Fetch classes
  const {
    data: classes = [],
    isLoading: isLoadingClasses,
    error: classesError
  } = useQuery({
    queryKey: ['classes-for-permissions'],
    queryFn: async () => {
      const res = await fetch('/api/classes')
      if (!res.ok) throw new Error('Failed to fetch classes')
      return res.json()
    },
    enabled: open // Only fetch when dialog is open
  })

  // Fetch existing permissions
  const {
    data: existingPermissions = [],
    isLoading: isLoadingPermissions
  } = useQuery({
    queryKey: ['teacher-permissions-for-dialog'],
    queryFn: async () => {
      const res = await fetch('/api/teacher-permissions')
      if (!res.ok) throw new Error('Failed to fetch permissions')
      const data = await res.json()
      return data.permissions || []
    },
    enabled: open // Only fetch when dialog is open
  })

  // Assign permissions mutation for multiple classes
  const assignPermissions = useMutation({
    mutationFn: async (data: {
      teacherId: string
      classIds: string[]
      permissions: {
        canViewAttendance: boolean
        canTakeAttendance: boolean
        canAddMarks: boolean
        canEditMarks: boolean
      }
    }) => {
      const response = await fetch('/api/teacher-permissions/batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to assign permissions')
      }

      return response.json()
    },
    onSuccess: (data) => {
      console.log('Teacher permissions assigned successfully:', data)

      // Invalidate and refetch all related queries
      queryClient.invalidateQueries({ queryKey: ['teacher-permissions'] })
      queryClient.invalidateQueries({ queryKey: ['teacher-permissions-for-dialog'] })
      queryClient.invalidateQueries({ queryKey: ['teachers'] }) // Main table teachers query
      queryClient.invalidateQueries({ queryKey: ['teachers-for-permissions'] }) // Dialog teachers query

      // Reset form
      resetForm()

      // Close dialog
      onOpenChange(false)

      // Call success callback
      onSuccess?.()

      // Show success message
      alert('Teacher permissions assigned successfully!')
    },
    onError: (error) => {
      console.error('Failed to assign teacher permissions:', error)
      alert(`Failed to assign permissions: ${error.message}`)
    }
  })

  // Reset form function
  const resetForm = () => {
    setSelectedTeacher('')
    setSelectedClasses([])
    setCanViewAttendance(false)
    setCanTakeAttendance(false)
    setCanAddMarks(false)
    setCanEditMarks(false)
    setClassSearchTerm('')
  }

  // Load existing permissions when teacher is selected
  useEffect(() => {
    if (selectedTeacher && existingPermissions.length > 0) {
      // Find all permissions for this teacher
      const teacherPermissions = existingPermissions.filter(
        (perm: TeacherPermission) => perm.teacherId === selectedTeacher
      )

      if (teacherPermissions.length > 0) {
        console.log('Found existing permissions for teacher:', teacherPermissions)
        // Set the classes that this teacher already has permissions for
        setSelectedClasses(teacherPermissions.map(perm => perm.classId))

        // Use the permissions from the first class as default (assuming same permissions for all classes)
        const firstPermission = teacherPermissions[0]
        setCanViewAttendance(firstPermission.canViewAttendance)
        setCanTakeAttendance(firstPermission.canTakeAttendance)
        setCanAddMarks(firstPermission.canAddMarks)
        setCanEditMarks(firstPermission.canEditMarks)
      } else {
        console.log('No existing permissions found, using defaults')
        setSelectedClasses([])
        setCanViewAttendance(false)
        setCanTakeAttendance(false)
        setCanAddMarks(false)
        setCanEditMarks(false)
      }
    }
  }, [selectedTeacher, existingPermissions])

  // Reset form when dialog closes
  useEffect(() => {
    if (!open) {
      resetForm()
    }
  }, [open])

  // Handle form submission
  const handleSubmit = () => {
    if (!selectedTeacher || selectedClasses.length === 0) {
      alert('Please select a teacher and at least one class')
      return
    }

    assignPermissions.mutate({
      teacherId: selectedTeacher,
      classIds: selectedClasses,
      permissions: {
        canViewAttendance,
        canTakeAttendance,
        canAddMarks,
        canEditMarks
      }
    })
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Assign Teacher Permissions</DialogTitle>
          <DialogDescription>
            Manage what a teacher can do for a specific class
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          {/* Teacher Selection */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="teacher" className="text-right">
              Teacher
            </Label>
            <div className="col-span-3">
              <Select value={selectedTeacher} onValueChange={setSelectedTeacher}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a teacher" />
                </SelectTrigger>
                <SelectContent>
                  {isLoadingTeachers ? (
                    <div className="flex items-center justify-center p-2">
                      <RefreshCw className="h-4 w-4 animate-spin text-gray-400 mr-2" />
                      <span className="text-sm">Loading teachers...</span>
                    </div>
                  ) : teachersError ? (
                    <div className="flex items-center justify-center p-2 text-red-500">
                      <span className="text-sm">Error loading teachers</span>
                    </div>
                  ) : teachers.length === 0 ? (
                    <div className="p-2 text-center text-gray-500">No teachers found</div>
                  ) : (
                    teachers.map((teacher: Teacher) => (
                      <SelectItem key={teacher.id} value={teacher.id}>
                        <div className="flex flex-col">
                          <span className="font-medium">
                            {teacher.name}
                            {teacher.source === 'user' && (
                              <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-1 rounded">User</span>
                            )}
                          </span>
                          <span className="text-xs text-gray-500">
                            {teacher.subject && `${teacher.subject} • `}{teacher.email}
                          </span>
                        </div>
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Class Selection - Multiple Classes */}
          <div className="grid grid-cols-4 items-start gap-4">
            <Label className="text-right pt-2">
              Classes
            </Label>
            <div className="col-span-3">
              {isLoadingClasses ? (
                <div className="flex items-center">
                  <RefreshCw className="h-4 w-4 animate-spin text-gray-400 mr-2" />
                  <span className="text-sm">Loading classes...</span>
                </div>
              ) : classesError ? (
                <div className="flex items-center text-red-500">
                  <span className="text-sm">Error loading classes</span>
                </div>
              ) : classes.length === 0 ? (
                <div className="text-sm text-gray-500">No classes available</div>
              ) : (
                <>
                  <div className="mb-3">
                    <div className="relative mb-2">
                      <input
                        type="text"
                        placeholder="Search classes..."
                        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                        value={classSearchTerm}
                        onChange={(e) => setClassSearchTerm(e.target.value)}
                      />
                    </div>
                    <div className="flex justify-between mb-2">
                      <button
                        type="button"
                        className="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded border"
                        onClick={() => {
                          const filteredClassIds = classes
                            .filter((cls: Class) =>
                              cls.name.toLowerCase().includes(classSearchTerm.toLowerCase())
                            )
                            .map((cls: Class) => cls.id);
                          setSelectedClasses(Array.from(new Set([...selectedClasses, ...filteredClassIds])));
                        }}
                      >
                        Select All {classSearchTerm && 'Filtered'}
                      </button>
                      <button
                        type="button"
                        className="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded border"
                        onClick={() => {
                          if (classSearchTerm) {
                            const filteredClassIds = classes
                              .filter((cls: Class) =>
                                cls.name.toLowerCase().includes(classSearchTerm.toLowerCase())
                              )
                              .map((cls: Class) => cls.id);
                            setSelectedClasses(selectedClasses.filter(id => !filteredClassIds.includes(id)));
                          } else {
                            setSelectedClasses([]);
                          }
                        }}
                      >
                        Clear {classSearchTerm ? 'Filtered' : 'All'}
                      </button>
                    </div>
                  </div>
                  <div className="max-h-40 overflow-y-auto pr-2 border rounded-md">
                    <div className="grid grid-cols-1 gap-1 p-2">
                      {classes
                        .filter((cls: Class) =>
                          cls.name.toLowerCase().includes(classSearchTerm.toLowerCase())
                        )
                        .map((cls: Class) => (
                        <div key={cls.id} className="flex items-center space-x-2 bg-gray-50 p-2 rounded-md hover:bg-gray-100">
                          <input
                            type="checkbox"
                            id={`class-${cls.id}`}
                            checked={selectedClasses.includes(cls.id)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedClasses([...selectedClasses, cls.id])
                              } else {
                                setSelectedClasses(selectedClasses.filter(id => id !== cls.id))
                              }
                            }}
                            className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600"
                          />
                          <Label htmlFor={`class-${cls.id}`} className="text-sm truncate flex-1">
                            {cls.name}
                            {cls.totalStudents && (
                              <span className="text-gray-500 ml-1">({cls.totalStudents} students)</span>
                            )}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                  {classSearchTerm && classes.filter((cls: Class) =>
                    cls.name.toLowerCase().includes(classSearchTerm.toLowerCase())
                  ).length === 0 && (
                    <div className="text-sm text-gray-500 text-center py-2">
                      No classes match your search
                    </div>
                  )}
                </>
              )}

              <div className="text-sm text-gray-500 mt-4 pt-2 border-t">
                Selected: {selectedClasses.length} class{selectedClasses.length !== 1 ? 'es' : ''}
              </div>
            </div>
          </div>

          {/* Permissions */}
          <div className="grid grid-cols-4 items-start gap-4">
            <Label className="text-right pt-2">
              Permissions
            </Label>
            <div className="col-span-3 space-y-4">
              <div className="space-y-2">
                <h3 className="text-sm font-medium">Attendance</h3>
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="view-attendance"
                    checked={canViewAttendance}
                    onChange={(e) => setCanViewAttendance(e.target.checked)}
                    disabled={!selectedTeacher || selectedClasses.length === 0}
                    className={`h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600 ${
                      !selectedTeacher || selectedClasses.length === 0 ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
                    }`}
                  />
                  <Label htmlFor="view-attendance" className="text-sm">
                    Can view attendance
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="take-attendance"
                    checked={canTakeAttendance}
                    onChange={(e) => setCanTakeAttendance(e.target.checked)}
                    disabled={!selectedTeacher || selectedClasses.length === 0}
                    className={`h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600 ${
                      !selectedTeacher || selectedClasses.length === 0 ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
                    }`}
                  />
                  <Label htmlFor="take-attendance" className="text-sm">
                    Can take/update attendance
                  </Label>
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="text-sm font-medium">Academic Records</h3>
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="add-marks"
                    checked={canAddMarks}
                    onChange={(e) => setCanAddMarks(e.target.checked)}
                    disabled={!selectedTeacher || selectedClasses.length === 0}
                    className={`h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600 ${
                      !selectedTeacher || selectedClasses.length === 0 ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
                    }`}
                  />
                  <Label htmlFor="add-marks" className="text-sm">
                    Can add marks
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="edit-marks"
                    checked={canEditMarks}
                    onChange={(e) => setCanEditMarks(e.target.checked)}
                    disabled={!selectedTeacher || selectedClasses.length === 0}
                    className={`h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600 ${
                      !selectedTeacher || selectedClasses.length === 0 ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
                    }`}
                  />
                  <Label htmlFor="edit-marks" className="text-sm">
                    Can edit marks
                  </Label>
                </div>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={assignPermissions.isPending || !selectedTeacher || selectedClasses.length === 0}
          >
            {assignPermissions.isPending ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : 'Save Permissions'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

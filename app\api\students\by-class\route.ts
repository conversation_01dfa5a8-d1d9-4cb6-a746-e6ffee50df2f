import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const className = searchParams.get('class');

    if (!className) {
      return NextResponse.json({ error: 'Class parameter is required' }, { status: 400 });
    }

    // Fetch students from the database for the specified class
    const students = await prisma.student.findMany({
      where: {
        className: className,
      },
      select: {
        id: true,
        sid: true,
        name: true,
      },
      orderBy: {
        name: 'asc',
      },
    });

    return NextResponse.json(students);
  } catch (error) {
    console.error('Error fetching students:', error);
    return NextResponse.json({ error: 'Failed to fetch students' }, { status: 500 });
  }
} 
import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { cookies } from 'next/headers'
import { verifyJWT } from '@/lib/jwt'

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const year = searchParams.get('year')

    // Year parameter is now optional
    console.log('Fetching classes, year parameter:', year);
    console.log('Fetching classes with HR teacher data');

    // Check database connection first
    await prisma.$connect()

    // Fetch classes from the database with HR teacher relation
    const classes = await prisma.class.findMany({
      include: {
        students: {
          select: {
            id: true,
          },
        },
        subjects: {
          select: {
            id: true,
          },
        },
        hrTeacher: {
          select: {
            id: true,
            name: true,
            email: true,
            subject: true,
            fatherName: true,
            gender: true,
            mobile: true,
          },
        },
        classTeachers: true,
        teacherAssignments: true,
      },
      orderBy: {
        name: 'asc',
      },
    })

    // Transform the data to include counts and HR teacher info
    const transformedClasses = classes.map(cls => ({
      id: cls.id,
      name: cls.name,
      hrTeacherId: cls.hrTeacherId,
      hrTeacherName: cls.hrTeacher?.name || null,
      hrTeacher: cls.hrTeacher,
      totalStudents: cls.students ? cls.students.length : cls.totalStudents || 0,
      totalSubjects: cls.subjects ? cls.subjects.length : cls.totalSubjects || 0,
      createdAt: cls.createdAt,
      updatedAt: cls.updatedAt
    }))

    console.log(`Fetched ${transformedClasses.length} classes with HR teacher data`)
    return NextResponse.json(transformedClasses)
  } catch (error) {
    console.error('Error fetching classes:', error)

    // More detailed error logging
    if (error instanceof Error) {
      console.error('Error name:', error.name)
      console.error('Error message:', error.message)
      console.error('Error stack:', error.stack)
    }

    // Handle specific database connection errors
    if (error instanceof Error && error.message.includes('connect')) {
      return NextResponse.json({
        error: 'Database connection failed',
        details: error.message
      }, { status: 500 })
    }

    return NextResponse.json({
      error: 'Failed to fetch classes',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  } finally {
    // Ensure database connection is closed
    await prisma.$disconnect()
  }
}

export async function POST(request: Request) {
  console.log('=== POST /api/classes - ENTRY POINT ===')
  try {
    // Verify authentication and authorization
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    }

    const decoded = await verifyJWT(token)

    // Only allow SUPER_ADMIN, ADMIN, and DATA_ENCODER to add classes
    if (!['SUPER_ADMIN', 'ADMIN', 'DATA_ENCODER'].includes(decoded.role)) {
      return NextResponse.json(
        { error: 'Forbidden - You do not have permission to add classes' },
        { status: 403 }
      )
    }

    const data = await request.json()
    console.log('Received class creation data:', JSON.stringify(data, null, 2))
    console.log('hrTeacherId from request:', data.hrTeacherId, 'type:', typeof data.hrTeacherId)

    // Validate required fields
    if (!data.name) {
      return NextResponse.json({ error: 'Class name is required' }, { status: 400 })
    }

    // Check if class already exists
    const existingClass = await prisma.class.findUnique({
      where: { name: data.name }
    })

    if (existingClass) {
      return NextResponse.json({ error: 'Class with this name already exists' }, { status: 400 })
    }

    // Convert empty strings to null for hrTeacherId
    let validatedHrTeacherId = null
    if (data.hrTeacherId && data.hrTeacherId !== 'none' && data.hrTeacherId.trim() !== '') {
      validatedHrTeacherId = data.hrTeacherId.trim()

      // Verify the teacher exists
      const teacher = await prisma.teacher.findUnique({
        where: { id: validatedHrTeacherId }
      })

      if (!teacher) {
        return NextResponse.json(
          { error: 'Selected HR teacher does not exist' },
          { status: 400 }
        )
      }
    }

    console.log(`HR Teacher ID processing: original="${data.hrTeacherId}", validated="${validatedHrTeacherId}"`)

    console.log(`Creating class with validated data:`, {
      name: data.name,
      hrTeacherId: validatedHrTeacherId,
      totalStudents: data.totalStudents || 0,
      totalSubjects: data.totalSubjects || 0,
    })

    // Create class with hrTeacherId if provided
    const classData: any = {
      name: data.name,
      totalStudents: data.totalStudents || 0,
      totalSubjects: data.totalSubjects || 0,
    }

    // Include hrTeacherId if provided and valid
    if (validatedHrTeacherId) {
      classData.hrTeacherId = validatedHrTeacherId
    }

    const newClass = await prisma.class.create({
      data: classData,
      include: {
        hrTeacher: {
          select: {
            id: true,
            name: true,
            email: true,
            subject: true,
          },
        },
      },
    })

    // Add HR teacher name to the response
    const responseClass = {
      ...newClass,
      hrTeacherName: newClass.hrTeacher?.name || null
    }

    console.log(`Successfully created class: ${newClass.name}`)
    return NextResponse.json(responseClass)
  } catch (error) {
    console.error('Error creating class:', error)

    // Handle specific Prisma errors
    if (error instanceof Error) {
      if (error.message.includes('Foreign key constraint') && error.message.includes('hrTeacherId')) {
        return NextResponse.json(
          { error: 'Invalid HR Teacher selected. Please choose a valid teacher or leave it empty.' },
          { status: 400 }
        )
      }

      if (error.message.includes('Unique constraint') && error.message.includes('name')) {
        return NextResponse.json(
          { error: 'A class with this name already exists' },
          { status: 400 }
        )
      }
    }

    return NextResponse.json(
      { error: 'Failed to create class' },
      { status: 500 }
    )
  }
}

'use client'

import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { Button } from '@/app/components/ui/button'
import { Label } from '@/app/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/app/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/app/components/ui/dialog'
import { RefreshCw, UserCheck } from 'lucide-react'
import { HRTeacher, HRClass, HRAssignmentRequest } from '@/app/types/hr-teacher'

interface AssignHRTeacherDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: () => void
}

export function AssignHRTeacherDialog({ 
  open, 
  onOpenChange, 
  onSuccess 
}: AssignHRTeacherDialogProps) {
  const [selectedTeacherId, setSelectedTeacherId] = useState<string>('')
  const [selectedClassId, setSelectedClassId] = useState<string>('')
  
  const queryClient = useQueryClient()

  // Fetch available teachers
  const {
    data: teachers = [],
    isLoading: isLoadingTeachers,
    error: teachersError
  } = useQuery<HRTeacher[]>({
    queryKey: ['hr-available-teachers'],
    queryFn: async () => {
      const res = await fetch('/api/hr-teachers/available-teachers')
      if (!res.ok) throw new Error('Failed to fetch teachers')
      return res.json()
    },
    enabled: open
  })

  // Fetch available classes
  const {
    data: classes = [],
    isLoading: isLoadingClasses,
    error: classesError
  } = useQuery<HRClass[]>({
    queryKey: ['hr-available-classes'],
    queryFn: async () => {
      const res = await fetch('/api/hr-teachers/available-classes')
      if (!res.ok) throw new Error('Failed to fetch classes')
      return res.json()
    },
    enabled: open
  })

  // Assign HR teacher mutation
  const assignMutation = useMutation({
    mutationFn: async (data: HRAssignmentRequest) => {
      const res = await fetch('/api/hr-teachers/assign', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })
      if (!res.ok) {
        const error = await res.json()
        throw new Error(error.error || 'Failed to assign HR teacher')
      }
      return res.json()
    },
    onSuccess: () => {
      // Invalidate and refetch related queries
      queryClient.invalidateQueries({ queryKey: ['hr-available-classes'] })
      queryClient.invalidateQueries({ queryKey: ['hr-teachers'] })
      queryClient.invalidateQueries({ queryKey: ['classes'] })
      queryClient.invalidateQueries({ queryKey: ['classes-with-hr'] })

      // Reset form
      setSelectedTeacherId('')
      setSelectedClassId('')

      // Close dialog and call success callback
      onOpenChange(false)
      onSuccess?.()
    },
    onError: (error: Error) => {
      alert(`Failed to assign HR teacher: ${error.message}`)
    }
  })

  const handleSubmit = () => {
    if (!selectedTeacherId || !selectedClassId) {
      alert('Please select both a teacher and a class')
      return
    }

    assignMutation.mutate({
      teacherId: selectedTeacherId,
      classId: selectedClassId
    })
  }

  const handleClose = () => {
    setSelectedTeacherId('')
    setSelectedClassId('')
    onOpenChange(false)
  }

  const selectedTeacher = teachers.find(t => t.id === selectedTeacherId)
  const selectedClass = classes.find(c => c.id === selectedClassId)

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserCheck className="h-5 w-5" />
            Assign HR Teacher
          </DialogTitle>
          <DialogDescription>
            Assign a teacher as HR (Head of Room) for a class
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-6 py-4">
          {/* Teacher Selection */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="teacher" className="text-right">
              Teacher
            </Label>
            <div className="col-span-3">
              <Select value={selectedTeacherId} onValueChange={setSelectedTeacherId}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a teacher" />
                </SelectTrigger>
                <SelectContent>
                  {isLoadingTeachers ? (
                    <div className="flex items-center justify-center p-4">
                      <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                      Loading teachers...
                    </div>
                  ) : teachersError ? (
                    <div className="p-4 text-center text-red-500">
                      Error loading teachers
                    </div>
                  ) : teachers.length === 0 ? (
                    <div className="p-4 text-center text-gray-500">
                      No teachers available
                    </div>
                  ) : (
                    teachers.map((teacher) => (
                      <SelectItem key={teacher.id} value={teacher.id}>
                        <div className="flex flex-col">
                          <span className="font-medium">
                            {teacher.name}
                            {teacher.source === 'user' && (
                              <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-1 rounded">User</span>
                            )}
                          </span>
                          <span className="text-xs text-gray-500">
                            {teacher.subject && `${teacher.subject} • `}{teacher.email}
                          </span>
                        </div>
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Class Selection */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="class" className="text-right">
              Class
            </Label>
            <div className="col-span-3">
              <Select value={selectedClassId} onValueChange={setSelectedClassId}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a class" />
                </SelectTrigger>
                <SelectContent>
                  {isLoadingClasses ? (
                    <div className="flex items-center justify-center p-4">
                      <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                      Loading classes...
                    </div>
                  ) : classesError ? (
                    <div className="p-4 text-center text-red-500">
                      Error loading classes
                    </div>
                  ) : classes.length === 0 ? (
                    <div className="p-4 text-center text-gray-500">
                      No classes available
                    </div>
                  ) : (
                    classes.map((cls) => (
                      <SelectItem key={cls.id} value={cls.id}>
                        <div className="flex flex-col">
                          <span className="font-medium">Class {cls.name}</span>
                          {cls.hrTeacher && (
                            <span className="text-xs text-orange-500">
                              Current HR: {cls.hrTeacher.name}
                            </span>
                          )}
                        </div>
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Assignment Summary */}
          {selectedTeacher && selectedClass && (
            <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
              <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
                Assignment Summary
              </h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Teacher:</span>
                  <span className="font-medium">{selectedTeacher.name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Subject:</span>
                  <span>{selectedTeacher.subject}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Class:</span>
                  <span className="font-medium">Class {selectedClass.name}</span>
                </div>
                {selectedClass.hrTeacher && (
                  <div className="flex justify-between text-orange-600">
                    <span>Note:</span>
                    <span className="text-xs">Will replace {selectedClass.hrTeacher.name}</span>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={!selectedTeacherId || !selectedClassId || assignMutation.isPending}
          >
            {assignMutation.isPending ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Assigning...
              </>
            ) : (
              'Assign HR Teacher'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

import { prisma } from '@/lib/prisma'

/**
 * Check if a teacher has permission to view attendance for a specific class
 */
export async function canTeacherViewAttendance(teacherId: string, classId: string): Promise<boolean> {
  try {
    // First check in the regular teacher permissions table
    const permission = await prisma.teacherPermission.findFirst({
      where: {
        teacherId,
        classId
      }
    })

    if (permission?.canViewAttendance) {
      return true
    }

    // If not found, check in the user teacher permissions table
    try {
      const userPermissions = await prisma.$queryRaw`
        SELECT * FROM user_teacher_permissions 
        WHERE userId = ${teacherId} AND classId = ${classId}
      `

      if (Array.isArray(userPermissions) && userPermissions.length > 0) {
        return !!userPermissions[0].canViewAttendance
      }
    } catch (error) {
      console.error('Error checking user_teacher_permissions table:', error)
      // Continue with the function even if this check fails
    }

    // If no permissions found, check if the teacher is the HR teacher for this class
    const classObj = await prisma.class.findFirst({
      where: {
        id: classId,
        hrTeacherId: teacherId
      }
    })

    // HR teachers can view attendance for their classes
    return !!classObj
  } catch (error) {
    console.error('Error checking teacher attendance view permission:', error)
    return false
  }
}

/**
 * Check if a teacher has permission to take/update attendance for a specific class
 */
export async function canTeacherTakeAttendance(teacherId: string, classId: string): Promise<boolean> {
  try {
    // First check in the regular teacher permissions table
    const permission = await prisma.teacherPermission.findFirst({
      where: {
        teacherId,
        classId
      }
    })

    if (permission?.canTakeAttendance) {
      return true
    }

    // If not found, check in the user teacher permissions table
    try {
      const userPermissions = await prisma.$queryRaw`
        SELECT * FROM user_teacher_permissions 
        WHERE userId = ${teacherId} AND classId = ${classId}
      `

      if (Array.isArray(userPermissions) && userPermissions.length > 0) {
        return !!userPermissions[0].canTakeAttendance
      }
    } catch (error) {
      console.error('Error checking user_teacher_permissions table:', error)
      // Continue with the function even if this check fails
    }

    // If no permissions found, check if the teacher is the HR teacher for this class
    const classObj = await prisma.class.findFirst({
      where: {
        id: classId,
        hrTeacherId: teacherId
      }
    })

    // HR teachers can take attendance for their classes
    return !!classObj
  } catch (error) {
    console.error('Error checking teacher attendance take permission:', error)
    return false
  }
}

/**
 * Check if a teacher has permission to add marks for a specific class
 */
export async function canTeacherAddMarks(teacherId: string, classId: string): Promise<boolean> {
  try {
    // First check in the regular teacher permissions table
    const permission = await prisma.teacherPermission.findFirst({
      where: {
        teacherId,
        classId
      }
    })

    if (permission?.canAddMarks) {
      return true
    }

    // If not found, check in the user teacher permissions table
    try {
      const userPermissions = await prisma.$queryRaw`
        SELECT * FROM user_teacher_permissions 
        WHERE userId = ${teacherId} AND classId = ${classId}
      `

      if (Array.isArray(userPermissions) && userPermissions.length > 0) {
        return !!userPermissions[0].canAddMarks
      }
    } catch (error) {
      console.error('Error checking user_teacher_permissions table:', error)
      // Continue with the function even if this check fails
    }

    // If no permissions found, check if the teacher is the HR teacher for this class
    const classObj = await prisma.class.findFirst({
      where: {
        id: classId,
        hrTeacherId: teacherId
      }
    })

    // HR teachers can add marks for their classes
    return !!classObj
  } catch (error) {
    console.error('Error checking teacher marks add permission:', error)
    return false
  }
}

/**
 * Check if a teacher has permission to edit marks for a specific class
 */
export async function canTeacherEditMarks(teacherId: string, classId: string): Promise<boolean> {
  try {
    // First check in the regular teacher permissions table
    const permission = await prisma.teacherPermission.findFirst({
      where: {
        teacherId,
        classId
      }
    })

    if (permission?.canEditMarks) {
      return true
    }

    // If not found, check in the user teacher permissions table
    try {
      const userPermissions = await prisma.$queryRaw`
        SELECT * FROM user_teacher_permissions 
        WHERE userId = ${teacherId} AND classId = ${classId}
      `

      if (Array.isArray(userPermissions) && userPermissions.length > 0) {
        return !!userPermissions[0].canEditMarks
      }
    } catch (error) {
      console.error('Error checking user_teacher_permissions table:', error)
      // Continue with the function even if this check fails
    }

    // If no permissions found, check if the teacher is the HR teacher for this class
    const classObj = await prisma.class.findFirst({
      where: {
        id: classId,
        hrTeacherId: teacherId
      }
    })

    // HR teachers can edit marks for their classes
    return !!classObj
  } catch (error) {
    console.error('Error checking teacher marks edit permission:', error)
    return false
  }
}

/**
 * Check if a teacher has any permission for a specific class
 */
export async function hasTeacherClassPermission(teacherId: string, classId: string): Promise<boolean> {
  try {
    return (
      await canTeacherViewAttendance(teacherId, classId) ||
      await canTeacherTakeAttendance(teacherId, classId) ||
      await canTeacherAddMarks(teacherId, classId) ||
      await canTeacherEditMarks(teacherId, classId)
    )
  } catch (error) {
    console.error('Error checking teacher class permission:', error)
    return false
  }
}

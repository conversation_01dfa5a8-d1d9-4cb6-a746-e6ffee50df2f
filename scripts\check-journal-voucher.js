// Script to check if JournalVoucher model is available in Prisma client
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    // Check if JournalVoucher model is available
    console.log('Checking if JournalVoucher model is available in Prisma client...');
    
    // Check if the model exists in the Prisma client
    const hasJournalVoucherModel = Object.prototype.hasOwnProperty.call(prisma, 'journalVoucher');
    console.log('JournalVoucher model exists in Prisma client:', hasJournalVoucherModel);
    
    if (hasJournalVoucherModel) {
      // Try to count the number of journal vouchers
      console.log('\nCounting journal vouchers...');
      const count = await prisma.journalVoucher.count();
      console.log(`Found ${count} journal vouchers in the database`);
      
      // Try to create a test journal voucher
      console.log('\nCreating a test journal voucher...');
      const testVoucher = await prisma.journalVoucher.create({
        data: {
          voucherNo: `TEST-JV-${Date.now()}`,
          date: new Date(),
          paidTo: 'Test Recipient',
          accountCode: '5504',
          amount: 100.0,
          paymentMethod: 'cash',
          purpose: 'Testing JournalVoucher model availability',
          status: 'pending'
        }
      });
      console.log('Test journal voucher created:', testVoucher);
      
      // Delete the test voucher
      console.log('\nDeleting the test journal voucher...');
      await prisma.journalVoucher.delete({
        where: {
          id: testVoucher.id
        }
      });
      console.log('Test journal voucher deleted successfully');
    }
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();

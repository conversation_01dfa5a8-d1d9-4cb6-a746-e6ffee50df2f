// Replace this in the Take Attendance view (around line 598)
              <SelectContent position="popper" sideOffset={5} className="z-[50]">
                {classes && classes.length > 0 ? (
                  classes.map((cls: { id: string, name: string }) => (
                    <SelectItem key={cls.id} value={cls.name}>
                      Class {cls.name}
                    </SelectItem>
                  ))
                ) : (
                  <div className="p-2 text-center text-sm text-gray-500">
                    {classesError ? 'Error loading classes' : 'No classes found'}
                  </div>
                )}
              </SelectContent>

// Replace this in the Report section (around line 770)
              <SelectContent position="popper" sideOffset={5} className="z-[50]">
                {classes && classes.length > 0 ? (
                  classes.map((cls: { id: string, name: string }) => (
                    <SelectItem key={cls.id} value={cls.name}>
                      Class {cls.name}
                    </SelectItem>
                  ))
                ) : (
                  <div className="p-2 text-center text-sm text-gray-500">
                    {classesError ? 'Error loading classes' : 'No classes found'}
                  </div>
                )}
              </SelectContent>

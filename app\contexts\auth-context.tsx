'use client'

import React, { createContext, useContext, useState, useEffect } from 'react'

// Define a simple user type
type User = {
  id: string
  name: string
  email: string
  role: string
  phone?: string
  address?: string
  bio?: string
  photoUrl?: string
  status?: string
  lastLogin?: string
  language?: string
  emailNotifications?: boolean
  appNotifications?: boolean
  theme?: string
}

// Define the auth context type
interface AuthContextType {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
  checkAuth: () => Promise<void>
  forceRefresh: () => Promise<void>
  logout: () => void
  updateProfile: (profileData: Partial<User>) => Promise<void>
  updatePassword: (currentPassword: string, newPassword: string) => Promise<void>
  hasPermission: (permission: string) => boolean
  hasSidebarAccess: (sidebarItem: string) => boolean
}

// Create the context with default values
const AuthContext = createContext<AuthContextType>({
  user: null,
  isLoading: false,
  isAuthenticated: false,
  checkAuth: async () => {},
  forceRefresh: async () => {},
  logout: () => {},
  updateProfile: async () => {},
  updatePassword: async () => {},
  hasPermission: () => false,
  hasSidebarAccess: () => false
})

// Hook to use the auth context
export function useAuth() {
  return useContext(AuthContext)
}

// Auth provider component
export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  // Helper function to handle API requests with token refresh
  const apiRequest = async (url: string, options: RequestInit) => {
    try {
      // First attempt
      const res = await fetch(url, {
        ...options,
        credentials: 'include', // Always include cookies
      })

      const data = await res.json()

      // If unauthorized and token expired, try to refresh token and retry
      if (res.status === 401 && data.error === 'Token expired') {
        console.log('Token expired, attempting to refresh...')

        // Try to refresh the token
        const refreshRes = await fetch('/api/auth/refresh', {
          method: 'POST',
          credentials: 'include'
        })

        if (refreshRes.ok) {
          console.log('Token refreshed successfully, retrying original request')

          // Retry the original request with the new token
          const retryRes = await fetch(url, {
            ...options,
            credentials: 'include',
          })

          const retryData = await retryRes.json()

          if (!retryRes.ok) {
            throw new Error(retryData.error || `Failed to ${options.method} ${url}`)
          }

          return retryData
        } else {
          console.error('Token refresh failed')
          throw new Error('Session expired. Please log in again.')
        }
      }

      // If not unauthorized or token refresh not needed/possible
      if (!res.ok) {
        throw new Error(data.error || `Failed to ${options.method} ${url}`)
      }

      return data
    } catch (error) {
      console.error(`API request error (${url}):`, error)
      throw error
    }
  }

  // Function to check authentication with the server
  const checkAuth = async () => {
    setIsLoading(true)
    try {
      console.log('Checking authentication status...')

      // Check if we have auth_status cookie (indicates logged in state)
      const authStatusCookie = document.cookie
        .split('; ')
        .find(row => row.startsWith('auth_status='));

      console.log('Auth status cookie found:', !!authStatusCookie);

      // Call the /api/auth/me endpoint to get the current user
      const response = await fetch('/api/auth/me', {
        credentials: 'include'
      });

      if (response.ok) {
        const data = await response.json();

        if (data.user) {
          // User is authenticated, set user data
          console.log('User authenticated:', data.user);

          // Force a new object to trigger re-renders
          setUser(prevUser => {
            const newUser = {
              id: data.user.id,
              name: data.user.name,
              email: data.user.email,
              role: data.user.role,
              status: data.user.status,
              phone: data.user.phone,
              address: data.user.address,
              bio: data.user.bio,
              photoUrl: data.user.photoUrl,
              lastLogin: data.user.lastLogin,
              language: data.user.language,
              emailNotifications: data.user.emailNotifications,
              appNotifications: data.user.appNotifications,
              theme: data.user.theme
            };

            // Only update if there's actually a change
            if (!prevUser || JSON.stringify(prevUser) !== JSON.stringify(newUser)) {
              console.log('User data updated, triggering re-render');
              return newUser;
            }

            return prevUser;
          });

          return;
        }
      }

      // If we get here, user is not authenticated
      console.log('User not authenticated');
      setUser(null);

    } catch (error) {
      console.error('Auth check error:', error);
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  }

  // Force refresh function - forces a re-check of auth status
  const forceRefresh = async () => {
    console.log('Force refreshing auth status...')
    await checkAuth()
  }

  // Logout function
  const logout = async () => {
    // Clear user data
    setUser(null)
    setIsLoading(true)

    try {
      // Call the logout API to clear the token cookie
      const response = await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include',
      })

      if (response.ok) {
        console.log('Logged out successfully')
      } else {
        console.error('Logout failed')
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      setIsLoading(false)
      // Redirect to login page
      window.location.href = '/login'
    }
  }

  // Check auth on mount
  useEffect(() => {
    const checkInitialAuth = async () => {
      await checkAuth()

      // Set up automatic refresh when the token is about to expire
      // Check auth every 5 minutes to keep token fresh
      const intervalId = setInterval(() => {
        checkAuth()
      }, 5 * 60 * 1000) // 5 minutes

      return () => clearInterval(intervalId)
    }

    checkInitialAuth()
  }, [])

  // Update profile function
  const updateProfile = async (profileData: Partial<User>) => {
    try {
      setIsLoading(true)
      console.log('Updating profile with:', profileData)

      // Ensure required fields are present
      if (!profileData.name || !profileData.email) {
        throw new Error('Name and email are required')
      }

      const data = await apiRequest('/api/user/profile', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(profileData)
      })

      console.log('Profile update response:', data)

      // Update the user state with the returned user data
      if (data.user) {
        setUser(prev => ({
          ...prev,
          ...data.user
        }))
      }

      return data
    } catch (error) {
      console.error('Profile update error:', error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  // Update password function
  const updatePassword = async (currentPassword: string, newPassword: string) => {
    try {
      setIsLoading(true)
      console.log('Updating password')

      const data = await apiRequest('/api/user/password', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ currentPassword, newPassword })
      })

      console.log('Password update response:', { success: true, message: data.message })
      return data
    } catch (error) {
      console.error('Password update error:', error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  // Helper function to check if user has a specific permission
  const hasPermission = (permission: string): boolean => {
    if (!user) return false;

    // Super Admin has all permissions
    if (user.role === 'SUPER_ADMIN') return true;

    // Define permissions for each role
    const rolePermissions = {
      'ADMIN': [
        'View Students', 'Edit Students', 'Add Students', 'Delete Students',
        'View Teachers', 'Edit Teachers', 'Add Teachers', 'Delete Teachers',
        'View Classes', 'Edit Classes', 'Add Classes', 'Delete Classes',
        'View Attendance', 'Add Attendance', 'Edit Attendance',
        'View Marks', 'Add Marks', 'Edit Marks',
        'View Reports', 'Generate Reports', 'Export Reports',
        'Manage Website'
      ],
      'SUPERVISOR': [
        'View Students', 'Edit Students',
        'View Teachers', 'Edit Teachers',
        'View Classes', 'Edit Classes',
        'View Marks', 'Add Marks', 'Edit Marks',
        'View Reports', 'Generate Reports', 'Export Reports',
        'View Visualizer',
        'Manage Website'
      ],
      'ACCOUNTANT': [
        'Manage Accounting', 'View Accounting', 'Edit Accounting',
        'Manage Vouchers', 'Create Vouchers', 'Edit Vouchers',
        'Manage Payments', 'Create Payments', 'Edit Payments'
      ],
      'TEACHER': [
        'View Students',
        'View Teachers',
        'View Classes',
        'View Attendance', 'Add Attendance', 'Edit Attendance',
        'View Marks', 'Add Marks', 'Edit Marks'
      ],
      'PARENT': [
        'View Own Student Data',
        'View Student Marks',
        'View Student Attendance',
        'View Student Reports'
      ],
      'UNIT_LEADER': [
        'View Students',
        'View Teachers',
        'View Classes',
        'View Marks',
        'View Attendance', 'Add Attendance', 'Edit Attendance',
        'View Visualizer'
      ],
      'DATA_ENCODER': [
        'View Students', 'Add Students', 'Edit Students',
        'View Teachers', 'Add Teachers', 'Edit Teachers',
        'View Classes', 'Add Classes', 'Edit Classes',
        'View Marks', 'Add Marks', 'Edit Marks'
      ]
    };

    // Check if the user's role has the requested permission
    return rolePermissions[user.role]?.includes(permission) || false;
  };

  // Helper function to check if user has access to a specific sidebar item
  const hasSidebarAccess = (sidebarItem: string): boolean => {
    if (!user) return false;

    // Super Admin has access to all sidebar items
    if (user.role === 'SUPER_ADMIN') return true;

    // Define sidebar access for each role
    const sidebarAccess = {
      'ADMIN': [
        'dashboard', 'students', 'students-all-students', 'students-id-card',
        'teachers', 'class', 'attendance', 'marklist', 'reportcard', 'progress-manager',
        'website', 'website-hero-slides', 'website-announcements', 'website-quick-links',
        'website-featured-content', 'website-news-events', 'website-academic-calendar',
        'website-testimonials', 'website-tuition-fees', 'website-call-to-action',
        'profile', 'roles'
      ],
      'SUPERVISOR': [
        'dashboard', 'students', 'students-all-students', 'students-id-card',
        'teachers', 'class', 'marklist', 'reportcard', 'visualizer', 'progress-manager',
        'website', 'website-hero-slides', 'website-announcements', 'website-quick-links',
        'website-featured-content', 'website-news-events', 'website-academic-calendar',
        'website-testimonials', 'website-tuition-fees', 'website-call-to-action',
        'profile'
      ],
      'ACCOUNTANT': [
        'dashboard', 'accounting', 'accounting-fee-types', 'accounting-account-codes',
        'accounting-fee-invoice', 'accounting-invoices-report', 'accounting-bank-payment-voucher',
        'accounting-journal-voucher', 'profile'
      ],
      'TEACHER': [
        'dashboard', 'students', 'students-all-students',
        'teachers', 'class', 'attendance', 'marklist', 'profile'
      ],
      'PARENT': [
        'dashboard', 'my-children', 'my-children-children-overview', 'my-children-children-attendance',
        'my-children-children-marks', 'my-children-children-reports', 'profile'
      ],
      'UNIT_LEADER': [
        'dashboard', 'students', 'students-all-students',
        'teachers', 'class', 'marklist', 'attendance', 'visualizer', 'profile'
      ],
      'DATA_ENCODER': [
        'dashboard', 'students', 'students-all-students',
        'teachers', 'class', 'marklist', 'profile'
      ]
    };

    // Check if the item is directly in the list
    if (sidebarAccess[user.role]?.includes(sidebarItem)) {
      return true;
    }

    // For parent items, check if any of their children are accessible
    // For example, if 'students-all-students' is accessible, then 'students' should be too
    if (sidebarItem.indexOf('-') === -1) {
      // This is a parent item, check if any child items are accessible
      return sidebarAccess[user.role]?.some(item =>
        item.startsWith(`${sidebarItem}-`)
      ) || false;
    }

    return false;
  };

  // Context value
  const value = {
    user,
    isLoading,
    isAuthenticated: !!user,
    checkAuth,
    forceRefresh,
    logout,
    updateProfile,
    updatePassword,
    hasPermission,
    hasSidebarAccess
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

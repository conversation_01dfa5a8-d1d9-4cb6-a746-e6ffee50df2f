"use client"

import React, { useState } from 'react'
import { <PERSON><PERSON> } from './ui/button'
import { Input } from './ui/input'
import {
  User,
  Bell,
  Palette,
  Shield,
  Save,
  ChevronRight,
  GraduationCap
} from 'lucide-react'
import { formatUserName } from '@/app/utils/formatters'
import dynamic from 'next/dynamic'

// Dynamically import ThemeSettings with SSR disabled to prevent hydration issues
const ThemeSettings = dynamic(() => import('./ThemeSettings').then(mod => ({ default: mod.ThemeSettings })), {
  ssr: false
})

// Dynamically import PromotionPolicy
const PromotionPolicy = dynamic(() => import('./PromotionPolicy').then(mod => ({ default: mod.PromotionPolicy })), {
  ssr: false
})

export function Settings() {
  const [activeTab, setActiveTab] = useState('profile')
  const [emailNotifications, setEmailNotifications] = useState(true)
  const [pushNotifications, setPushNotifications] = useState(true)
  const [language, setLanguage] = useState('english')

  // Form data
  const [profileData, setProfileData] = useState({
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '******-567-8901'
  })

  const handleProfileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setProfileData({
      ...profileData,
      [name]: name === 'name' ? formatUserName(value) : value
    })
  }

  const handleSaveProfile = (e: React.FormEvent) => {
    e.preventDefault()
    // Simulate saving with a success message
    alert('Profile updated successfully!')
  }

  const renderTabContent = () => {
    switch (activeTab) {
      case 'profile':
        return (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold">Profile Settings</h2>
            <p className="text-gray-500 text-sm">Update your personal information</p>

            <form onSubmit={handleSaveProfile} className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1" htmlFor="name">Full Name</label>
                <Input
                  id="name"
                  name="name"
                  value={profileData.name}
                  onChange={handleProfileChange}
                  className="max-w-md"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1" htmlFor="email">Email Address</label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={profileData.email}
                  onChange={handleProfileChange}
                  className="max-w-md"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1" htmlFor="phone">Phone Number</label>
                <Input
                  id="phone"
                  name="phone"
                  value={profileData.phone}
                  onChange={handleProfileChange}
                  className="max-w-md"
                />
              </div>

              <div className="pt-4">
                <Button type="submit" className="bg-indigo-600 hover:bg-indigo-700">
                  <Save className="h-4 w-4 mr-2" /> Save Changes
                </Button>
              </div>
            </form>
          </div>
        )

      case 'appearance':
        return (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold">Appearance</h2>
            <p className="text-gray-500 text-sm">Customize how the application looks</p>

            {typeof ThemeSettings !== 'undefined' ? <ThemeSettings /> : (
              <div className="p-4 border rounded-lg bg-gray-50">
                <p className="text-center text-gray-500">Loading theme settings...</p>
              </div>
            )}

            <div className="space-y-4 mt-6">
              <div className="flex items-center justify-between py-3 border-b">
                <div>
                  <h3 className="font-medium">Language</h3>
                  <p className="text-sm text-gray-500">Select your preferred language</p>
                </div>
                <div>
                  <select
                    value={language}
                    onChange={(e) => setLanguage(e.target.value)}
                    className="rounded-md border border-gray-300 py-2 px-3 focus:outline-none focus:ring-2 focus:ring-indigo-600"
                  >
                    <option value="english">English</option>
                    <option value="spanish">Spanish</option>
                    <option value="french">French</option>
                    <option value="german">German</option>
                    <option value="japanese">Japanese</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        )

      case 'notifications':
        return (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold">Notifications</h2>
            <p className="text-gray-500 text-sm">Manage your notification preferences</p>

            <div className="space-y-4">
              <div className="flex items-center justify-between py-3 border-b">
                <div>
                  <h3 className="font-medium">Email Notifications</h3>
                  <p className="text-sm text-gray-500">Receive email updates about system activities</p>
                </div>
                <div>
                  <label className="inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={emailNotifications}
                      onChange={() => setEmailNotifications(!emailNotifications)}
                      className="sr-only peer"
                    />
                    <div className="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-indigo-600 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                  </label>
                </div>
              </div>

              <div className="flex items-center justify-between py-3 border-b">
                <div>
                  <h3 className="font-medium">Push Notifications</h3>
                  <p className="text-sm text-gray-500">Receive push notifications in your browser</p>
                </div>
                <div>
                  <label className="inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={pushNotifications}
                      onChange={() => setPushNotifications(!pushNotifications)}
                      className="sr-only peer"
                    />
                    <div className="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-indigo-600 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                  </label>
                </div>
              </div>
            </div>
          </div>
        )

      case 'security':
        return (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold">Security</h2>
            <p className="text-gray-500 text-sm">Manage account security settings</p>

            <div className="space-y-4">
              <div className="flex items-center justify-between py-4 px-4 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer">
                <div className="flex items-center">
                  <div className="bg-indigo-100 p-2.5 rounded-full text-indigo-600 mr-3">
                    <Shield className="h-5 w-5" />
                  </div>
                  <div>
                    <h3 className="font-medium">Change Password</h3>
                    <p className="text-sm text-gray-500">Update your password regularly for better security</p>
                  </div>
                </div>
                <ChevronRight className="h-5 w-5 text-gray-400" />
              </div>

              <div className="flex items-center justify-between py-4 px-4 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer">
                <div className="flex items-center">
                  <div className="bg-indigo-100 p-2.5 rounded-full text-indigo-600 mr-3">
                    <Shield className="h-5 w-5" />
                  </div>
                  <div>
                    <h3 className="font-medium">Two-Factor Authentication</h3>
                    <p className="text-sm text-gray-500">Add an extra layer of security to your account</p>
                  </div>
                </div>
                <ChevronRight className="h-5 w-5 text-gray-400" />
              </div>

              <div className="flex items-center justify-between py-4 px-4 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer">
                <div className="flex items-center">
                  <div className="bg-indigo-100 p-2.5 rounded-full text-indigo-600 mr-3">
                    <Shield className="h-5 w-5" />
                  </div>
                  <div>
                    <h3 className="font-medium">Session Management</h3>
                    <p className="text-sm text-gray-500">Manage your active sessions and devices</p>
                  </div>
                </div>
                <ChevronRight className="h-5 w-5 text-gray-400" />
              </div>
            </div>
          </div>
        )

      case 'promotion':
        return (
          <div className="space-y-6">
            {typeof PromotionPolicy !== 'undefined' ? <PromotionPolicy /> : (
              <div className="p-4 border rounded-lg bg-gray-50">
                <p className="text-center text-gray-500">Loading promotion policy settings...</p>
              </div>
            )}
          </div>
        )

      default:
        return null
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold">Settings</h1>
        <p className="text-gray-600 mt-1">Manage your account settings and preferences</p>
      </div>

      <div className="flex flex-col md:flex-row gap-6">
        {/* Settings navigation */}
        <div className="w-full md:w-64 shrink-0">
          <div className="bg-white rounded-lg border border-gray-200">
            <button
              className={`flex items-center w-full px-4 py-3 text-left ${
                activeTab === 'profile'
                  ? 'bg-indigo-50 text-indigo-700 border-l-4 border-indigo-700'
                  : 'hover:bg-gray-50'
              }`}
              onClick={() => setActiveTab('profile')}
            >
              <User className="h-5 w-5 mr-3" />
              <span className="font-medium">Profile</span>
            </button>

            <button
              className={`flex items-center w-full px-4 py-3 text-left ${
                activeTab === 'appearance'
                  ? 'bg-indigo-50 text-indigo-700 border-l-4 border-indigo-700'
                  : 'hover:bg-gray-50'
              }`}
              onClick={() => setActiveTab('appearance')}
            >
              <Palette className="h-5 w-5 mr-3" />
              <span className="font-medium">Appearance</span>
            </button>

            <button
              className={`flex items-center w-full px-4 py-3 text-left ${
                activeTab === 'notifications'
                  ? 'bg-indigo-50 text-indigo-700 border-l-4 border-indigo-700'
                  : 'hover:bg-gray-50'
              }`}
              onClick={() => setActiveTab('notifications')}
            >
              <Bell className="h-5 w-5 mr-3" />
              <span className="font-medium">Notifications</span>
            </button>

            <button
              className={`flex items-center w-full px-4 py-3 text-left ${
                activeTab === 'promotion'
                  ? 'bg-indigo-50 text-indigo-700 border-l-4 border-indigo-700'
                  : 'hover:bg-gray-50'
              }`}
              onClick={() => setActiveTab('promotion')}
            >
              <GraduationCap className="h-5 w-5 mr-3" />
              <span className="font-medium">Promotion Policy</span>
            </button>

            <button
              className={`flex items-center w-full px-4 py-3 text-left ${
                activeTab === 'security'
                  ? 'bg-indigo-50 text-indigo-700 border-l-4 border-indigo-700'
                  : 'hover:bg-gray-50'
              }`}
              onClick={() => setActiveTab('security')}
            >
              <Shield className="h-5 w-5 mr-3" />
              <span className="font-medium">Security</span>
            </button>
          </div>
        </div>

        {/* Settings content */}
        <div className="flex-1 bg-white p-6 rounded-lg border border-gray-200">
          {renderTabContent()}
        </div>
      </div>
    </div>
  )
}
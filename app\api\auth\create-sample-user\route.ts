import { NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

export async function GET() {
  let prisma: PrismaClient | null = null;
  
  try {
    console.log('Creating sample admin user...')
    
    // Create a new PrismaClient instance specifically for this operation
    prisma = new PrismaClient({
      log: ['error', 'warn'],
      datasources: {
        db: {
          url: process.env.DATABASE_URL
        }
      }
    })
    
    // Connect to the database
    await prisma.$connect()
    console.log('Database connection successful')
    
    // Check if admin user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
    })

    if (existingUser) {
      console.log('Sample admin user already exists')
      return NextResponse.json({
        status: 'success',
        message: 'Sample admin user already exists',
        user: {
          email: existingUser.email,
          name: existingUser.name,
          role: existingUser.role,
          password: 'admin123' // Only showing this for development purposes
        }
      })
    }

    // Hash the password
    const hashedPassword = await bcrypt.hash('admin123', 10)

    // Create a new admin user
    const newUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Admin User',
        password: hashedPassword,
        role: 'ADMIN',
        status: 'active',
        createdAt: new Date(),
        updatedAt: new Date()
      },
    })

    console.log('Sample admin user created successfully')
    return NextResponse.json({
      status: 'success',
      message: 'Sample admin user created successfully',
      user: {
        email: newUser.email,
        name: newUser.name,
        role: newUser.role,
        password: 'admin123' // Only showing this for development purposes
      }
    })
  } catch (error) {
    console.error('Error creating sample admin user:', error)
    return NextResponse.json(
      { 
        status: 'error',
        message: 'Failed to create sample admin user',
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      },
      { status: 500 }
    )
  } finally {
    // Clean up the connection
    if (prisma) {
      await prisma.$disconnect().catch(e => console.error('Error disconnecting from database:', e))
    }
  }
}

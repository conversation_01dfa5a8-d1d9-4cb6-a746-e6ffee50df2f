import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { cookies } from 'next/headers'
import { verifyJWT } from '@/lib/jwt'
import { format, parseISO, startOfDay, endOfDay } from 'date-fns'

// GET attendance progress data
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')

    if (!startDate || !endDate) {
      return NextResponse.json(
        { error: 'startDate and endDate are required' },
        { status: 400 }
      )
    }

    // Verify authentication
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    }

    const decoded = await verifyJWT(token)
    
    // Check if user has permission to view progress
    const allowedRoles = ['SUPER_ADMIN', 'ADMIN', 'SUPERVISOR']
    if (!allowedRoles.includes(decoded.role)) {
      return NextResponse.json(
        { error: 'Forbidden - Insufficient permissions' },
        { status: 403 }
      )
    }

    const start = startOfDay(parseISO(startDate))
    const end = endOfDay(parseISO(endDate))

    // Get all classes
    const classes = await prisma.class.findMany({
      select: {
        name: true,
        _count: {
          select: {
            students: true
          }
        }
      }
    })

    // Get attendance records for the date range
    // Since date is stored as string in format 'YYYY-MM-DD', we need to use string comparison
    const attendanceRecords = await prisma.attendance.findMany({
      where: {
        date: {
          gte: startDate,
          lte: endDate
        }
      },
      select: {
        date: true,
        className: true,
        studentId: true,
        status: true,
        createdAt: true
      }
    })



    // Process data by date and class
    const progressData = []

    // Generate all dates in range
    let currentDate = new Date(start)
    while (currentDate <= end) {
      const dateStr = format(currentDate, 'yyyy-MM-dd')

      for (const classInfo of classes) {
        const className = classInfo.name
        const totalStudents = classInfo._count.students || 25 // Default for demo

        // Get attendance records for this class and date
        const classAttendance = attendanceRecords.filter(record => {
          return record.date === dateStr && record.className === className
        })

        // Count students by status (handle both uppercase and lowercase)
        const presentStudents = classAttendance.filter(record =>
          record.status?.toLowerCase() === 'present'
        ).length

        const absentStudents = classAttendance.filter(record =>
          record.status?.toLowerCase() === 'absent'
        ).length

        const permissionStudents = classAttendance.filter(record =>
          record.status?.toLowerCase() === 'permission'
        ).length

        // Calculate attendance rate (present / total recorded)
        const attendanceRate = classAttendance.length > 0 ?
          Math.round((presentStudents / classAttendance.length) * 100) : 0

        // Determine status based on real data only
        let status: 'completed' | 'partial' | 'not_started'
        const attendanceCount = classAttendance.length

        if (attendanceCount === 0) {
          status = 'not_started'
        } else if (attendanceCount < totalStudents) {
          status = 'partial'
        } else {
          status = 'completed'
        }

        // Get completion time (latest record for this class/date)
        const latestRecord = classAttendance.reduce((latest, current) =>
          !latest || current.createdAt > latest.createdAt ? current : latest
        , null as any)

        progressData.push({
          date: dateStr,
          className,
          totalStudents,
          presentStudents,
          absentStudents,
          permissionStudents,
          attendanceCount,
          attendanceRate,
          status,
          completedAt: latestRecord?.createdAt || (status !== 'not_started' ? new Date().toISOString() : null),
          completionPercentage: totalStudents > 0 ? Math.round((attendanceCount / totalStudents) * 100) : 0
        })
      }

      currentDate.setDate(currentDate.getDate() + 1)
    }

    // Calculate summary statistics
    const summary = {
      totalClasses: classes.length,
      dateRange: {
        start: startDate,
        end: endDate
      },
      overallStats: {
        completed: progressData.filter(p => p.status === 'completed').length,
        partial: progressData.filter(p => p.status === 'partial').length,
        notStarted: progressData.filter(p => p.status === 'not_started').length
      }
    }

    return NextResponse.json({
      success: true,
      data: progressData,
      summary,
      message: 'Attendance progress data retrieved successfully'
    })
  } catch (error) {
    console.error('Error fetching attendance progress:', error)
    return NextResponse.json(
      { error: 'Failed to fetch attendance progress' },
      { status: 500 }
    )
  }
}

import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { cookies } from 'next/headers'
import { verifyJWT } from '@/lib/jwt'

// POST endpoint to fix inconsistent Student IDs
export async function POST(request: Request) {
  try {
    console.log('POST /api/students/fix-sids - Starting SID fix process')
    
    // Verify authentication and authorization
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    }

    const decoded = await verifyJWT(token)

    // Only allow SUPER_ADMIN to fix SIDs
    if (decoded.role !== 'SUPER_ADMIN') {
      return NextResponse.json(
        { error: 'Forbidden - Only Super Admin can fix Student IDs' },
        { status: 403 }
      )
    }

    // Get all students with inconsistent SIDs
    const allStudents = await prisma.student.findMany({
      select: {
        id: true,
        sid: true,
        name: true,
        className: true
      },
      orderBy: {
        className: 'asc'
      }
    })

    console.log(`Found ${allStudents.length} students to check`)

    const inconsistentStudents = []
    const fixedStudents = []

    // Check each student for SID consistency
    for (const student of allStudents) {
      const classPrefix = student.className.match(/^\d+[A-Z]+/)?.[0]
      if (!classPrefix) {
        console.log(`Invalid class name format for student ${student.name}: ${student.className}`)
        continue
      }

      const sidPrefix = student.sid.match(/^\d+[A-Z]+/)?.[0]
      if (!sidPrefix) {
        console.log(`Invalid SID format for student ${student.name}: ${student.sid}`)
        continue
      }

      // Check if SID prefix matches class prefix
      if (classPrefix !== sidPrefix) {
        inconsistentStudents.push({
          id: student.id,
          name: student.name,
          currentSid: student.sid,
          currentClass: student.className,
          expectedPrefix: classPrefix
        })
      }
    }

    console.log(`Found ${inconsistentStudents.length} students with inconsistent SIDs`)

    if (inconsistentStudents.length === 0) {
      return NextResponse.json({
        message: 'All student IDs are consistent',
        totalStudents: allStudents.length,
        inconsistentCount: 0,
        fixedCount: 0
      })
    }

    // Fix inconsistent SIDs in a transaction
    const result = await prisma.$transaction(async (tx) => {
      for (const student of inconsistentStudents) {
        const newClassPrefix = student.expectedPrefix

        // Get the highest student number for this class prefix
        const existingStudents = await tx.student.findMany({
          where: {
            sid: {
              startsWith: newClassPrefix
            }
          },
          select: {
            sid: true
          },
          orderBy: {
            sid: 'desc'
          }
        })

        let nextNumber = 1
        if (existingStudents.length > 0) {
          const numbers = existingStudents
            .map(s => {
              const numberPart = s.sid.replace(newClassPrefix, '')
              return parseInt(numberPart)
            })
            .filter(num => !isNaN(num))
            .sort((a, b) => b - a)

          if (numbers.length > 0) {
            nextNumber = numbers[0] + 1
          }
        }

        const newSid = `${newClassPrefix}${nextNumber}`

        // Double-check that this SID doesn't already exist
        const existingSid = await tx.student.findUnique({
          where: { sid: newSid }
        })

        if (existingSid) {
          console.error(`Generated SID ${newSid} already exists, skipping student ${student.name}`)
          continue
        }

        // Update the student with the new SID
        await tx.student.update({
          where: { id: student.id },
          data: { sid: newSid }
        })

        fixedStudents.push({
          name: student.name,
          oldSid: student.currentSid,
          newSid: newSid,
          className: student.currentClass
        })

        console.log(`Fixed ${student.name}: ${student.currentSid} -> ${newSid}`)
      }

      return fixedStudents
    })

    console.log(`Successfully fixed ${result.length} student SIDs`)

    return NextResponse.json({
      message: 'Student IDs fixed successfully',
      totalStudents: allStudents.length,
      inconsistentCount: inconsistentStudents.length,
      fixedCount: result.length,
      fixedStudents: result
    })

  } catch (error) {
    console.error('Failed to fix student SIDs:', error)
    return NextResponse.json(
      { 
        error: 'Failed to fix student IDs', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
}

// GET endpoint to check for inconsistent SIDs without fixing them
export async function GET(request: Request) {
  try {
    console.log('GET /api/students/fix-sids - Checking for inconsistent SIDs')
    
    // Verify authentication
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value

    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    }

    const decoded = await verifyJWT(token)

    // Get all students
    const allStudents = await prisma.student.findMany({
      select: {
        id: true,
        sid: true,
        name: true,
        className: true
      },
      orderBy: {
        className: 'asc'
      }
    })

    const inconsistentStudents = []

    // Check each student for SID consistency
    for (const student of allStudents) {
      const classPrefix = student.className.match(/^\d+[A-Z]+/)?.[0]
      const sidPrefix = student.sid.match(/^\d+[A-Z]+/)?.[0]

      if (classPrefix && sidPrefix && classPrefix !== sidPrefix) {
        inconsistentStudents.push({
          name: student.name,
          currentSid: student.sid,
          currentClass: student.className,
          expectedPrefix: classPrefix,
          issue: `SID prefix "${sidPrefix}" doesn't match class prefix "${classPrefix}"`
        })
      }
    }

    return NextResponse.json({
      totalStudents: allStudents.length,
      inconsistentCount: inconsistentStudents.length,
      inconsistentStudents: inconsistentStudents,
      needsFix: inconsistentStudents.length > 0
    })

  } catch (error) {
    console.error('Failed to check student SIDs:', error)
    return NextResponse.json(
      { 
        error: 'Failed to check student IDs', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
}

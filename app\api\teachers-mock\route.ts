import { NextResponse } from 'next/server'
import { verifyJWT } from '@/lib/jwt'
import { cookies } from 'next/headers'

// GET all teachers
export async function GET() {
  try {
    console.log('Fetching all teachers')
    
    // Verify authentication
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value
    
    if (!token) {
      console.error('No authentication token found')
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    }
    
    try {
      // Verify the token
      const decoded = await verifyJWT(token)
      
      // Mock data for teachers
      const teachers = [
        { 
          id: '1', 
          name: '<PERSON>', 
          email: '<EMAIL>',
          subjects: ['Mathematics', 'Physics'],
          isHRTeacher: true,
          hrClass: '10A'
        },
        { 
          id: '2', 
          name: '<PERSON>', 
          email: '<EMAIL>',
          subjects: ['English', 'Literature'],
          isHRTeacher: true,
          hrClass: '9B'
        },
        { 
          id: '3', 
          name: '<PERSON>', 
          email: 'micha<PERSON>.<EMAIL>',
          subjects: ['Chemistry', 'Biology'],
          isHRTeacher: false
        },
        { 
          id: '4', 
          name: '<PERSON>', 
          email: '<EMAIL>',
          subjects: ['History', 'Geography'],
          isHRTeacher: true,
          hrClass: '8C'
        },
        { 
          id: '5', 
          name: 'Robert Wilson', 
          email: '<EMAIL>',
          subjects: ['Physical Education'],
          isHRTeacher: false
        }
      ]
      
      console.log(`Returning ${teachers.length} teachers`)
      return NextResponse.json(teachers)
    } catch (jwtError) {
      console.error('JWT verification error:', jwtError)
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      )
    }
  } catch (error) {
    console.error('Error fetching teachers:', error)
    return NextResponse.json(
      { error: 'Failed to fetch teachers' },
      { status: 500 }
    )
  }
}

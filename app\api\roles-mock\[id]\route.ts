import { NextResponse } from 'next/server'
import { verifyJWT } from '@/lib/jwt'
import { cookies } from 'next/headers'

// GET a single role by ID
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    console.log(`Fetching role with ID: ${params.id}`)
    
    // Verify authentication
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value
    
    if (!token) {
      console.error('No authentication token found')
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    }
    
    try {
      // Verify the token
      const decoded = await verifyJWT(token)
      
      // Mock roles data
      const roles = [
        { 
          id: 'admin', 
          name: 'Administrator', 
          description: 'Full system access', 
          permissions: [
            'View Students', 'Add Students', 'Edit Students', 'Delete Students',
            'View Classes', 'Add Classes', 'Edit Classes', 'Delete Classes',
            'View Teachers', 'Add Teachers', 'Edit Teachers', 'Delete Teachers',
            'View Marks', 'Add Marks', 'Edit Marks', 'Delete Marks',
            'View Attendance', 'Add Attendance', 'Edit Attendance',
            'Generate Reports', 'System Settings',
            'Manage Users', 'Manage Roles'
          ],
          systemDefined: true
        },
        { 
          id: 'teacher', 
          name: 'Teacher', 
          description: 'Access to classes and students', 
          permissions: [
            'View Students', 'View Classes',
            'View Marks', 'Add Marks', 'Edit Marks',
            'View Attendance', 'Add Attendance', 'Edit Attendance',
            'Generate Reports'
          ],
          systemDefined: true
        },
        { 
          id: 'hrteacher', 
          name: 'HR Teacher', 
          description: 'Manage class and attendance', 
          permissions: [
            'View Students', 'Edit Students',
            'View Classes', 'Edit Classes',
            'View Marks', 'Add Marks', 'Edit Marks',
            'View Attendance', 'Add Attendance', 'Edit Attendance',
            'Generate Reports'
          ],
          systemDefined: true
        },
        { 
          id: 'staff', 
          name: 'Staff', 
          description: 'Limited system access', 
          permissions: [
            'View Students', 'View Classes',
            'View Marks', 'View Attendance',
            'Generate Reports'
          ],
          systemDefined: true
        },
        { 
          id: 'custom-role-1', 
          name: 'Principal', 
          description: 'School principal with oversight privileges', 
          permissions: [
            'View Students', 'View Classes', 'View Teachers',
            'View Marks', 'View Attendance', 'Generate Reports',
            'System Settings'
          ],
          systemDefined: false
        }
      ]
      
      const role = roles.find(r => r.id === params.id)
      
      if (!role) {
        console.error(`Role with ID ${params.id} not found`)
        return NextResponse.json(
          { error: 'Role not found' },
          { status: 404 }
        )
      }
      
      console.log(`Found role: ${role.name}`)
      return NextResponse.json(role)
    } catch (jwtError) {
      console.error('JWT verification error:', jwtError)
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      )
    }
  } catch (error) {
    console.error('Error fetching role:', error)
    return NextResponse.json(
      { error: 'Failed to fetch role' },
      { status: 500 }
    )
  }
}

// PUT to update a role
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    console.log(`Updating role with ID: ${params.id}`)
    
    // Verify authentication
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value
    
    if (!token) {
      console.error('No authentication token found')
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    }
    
    try {
      // Verify the token
      const decoded = await verifyJWT(token)
      
      // Only allow admins to update roles
      if (decoded.role !== 'ADMIN') {
        console.error('Unauthorized role update attempt', { 
          requesterId: decoded.id, 
          requesterRole: decoded.role 
        })
        return NextResponse.json(
          { error: 'Forbidden - Only administrators can update roles' },
          { status: 403 }
        )
      }
      
      // Get the request body
      const data = await request.json()
      
      // Validate the role data
      if (!data.name || !data.description) {
        console.error('Invalid role data:', data)
        return NextResponse.json(
          { error: 'Role name and description are required' },
          { status: 400 }
        )
      }
      
      if (!Array.isArray(data.permissions) || data.permissions.length === 0) {
        console.error('Invalid permissions:', data.permissions)
        return NextResponse.json(
          { error: 'At least one permission is required' },
          { status: 400 }
        )
      }
      
      // Check if the role exists and is not system-defined
      const systemRoles = ['admin', 'teacher', 'hrteacher', 'staff']
      if (systemRoles.includes(params.id)) {
        console.error(`Cannot update system-defined role: ${params.id}`)
        return NextResponse.json(
          { error: 'Cannot update system-defined roles' },
          { status: 400 }
        )
      }
      
      // In a real implementation, we would update the role in the database
      // For now, we'll just return a success response
      const updatedRole = {
        id: params.id,
        name: data.name,
        description: data.description,
        permissions: data.permissions,
        systemDefined: false,
        updatedAt: new Date().toISOString()
      }
      
      console.log('Role updated:', updatedRole)
      return NextResponse.json({
        message: 'Role updated successfully',
        role: updatedRole
      })
    } catch (jwtError) {
      console.error('JWT verification error:', jwtError)
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      )
    }
  } catch (error) {
    console.error('Error updating role:', error)
    return NextResponse.json(
      { error: 'Failed to update role' },
      { status: 500 }
    )
  }
}

// DELETE a role
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    console.log(`Deleting role with ID: ${params.id}`)
    
    // Verify authentication
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value
    
    if (!token) {
      console.error('No authentication token found')
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      )
    }
    
    try {
      // Verify the token
      const decoded = await verifyJWT(token)
      
      // Only allow admins to delete roles
      if (decoded.role !== 'ADMIN') {
        console.error('Unauthorized role deletion attempt', { 
          requesterId: decoded.id, 
          requesterRole: decoded.role 
        })
        return NextResponse.json(
          { error: 'Forbidden - Only administrators can delete roles' },
          { status: 403 }
        )
      }
      
      // Check if the role exists and is not system-defined
      const systemRoles = ['admin', 'teacher', 'hrteacher', 'staff']
      if (systemRoles.includes(params.id)) {
        console.error(`Cannot delete system-defined role: ${params.id}`)
        return NextResponse.json(
          { error: 'Cannot delete system-defined roles' },
          { status: 400 }
        )
      }
      
      // In a real implementation, we would delete the role from the database
      // For now, we'll just return a success response
      console.log(`Role with ID ${params.id} deleted`)
      return NextResponse.json({
        message: 'Role deleted successfully'
      })
    } catch (jwtError) {
      console.error('JWT verification error:', jwtError)
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      )
    }
  } catch (error) {
    console.error('Error deleting role:', error)
    return NextResponse.json(
      { error: 'Failed to delete role' },
      { status: 500 }
    )
  }
}

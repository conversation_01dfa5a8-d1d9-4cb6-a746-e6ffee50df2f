'use client'

import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  MoreHorizontal,
  Search,
  Edit,
  Trash2,
  Loader2,
  UserCircle2,
  AlertCircle
} from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/components/ui/use-toast'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { StudentForm } from './StudentForm'
import { Card, CardContent } from '@/components/ui/card'
import { formatStudentName } from '@/lib/utils'

interface Student {
  id: string
  sid: string
  name: string
  fatherName: string
  gfName: string
  className: string
  age: number
  gender: string
  class?: {
    name: string
  }
}

export function StudentTable({ className }: { className: string }) {
  const [searchTerm, setSearchTerm] = useState('')
  const [studentToEdit, setStudentToEdit] = useState<Student | null>(null)
  const [studentToDelete, setStudentToDelete] = useState<string | null>(null)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)

  const { toast } = useToast()
  const queryClient = useQueryClient()

  // Fetch students from the database
  const { data: students, isLoading, isError, error } = useQuery({
    queryKey: ['students', className],
    queryFn: async () => {
      console.log(`Fetching students for class: ${className === 'all' ? 'All Classes' : className}`)
      const url = className === 'all'
        ? '/api/students'
        : `/api/students?class=${encodeURIComponent(className)}`

      const res = await fetch(url, {
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      })

      if (!res.ok) {
        const errorData = await res.json().catch(() => ({}))
        console.error('Failed to fetch students:', errorData)
        throw new Error(errorData.error || `Failed to fetch students: ${res.status} ${res.statusText}`)
      }

      const data = await res.json()
      console.log(`Successfully fetched ${data.length} students`)
      return data
    },
    refetchOnWindowFocus: false
  })

  // Delete student mutation
  const deleteStudentMutation = useMutation({
    mutationFn: async (id: string) => {
      console.log(`Deleting student with ID: ${id}`);

      const res = await fetch(`/api/students/${id}`, {
        method: 'DELETE',
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      })

      const responseData = await res.json();
      console.log('Delete response:', responseData);

      if (!res.ok) {
        throw new Error(responseData.error || responseData.details || 'Failed to delete student')
      }

      return responseData;
    },
    onSuccess: (data) => {
      console.log('Delete mutation succeeded:', data);

      // Invalidate queries to refresh the data
      queryClient.invalidateQueries({ queryKey: ['students'] })
      queryClient.invalidateQueries({ queryKey: ['classes'] })

      toast({
        title: 'Success',
        description: data.message || 'Student deleted successfully',
      })

      setIsDeleteDialogOpen(false)
    },
    onError: (error) => {
      console.error('Failed to delete student:', error)

      // Check if the error message contains specific information
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete student';

      if (errorMessage.includes('not found')) {
        toast({
          title: 'Student Not Found',
          description: 'The student you are trying to delete no longer exists. The list will be refreshed.',
          variant: 'destructive',
        })

        // Refresh the data since the student might have been deleted already
        queryClient.invalidateQueries({ queryKey: ['students'] })
        setIsDeleteDialogOpen(false)
      } else {
        toast({
          title: 'Error',
          description: errorMessage,
          variant: 'destructive',
        })
      }
    },
  })

  // Filter students based on search term
  const filteredStudents = students ? students.filter((student: Student) => {
    const searchString = searchTerm.toLowerCase()
    return (
      student.name.toLowerCase().includes(searchString) ||
      student.fatherName.toLowerCase().includes(searchString) ||
      student.sid.toLowerCase().includes(searchString) ||
      (student.className && student.className.toLowerCase().includes(searchString))
    )
  }) : []

  const handleEdit = (student: Student) => {
    setStudentToEdit(student)
    setIsEditDialogOpen(true)
  }

  const handleDelete = (id: string) => {
    setStudentToDelete(id)
    setIsDeleteDialogOpen(true)
  }

  const confirmDelete = () => {
    if (studentToDelete) {
      deleteStudentMutation.mutate(studentToDelete)
    }
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="flex flex-col items-center gap-2">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-sm text-muted-foreground">Loading students...</p>
        </div>
      </div>
    )
  }

  // Error state
  if (isError) {
    return (
      <Card className="border-destructive/50">
        <CardContent className="pt-6">
          <div className="flex flex-col items-center justify-center text-center p-6 gap-2">
            <AlertCircle className="h-10 w-10 text-destructive" />
            <h3 className="text-lg font-semibold">Failed to load students</h3>
            <p className="text-sm text-muted-foreground max-w-md">
              {error instanceof Error ? error.message : 'An unexpected error occurred'}
            </p>
            <Button
              variant="outline"
              className="mt-2"
              onClick={() => queryClient.invalidateQueries({ queryKey: ['students'] })}
            >
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Empty state
  if (!students || students.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center text-center p-8 gap-2">
        <UserCircle2 className="h-12 w-12 text-muted-foreground/50" />
        <h3 className="text-lg font-semibold">No students found</h3>
        <p className="text-sm text-muted-foreground max-w-md">
          {className === 'all'
            ? 'There are no students in the system yet.'
            : `There are no students in class ${className} yet.`}
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search students..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="text-sm text-muted-foreground">
          {filteredStudents.length} {filteredStudents.length === 1 ? 'student' : 'students'}
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>SID</TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Father's Name</TableHead>
              <TableHead>Class</TableHead>
              <TableHead>Age</TableHead>
              <TableHead>Gender</TableHead>
              <TableHead className="w-[80px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredStudents.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="h-24 text-center">
                  No results found.
                </TableCell>
              </TableRow>
            ) : (
              filteredStudents.map((student: Student) => (
                <TableRow key={student.id}>
                  <TableCell className="font-mono text-xs">
                    {student.sid}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center text-primary font-medium">
                        {student.name.charAt(0)}
                      </div>
                      <div className="font-medium">{formatStudentName(student.name)}</div>
                    </div>
                  </TableCell>
                  <TableCell>{formatStudentName(student.fatherName)}</TableCell>
                  <TableCell>
                    <Badge variant="outline" className="bg-primary/10">
                      {student.className || (student.class && student.class.name) || 'N/A'}
                    </Badge>
                  </TableCell>
                  <TableCell>{student.age}</TableCell>
                  <TableCell>
                    <Badge variant={student.gender === 'Male' ? 'secondary' : 'default'} className="font-normal">
                      {student.gender}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => handleEdit(student)}>
                          <Edit className="h-4 w-4 mr-2" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleDelete(student.id)}
                          className="text-destructive focus:text-destructive"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Edit Student Dialog */}
      {studentToEdit && (
        <StudentForm
          open={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
          student={studentToEdit}
        />
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the student
              and remove their data from the system.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {deleteStudentMutation.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                'Delete'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}

"use client"

import React, { useState } from 'react'
import { ResponsiveTableExample } from './ResponsiveTableExample'
import { Button } from '../ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '../ui/tabs'
import { 
  Smartphone, 
  Tablet, 
  Monitor, 
  Accessibility, 
  Zap, 
  TouchpadIcon,
  RefreshCw,
  ArrowUpDown,
  Search,
  Filter
} from 'lucide-react'

export function ResponsiveTableTestPage() {
  const [currentBreakpoint, setCurrentBreakpoint] = useState<'mobile' | 'tablet' | 'desktop'>('desktop')

  const features = [
    {
      icon: <Smartphone className="h-5 w-5" />,
      title: "Mobile-First Design",
      description: "Card-based layout optimized for touch interactions",
      details: [
        "Swipe left/right for actions",
        "Pull-to-refresh functionality", 
        "Touch-friendly pagination",
        "Collapsible card details"
      ]
    },
    {
      icon: <Tablet className="h-5 w-5" />,
      title: "Tablet Optimization",
      description: "Horizontal scrolling with enhanced touch support",
      details: [
        "Drag-to-scroll with momentum",
        "Sticky action columns",
        "Touch-pan gestures",
        "Scroll shadows for navigation"
      ]
    },
    {
      icon: <Monitor className="h-5 w-5" />,
      title: "Desktop Experience",
      description: "Full-featured table with advanced interactions",
      details: [
        "Advanced sorting & filtering",
        "Keyboard navigation",
        "Column resizing",
        "Bulk operations"
      ]
    },
    {
      icon: <Accessibility className="h-5 w-5" />,
      title: "Accessibility",
      description: "WCAG 2.1 AA compliant with screen reader support",
      details: [
        "Keyboard navigation (Arrow keys, Tab, Enter)",
        "Screen reader announcements",
        "Focus management",
        "High contrast support"
      ]
    },
    {
      icon: <Zap className="h-5 w-5" />,
      title: "Performance",
      description: "Optimized for large datasets and smooth interactions",
      details: [
        "Virtual scrolling for 1000+ rows",
        "Debounced search & filtering",
        "Memoized components",
        "Lazy loading support"
      ]
    },
    {
      icon: <TouchpadIcon className="h-5 w-5" />,
      title: "Touch Gestures",
      description: "Comprehensive touch interaction support",
      details: [
        "Swipe gestures for actions",
        "Long press for context",
        "Pull-to-refresh",
        "Momentum scrolling"
      ]
    }
  ]

  const testInstructions = {
    mobile: [
      "Swipe left on a card to edit",
      "Swipe right on a card to delete",
      "Long press a card to show/hide details",
      "Pull down from the top to refresh",
      "Tap the search bar to filter results"
    ],
    tablet: [
      "Drag horizontally to scroll the table",
      "Use two-finger scroll for vertical navigation",
      "Tap and hold to start drag scrolling",
      "Notice the scroll shadows on edges",
      "Action buttons remain sticky on the right"
    ],
    desktop: [
      "Use arrow keys to navigate between cells",
      "Press Enter to activate a row",
      "Press Space to select a row",
      "Use Ctrl+Home/End for quick navigation",
      "Click column headers to sort"
    ]
  }

  const breakpointClasses = {
    mobile: 'max-w-sm mx-auto',
    tablet: 'max-w-4xl mx-auto',
    desktop: 'max-w-full'
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold text-gray-900">
            Mobile-First Responsive Table
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            A comprehensive table component that adapts to any screen size with 
            touch-first interactions, accessibility features, and performance optimizations.
          </p>
        </div>

        {/* Breakpoint Simulator */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Monitor className="h-5 w-5" />
              Breakpoint Simulator
            </CardTitle>
            <CardDescription>
              Test the responsive behavior across different screen sizes
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex gap-2 mb-4">
              <Button
                variant={currentBreakpoint === 'mobile' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setCurrentBreakpoint('mobile')}
                className="flex items-center gap-2"
              >
                <Smartphone className="h-4 w-4" />
                Mobile
              </Button>
              <Button
                variant={currentBreakpoint === 'tablet' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setCurrentBreakpoint('tablet')}
                className="flex items-center gap-2"
              >
                <Tablet className="h-4 w-4" />
                Tablet
              </Button>
              <Button
                variant={currentBreakpoint === 'desktop' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setCurrentBreakpoint('desktop')}
                className="flex items-center gap-2"
              >
                <Monitor className="h-4 w-4" />
                Desktop
              </Button>
            </div>

            <div className={`border rounded-lg bg-white transition-all duration-300 ${breakpointClasses[currentBreakpoint]}`}>
              <div className="p-4">
                <ResponsiveTableExample />
              </div>
            </div>

            <div className="mt-4 p-4 bg-blue-50 rounded-lg">
              <h4 className="font-semibold text-blue-900 mb-2">
                Test Instructions for {currentBreakpoint.charAt(0).toUpperCase() + currentBreakpoint.slice(1)}:
              </h4>
              <ul className="space-y-1 text-blue-800">
                {testInstructions[currentBreakpoint].map((instruction, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <span className="text-blue-600 mt-1">•</span>
                    {instruction}
                  </li>
                ))}
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {features.map((feature, index) => (
            <Card key={index} className="h-full">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  {feature.icon}
                  {feature.title}
                </CardTitle>
                <CardDescription>{feature.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {feature.details.map((detail, detailIndex) => (
                    <li key={detailIndex} className="flex items-start gap-2 text-sm">
                      <span className="text-green-600 mt-1">✓</span>
                      {detail}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Implementation Guide */}
        <Card>
          <CardHeader>
            <CardTitle>Implementation Guide</CardTitle>
            <CardDescription>
              How to integrate the responsive table into your application
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="basic" className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="basic">Basic Usage</TabsTrigger>
                <TabsTrigger value="advanced">Advanced Features</TabsTrigger>
                <TabsTrigger value="customization">Customization</TabsTrigger>
                <TabsTrigger value="accessibility">Accessibility</TabsTrigger>
              </TabsList>

              <TabsContent value="basic" className="space-y-4">
                <div className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto">
                  <pre className="text-sm">
{`import { ResponsiveTable } from '@/components/ui/responsive-table'

const columns = [
  {
    id: 'name',
    header: 'Name',
    accessorKey: 'name',
    priority: 'high'
  },
  {
    id: 'email', 
    header: 'Email',
    accessorKey: 'email',
    priority: 'medium'
  }
]

<ResponsiveTable
  data={data}
  columns={columns}
  searchable={true}
  sortable={true}
  pagination={true}
/>`}
                  </pre>
                </div>
              </TabsContent>

              <TabsContent value="advanced" className="space-y-4">
                <div className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto">
                  <pre className="text-sm">
{`<ResponsiveTable
  data={data}
  columns={columns}
  virtualScrolling={true}
  virtualRowHeight={60}
  infiniteScroll={true}
  onLoadMore={loadMoreData}
  enableSwipeActions={true}
  enablePullToRefresh={true}
  onRowSwipeLeft={handleEdit}
  onRowSwipeRight={handleDelete}
  onRefresh={handleRefresh}
/>`}
                  </pre>
                </div>
              </TabsContent>

              <TabsContent value="customization" className="space-y-4">
                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold mb-2">Column Priorities</h4>
                    <div className="flex gap-2 flex-wrap">
                      <Badge variant="default">high - Always visible</Badge>
                      <Badge variant="secondary">medium - Hidden on mobile</Badge>
                      <Badge variant="outline">low - Desktop only</Badge>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold mb-2">Breakpoints</h4>
                    <ul className="space-y-1 text-sm">
                      <li>• Mobile: &lt; 768px (Card view)</li>
                      <li>• Tablet: 768px - 1024px (Horizontal scroll)</li>
                      <li>• Desktop: &gt; 1024px (Full table)</li>
                    </ul>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="accessibility" className="space-y-4">
                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold mb-2">Keyboard Navigation</h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <strong>Arrow Keys:</strong> Navigate cells<br/>
                        <strong>Tab:</strong> Move between interactive elements<br/>
                        <strong>Enter:</strong> Activate row<br/>
                        <strong>Space:</strong> Select row
                      </div>
                      <div>
                        <strong>Home/End:</strong> First/last column<br/>
                        <strong>Ctrl+Home/End:</strong> First/last cell<br/>
                        <strong>Page Up/Down:</strong> Jump 10 rows<br/>
                        <strong>Escape:</strong> Clear focus
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold mb-2">Screen Reader Support</h4>
                    <ul className="space-y-1 text-sm">
                      <li>• ARIA labels and descriptions</li>
                      <li>• Live region announcements</li>
                      <li>• Proper table semantics</li>
                      <li>• Sort state announcements</li>
                    </ul>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

"use client"

import { useEffect, useRef, useCallback, useState } from 'react'

// Keyboard navigation hook for tables
export function useKeyboardNavigation<T>(
  data: T[],
  columns: Array<{ id: string }>,
  onRowSelect?: (row: T, index: number) => void,
  onRowActivate?: (row: T, index: number) => void
) {
  const [focusedRowIndex, setFocusedRowIndex] = useState(-1)
  const [focusedColumnIndex, setFocusedColumnIndex] = useState(0)
  const tableRef = useRef<HTMLTableElement>(null)
  const cellRefs = useRef<Map<string, HTMLElement>>(new Map())

  const getCellKey = (rowIndex: number, columnIndex: number) => 
    `${rowIndex}-${columnIndex}`

  const setCellRef = useCallback((rowIndex: number, columnIndex: number, element: HTMLElement | null) => {
    const key = getCellKey(rowIndex, columnIndex)
    if (element) {
      cellRefs.current.set(key, element)
    } else {
      cellRefs.current.delete(key)
    }
  }, [])

  const focusCell = useCallback((rowIndex: number, columnIndex: number) => {
    const key = getCellKey(rowIndex, columnIndex)
    const cell = cellRefs.current.get(key)
    if (cell) {
      cell.focus()
      setFocusedRowIndex(rowIndex)
      setFocusedColumnIndex(columnIndex)
    }
  }, [])

  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    if (!tableRef.current?.contains(e.target as Node)) return

    const maxRowIndex = data.length - 1
    const maxColumnIndex = columns.length - 1

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        if (focusedRowIndex < maxRowIndex) {
          focusCell(focusedRowIndex + 1, focusedColumnIndex)
        }
        break

      case 'ArrowUp':
        e.preventDefault()
        if (focusedRowIndex > 0) {
          focusCell(focusedRowIndex - 1, focusedColumnIndex)
        }
        break

      case 'ArrowRight':
        e.preventDefault()
        if (focusedColumnIndex < maxColumnIndex) {
          focusCell(focusedRowIndex, focusedColumnIndex + 1)
        }
        break

      case 'ArrowLeft':
        e.preventDefault()
        if (focusedColumnIndex > 0) {
          focusCell(focusedRowIndex, focusedColumnIndex - 1)
        }
        break

      case 'Home':
        e.preventDefault()
        if (e.ctrlKey) {
          focusCell(0, 0)
        } else {
          focusCell(focusedRowIndex, 0)
        }
        break

      case 'End':
        e.preventDefault()
        if (e.ctrlKey) {
          focusCell(maxRowIndex, maxColumnIndex)
        } else {
          focusCell(focusedRowIndex, maxColumnIndex)
        }
        break

      case 'PageDown':
        e.preventDefault()
        const nextRow = Math.min(focusedRowIndex + 10, maxRowIndex)
        focusCell(nextRow, focusedColumnIndex)
        break

      case 'PageUp':
        e.preventDefault()
        const prevRow = Math.max(focusedRowIndex - 10, 0)
        focusCell(prevRow, focusedColumnIndex)
        break

      case 'Enter':
      case ' ':
        e.preventDefault()
        if (focusedRowIndex >= 0 && focusedRowIndex < data.length) {
          if (e.key === 'Enter' && onRowActivate) {
            onRowActivate(data[focusedRowIndex], focusedRowIndex)
          } else if (e.key === ' ' && onRowSelect) {
            onRowSelect(data[focusedRowIndex], focusedRowIndex)
          }
        }
        break

      case 'Escape':
        e.preventDefault()
        setFocusedRowIndex(-1)
        setFocusedColumnIndex(0)
        break
    }
  }, [data, columns, focusedRowIndex, focusedColumnIndex, focusCell, onRowSelect, onRowActivate])

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [handleKeyDown])

  return {
    tableRef,
    focusedRowIndex,
    focusedColumnIndex,
    setCellRef,
    focusCell
  }
}

// Screen reader announcements
export function useScreenReader() {
  const announcementRef = useRef<HTMLDivElement>(null)

  const announce = useCallback((message: string, priority: 'polite' | 'assertive' = 'polite') => {
    if (!announcementRef.current) return

    // Clear previous announcement
    announcementRef.current.textContent = ''
    announcementRef.current.setAttribute('aria-live', priority)

    // Add new announcement after a brief delay to ensure it's read
    setTimeout(() => {
      if (announcementRef.current) {
        announcementRef.current.textContent = message
      }
    }, 100)
  }, [])

  const AnnouncementRegion = useCallback(() => (
    <div
      ref={announcementRef}
      aria-live="polite"
      aria-atomic="true"
      className="sr-only"
    />
  ), [])

  return {
    announce,
    AnnouncementRegion
  }
}

// Focus management for modals and overlays
export function useFocusManagement(isOpen: boolean) {
  const containerRef = useRef<HTMLElement>(null)
  const previousFocusRef = useRef<HTMLElement | null>(null)

  useEffect(() => {
    if (!isOpen) return

    // Store the currently focused element
    previousFocusRef.current = document.activeElement as HTMLElement

    // Focus the container or first focusable element
    const container = containerRef.current
    if (container) {
      const focusableElements = container.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      )
      
      if (focusableElements.length > 0) {
        (focusableElements[0] as HTMLElement).focus()
      } else {
        container.focus()
      }
    }

    // Trap focus within the container
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key !== 'Tab' || !container) return

      const focusableElements = Array.from(
        container.querySelectorAll(
          'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        )
      ) as HTMLElement[]

      if (focusableElements.length === 0) return

      const firstElement = focusableElements[0]
      const lastElement = focusableElements[focusableElements.length - 1]

      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          e.preventDefault()
          lastElement.focus()
        }
      } else {
        if (document.activeElement === lastElement) {
          e.preventDefault()
          firstElement.focus()
        }
      }
    }

    document.addEventListener('keydown', handleKeyDown)

    return () => {
      document.removeEventListener('keydown', handleKeyDown)
      
      // Restore focus to the previously focused element
      if (previousFocusRef.current) {
        previousFocusRef.current.focus()
      }
    }
  }, [isOpen])

  return containerRef
}

// Skip links for better navigation
export function useSkipLinks() {
  const skipLinksRef = useRef<HTMLDivElement>(null)

  const SkipLinks = useCallback(({ links }: { 
    links: Array<{ href: string; label: string }> 
  }) => (
    <div ref={skipLinksRef} className="sr-only focus-within:not-sr-only">
      <div className="fixed top-0 left-0 z-50 bg-white border border-gray-300 p-2 space-x-2">
        {links.map((link, index) => (
          <a
            key={index}
            href={link.href}
            className="inline-block px-3 py-1 bg-blue-600 text-white rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {link.label}
          </a>
        ))}
      </div>
    </div>
  ), [])

  return { SkipLinks }
}

// Reduced motion preferences
export function useReducedMotion() {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false)

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
    setPrefersReducedMotion(mediaQuery.matches)

    const handleChange = (e: MediaQueryListEvent) => {
      setPrefersReducedMotion(e.matches)
    }

    mediaQuery.addEventListener('change', handleChange)
    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [])

  return prefersReducedMotion
}

// High contrast mode detection
export function useHighContrast() {
  const [prefersHighContrast, setPrefersHighContrast] = useState(false)

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-contrast: high)')
    setPrefersHighContrast(mediaQuery.matches)

    const handleChange = (e: MediaQueryListEvent) => {
      setPrefersHighContrast(e.matches)
    }

    mediaQuery.addEventListener('change', handleChange)
    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [])

  return prefersHighContrast
}

// ARIA attributes helper
export function useAriaAttributes() {
  const generateId = useCallback((prefix: string = 'element') => {
    return `${prefix}-${Math.random().toString(36).substr(2, 9)}`
  }, [])

  const getTableAriaProps = useCallback((
    rowCount: number,
    columnCount: number,
    sortColumn?: string,
    sortDirection?: 'asc' | 'desc'
  ) => ({
    role: 'table',
    'aria-rowcount': rowCount + 1, // +1 for header
    'aria-colcount': columnCount,
    ...(sortColumn && {
      'aria-sort': sortDirection === 'asc' ? 'ascending' : 'descending'
    })
  }), [])

  const getRowAriaProps = useCallback((
    rowIndex: number,
    isSelected?: boolean,
    isExpanded?: boolean
  ) => ({
    role: 'row',
    'aria-rowindex': rowIndex + 2, // +2 because aria-rowindex is 1-based and includes header
    ...(isSelected !== undefined && { 'aria-selected': isSelected }),
    ...(isExpanded !== undefined && { 'aria-expanded': isExpanded })
  }), [])

  const getCellAriaProps = useCallback((
    columnIndex: number,
    isHeader: boolean = false
  ) => ({
    role: isHeader ? 'columnheader' : 'cell',
    'aria-colindex': columnIndex + 1 // 1-based
  }), [])

  return {
    generateId,
    getTableAriaProps,
    getRowAriaProps,
    getCellAriaProps
  }
}

import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const classId = searchParams.get('classId')
    const className = searchParams.get('className')

    if (!classId && !className) {
      return NextResponse.json(
        { error: 'Either classId or className is required' },
        { status: 400 }
      )
    }

    console.log(`Fetching subjects for class: ${classId || className}`)

    let whereClause = {}

    // Filter by classId if provided
    if (classId) {
      whereClause = { classId }
    }
    // Filter by className if provided
    else if (className) {
      // First find the class by name
      const classRecord = await prisma.class.findFirst({
        where: { name: className },
        select: { id: true }
      })

      if (classRecord) {
        whereClause = { classId: classRecord.id }
      } else {
        console.log(`Class ${className} not found in database`)
        return NextResponse.json(
          { error: `Class '${className}' not found` },
          { status: 404 }
        )
      }
    }

    const subjects = await prisma.subject.findMany({
      where: whereClause,
      select: {
        id: true,
        name: true,
        classId: true,
        class: {
          select: {
            id: true,
            name: true
          }
        },
        createdAt: true,
        updatedAt: true
      },
      orderBy: {
        name: 'asc'
      }
    })

    console.log(`Found ${subjects.length} subjects for class`)

    // Validate that subjects belong to the correct class
    const validatedSubjects = subjects.map(subject => ({
      id: subject.id,
      name: subject.name,
      classId: subject.classId,
      className: subject.class.name,
      isValidForClass: true, // All subjects from query are valid
      createdAt: subject.createdAt,
      updatedAt: subject.updatedAt
    }))

    return NextResponse.json(validatedSubjects)
  } catch (error) {
    console.error('Error fetching subjects for class:', error)
    return NextResponse.json(
      { error: 'Failed to fetch subjects for class' },
      { status: 500 }
    )
  }
}

// Validate subject-class relationship
export async function POST(request: Request) {
  try {
    const { classId, className, subjectId, subjectName } = await request.json()

    if ((!classId && !className) || (!subjectId && !subjectName)) {
      return NextResponse.json(
        { error: 'Class and subject identifiers are required' },
        { status: 400 }
      )
    }

    console.log('Validating subject-class relationship:', { classId, className, subjectId, subjectName })

    // Find the class
    let classRecord
    if (classId) {
      classRecord = await prisma.class.findUnique({
        where: { id: classId },
        select: { id: true, name: true }
      })
    } else {
      classRecord = await prisma.class.findFirst({
        where: { name: className },
        select: { id: true, name: true }
      })
    }

    if (!classRecord) {
      return NextResponse.json(
        { error: 'Class not found', isValid: false },
        { status: 404 }
      )
    }

    // Find the subject and validate it belongs to the class
    let subjectRecord
    if (subjectId) {
      subjectRecord = await prisma.subject.findFirst({
        where: {
          id: subjectId,
          classId: classRecord.id
        },
        select: { id: true, name: true, classId: true }
      })
    } else {
      subjectRecord = await prisma.subject.findFirst({
        where: {
          name: subjectName,
          classId: classRecord.id
        },
        select: { id: true, name: true, classId: true }
      })
    }

    const isValid = !!subjectRecord

    if (!isValid) {
      return NextResponse.json({
        isValid: false,
        error: `Subject '${subjectName || subjectId}' is not assigned to class '${classRecord.name}'`,
        availableSubjects: await prisma.subject.findMany({
          where: { classId: classRecord.id },
          select: { id: true, name: true }
        })
      })
    }

    return NextResponse.json({
      isValid: true,
      class: classRecord,
      subject: subjectRecord,
      message: 'Subject-class relationship is valid'
    })
  } catch (error) {
    console.error('Error validating subject-class relationship:', error)
    return NextResponse.json(
      { error: 'Failed to validate subject-class relationship', isValid: false },
      { status: 500 }
    )
  }
}

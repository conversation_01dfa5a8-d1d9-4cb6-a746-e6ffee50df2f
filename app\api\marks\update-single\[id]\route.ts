import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const markId = params.id
    const body = await request.json()
    const {
      studentId,
      className,
      subject,
      term,
      academicYear,
      marks,
      totalMarks,
      remarks
    } = body

    // Validate required fields
    if (!studentId || !className || !subject || !term || !academicYear) {
      return NextResponse.json(
        { error: 'Student ID, class name, subject, term, and academic year are required' },
        { status: 400 }
      )
    }

    if (typeof marks !== 'number' || typeof totalMarks !== 'number') {
      return NextResponse.json(
        { error: 'Marks and total marks must be numbers' },
        { status: 400 }
      )
    }

    if (marks < 0 || marks > totalMarks) {
      return NextResponse.json(
        { error: `Invalid marks: ${marks}. Must be between 0 and ${totalMarks}` },
        { status: 400 }
      )
    }

    console.log(`Updating mark ${markId} for student ${studentId}`)

    // Check if mark exists
    const existingMark = await prisma.mark.findUnique({
      where: { id: markId },
      include: {
        student: {
          select: {
            id: true,
            name: true,
            sid: true,
            className: true,
          },
        },
      },
    })

    if (!existingMark) {
      return NextResponse.json(
        { error: 'Mark not found' },
        { status: 404 }
      )
    }

    // Check if student exists
    const student = await prisma.student.findUnique({
      where: { id: studentId },
      select: { id: true, name: true, sid: true, className: true }
    })

    if (!student) {
      return NextResponse.json(
        { error: 'Student not found' },
        { status: 404 }
      )
    }

    // Check for duplicate marks if student/subject/term combination is being changed
    if (
      existingMark.studentId !== studentId ||
      existingMark.className !== className ||
      existingMark.subject !== subject ||
      existingMark.term !== term ||
      existingMark.academicYear !== academicYear
    ) {
      const duplicateMark = await prisma.mark.findFirst({
        where: {
          studentId: studentId,
          className: className,
          subject: subject,
          term: term,
          academicYear: academicYear,
          id: { not: markId }, // Exclude current mark
        }
      })

      if (duplicateMark) {
        return NextResponse.json(
          { error: `A mark already exists for ${student.name} in ${subject} for ${term} ${academicYear}` },
          { status: 409 }
        )
      }
    }

    // Update the mark
    const updatedMark = await prisma.mark.update({
      where: { id: markId },
      data: {
        studentId: studentId,
        className: className,
        subject: subject,
        term: term,
        academicYear: academicYear,
        marks: marks,
        totalMarks: totalMarks,
        remarks: remarks || '',
        updatedAt: new Date(),
      },
      include: {
        student: {
          select: {
            id: true,
            name: true,
            sid: true,
            className: true,
          },
        },
      },
    })

    console.log(`Successfully updated mark with ID: ${updatedMark.id}`)

    // Return the updated mark with formatted data
    const formattedMark = {
      id: updatedMark.id,
      studentId: updatedMark.studentId,
      studentName: updatedMark.student.name,
      studentSid: updatedMark.student.sid,
      className: updatedMark.className,
      subject: updatedMark.subject,
      term: updatedMark.term,
      academicYear: updatedMark.academicYear,
      marks: updatedMark.marks,
      totalMarks: updatedMark.totalMarks,
      remarks: updatedMark.remarks,
      createdAt: updatedMark.createdAt,
      updatedAt: updatedMark.updatedAt,
    }

    return NextResponse.json({
      message: 'Mark updated successfully',
      mark: formattedMark,
    })
  } catch (error) {
    console.error('Error updating mark:', error)
    
    // Handle specific Prisma errors
    if (error instanceof Error) {
      if (error.message.includes('Record to update not found')) {
        return NextResponse.json(
          { error: 'Mark not found. It may have been deleted.' },
          { status: 404 }
        )
      }
      if (error.message.includes('Unique constraint failed')) {
        return NextResponse.json(
          { error: 'A mark already exists for this student in this subject and term' },
          { status: 409 }
        )
      }
      if (error.message.includes('Foreign key constraint failed')) {
        return NextResponse.json(
          { error: 'Invalid student reference. Please check the student ID.' },
          { status: 400 }
        )
      }
    }

    return NextResponse.json(
      { error: 'Failed to update mark' },
      { status: 500 }
    )
  }
}

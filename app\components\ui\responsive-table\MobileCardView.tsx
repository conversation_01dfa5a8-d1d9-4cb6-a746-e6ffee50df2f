"use client"

import React, { useState, useMemo } from 'react'
import { Search, Filter, RefreshCw, Edit, Trash2, MoreVertical } from 'lucide-react'
import { Button } from '../button'
import { Input } from '../input'
import { Card, CardContent } from '../card'
import { Badge } from '../badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../dropdown-menu'
import { useTouchGestures } from '../../../hooks/useTouchGestures'
import { cn } from '../../../lib/utils'
import { ResponsiveTableColumn } from './index'

interface MobileCardViewProps<T = any> {
  data: T[]
  columns: ResponsiveTableColumn<T>[]
  isLoading: boolean
  onRowClick?: (row: T) => void
  onRowEdit?: (row: T) => void
  onRowDelete?: (row: T) => void
  onRowSwipeLeft?: (row: T) => void
  onRowSwipeRight?: (row: T) => void
  searchable: boolean
  filterable: boolean
  emptyMessage: string
  loadingMessage: string
  onRefresh?: () => void
  getRowKey: (row: T) => string
  pageSize: number
  enableSwipeActions: boolean
  enablePullToRefresh: boolean
  virtualScrolling: boolean
  virtualRowHeight: number
  virtualContainerHeight: number
  infiniteScroll: boolean
  onLoadMore?: () => Promise<void>
  hasMore: boolean
}

export function MobileCardView<T = any>({
  data,
  columns,
  isLoading,
  onRowClick,
  onRowEdit,
  onRowDelete,
  onRowSwipeLeft,
  onRowSwipeRight,
  searchable,
  filterable,
  emptyMessage,
  loadingMessage,
  onRefresh,
  getRowKey,
  pageSize,
  enableSwipeActions,
  enablePullToRefresh,
  virtualScrolling,
  virtualRowHeight,
  virtualContainerHeight,
  infiniteScroll,
  onLoadMore,
  hasMore
}: MobileCardViewProps<T>) {
  const [searchTerm, setSearchTerm] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [isRefreshing, setIsRefreshing] = useState(false)

  // Filter data based on search term
  const filteredData = useMemo(() => {
    if (!searchTerm) return data

    return data.filter(row => {
      return columns.some(column => {
        if (!column.accessorKey) return false
        const value = row[column.accessorKey]
        return String(value).toLowerCase().includes(searchTerm.toLowerCase())
      })
    })
  }, [data, searchTerm, columns])

  // Paginate data
  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize
    return filteredData.slice(startIndex, startIndex + pageSize)
  }, [filteredData, currentPage, pageSize])

  const totalPages = Math.ceil(filteredData.length / pageSize)

  // Handle pull to refresh
  const handleRefresh = async () => {
    if (!onRefresh) return

    setIsRefreshing(true)
    try {
      await onRefresh()
    } finally {
      setIsRefreshing(false)
    }
  }

  const { elementRef, isPulling, pullDistance } = useTouchGestures({
    onPullToRefresh: enablePullToRefresh ? handleRefresh : undefined,
    pullThreshold: 80
  })

  // Get primary columns for card display (high priority first)
  const primaryColumns = columns.filter(col => col.priority === 'high').slice(0, 3)
  const secondaryColumns = columns.filter(col => col.priority !== 'high')

  if (isLoading) {
    return (
      <div className="space-y-4 p-4">
        <div className="text-center py-8">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2 text-gray-400" />
          <p className="text-gray-500">{loadingMessage}</p>
        </div>
      </div>
    )
  }

  return (
    <div 
      ref={elementRef}
      className="mobile-card-view space-y-4 p-4 relative"
      style={{
        transform: isPulling ? `translateY(${Math.min(pullDistance, 80)}px)` : undefined,
        transition: isPulling ? 'none' : 'transform 0.3s ease-out'
      }}
    >
      {/* Pull to refresh indicator */}
      {isPulling && (
        <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-full">
          <div className="bg-white rounded-full p-2 shadow-lg border">
            <RefreshCw className={cn(
              "h-4 w-4 text-gray-600",
              pullDistance > 80 && "animate-spin"
            )} />
          </div>
        </div>
      )}

      {/* Search and filters */}
      {searchable && (
        <div className="sticky top-0 z-10 bg-white pb-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 h-12 text-base"
            />
          </div>
        </div>
      )}

      {/* Data cards */}
      <div className="space-y-3">
        {paginatedData.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-2">
              <Search className="h-12 w-12 mx-auto" />
            </div>
            <p className="text-gray-500">{emptyMessage}</p>
          </div>
        ) : (
          paginatedData.map((row) => (
            <MobileCard
              key={getRowKey(row)}
              row={row}
              primaryColumns={primaryColumns}
              secondaryColumns={secondaryColumns}
              onRowClick={onRowClick}
              onRowEdit={onRowEdit}
              onRowDelete={onRowDelete}
              onRowSwipeLeft={onRowSwipeLeft}
              onRowSwipeRight={onRowSwipeRight}
              enableSwipeActions={enableSwipeActions}
            />
          ))
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-between items-center pt-4 border-t">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
            disabled={currentPage === 1}
          >
            Previous
          </Button>
          
          <span className="text-sm text-gray-600">
            Page {currentPage} of {totalPages}
          </span>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}
            disabled={currentPage === totalPages}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  )
}

interface MobileCardProps<T = any> {
  row: T
  primaryColumns: ResponsiveTableColumn<T>[]
  secondaryColumns: ResponsiveTableColumn<T>[]
  onRowClick?: (row: T) => void
  onRowEdit?: (row: T) => void
  onRowDelete?: (row: T) => void
  onRowSwipeLeft?: (row: T) => void
  onRowSwipeRight?: (row: T) => void
  enableSwipeActions: boolean
}

function MobileCard<T = any>({
  row,
  primaryColumns,
  secondaryColumns,
  onRowClick,
  onRowEdit,
  onRowDelete,
  onRowSwipeLeft,
  onRowSwipeRight,
  enableSwipeActions
}: MobileCardProps<T>) {
  const [showDetails, setShowDetails] = useState(false)

  const { elementRef } = useTouchGestures({
    onSwipeLeft: enableSwipeActions ? (onRowSwipeLeft || onRowEdit) ? () => (onRowSwipeLeft || onRowEdit)?.(row) : undefined : undefined,
    onSwipeRight: enableSwipeActions ? (onRowSwipeRight || onRowDelete) ? () => (onRowSwipeRight || onRowDelete)?.(row) : undefined : undefined,
    onLongPress: () => setShowDetails(!showDetails)
  })

  const renderCellValue = (column: ResponsiveTableColumn<T>) => {
    if (column.cell) {
      return column.cell(row)
    }
    if (column.accessorKey) {
      const value = row[column.accessorKey]
      return value !== null && value !== undefined ? String(value) : '-'
    }
    return '-'
  }

  return (
    <Card 
      ref={elementRef}
      className={cn(
        "mobile-card transition-all duration-200 hover:shadow-md",
        onRowClick && "cursor-pointer"
      )}
      onClick={() => onRowClick?.(row)}
    >
      <CardContent className="p-4">
        {/* Primary information */}
        <div className="space-y-2 mb-3">
          {primaryColumns.map((column) => (
            <div key={column.id} className="flex justify-between items-start">
              <span className="text-sm font-medium text-gray-900">
                {renderCellValue(column)}
              </span>
              {column.id === primaryColumns[0]?.id && (onRowEdit || onRowDelete) && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    {onRowEdit && (
                      <DropdownMenuItem onClick={(e) => {
                        e.stopPropagation()
                        onRowEdit(row)
                      }}>
                        <Edit className="h-4 w-4 mr-2" />
                        Edit
                      </DropdownMenuItem>
                    )}
                    {onRowDelete && (
                      <DropdownMenuItem 
                        onClick={(e) => {
                          e.stopPropagation()
                          onRowDelete(row)
                        }}
                        className="text-red-600"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
            </div>
          ))}
        </div>

        {/* Secondary information (collapsible) */}
        {secondaryColumns.length > 0 && (
          <div className="border-t pt-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation()
                setShowDetails(!showDetails)
              }}
              className="text-xs text-gray-500 p-0 h-auto"
            >
              {showDetails ? 'Show less' : 'Show more'}
            </Button>
            
            {showDetails && (
              <div className="mt-2 space-y-1">
                {secondaryColumns.map((column) => (
                  <div key={column.id} className="flex justify-between text-xs">
                    <span className="text-gray-500">
                      {column.mobileLabel || column.header}:
                    </span>
                    <span className="text-gray-700 text-right max-w-[60%] truncate">
                      {renderCellValue(column)}
                    </span>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

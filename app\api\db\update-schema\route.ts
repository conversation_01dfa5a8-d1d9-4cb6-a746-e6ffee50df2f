import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

/**
 * This API route updates the database schema to support the new role system
 * It will:
 * 1. Modify the User table to ensure role field accepts only the specified roles
 * 2. Update existing users to use the new roles
 */
export async function GET() {
  try {
    console.log('Starting database schema update process')
    
    // Step 1: Define the required roles
    const validRoles = [
      'SUPER_ADMIN',
      'ADMIN',
      'SUPERVISOR',
      'ACCOUNTANT',
      'TEACHER'
    ]
    
    // Step 2: Get all users
    const users = await prisma.user.findMany()
    console.log(`Found ${users.length} users`)
    
    // Step 3: Map old roles to new roles
    const roleMapping = {
      'ADMIN': 'SUPER_ADMIN', // Map existing ADMIN to SUPER_ADMIN
      'USER': 'TEACHER',      // Map existing USER to TEACHER
      'STAFF': 'ACCOUNTANT',  // Map existing STAFF to ACCOUNTANT
      'HR_TEACHER': 'TEACHER', // Map existing HR_TEACHER to TEACHER
      'SUBJECT_TEACHER': 'TEACHER' // Map existing SUBJECT_TEACHER to TEACHER
    }
    
    // Step 4: Update users with invalid roles
    const updatedUsers = []
    for (const user of users) {
      const currentRole = user.role
      
      // Check if current role is valid
      if (!validRoles.includes(currentRole)) {
        // Map to new role or default to TEACHER
        const newRole = roleMapping[currentRole] || 'TEACHER'
        
        const updatedUser = await prisma.user.update({
          where: { id: user.id },
          data: { role: newRole }
        })
        
        updatedUsers.push({
          id: updatedUser.id,
          email: updatedUser.email,
          oldRole: currentRole,
          newRole: updatedUser.role
        })
        
        console.log(`Updated user ${user.email} role from ${currentRole} to ${newRole}`)
      }
    }
    
    // Step 5: Update the roles table
    // First, delete all existing roles
    await prisma.rolePermission.deleteMany()
    await prisma.role.deleteMany()
    
    console.log('Deleted all existing roles and permissions')
    
    // Create the new roles
    const roleDescriptions = {
      'SUPER_ADMIN': 'Full system access with all permissions',
      'ADMIN': 'Administrative access with most permissions',
      'SUPERVISOR': 'Supervisory access with oversight permissions',
      'ACCOUNTANT': 'Access to financial and accounting features',
      'TEACHER': 'Access to teaching and classroom management features'
    }
    
    const createdRoles = []
    for (const roleName of validRoles) {
      const newRole = await prisma.role.create({
        data: {
          name: roleName,
          description: roleDescriptions[roleName],
          systemDefined: true
        }
      })
      createdRoles.push(newRole)
      console.log(`Created role: ${newRole.name}`)
    }
    
    // Step 6: Create basic permissions
    const permissionCategories = {
      students: [
        { name: 'View Students', description: 'View student records' },
        { name: 'Add Students', description: 'Add new students' },
        { name: 'Edit Students', description: 'Edit student records' },
        { name: 'Delete Students', description: 'Delete student records' }
      ],
      classes: [
        { name: 'View Classes', description: 'View class records' },
        { name: 'Add Classes', description: 'Add new classes' },
        { name: 'Edit Classes', description: 'Edit class records' },
        { name: 'Delete Classes', description: 'Delete class records' }
      ],
      teachers: [
        { name: 'View Teachers', description: 'View teacher records' },
        { name: 'Add Teachers', description: 'Add new teachers' },
        { name: 'Edit Teachers', description: 'Edit teacher records' },
        { name: 'Delete Teachers', description: 'Delete teacher records' }
      ],
      marks: [
        { name: 'View Marks', description: 'View mark records' },
        { name: 'Add Marks', description: 'Add new marks' },
        { name: 'Edit Marks', description: 'Edit mark records' },
        { name: 'Delete Marks', description: 'Delete mark records' }
      ],
      attendance: [
        { name: 'View Attendance', description: 'View attendance records' },
        { name: 'Add Attendance', description: 'Add attendance records' },
        { name: 'Edit Attendance', description: 'Edit attendance records' }
      ],
      accounting: [
        { name: 'View Payments', description: 'View payment records' },
        { name: 'Add Payments', description: 'Add payment records' },
        { name: 'Edit Payments', description: 'Edit payment records' },
        { name: 'Delete Payments', description: 'Delete payment records' },
        { name: 'Manage Vouchers', description: 'Manage payment vouchers' }
      ],
      reports: [
        { name: 'Generate Reports', description: 'Generate reports' },
        { name: 'View Reports', description: 'View generated reports' },
        { name: 'Export Reports', description: 'Export reports to PDF/Excel' }
      ],
      system: [
        { name: 'System Settings', description: 'Manage system settings' },
        { name: 'Manage Users', description: 'Manage user accounts' },
        { name: 'Manage Roles', description: 'Manage roles and permissions' }
      ]
    }
    
    // Create permissions
    const createdPermissions = {}
    for (const category in permissionCategories) {
      for (const permission of permissionCategories[category]) {
        const newPermission = await prisma.permission.create({
          data: {
            name: permission.name,
            description: permission.description,
            category: category
          }
        })
        createdPermissions[permission.name] = newPermission.id
        console.log(`Created permission: ${newPermission.name}`)
      }
    }
    
    // Assign permissions to roles
    const superAdminRole = await prisma.role.findFirst({
      where: { name: 'SUPER_ADMIN' }
    })
    
    // Assign all permissions to Super Admin
    for (const permName in createdPermissions) {
      await prisma.rolePermission.create({
        data: {
          roleId: superAdminRole.id,
          permissionId: createdPermissions[permName]
        }
      })
    }
    
    console.log('Assigned all permissions to Super Admin role')
    
    return NextResponse.json({
      success: true,
      message: 'Database schema updated successfully',
      updatedUsers,
      createdRoles
    })
    
  } catch (error) {
    console.error('Error updating database schema:', error)
    return NextResponse.json(
      { error: 'Failed to update database schema', details: error.message },
      { status: 500 }
    )
  }
}

"use client"

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import { useQuery, useMutation, QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { format } from 'date-fns'
import {
  Globe,
  Plus,
  Pencil,
  Trash2,
  Calendar as CalendarIcon,
  ChevronDown,
  Tag,
  CheckCircle,
  XCircle
} from 'lucide-react'
import { useToast } from '@/app/components/ui/use-toast'
import { Button } from '@/app/components/ui/button'
import { Input } from '@/app/components/ui/input'
import { Label } from '@/app/components/ui/label'
import { Textarea } from '@/app/components/ui/textarea'
import {
  RecursionSafeDialog,
  RecursionSafeDialogContent,
  RecursionSafeDialogDescription,
  RecursionSafeDialogFooter,
  RecursionSafeDialogHeader,
  RecursionSafeDialogTitle
} from '@/app/components/RecursionSafeDialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/app/components/ui/select'
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/app/components/ui/tabs'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/app/components/ui/table'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/app/components/ui/popover'
import { Calendar } from '@/app/components/ui/calendar'
import { Switch } from '@/app/components/ui/switch'
import Sidebar from '@/app/components/Sidebar'
import Header from '@/app/components/Header'

// Create a client
const queryClient = new QueryClient()

// Define the CalendarEvent type
interface CalendarEvent {
  id: string
  title: string
  date: string
  category: 'academic' | 'holiday' | 'exam' | 'staff' | 'student' | 'parent'
  description: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

// Category options with colors
const categoryOptions = [
  { value: 'academic', label: 'Academic', color: 'bg-blue-100 text-blue-800' },
  { value: 'holiday', label: 'Holiday', color: 'bg-red-100 text-red-800' },
  { value: 'exam', label: 'Exam', color: 'bg-purple-100 text-purple-800' },
  { value: 'staff', label: 'Staff', color: 'bg-green-100 text-green-800' },
  { value: 'student', label: 'Student', color: 'bg-amber-100 text-amber-800' },
  { value: 'parent', label: 'Parent', color: 'bg-indigo-100 text-indigo-800' },
]

function AcademicCalendarManagement() {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false)
  const [isMounted, setIsMounted] = useState(false)
  const [activeTab, setActiveTab] = useState('events') // Default tab is 'events'

  // Dialog states
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isSettingsDialogOpen, setIsSettingsDialogOpen] = useState(false)
  const [isSemesterDialogOpen, setIsSemesterDialogOpen] = useState(false)
  const [isSemesterEventDialogOpen, setIsSemesterEventDialogOpen] = useState(false)
  const [isImportantDateDialogOpen, setIsImportantDateDialogOpen] = useState(false)
  const [isHolidayDialogOpen, setIsHolidayDialogOpen] = useState(false)
  const [isConferenceDialogOpen, setIsConferenceDialogOpen] = useState(false)
  const [isExamDialogOpen, setIsExamDialogOpen] = useState(false)

  // Current item states
  const [currentEvent, setCurrentEvent] = useState<CalendarEvent | null>(null)
  const [currentSemester, setCurrentSemester] = useState<SemesterData | null>(null)
  const [currentSemesterEvent, setCurrentSemesterEvent] = useState<{semesterId: string, eventIndex: number, event: any} | null>(null)
  const [currentImportantDate, setCurrentImportantDate] = useState<CalendarItem | null>(null)
  const [currentHoliday, setCurrentHoliday] = useState<CalendarItem | null>(null)
  const [currentConference, setCurrentConference] = useState<CalendarItem | null>(null)
  const [currentExam, setCurrentExam] = useState<CalendarItem | null>(null)

  // Filter states
  const [categoryFilter, setCategoryFilter] = useState<string | null>(null)
  const [date, setDate] = useState<Date | undefined>(new Date())

  // Form data states
  const [formData, setFormData] = useState({
    title: '',
    date: new Date(),
    category: 'academic' as 'academic' | 'holiday' | 'exam' | 'staff' | 'student' | 'parent',
    description: '',
    isActive: true
  })

  const [settingsFormData, setSettingsFormData] = useState({
    academicYear: '',
    currentSemester: 1
  })

  const [semesterFormData, setSemseterFormData] = useState({
    name: ''
  })

  const [semesterEventFormData, setSemesterEventFormData] = useState({
    date: '',
    event: '',
    category: 'academic'
  })

  const [calendarItemFormData, setCalendarItemFormData] = useState({
    date: '',
    event: '',
    category: 'academic'
  })
  const { toast } = useToast()

  // Define interfaces for different types of calendar content
  interface CalendarSettings {
    id: string;
    academicYear: string;
    currentSemester: number;
    updatedAt: string;
  }

  interface SemesterData {
    id: string;
    name: string;
    events: {
      date: string;
      event: string;
      category: string;
    }[];
  }

  interface CalendarItem {
    id: string;
    date: string;
    event: string;
    category: string;
  }

  // Fetch all calendar data from the API
  const { data: allCalendarData, isLoading: isLoadingAll, refetch: refetchAll } = useQuery({
    queryKey: ['allCalendarData'],
    queryFn: async () => {
      try {
        const url = '/api/website-management/academic-calendar?dataType=all';
        console.log('Fetching all calendar data from:', url);
        const res = await fetch(url);

        if (!res.ok) {
          const errorData = await res.json().catch(() => ({}));
          console.error('API error:', errorData);
          throw new Error(`Failed to fetch calendar data: ${res.status} ${res.statusText}`);
        }

        const data = await res.json();
        console.log('Fetched all calendar data:', data);
        return data;
      } catch (error) {
        console.error('Error fetching all calendar data:', error);
        return null;
      }
    },
    staleTime: 60000,
    refetchOnWindowFocus: false,
  });

  // Fetch individual calendar events from the API
  const { data: calendarEvents, isLoading, refetch } = useQuery<CalendarEvent[]>({
    queryKey: ['calendarEvents', categoryFilter],
    queryFn: async () => {
      try {
        const url = categoryFilter
          ? `/api/website-management/academic-calendar?dataType=events&category=${categoryFilter}`
          : '/api/website-management/academic-calendar?dataType=events'

        console.log('Fetching calendar events from:', url);
        const res = await fetch(url)

        if (!res.ok) {
          const errorData = await res.json().catch(() => ({}));
          console.error('API error:', errorData);
          throw new Error(`Failed to fetch calendar events: ${res.status} ${res.statusText}`);
        }

        const data = await res.json();
        console.log('Fetched calendar events:', data);
        return data;
      } catch (error) {
        console.error('Error fetching calendar events:', error);
        // If API doesn't exist yet, return mock data
        return [
          {
            id: '1',
            title: 'First Day of School',
            date: new Date('2023-08-21').toISOString(),
            category: 'academic',
            description: 'Welcome back to school! First day of the academic year.',
            isActive: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          },
          {
            id: '2',
            title: 'Fall Break',
            date: new Date('2023-10-09').toISOString(),
            category: 'holiday',
            description: 'Fall Break (No School) from October 9-13, 2023',
            isActive: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          },
          {
            id: '3',
            title: 'Mid-Term Exams',
            date: new Date('2023-11-15').toISOString(),
            category: 'exam',
            description: 'Mid-term examinations for all grades',
            isActive: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          },
          {
            id: '4',
            title: 'Teacher Orientation',
            date: new Date('2023-08-15').toISOString(),
            category: 'staff',
            description: 'Orientation and preparation day for all teaching staff',
            isActive: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }
        ]
      }
    },
    staleTime: 60000, // 1 minute
    refetchOnWindowFocus: false,
  })

  useEffect(() => {
    setIsMounted(true)
  }, [])

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  const toggleMobileSidebar = () => {
    setMobileSidebarOpen(!mobileSidebarOpen)
  }

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  // Handle category change
  const handleCategoryChange = (value: 'academic' | 'holiday' | 'exam' | 'staff' | 'student' | 'parent') => {
    setFormData(prev => ({ ...prev, category: value }))
  }

  // Handle date change
  const handleDateChange = (date: Date | undefined) => {
    if (date) {
      setFormData(prev => ({ ...prev, date }))
    }
  }

  // Handle active status change
  const handleActiveChange = (checked: boolean) => {
    setFormData(prev => ({ ...prev, isActive: checked }))
  }

  // Reset form data
  const resetFormData = () => {
    setFormData({
      title: '',
      date: new Date(),
      category: 'academic',
      description: '',
      isActive: true
    })
  }

  // Open edit dialog
  const openEditDialog = (event: CalendarEvent) => {
    setCurrentEvent(event)
    setFormData({
      title: event.title,
      date: new Date(event.date),
      category: event.category,
      description: event.description,
      isActive: event.isActive
    })
    setIsEditDialogOpen(true)
  }

  // Open delete dialog
  const openDeleteDialog = (event: CalendarEvent) => {
    setCurrentEvent(event)
    setIsDeleteDialogOpen(true)
  }

  // Add calendar event mutation
  const addCalendarEventMutation = useMutation({
    mutationFn: async (newEvent: Omit<CalendarEvent, 'id' | 'createdAt' | 'updatedAt'>) => {
      console.log('Adding new calendar event:', newEvent)
      // Make an actual API call to store the event
      const response = await fetch('/api/website-management/academic-calendar', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newEvent),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('API error:', errorData);
        throw new Error(`Failed to add calendar event: ${response.status} ${response.statusText}`);
      }

      return await response.json()
    },
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Calendar event added successfully',
      })
      setIsAddDialogOpen(false)
      resetFormData()
      refetch()
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: 'Failed to add calendar event',
        variant: 'destructive',
      })
    }
  })

  // Edit calendar event mutation
  const editCalendarEventMutation = useMutation({
    mutationFn: async (updatedEvent: Partial<CalendarEvent> & { id: string }) => {
      console.log('Updating calendar event:', updatedEvent)
      // Make an actual API call to update the event
      const response = await fetch('/api/website-management/academic-calendar', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedEvent),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('API error:', errorData);
        throw new Error(`Failed to update calendar event: ${response.status} ${response.statusText}`);
      }

      return await response.json()
    },
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Calendar event updated successfully',
      })
      setIsEditDialogOpen(false)
      resetFormData()
      refetch()
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: 'Failed to update calendar event',
        variant: 'destructive',
      })
    }
  })

  // Delete calendar event mutation
  const deleteCalendarEventMutation = useMutation({
    mutationFn: async (id: string) => {
      console.log('Deleting calendar event with ID:', id)
      // Make an actual API call to delete the event
      const response = await fetch(`/api/website-management/academic-calendar?id=${id}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('API error:', errorData);
        throw new Error(`Failed to delete calendar event: ${response.status} ${response.statusText}`);
      }

      return await response.json()
    },
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Calendar event deleted successfully',
      })
      setIsDeleteDialogOpen(false)
      refetch()
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: 'Failed to delete calendar event',
        variant: 'destructive',
      })
    }
  })

  // Handle form submission for adding a new calendar event
  const handleAddCalendarEvent = (e: React.FormEvent) => {
    e.preventDefault()
    const newEvent = {
      title: formData.title,
      date: formData.date.toISOString(),
      category: formData.category,
      description: formData.description,
      isActive: formData.isActive
    }
    addCalendarEventMutation.mutate(newEvent)
  }

  // Handle form submission for editing a calendar event
  const handleEditCalendarEvent = (e: React.FormEvent) => {
    e.preventDefault()
    if (!currentEvent) return

    const updatedEvent = {
      id: currentEvent.id,
      title: formData.title,
      date: formData.date.toISOString(),
      category: formData.category,
      description: formData.description,
      isActive: formData.isActive
    }
    editCalendarEventMutation.mutate(updatedEvent)
  }

  // Handle calendar event deletion
  const handleDeleteCalendarEvent = () => {
    if (!currentEvent) return
    deleteCalendarEventMutation.mutate(currentEvent.id)
  }

  // Get category label and color
  const getCategoryInfo = (categoryValue: string) => {
    const category = categoryOptions.find(cat => cat.value === categoryValue)
    return category || { value: categoryValue, label: categoryValue, color: 'bg-gray-100 text-gray-800' }
  }

  if (!isMounted) {
    return null
  }

  return (
    <div className="flex h-screen bg-gray-100 dark:bg-gray-900">
      <Sidebar
        isCollapsed={sidebarCollapsed}
        toggleSidebar={toggleSidebar}
        isMobileOpen={mobileSidebarOpen}
      />

      <main className={`main-content ${sidebarCollapsed ? 'sidebar-collapsed' : ''} w-full h-screen overflow-y-auto`}>
        <Header toggleMobileSidebar={toggleMobileSidebar} />

        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-2xl font-semibold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent inline-block">
                Academic Calendar Management
              </h1>
              <p className="text-gray-600 dark:text-gray-300 mt-1">
                Manage the academic calendar displayed on the website
              </p>
            </div>
            <div className="flex space-x-2">
              <Link href="/website/academics/calendar" target="_blank">
                <Button variant="outline" size="sm" className="flex items-center">
                  <Globe className="h-4 w-4 mr-1" />
                  <span>View Calendar</span>
                </Button>
              </Link>
              <Button onClick={() => setIsAddDialogOpen(true)} className="flex items-center">
                <Plus className="h-4 w-4 mr-1" />
                <span>Add Calendar Event</span>
              </Button>
            </div>
          </div>

          <Tabs defaultValue="events" className="w-full" onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-5 mb-6">
              <TabsTrigger value="events">Events</TabsTrigger>
              <TabsTrigger value="academic-year">Academic Year</TabsTrigger>
              <TabsTrigger value="semesters">Semesters</TabsTrigger>
              <TabsTrigger value="important-dates">Important Dates</TabsTrigger>
              <TabsTrigger value="holidays">Holidays</TabsTrigger>
            </TabsList>

            {/* Events Tab */}
            <TabsContent value="events" className="space-y-4">
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
                <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                  <h2 className="text-lg font-medium text-gray-900 dark:text-white">Calendar Events</h2>
                  <div className="flex space-x-2">
                    <Select
                      value={categoryFilter || 'all'}
                      onValueChange={(value) => setCategoryFilter(value === 'all' ? null : value)}
                    >
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Filter by category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Categories</SelectItem>
                        {categoryOptions.map((category) => (
                          <SelectItem key={category.value} value={category.value}>
                            {category.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Date</TableHead>
                        <TableHead>Event</TableHead>
                        <TableHead>Category</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {isLoading ? (
                        <TableRow>
                          <TableCell colSpan={5} className="text-center py-4">
                            Loading calendar events...
                          </TableCell>
                        </TableRow>
                      ) : calendarEvents && calendarEvents.length > 0 ? (
                        calendarEvents.map((event) => (
                          <TableRow key={event.id}>
                            <TableCell className="font-medium">
                              {format(new Date(event.date), 'MMM d, yyyy')}
                            </TableCell>
                            <TableCell>{event.title}</TableCell>
                            <TableCell>
                              <span className={`px-2 py-1 rounded-full text-xs ${getCategoryInfo(event.category).color}`}>
                                {getCategoryInfo(event.category).label}
                              </span>
                            </TableCell>
                            <TableCell>
                              {event.isActive ? (
                                <span className="flex items-center text-green-600">
                                  <CheckCircle className="h-4 w-4 mr-1" />
                                  Active
                                </span>
                              ) : (
                                <span className="flex items-center text-gray-500">
                                  <XCircle className="h-4 w-4 mr-1" />
                                  Inactive
                                </span>
                              )}
                            </TableCell>
                            <TableCell className="text-right">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => openEditDialog(event)}
                                className="h-8 w-8 p-0 mr-1"
                              >
                                <Pencil className="h-4 w-4" />
                                <span className="sr-only">Edit</span>
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => openDeleteDialog(event)}
                                className="h-8 w-8 p-0 text-red-600"
                              >
                                <Trash2 className="h-4 w-4" />
                                <span className="sr-only">Delete</span>
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={5} className="text-center py-4">
                            No calendar events found.
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              </div>
            </TabsContent>

            {/* Academic Year Tab */}
            <TabsContent value="academic-year" className="space-y-4">
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
                <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                  <h2 className="text-lg font-medium text-gray-900 dark:text-white">Academic Year Settings</h2>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    Configure the current academic year and semester
                  </p>
                </div>

                <div className="p-6">
                  {allCalendarData?.settings ? (
                    <div className="space-y-6">
                      <div className="flex justify-between items-center">
                        <div>
                          <h3 className="text-md font-medium">Current Academic Year: <span className="text-blue-600">{allCalendarData.settings.academicYear}</span></h3>
                          <p className="text-sm text-gray-500 mt-1">Current Semester: {allCalendarData.settings.currentSemester}</p>
                          <p className="text-xs text-gray-400 mt-1">Last Updated: {format(new Date(allCalendarData.settings.updatedAt), 'MMM d, yyyy')}</p>
                        </div>
                        <Button onClick={() => setIsSettingsDialogOpen(true)}>
                          Edit Settings
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <p className="text-gray-500 mb-4">No academic year settings found</p>
                      <Button onClick={() => setIsSettingsDialogOpen(true)}>
                        Configure Academic Year
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </TabsContent>

            {/* Semesters Tab */}
            <TabsContent value="semesters" className="space-y-4">
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
                <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                  <div>
                    <h2 className="text-lg font-medium text-gray-900 dark:text-white">Semesters</h2>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Manage semester information and events
                    </p>
                  </div>
                  <Button onClick={() => setIsSemesterDialogOpen(true)}>
                    Add Semester
                  </Button>
                </div>

                <div className="p-6">
                  {allCalendarData?.semesters && allCalendarData.semesters.length > 0 ? (
                    <div className="space-y-6">
                      {allCalendarData.semesters.map((semester) => (
                        <div key={semester.id} className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                          <div className="p-4 bg-gray-50 dark:bg-gray-800 flex justify-between items-center">
                            <h3 className="text-md font-medium">{semester.name}</h3>
                            <div className="flex space-x-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  setCurrentSemester(semester);
                                  setSemseterFormData({ name: semester.name });
                                  setIsSemesterDialogOpen(true);
                                }}
                              >
                                Edit
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  setCurrentSemester(semester);
                                  setIsSemesterEventDialogOpen(true);
                                }}
                              >
                                Add Event
                              </Button>
                            </div>
                          </div>

                          <div className="p-4">
                            <h4 className="text-sm font-medium mb-2">Events ({semester.events.length})</h4>
                            {semester.events.length > 0 ? (
                              <table className="w-full text-sm">
                                <thead className="bg-gray-50 dark:bg-gray-800">
                                  <tr>
                                    <th className="px-4 py-2 text-left">Date</th>
                                    <th className="px-4 py-2 text-left">Event</th>
                                    <th className="px-4 py-2 text-left">Category</th>
                                    <th className="px-4 py-2 text-right">Actions</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  {semester.events.map((event, index) => (
                                    <tr key={index} className="border-t border-gray-200 dark:border-gray-700">
                                      <td className="px-4 py-2">{event.date}</td>
                                      <td className="px-4 py-2">{event.event}</td>
                                      <td className="px-4 py-2">
                                        <span className={`px-2 py-1 rounded-full text-xs ${getCategoryInfo(event.category).color}`}>
                                          {getCategoryInfo(event.category).label}
                                        </span>
                                      </td>
                                      <td className="px-4 py-2 text-right">
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={() => {
                                            setCurrentSemesterEvent({
                                              semesterId: semester.id,
                                              eventIndex: index,
                                              event: event
                                            });
                                            setSemesterEventFormData({
                                              date: event.date,
                                              event: event.event,
                                              category: event.category as any
                                            });
                                            setIsSemesterEventDialogOpen(true);
                                          }}
                                          className="h-8 w-8 p-0 mr-1"
                                        >
                                          <Pencil className="h-4 w-4" />
                                        </Button>
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={() => {
                                            // Delete semester event
                                            if (confirm('Are you sure you want to delete this event?')) {
                                              console.log(`Deleting semester event at index ${index} from semester ${semester.id}`);

                                              fetch(`/api/website-management/academic-calendar?dataType=semesterEvent&semesterId=${semester.id}&eventIndex=${index}`, {
                                                method: 'DELETE',
                                              })
                                              .then(async response => {
                                                const responseText = await response.text();
                                                console.log('Delete response:', responseText);

                                                if (!response.ok) {
                                                  let errorMessage = 'Failed to delete semester event';
                                                  try {
                                                    const errorData = JSON.parse(responseText);
                                                    if (errorData.error) {
                                                      errorMessage = errorData.error;
                                                    }
                                                  } catch (e) {
                                                    // If JSON parsing fails, use the default error message
                                                  }
                                                  throw new Error(errorMessage);
                                                }

                                                return JSON.parse(responseText);
                                              })
                                              .then(data => {
                                                console.log('Delete success data:', data);
                                                toast({
                                                  title: 'Success',
                                                  description: 'Semester event deleted successfully',
                                                });
                                                refetchAll();
                                              })
                                              .catch(error => {
                                                toast({
                                                  title: 'Error',
                                                  description: error.message || 'Failed to delete semester event',
                                                  variant: 'destructive',
                                                });
                                                console.error('Error deleting semester event:', error);
                                              });
                                            }
                                          }}
                                          className="h-8 w-8 p-0 text-red-600"
                                        >
                                          <Trash2 className="h-4 w-4" />
                                        </Button>
                                      </td>
                                    </tr>
                                  ))}
                                </tbody>
                              </table>
                            ) : (
                              <p className="text-gray-500 text-center py-4">No events added to this semester yet</p>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <p className="text-gray-500 mb-4">No semesters found</p>
                      <Button onClick={() => setIsSemesterDialogOpen(true)}>
                        Add First Semester
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </TabsContent>

            {/* Important Dates Tab */}
            <TabsContent value="important-dates" className="space-y-4">
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
                <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                  <div>
                    <h2 className="text-lg font-medium text-gray-900 dark:text-white">Important Dates</h2>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Key academic dates for the school year
                    </p>
                  </div>
                  <Button onClick={() => setIsImportantDateDialogOpen(true)}>
                    Add Important Date
                  </Button>
                </div>

                <div className="p-6">
                  {allCalendarData?.importantDates && allCalendarData.importantDates.length > 0 ? (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Date</TableHead>
                          <TableHead>Event</TableHead>
                          <TableHead>Category</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {allCalendarData.importantDates.map((date) => (
                          <TableRow key={date.id}>
                            <TableCell>{date.date}</TableCell>
                            <TableCell>{date.event}</TableCell>
                            <TableCell>
                              <span className={`px-2 py-1 rounded-full text-xs ${getCategoryInfo(date.category).color}`}>
                                {getCategoryInfo(date.category).label}
                              </span>
                            </TableCell>
                            <TableCell className="text-right">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  setCurrentImportantDate(date);
                                  setCalendarItemFormData({
                                    date: date.date,
                                    event: date.event,
                                    category: date.category as any
                                  });
                                  setIsImportantDateDialogOpen(true);
                                }}
                                className="h-8 w-8 p-0 mr-1"
                              >
                                <Pencil className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  // Delete important date
                                  if (confirm('Are you sure you want to delete this important date?')) {
                                    fetch(`/api/website-management/academic-calendar?dataType=importantDates&id=${date.id}`, {
                                      method: 'DELETE',
                                    })
                                    .then(response => {
                                      if (!response.ok) throw new Error('Failed to delete important date');
                                      return response.json();
                                    })
                                    .then(data => {
                                      toast({
                                        title: 'Success',
                                        description: 'Important date deleted successfully',
                                      });
                                      refetchAll();
                                    })
                                    .catch(error => {
                                      toast({
                                        title: 'Error',
                                        description: 'Failed to delete important date',
                                        variant: 'destructive',
                                      });
                                      console.error('Error deleting important date:', error);
                                    });
                                  }
                                }}
                                className="h-8 w-8 p-0 text-red-600"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  ) : (
                    <div className="text-center py-8">
                      <p className="text-gray-500 mb-4">No important dates found</p>
                      <Button onClick={() => setIsImportantDateDialogOpen(true)}>
                        Add First Important Date
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </TabsContent>

            {/* Holidays Tab */}
            <TabsContent value="holidays" className="space-y-4">
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
                <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                  <div>
                    <h2 className="text-lg font-medium text-gray-900 dark:text-white">School Holidays</h2>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Days when school is not in session
                    </p>
                  </div>
                  <Button onClick={() => setIsHolidayDialogOpen(true)}>
                    Add Holiday
                  </Button>
                </div>

                <div className="p-6">
                  {allCalendarData?.holidays && allCalendarData.holidays.length > 0 ? (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Date</TableHead>
                          <TableHead>Holiday</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {allCalendarData.holidays.map((holiday) => (
                          <TableRow key={holiday.id}>
                            <TableCell>{holiday.date}</TableCell>
                            <TableCell>{holiday.event}</TableCell>
                            <TableCell className="text-right">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  setCurrentHoliday(holiday);
                                  setCalendarItemFormData({
                                    date: holiday.date,
                                    event: holiday.event,
                                    category: 'holiday'
                                  });
                                  setIsHolidayDialogOpen(true);
                                }}
                                className="h-8 w-8 p-0 mr-1"
                              >
                                <Pencil className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  // Delete holiday
                                  if (confirm('Are you sure you want to delete this holiday?')) {
                                    fetch(`/api/website-management/academic-calendar?dataType=holidays&id=${holiday.id}`, {
                                      method: 'DELETE',
                                    })
                                    .then(response => {
                                      if (!response.ok) throw new Error('Failed to delete holiday');
                                      return response.json();
                                    })
                                    .then(data => {
                                      toast({
                                        title: 'Success',
                                        description: 'Holiday deleted successfully',
                                      });
                                      refetchAll();
                                    })
                                    .catch(error => {
                                      toast({
                                        title: 'Error',
                                        description: 'Failed to delete holiday',
                                        variant: 'destructive',
                                      });
                                      console.error('Error deleting holiday:', error);
                                    });
                                  }
                                }}
                                className="h-8 w-8 p-0 text-red-600"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  ) : (
                    <div className="text-center py-8">
                      <p className="text-gray-500 mb-4">No holidays found</p>
                      <Button onClick={() => setIsHolidayDialogOpen(true)}>
                        Add First Holiday
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </main>

      {/* Add Calendar Event Dialog */}
      <RecursionSafeDialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <RecursionSafeDialogContent>
          <RecursionSafeDialogHeader>
            <RecursionSafeDialogTitle>Add Calendar Event</RecursionSafeDialogTitle>
            <RecursionSafeDialogDescription>
              Add a new event to the academic calendar. Fill in the details below.
            </RecursionSafeDialogDescription>
          </RecursionSafeDialogHeader>
          <form onSubmit={handleAddCalendarEvent}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Event Title</Label>
                  <Input
                    id="title"
                    name="title"
                    value={formData.title}
                    onChange={handleInputChange}
                    placeholder="Enter event title"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="category">Category</Label>
                  <Select
                    value={formData.category}
                    onValueChange={(value: 'academic' | 'holiday' | 'exam' | 'staff' | 'student' | 'parent') => handleCategoryChange(value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categoryOptions.map((category) => (
                        <SelectItem key={category.value} value={category.value}>
                          {category.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="space-y-2">
                <Label>Event Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formData.date ? format(formData.date, 'PPP') : <span>Pick a date</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={formData.date}
                      onSelect={handleDateChange}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  placeholder="Enter event description"
                  rows={3}
                />
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="isActive"
                  checked={formData.isActive}
                  onCheckedChange={handleActiveChange}
                />
                <Label htmlFor="isActive">Active</Label>
              </div>
            </div>
            <RecursionSafeDialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={addCalendarEventMutation.isPending}>
                {addCalendarEventMutation.isPending ? 'Adding...' : 'Add Event'}
              </Button>
            </RecursionSafeDialogFooter>
          </form>
        </RecursionSafeDialogContent>
      </RecursionSafeDialog>

      {/* Edit Calendar Event Dialog */}
      <RecursionSafeDialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <RecursionSafeDialogContent>
          <RecursionSafeDialogHeader>
            <RecursionSafeDialogTitle>Edit Calendar Event</RecursionSafeDialogTitle>
            <RecursionSafeDialogDescription>
              Update the details of this calendar event.
            </RecursionSafeDialogDescription>
          </RecursionSafeDialogHeader>
          <form onSubmit={handleEditCalendarEvent}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-title">Event Title</Label>
                  <Input
                    id="edit-title"
                    name="title"
                    value={formData.title}
                    onChange={handleInputChange}
                    placeholder="Enter event title"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-category">Category</Label>
                  <Select
                    value={formData.category}
                    onValueChange={(value: 'academic' | 'holiday' | 'exam' | 'staff' | 'student' | 'parent') => handleCategoryChange(value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categoryOptions.map((category) => (
                        <SelectItem key={category.value} value={category.value}>
                          {category.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="space-y-2">
                <Label>Event Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formData.date ? format(formData.date, 'PPP') : <span>Pick a date</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={formData.date}
                      onSelect={handleDateChange}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-description">Description</Label>
                <Textarea
                  id="edit-description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  placeholder="Enter event description"
                  rows={3}
                />
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="edit-isActive"
                  checked={formData.isActive}
                  onCheckedChange={handleActiveChange}
                />
                <Label htmlFor="edit-isActive">Active</Label>
              </div>
            </div>
            <RecursionSafeDialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={editCalendarEventMutation.isPending}>
                {editCalendarEventMutation.isPending ? 'Updating...' : 'Update Event'}
              </Button>
            </RecursionSafeDialogFooter>
          </form>
        </RecursionSafeDialogContent>
      </RecursionSafeDialog>

      {/* Delete Calendar Event Dialog */}
      <RecursionSafeDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <RecursionSafeDialogContent>
          <RecursionSafeDialogHeader>
            <RecursionSafeDialogTitle>Delete Calendar Event</RecursionSafeDialogTitle>
            <RecursionSafeDialogDescription>
              Are you sure you want to delete this calendar event? This action cannot be undone.
            </RecursionSafeDialogDescription>
          </RecursionSafeDialogHeader>
          <div className="py-4">
            {currentEvent && (
              <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-md">
                <p className="font-medium">{currentEvent.title}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {format(new Date(currentEvent.date), 'MMMM d, yyyy')}
                </p>
                <p className="text-sm mt-1">{currentEvent.description}</p>
              </div>
            )}
          </div>
          <RecursionSafeDialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteCalendarEvent}
              disabled={deleteCalendarEventMutation.isPending}
            >
              {deleteCalendarEventMutation.isPending ? 'Deleting...' : 'Delete Event'}
            </Button>
          </RecursionSafeDialogFooter>
        </RecursionSafeDialogContent>
      </RecursionSafeDialog>

      {/* Academic Year Settings Dialog */}
      <RecursionSafeDialog open={isSettingsDialogOpen} onOpenChange={setIsSettingsDialogOpen}>
        <RecursionSafeDialogContent>
          <RecursionSafeDialogHeader>
            <RecursionSafeDialogTitle>Academic Year Settings</RecursionSafeDialogTitle>
            <RecursionSafeDialogDescription>
              Configure the current academic year and semester
            </RecursionSafeDialogDescription>
          </RecursionSafeDialogHeader>
          <form onSubmit={(e) => {
            e.preventDefault();
            // Handle settings update
            const updatedSettings = {
              id: allCalendarData?.settings?.id || 'settings-1',
              academicYear: settingsFormData.academicYear,
              currentSemester: settingsFormData.currentSemester
            };

            // Call API to update settings
            fetch('/api/website-management/academic-calendar?dataType=settings', {
              method: 'PUT',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(updatedSettings),
            })
            .then(response => {
              if (!response.ok) throw new Error('Failed to update settings');
              return response.json();
            })
            .then(data => {
              toast({
                title: 'Success',
                description: 'Academic year settings updated successfully',
              });
              setIsSettingsDialogOpen(false);
              refetchAll();
            })
            .catch(error => {
              toast({
                title: 'Error',
                description: 'Failed to update academic year settings',
                variant: 'destructive',
              });
              console.error('Error updating settings:', error);
            });
          }}>
            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="academicYear">Academic Year</Label>
                <Select
                  value={settingsFormData.academicYear}
                  onValueChange={(value) => setSettingsFormData(prev => ({ ...prev, academicYear: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select academic year" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="2023-2024">2023-2024</SelectItem>
                    <SelectItem value="2024-2025">2024-2025</SelectItem>
                    <SelectItem value="2025-2026">2025-2026</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="currentSemester">Current Semester</Label>
                <Select
                  value={settingsFormData.currentSemester.toString()}
                  onValueChange={(value) => setSettingsFormData(prev => ({ ...prev, currentSemester: parseInt(value) }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select current semester" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">First Semester</SelectItem>
                    <SelectItem value="2">Second Semester</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <RecursionSafeDialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsSettingsDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit">
                Save Settings
              </Button>
            </RecursionSafeDialogFooter>
          </form>
        </RecursionSafeDialogContent>
      </RecursionSafeDialog>

      {/* Add/Edit Semester Dialog */}
      <RecursionSafeDialog open={isSemesterDialogOpen} onOpenChange={setIsSemesterDialogOpen}>
        <RecursionSafeDialogContent>
          <RecursionSafeDialogHeader>
            <RecursionSafeDialogTitle>{currentSemester ? 'Edit' : 'Add'} Semester</RecursionSafeDialogTitle>
            <RecursionSafeDialogDescription>
              {currentSemester ? 'Update semester information' : 'Add a new semester to the academic calendar'}
            </RecursionSafeDialogDescription>
          </RecursionSafeDialogHeader>
          <form onSubmit={(e) => {
            e.preventDefault();

            const semesterData = {
              id: currentSemester?.id,
              name: semesterFormData.name,
              events: currentSemester?.events || []
            };

            // Call API to add/update semester
            fetch('/api/website-management/academic-calendar?dataType=semesters', {
              method: currentSemester ? 'PUT' : 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(semesterData),
            })
            .then(response => {
              if (!response.ok) throw new Error(`Failed to ${currentSemester ? 'update' : 'add'} semester`);
              return response.json();
            })
            .then(data => {
              toast({
                title: 'Success',
                description: `Semester ${currentSemester ? 'updated' : 'added'} successfully`,
              });
              setIsSemesterDialogOpen(false);
              setCurrentSemester(null);
              setSemseterFormData({ name: '' });
              refetchAll();
            })
            .catch(error => {
              toast({
                title: 'Error',
                description: `Failed to ${currentSemester ? 'update' : 'add'} semester`,
                variant: 'destructive',
              });
              console.error('Error with semester:', error);
            });
          }}>
            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="semesterName">Semester Name</Label>
                <Input
                  id="semesterName"
                  value={semesterFormData.name}
                  onChange={(e) => setSemseterFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="e.g., First Semester"
                  required
                />
              </div>
            </div>
            <RecursionSafeDialogFooter>
              <Button type="button" variant="outline" onClick={() => {
                setIsSemesterDialogOpen(false);
                setCurrentSemester(null);
                setSemseterFormData({ name: '' });
              }}>
                Cancel
              </Button>
              <Button type="submit">
                {currentSemester ? 'Update' : 'Add'} Semester
              </Button>
            </RecursionSafeDialogFooter>
          </form>
        </RecursionSafeDialogContent>
      </RecursionSafeDialog>

      {/* Add/Edit Semester Event Dialog */}
      <RecursionSafeDialog open={isSemesterEventDialogOpen} onOpenChange={setIsSemesterEventDialogOpen}>
        <RecursionSafeDialogContent>
          <RecursionSafeDialogHeader>
            <RecursionSafeDialogTitle>{currentSemesterEvent ? 'Edit' : 'Add'} Semester Event</RecursionSafeDialogTitle>
            <RecursionSafeDialogDescription>
              {currentSemesterEvent ? 'Update event information' : 'Add a new event to this semester'}
            </RecursionSafeDialogDescription>
          </RecursionSafeDialogHeader>
          <form onSubmit={(e) => {
            e.preventDefault();

            if (!currentSemester) return;

            const eventData = {
              semesterId: currentSemester.id,
              eventIndex: currentSemesterEvent?.eventIndex,
              event: {
                date: semesterEventFormData.date,
                event: semesterEventFormData.event,
                category: semesterEventFormData.category
              }
            };

            // Call API to add/update semester event
            fetch('/api/website-management/academic-calendar?dataType=semesterEvent', {
              method: currentSemesterEvent ? 'PUT' : 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(eventData),
            })
            .then(response => {
              if (!response.ok) throw new Error(`Failed to ${currentSemesterEvent ? 'update' : 'add'} semester event`);
              return response.json();
            })
            .then(data => {
              toast({
                title: 'Success',
                description: `Semester event ${currentSemesterEvent ? 'updated' : 'added'} successfully`,
              });
              setIsSemesterEventDialogOpen(false);
              setCurrentSemesterEvent(null);
              setSemesterEventFormData({
                date: '',
                event: '',
                category: 'academic'
              });
              refetchAll();
            })
            .catch(error => {
              toast({
                title: 'Error',
                description: `Failed to ${currentSemesterEvent ? 'update' : 'add'} semester event`,
                variant: 'destructive',
              });
              console.error('Error with semester event:', error);
            });
          }}>
            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="eventDate">Event Date</Label>
                <Input
                  id="eventDate"
                  value={semesterEventFormData.date}
                  onChange={(e) => setSemesterEventFormData(prev => ({ ...prev, date: e.target.value }))}
                  placeholder="e.g., January 15, 2024"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="eventName">Event Name</Label>
                <Input
                  id="eventName"
                  value={semesterEventFormData.event}
                  onChange={(e) => setSemesterEventFormData(prev => ({ ...prev, event: e.target.value }))}
                  placeholder="e.g., First Day of School"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="eventCategory">Category</Label>
                <Select
                  value={semesterEventFormData.category}
                  onValueChange={(value: any) => setSemesterEventFormData(prev => ({ ...prev, category: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categoryOptions.map((category) => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <RecursionSafeDialogFooter>
              <Button type="button" variant="outline" onClick={() => {
                setIsSemesterEventDialogOpen(false);
                setCurrentSemesterEvent(null);
                setSemesterEventFormData({
                  date: '',
                  event: '',
                  category: 'academic'
                });
              }}>
                Cancel
              </Button>
              <Button type="submit">
                {currentSemesterEvent ? 'Update' : 'Add'} Event
              </Button>
            </RecursionSafeDialogFooter>
          </form>
        </RecursionSafeDialogContent>
      </RecursionSafeDialog>

      {/* Add/Edit Important Date Dialog */}
      <RecursionSafeDialog open={isImportantDateDialogOpen} onOpenChange={setIsImportantDateDialogOpen}>
        <RecursionSafeDialogContent>
          <RecursionSafeDialogHeader>
            <RecursionSafeDialogTitle>{currentImportantDate ? 'Edit' : 'Add'} Important Date</RecursionSafeDialogTitle>
            <RecursionSafeDialogDescription>
              {currentImportantDate ? 'Update important date information' : 'Add a new important date to the calendar'}
            </RecursionSafeDialogDescription>
          </RecursionSafeDialogHeader>
          <form onSubmit={(e) => {
            e.preventDefault();

            const dateData = {
              id: currentImportantDate?.id,
              date: calendarItemFormData.date,
              event: calendarItemFormData.event,
              category: calendarItemFormData.category
            };

            // Call API to add/update important date
            fetch('/api/website-management/academic-calendar?dataType=importantDates', {
              method: currentImportantDate ? 'PUT' : 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(dateData),
            })
            .then(response => {
              if (!response.ok) throw new Error(`Failed to ${currentImportantDate ? 'update' : 'add'} important date`);
              return response.json();
            })
            .then(data => {
              toast({
                title: 'Success',
                description: `Important date ${currentImportantDate ? 'updated' : 'added'} successfully`,
              });
              setIsImportantDateDialogOpen(false);
              setCurrentImportantDate(null);
              setCalendarItemFormData({
                date: '',
                event: '',
                category: 'academic'
              });
              refetchAll();
            })
            .catch(error => {
              toast({
                title: 'Error',
                description: `Failed to ${currentImportantDate ? 'update' : 'add'} important date`,
                variant: 'destructive',
              });
              console.error('Error with important date:', error);
            });
          }}>
            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="importantDate">Date</Label>
                <Input
                  id="importantDate"
                  value={calendarItemFormData.date}
                  onChange={(e) => setCalendarItemFormData(prev => ({ ...prev, date: e.target.value }))}
                  placeholder="e.g., January 15, 2024"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="importantEvent">Event</Label>
                <Input
                  id="importantEvent"
                  value={calendarItemFormData.event}
                  onChange={(e) => setCalendarItemFormData(prev => ({ ...prev, event: e.target.value }))}
                  placeholder="e.g., First Day of School"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="importantCategory">Category</Label>
                <Select
                  value={calendarItemFormData.category}
                  onValueChange={(value: any) => setCalendarItemFormData(prev => ({ ...prev, category: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categoryOptions.map((category) => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <RecursionSafeDialogFooter>
              <Button type="button" variant="outline" onClick={() => {
                setIsImportantDateDialogOpen(false);
                setCurrentImportantDate(null);
                setCalendarItemFormData({
                  date: '',
                  event: '',
                  category: 'academic'
                });
              }}>
                Cancel
              </Button>
              <Button type="submit">
                {currentImportantDate ? 'Update' : 'Add'} Important Date
              </Button>
            </RecursionSafeDialogFooter>
          </form>
        </RecursionSafeDialogContent>
      </RecursionSafeDialog>

      {/* Add/Edit Holiday Dialog */}
      <RecursionSafeDialog open={isHolidayDialogOpen} onOpenChange={setIsHolidayDialogOpen}>
        <RecursionSafeDialogContent>
          <RecursionSafeDialogHeader>
            <RecursionSafeDialogTitle>{currentHoliday ? 'Edit' : 'Add'} Holiday</RecursionSafeDialogTitle>
            <RecursionSafeDialogDescription>
              {currentHoliday ? 'Update holiday information' : 'Add a new holiday to the calendar'}
            </RecursionSafeDialogDescription>
          </RecursionSafeDialogHeader>
          <form onSubmit={(e) => {
            e.preventDefault();

            const holidayData = {
              id: currentHoliday?.id,
              date: calendarItemFormData.date,
              event: calendarItemFormData.event,
              category: 'holiday' // Always set to holiday
            };

            // Call API to add/update holiday
            fetch('/api/website-management/academic-calendar?dataType=holidays', {
              method: currentHoliday ? 'PUT' : 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(holidayData),
            })
            .then(response => {
              if (!response.ok) throw new Error(`Failed to ${currentHoliday ? 'update' : 'add'} holiday`);
              return response.json();
            })
            .then(data => {
              toast({
                title: 'Success',
                description: `Holiday ${currentHoliday ? 'updated' : 'added'} successfully`,
              });
              setIsHolidayDialogOpen(false);
              setCurrentHoliday(null);
              setCalendarItemFormData({
                date: '',
                event: '',
                category: 'holiday'
              });
              refetchAll();
            })
            .catch(error => {
              toast({
                title: 'Error',
                description: `Failed to ${currentHoliday ? 'update' : 'add'} holiday`,
                variant: 'destructive',
              });
              console.error('Error with holiday:', error);
            });
          }}>
            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="holidayDate">Date</Label>
                <Input
                  id="holidayDate"
                  value={calendarItemFormData.date}
                  onChange={(e) => setCalendarItemFormData(prev => ({ ...prev, date: e.target.value }))}
                  placeholder="e.g., December 25, 2023"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="holidayName">Holiday Name</Label>
                <Input
                  id="holidayName"
                  value={calendarItemFormData.event}
                  onChange={(e) => setCalendarItemFormData(prev => ({ ...prev, event: e.target.value }))}
                  placeholder="e.g., Christmas Day (No School)"
                  required
                />
              </div>
            </div>
            <RecursionSafeDialogFooter>
              <Button type="button" variant="outline" onClick={() => {
                setIsHolidayDialogOpen(false);
                setCurrentHoliday(null);
                setCalendarItemFormData({
                  date: '',
                  event: '',
                  category: 'holiday'
                });
              }}>
                Cancel
              </Button>
              <Button type="submit">
                {currentHoliday ? 'Update' : 'Add'} Holiday
              </Button>
            </RecursionSafeDialogFooter>
          </form>
        </RecursionSafeDialogContent>
      </RecursionSafeDialog>

      {/* Overlay for mobile menu */}
      {mobileSidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 md:hidden z-40"
          onClick={toggleMobileSidebar}
        ></div>
      )}
    </div>
  )
}

// Export a wrapper component that provides the QueryClient
export default function AcademicCalendarManagementPage() {
  return (
    <QueryClientProvider client={queryClient}>
      <AcademicCalendarManagement />
    </QueryClientProvider>
  )
}

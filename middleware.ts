import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { hasPageAccess } from './app/config/page-access-config'

// Define public paths that don't require authentication
const publicPaths = [
  // Public pages
  '/login',
  '/register',
  '/',  // Root path should be public to allow initial redirection

  // Admin setup pages are now protected
  // '/admin',
  // '/admin/create-super-admin',

  // Website pages - make sure ALL website paths are public
  '/website',
  '/website/about',
  '/website/about/mission-vision',
  '/website/about/history',
  '/website/about/achievements',
  '/website/about/leadership',

  // Admissions pages
  '/website/admissions',
  '/website/admissions/how-to-apply',
  '/website/admissions/requirements',
  '/website/admissions/tuition-fees',
  '/website/admissions/forms',

  // Academics pages
  '/website/academics',
  '/website/academics/curriculum',
  '/website/academics/departments',
  '/website/academics/calendar',
  '/website/academics/resources',

  // Other website pages
  '/website/news-events',
  '/website/contact',

  // Public API endpoints
  '/api/auth/login',
  '/api/auth/register',
  '/api/auth/logout',
  '/api/auth/me',
  // '/api/auth/create-super-admin', // Protected now that we have a super admin
  '/api/auth/debug',
  '/api/setup',
  '/api/db-test', // For debugging database connection issues

  // Common API endpoints that should be accessible to all authenticated users
  '/api/teachers',
  '/api/subjects',
  '/api/classes',
  '/api/teacher-assignments-mock',

  // Static assets
  '/favicon.ico',
  '/_next'
]

// Define role-based access paths
const roleBasedPaths = {
  // Super Admin paths - has access to everything
  superAdmin: [
    '/api/students',
    '/api/classes',
    '/api/marks',
    '/api/marks/submitted-subjects',
    '/api/attendance',
    '/api/teachers',
    '/api/subjects',
    '/api/roles',
    '/api/permissions',
    '/api/users',
    '/api/progress',
    '/role-management',
    '/system-settings',
    '/api/fee-types',
    '/api/payments',
    '/api/bank-payment-vouchers',
    '/api/journal-vouchers',
    '/api/account-codes'
  ],

  // Admin paths - similar to Super Admin but with some restrictions
  admin: [
    '/api/students',
    '/api/classes',
    '/api/marks',
    '/api/marks/submitted-subjects',
    '/api/attendance',
    '/api/teachers',
    '/api/subjects',
    '/api/roles',
    '/api/permissions',
    '/api/users',
    '/api/progress',
    '/role-management',
    '/api/fee-types',
    '/api/payments',
    '/api/bank-payment-vouchers',
    '/api/journal-vouchers',
    '/api/account-codes'
  ],

  // Supervisor paths
  supervisor: [
    '/api/students',
    '/api/classes',
    '/api/marks',
    '/api/marks/submitted-subjects',
    '/api/attendance',
    '/api/teachers',
    '/api/subjects',
    '/api/progress',
    '/api/payments'
  ],

  // Accountant paths
  accountant: [
    '/api/students',
    '/api/classes',
    '/api/fee-types',
    '/api/payments',
    '/api/bank-payment-vouchers',
    '/api/journal-vouchers',
    '/api/account-codes'
  ],

  // Teacher paths
  teacher: [
    '/api/marks',
    '/api/marks/submitted-subjects',
    '/api/attendance',
    '/api/students',
    '/api/classes',
    '/api/subjects'
  ],

  // Parent paths
  parent: [
    '/api/parent',
    '/api/parent/children',
    '/api/parent/attendance',
    '/api/parent/marks',
    '/api/parent/reports',
    '/api/parent-students'
  ],

  // Unit Leader paths
  unit_leader: [
    '/api/students',
    '/api/teachers',
    '/api/classes',
    '/api/marks',
    '/api/attendance',
    '/api/subjects',
    '/api/visualizer/classes',
    '/api/visualizer/subjects'
  ],

  // Data Encoder paths
  data_encoder: [
    '/api/students',
    '/api/teachers',
    '/api/classes',
    '/api/marks',
    '/api/subjects'
  ]
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Special case for root path - let the root page handle the redirect
  // based on authentication status
  if (pathname === '/') {
    console.log('Middleware: Root path detected, letting root page handle redirect');
    return NextResponse.next()
  }

  // Check if the path is public
  if (isPublicPath(pathname)) {
    return NextResponse.next()
  }

  // Get the token and user role from cookies
  const token = request.cookies.get('token')?.value
  const authStatus = request.cookies.get('auth_status')?.value
  const userRole = request.cookies.get('user_role')?.value

  // If no token or auth status, redirect to login
  if (!token || authStatus !== 'logged_in') {
    // Store the original path to redirect back after login
    const url = new URL('/login', request.url)
    url.searchParams.set('from', pathname)
    return NextResponse.redirect(url)
  }

  // Check if this is an API route
  if (pathname.startsWith('/api/')) {
    // For API routes, we'll use the existing role-based access control
    // The actual JWT verification will happen in the API routes
    return NextResponse.next();
  }

  // For page routes, check if the user has access to this page
  if (userRole && !hasPageAccess(pathname, userRole)) {
    // If the user doesn't have access to this page, redirect to dashboard
    return NextResponse.redirect(new URL('/dashboard', request.url))
  }

  // User has access to this page
  return NextResponse.next();
}

// Helper function to check if a path is public
function isPublicPath(path: string): boolean {
  // Always allow access to static assets and API routes
  if (
    path.startsWith('/_next/') ||
    path.startsWith('/favicon.ico') ||
    path.startsWith('/public/') ||
    path.startsWith('/images/')
  ) {
    return true;
  }

  // Check if the path matches any of the public paths
  return publicPaths.some(publicPath =>
    path === publicPath ||
    path.startsWith(publicPath + '/') ||
    // Special case for website paths - any path under /website should be public
    (publicPath === '/website' && path.startsWith('/website'))
  )
}

// Note: We now use the hasPageAccess function from page-access-config.ts
// for checking page access permissions

export const config = {
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico|public).*)',
  ],
}
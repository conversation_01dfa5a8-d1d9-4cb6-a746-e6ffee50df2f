import { NextResponse } from 'next/server';
import { prisma, isUsingMockPrisma } from '@/lib/prisma';

export async function GET() {
  try {
    // Check if we're using the mock client
    const usingMock = isUsingMockPrisma();

    // Use safe count operations that won't throw if a model has issues
    const getCounts = async () => {
      return {
        heroSlides: await prisma.heroSlide.count().catch(() => 0),
        announcements: await prisma.announcement.count().catch(() => 0),
        quickLinks: await prisma.quickLink.count().catch(() => 0),
        featuredContent: await prisma.featuredContent.count().catch(() => 0),
        newsEvents: await prisma.newsEvent.count().catch(() => 0),
        academicCalendar: await prisma.academicCalendar?.count().catch(() => 4),
        testimonials: await prisma.testimonial.count().catch(() => 0),
        callToAction: await prisma.callToAction.count().catch(() => 0)
      };
    };

    // Get the counts
    const counts = await getCounts();

    return NextResponse.json({
      status: 'success',
      usingMockClient: usingMock,
      counts,
      message: usingMock
        ? 'Using mock data for website stats'
        : 'Using database data for website stats'
    });
  } catch (error) {
    console.error('Error fetching website stats:', error);

    // Provide fallback counts if there's an error
    const fallbackCounts = {
      heroSlides: 0,
      announcements: 0,
      quickLinks: 0,
      featuredContent: 0,
      newsEvents: 0,
      academicCalendar: 4,
      testimonials: 0,
      callToAction: 0
    };

    return NextResponse.json({
      status: 'error',
      counts: fallbackCounts,
      error: error instanceof Error ? error.message : 'Unknown error',
      suggestion: 'Please check your database connection and ensure the tables exist.',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// Helper function to calculate grade based on marks
function calculateGrade(marks: number): string {
  if (marks >= 90) return 'Excellent'
  if (marks >= 80) return 'Very Good'
  if (marks >= 60) return 'Good'
  if (marks >= 50) return 'Satisfactory'
  return 'Failure'
}

// Helper function to get remarks based on grade
function getRemarks(grade: string): string {
  switch (grade) {
    case 'Excellent': return 'Outstanding performance'
    case 'Very Good': return 'Excellent performance'
    case 'Good': return 'Good performance'
    case 'Satisfactory': return 'Satisfactory performance'
    case 'Failure': return 'Needs significant improvement'
    default: return 'No remarks'
  }
}

// Helper function to get attendance data for a student
async function getStudentAttendance(studentId: string, academicYear: string, semester: string) {
  try {
    // Determine date range based on semester and academic year
    const year = parseInt(academicYear.split('-')[0])
    let startDate: string, endDate: string

    if (semester === 'First Semester') {
      // First semester: September 1st to January 30th (next year)
      startDate = `${year}-09-01`
      endDate = `${year + 1}-01-30`
    } else if (semester === 'Second Semester') {
      // Second semester: February 1st to June 30th (next year)
      startDate = `${year + 1}-02-01`
      endDate = `${year + 1}-06-30`
    } else { // Annual
      // Full academic year: September 1st to June 30th (next year)
      startDate = `${year}-09-01`
      endDate = `${year + 1}-06-30`
    }

    // Count attendance records
    const attendanceRecords = await prisma.attendance.findMany({
      where: {
        studentId: studentId,
        date: {
          gte: startDate,
          lte: endDate
        }
      }
    })

    const totalDays = attendanceRecords.length
    const present = attendanceRecords.filter(record => record.status === 'present').length
    const absent = totalDays - present
    const percentage = totalDays > 0 ? Math.round((present / totalDays) * 100) : 0

    return {
      present,
      absent,
      totalDays,
      percentage
    }
  } catch (error) {
    console.error('Error fetching attendance:', error)
    return {
      present: 0,
      absent: 0,
      totalDays: 0,
      percentage: 0
    }
  }
}

export async function GET(request: Request) {
  try {
    // Get URL parameters
    const { searchParams } = new URL(request.url)
    const studentId = searchParams.get('studentId')
    const className = searchParams.get('className')
    const academicYear = searchParams.get('academicYear') || '2023-2024'
    const semester = searchParams.get('semester') || 'First Semester'
    const reportType = searchParams.get('reportType') || 'student' // 'student' or 'class'

    // Debug log to see what parameters are being received
    console.log('Report Card API Parameters:', {
      studentId,
      className,
      academicYear,
      semester,
      reportType
    })

    // Validate required parameters
    if (reportType === 'student' && !studentId) {
      return NextResponse.json(
        { error: 'Student ID is required for student report' },
        { status: 400 }
      )
    }

    if (reportType === 'class' && !className) {
      return NextResponse.json(
        { error: 'Class name is required for class report' },
        { status: 400 }
      )
    }

    // Build where clause based on provided filters
    const whereClause: any = {}

    // For term/semester filtering
    if (semester === 'Annual') {
      // For annual reports, include both semesters
      whereClause.term = {
        in: ['First Semester', 'Second Semester']
      }
    } else {
      whereClause.term = semester
    }

    // For student or class filtering
    if (reportType === 'student' && studentId) {
      // Check if studentId is a SID (student identifier) or internal ID
      // First, try to find the student by SID
      const student = await prisma.student.findFirst({
        where: {
          OR: [
            { id: studentId },
            { sid: studentId }
          ]
        }
      });

      if (student) {
        console.log('Found student:', student);
        whereClause.studentId = student.id;
      } else {
        console.log('Student not found with ID or SID:', studentId);
        whereClause.studentId = studentId;
      }
    } else if (reportType === 'class' && className) {
      whereClause.className = className
    }

    // Debug log to see what we're querying for
    console.log('Querying marks with where clause:', JSON.stringify(whereClause, null, 2));

    // Fetch marks with related student data
    const marks = await prisma.mark.findMany({
      where: whereClause,
      include: {
        student: {
          select: {
            id: true,
            sid: true,
            name: true,
            className: true,
            fatherName: true,
            gender: true,
            age: true
          }
        }
      },
      orderBy: [
        { studentId: 'asc' },
        { subject: 'asc' }
      ]
    })

    console.log(`Found ${marks.length} marks for the query`);

    // If no marks found, check if the student exists and has any marks at all
    if (marks.length === 0) {
      if (reportType === 'student' && studentId) {
        // Check if student exists
        const student = await prisma.student.findFirst({
          where: {
            OR: [
              { id: studentId },
              { sid: studentId }
            ]
          }
        });

        if (student) {
          // Student exists, check if they have any marks at all
          const anyMarks = await prisma.mark.findFirst({
            where: {
              studentId: student.id
            }
          });

          if (anyMarks) {
            console.log('Student has marks, but none match the semester criteria');
            return NextResponse.json(
              { error: `No marks found for ${student.name} in ${semester}` },
              { status: 404 }
            );
          } else {
            console.log('Student exists but has no marks at all');

            return NextResponse.json(
              { error: `No marks have been recorded for ${student.name}` },
              { status: 404 }
            );
          }
        }
      }

      // Default error for no marks found
      return NextResponse.json(
        { error: 'No marks found for the specified criteria' },
        { status: 404 }
      );
    }

    return processMarks(marks, reportType, marks[0].student, academicYear, semester, className);
  } catch (error) {
    console.error('Error generating report card:', error)
    return NextResponse.json(
      { error: 'Failed to generate report card', details: error.message },
      { status: 500 }
    )
  }
}

// Helper function to process marks data
async function processMarks(marks: any[], reportType: string, student: any, academicYear: string, semester: string, className?: string) {
  try {
    // Process the data based on report type
    if (reportType === 'student') {
      // Get attendance data
      const attendance = await getStudentAttendance(student.id, academicYear, semester)

      // Group marks by subject
      const subjectMarks: Record<string, any> = {}

      for (const mark of marks) {
        const subject = mark.subject
        const term = mark.term

        if (!subjectMarks[subject]) {
          subjectMarks[subject] = {
            name: subject,
            'First Semester': null,
            'Second Semester': null,
            average: 0,
            grade: '',
            remarks: ''
          }
        }

        subjectMarks[subject][term] = mark.marks
      }

      // Calculate averages and grades for each subject
      let totalMarks = 0
      let totalSubjects = 0

      Object.keys(subjectMarks).forEach(subject => {
        const firstSem = subjectMarks[subject]['First Semester'] || 0
        const secondSem = subjectMarks[subject]['Second Semester'] || 0

        if (semester === 'Annual') {
          // For annual reports, calculate average of both semesters
          const validSemesters = (firstSem > 0 ? 1 : 0) + (secondSem > 0 ? 1 : 0)
          const sum = firstSem + secondSem

          if (validSemesters > 0) {
            subjectMarks[subject].average = Math.round(sum / validSemesters)
            subjectMarks[subject].grade = calculateGrade(subjectMarks[subject].average)
            subjectMarks[subject].remarks = getRemarks(subjectMarks[subject].grade)

            totalMarks += subjectMarks[subject].average
            totalSubjects++
          }
        } else {
          // For semester reports, use the semester's marks
          const semMarks = semester === 'First Semester' ? firstSem : secondSem

          if (semMarks > 0) {
            subjectMarks[subject].average = semMarks
            subjectMarks[subject].grade = calculateGrade(semMarks)
            subjectMarks[subject].remarks = getRemarks(subjectMarks[subject].grade)

            totalMarks += semMarks
            totalSubjects++
          }
        }
      })

      // Calculate overall average
      const overallAverage = totalSubjects > 0 ? Math.round(totalMarks / totalSubjects) : 0
      const overallGrade = calculateGrade(overallAverage)

      // Calculate rank within the same class
      const studentRank = await calculateStudentRankInClass(
        student.id,
        student.className,
        academicYear,
        semester,
        totalMarks,
        overallAverage
      )

      // For annual reports, also calculate individual semester ranks
      let firstSemesterRank = null
      let secondSemesterRank = null

      if (semester === 'Annual') {
        // Calculate 1st semester rank
        const firstSemTotalMarks = Object.keys(subjectMarks).reduce((total, subject) => {
          const firstSem = subjectMarks[subject]['First Semester'] || 0
          return firstSem > 0 ? total + firstSem : total
        }, 0)
        const firstSemSubjects = Object.keys(subjectMarks).filter(subject =>
          (subjectMarks[subject]['First Semester'] || 0) > 0
        ).length
        const firstSemAverage = firstSemSubjects > 0 ? Math.round(firstSemTotalMarks / firstSemSubjects) : 0

        if (firstSemSubjects > 0) {
          firstSemesterRank = await calculateStudentRankInClass(
            student.id,
            student.className,
            academicYear,
            'First Semester',
            firstSemTotalMarks,
            firstSemAverage
          )
        }

        // Calculate 2nd semester rank
        const secondSemTotalMarks = Object.keys(subjectMarks).reduce((total, subject) => {
          const secondSem = subjectMarks[subject]['Second Semester'] || 0
          return secondSem > 0 ? total + secondSem : total
        }, 0)
        const secondSemSubjects = Object.keys(subjectMarks).filter(subject =>
          (subjectMarks[subject]['Second Semester'] || 0) > 0
        ).length
        const secondSemAverage = secondSemSubjects > 0 ? Math.round(secondSemTotalMarks / secondSemSubjects) : 0

        if (secondSemSubjects > 0) {
          secondSemesterRank = await calculateStudentRankInClass(
            student.id,
            student.className,
            academicYear,
            'Second Semester',
            secondSemTotalMarks,
            secondSemAverage
          )
        }
      }

      // Format the response
      const studentReport = {
        student: {
          id: student.id,
          sid: student.sid,
          name: student.name,
          className: student.className,
          fatherName: student.fatherName,
          gender: student.gender,
          age: student.age
        },
        academicYear,
        semester,
        subjects: Object.values(subjectMarks),
        summary: {
          totalMarks,
          totalSubjects,
          average: overallAverage,
          grade: overallGrade,
          remarks: getRemarks(overallGrade),
          rank: studentRank,
          firstSemesterRank,
          secondSemesterRank
        },
        attendance
      }

      return NextResponse.json(studentReport)
    } else {
      // For class reports, group by student
      const studentReports: Record<string, any> = {}
      const students: Record<string, any> = {}

      // First, collect all students and their marks
      for (const mark of marks) {
        const studentId = mark.studentId
        const subject = mark.subject
        const term = mark.term

        if (!students[studentId]) {
          students[studentId] = mark.student
        }

        if (!studentReports[studentId]) {
          studentReports[studentId] = {
            studentId,
            subjects: {}
          }
        }

        if (!studentReports[studentId].subjects[subject]) {
          studentReports[studentId].subjects[subject] = {
            name: subject,
            'First Semester': null,
            'Second Semester': null,
            average: 0
          }
        }

        studentReports[studentId].subjects[subject][term] = mark.marks
      }

      // Calculate averages for each student and subject
      const processedReports = []

      for (const studentId in studentReports) {
        const report = studentReports[studentId]
        let totalMarks = 0
        let totalSubjects = 0

        // Calculate subject averages
        for (const subject in report.subjects) {
          const subjectData = report.subjects[subject]
          const firstSem = subjectData['First Semester'] || 0
          const secondSem = subjectData['Second Semester'] || 0

          if (semester === 'Annual') {
            // For annual reports, calculate average of both semesters
            const validSemesters = (firstSem > 0 ? 1 : 0) + (secondSem > 0 ? 1 : 0)
            const sum = firstSem + secondSem

            if (validSemesters > 0) {
              subjectData.average = Math.round(sum / validSemesters)
              totalMarks += subjectData.average
              totalSubjects++
            }
          } else {
            // For semester reports, use the semester's marks
            const semMarks = semester === 'First Semester' ? firstSem : secondSem

            if (semMarks > 0) {
              subjectData.average = semMarks
              totalMarks += semMarks
              totalSubjects++
            }
          }
        }

        // Calculate overall average
        const overallAverage = totalSubjects > 0 ? Math.round(totalMarks / totalSubjects) : 0

        // Get attendance data for this student
        const attendance = await getStudentAttendance(studentId, academicYear, semester)

        // For annual reports, also calculate individual semester ranks
        let firstSemesterRank = null
        let secondSemesterRank = null

        if (semester === 'Annual') {
          // Calculate 1st semester rank
          const firstSemTotalMarks = Object.values(report.subjects).reduce((total: number, subject: any) => {
            const firstSem = subject['First Semester'] || 0
            return firstSem > 0 ? total + firstSem : total
          }, 0)
          const firstSemSubjects = Object.values(report.subjects).filter((subject: any) =>
            (subject['First Semester'] || 0) > 0
          ).length
          const firstSemAverage = firstSemSubjects > 0 ? Math.round(firstSemTotalMarks / firstSemSubjects) : 0

          if (firstSemSubjects > 0) {
            firstSemesterRank = await calculateStudentRankInClass(
              studentId,
              students[studentId].className,
              academicYear,
              'First Semester',
              firstSemTotalMarks,
              firstSemAverage
            )
          }

          // Calculate 2nd semester rank
          const secondSemTotalMarks = Object.values(report.subjects).reduce((total: number, subject: any) => {
            const secondSem = subject['Second Semester'] || 0
            return secondSem > 0 ? total + secondSem : total
          }, 0)
          const secondSemSubjects = Object.values(report.subjects).filter((subject: any) =>
            (subject['Second Semester'] || 0) > 0
          ).length
          const secondSemAverage = secondSemSubjects > 0 ? Math.round(secondSemTotalMarks / secondSemSubjects) : 0

          if (secondSemSubjects > 0) {
            secondSemesterRank = await calculateStudentRankInClass(
              studentId,
              students[studentId].className,
              academicYear,
              'Second Semester',
              secondSemTotalMarks,
              secondSemAverage
            )
          }
        }

        processedReports.push({
          student: students[studentId],
          subjects: Object.values(report.subjects),
          summary: {
            totalMarks,
            totalSubjects,
            average: overallAverage,
            grade: calculateGrade(overallAverage),
            firstSemesterRank,
            secondSemesterRank
          },
          attendance
        })
      }

      // Sort by total marks (descending) first, then by average if total marks are equal
      processedReports.sort((a, b) => {
        // First compare by total marks (with small tolerance for floating point comparison)
        if (Math.abs(b.summary.totalMarks - a.summary.totalMarks) > 0.01) {
          return b.summary.totalMarks - a.summary.totalMarks;
        }
        // If total marks are very close, compare by average
        return b.summary.average - a.summary.average;
      });

      // Add rank to each student with proper logging
      console.log(`Class ${className} ranking for ${semester}:`)
      processedReports.forEach((report, index) => {
        report.summary.rank = index + 1;
        console.log(`${index + 1}. ${report.student.name} (${report.student.sid}) - Total: ${report.summary.totalMarks}, Avg: ${report.summary.average}`)
      });

      return NextResponse.json({
        className,
        academicYear,
        semester,
        students: processedReports
      })
    }
  } catch (error) {
    console.error('Error processing marks:', error);
    throw error;
  }
}

// Helper function to calculate a student's rank within their class
async function calculateStudentRankInClass(
  studentId: string,
  className: string,
  academicYear: string,
  semester: string,
  studentTotalMarks: number,
  studentAverage: number
): Promise<number> {
  try {
    console.log(`Calculating rank for student ${studentId} in class ${className}`)

    // Build where clause for marks query
    const whereClause: any = {}

    // For term/semester filtering
    if (semester === 'Annual') {
      whereClause.term = {
        in: ['First Semester', 'Second Semester']
      }
    } else {
      whereClause.term = semester
    }

    // Get all students in the same class
    const classStudents = await prisma.student.findMany({
      where: {
        className: className
      },
      select: {
        id: true,
        sid: true,
        name: true
      }
    })

    console.log(`Found ${classStudents.length} students in class ${className}`)

    // Get marks for all students in the class
    const allClassMarks = await prisma.mark.findMany({
      where: {
        ...whereClause,
        student: {
          className: className
        }
      },
      include: {
        student: {
          select: {
            id: true,
            sid: true,
            name: true,
            className: true
          }
        }
      },
      orderBy: [
        { studentId: 'asc' },
        { subject: 'asc' },
        { term: 'asc' }
      ]
    })

    console.log(`Found ${allClassMarks.length} marks for class ${className}`)

    // Group marks by student and calculate totals
    const studentPerformance: Record<string, {
      studentId: string,
      totalMarks: number,
      totalSubjects: number,
      average: number,
      student: any
    }> = {}

    // Process marks for each student
    for (const mark of allClassMarks) {
      const sId = mark.studentId

      if (!studentPerformance[sId]) {
        studentPerformance[sId] = {
          studentId: sId,
          totalMarks: 0,
          totalSubjects: 0,
          average: 0,
          student: mark.student
        }
      }

      // For annual reports, we need to handle averaging across semesters
      if (semester === 'Annual') {
        // Group by subject first, then average across semesters
        const subjectKey = `${sId}-${mark.subject}`
        if (!studentPerformance[sId][subjectKey]) {
          studentPerformance[sId][subjectKey] = []
        }
        studentPerformance[sId][subjectKey].push(mark.marks)
      } else {
        // For single semester, just add the marks
        studentPerformance[sId].totalMarks += mark.marks
        studentPerformance[sId].totalSubjects += 1
      }
    }

    // For annual reports, calculate averages per subject then total
    if (semester === 'Annual') {
      for (const sId in studentPerformance) {
        const performance = studentPerformance[sId]
        let totalMarks = 0
        let totalSubjects = 0

        // Calculate average for each subject across semesters
        for (const key in performance) {
          if (key.startsWith(`${sId}-`) && Array.isArray(performance[key])) {
            const subjectMarks = performance[key] as number[]
            const subjectAverage = subjectMarks.reduce((sum, mark) => sum + mark, 0) / subjectMarks.length
            totalMarks += subjectAverage
            totalSubjects += 1
          }
        }

        performance.totalMarks = totalMarks
        performance.totalSubjects = totalSubjects
      }
    }

    // Calculate averages
    for (const sId in studentPerformance) {
      const performance = studentPerformance[sId]
      performance.average = performance.totalSubjects > 0
        ? Math.round(performance.totalMarks / performance.totalSubjects)
        : 0
    }

    // Convert to array and sort by total marks (descending), then by average
    const sortedStudents = Object.values(studentPerformance).sort((a, b) => {
      // First compare by total marks
      if (Math.abs(b.totalMarks - a.totalMarks) > 0.01) {
        return b.totalMarks - a.totalMarks
      }
      // If total marks are very close, compare by average
      return b.average - a.average
    })

    console.log('Class performance ranking:')
    sortedStudents.forEach((student, index) => {
      console.log(`${index + 1}. ${student.student.name} (${student.student.sid}) - Total: ${student.totalMarks.toFixed(2)}, Avg: ${student.average}`)
    })

    // Find the rank of the requested student
    const studentRank = sortedStudents.findIndex(s => s.studentId === studentId) + 1

    console.log(`Student ${studentId} rank in class ${className}: ${studentRank}`)

    return studentRank > 0 ? studentRank : 1

  } catch (error) {
    console.error('Error calculating student rank:', error)
    return 1 // Default to rank 1 if there's an error
  }
}

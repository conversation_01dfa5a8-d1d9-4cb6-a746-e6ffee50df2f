import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET all announcements
export async function GET() {
  try {
    console.log('Fetching all announcements')

    try {
      // Check if the announcement table exists and has data
      const count = await prisma.announcement.count()
      console.log('Total announcements in database:', count)

      const announcements = await prisma.announcement.findMany({
        orderBy: {
          date: 'desc'
        }
      })

      console.log('Successfully fetched announcements:', announcements.length)

      // If no announcements are found, return an empty array instead of null
      return NextResponse.json(announcements || [])
    } catch (dbError: any) {
      console.error('Database error when fetching announcements:', dbError.message)

      // If the table doesn't exist, return mock data
      if (dbError.message.includes('does not exist in the current database')) {
        console.log('Announcement table does not exist yet, returning mock data')

        // Create mock announcements
        const mockAnnouncements = [
          {
            id: 'mock-1',
            title: 'Welcome to Al-Falah School',
            content: 'Welcome to our school management system. This is a sample announcement.',
            date: new Date().toISOString(),
            type: 'important',
            isActive: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          },
          {
            id: 'mock-2',
            title: 'System Update',
            content: 'The system is being updated with new features. Please check back later for more announcements.',
            date: new Date(Date.now() - 86400000).toISOString(), // Yesterday
            type: 'event',
            isActive: true,
            createdAt: new Date(Date.now() - 86400000).toISOString(),
            updatedAt: new Date(Date.now() - 86400000).toISOString()
          }
        ]

        console.log('Returning mock announcements:', mockAnnouncements.length)
        return NextResponse.json(mockAnnouncements)
      }

      // Re-throw the error
      throw dbError
    }
  } catch (error: any) {
    console.error('Error fetching announcements:', error)
    console.error('Error stack:', error.stack)

    // Check if Prisma is connected
    try {
      await prisma.$queryRaw`SELECT 1`
      console.log('Database connection is working')
    } catch (dbError: any) {
      console.error('Database connection error:', dbError.message)
    }

    return NextResponse.json({
      error: error.message || 'Failed to fetch announcements'
    }, { status: 500 })
  }
}

// POST a new announcement
export async function POST(request: Request) {
  try {
    console.log('Creating new announcement')
    const data = await request.json()
    console.log('Received data:', data)

    // Validate required fields
    if (!data.title || !data.content || !data.date || !data.type) {
      console.log('Missing required fields:', {
        title: !!data.title,
        content: !!data.content,
        date: !!data.date,
        type: !!data.type
      })
      return NextResponse.json({
        error: 'All fields are required'
      }, { status: 400 })
    }

    // Validate type
    const validTypes = ['urgent', 'important', 'event']
    if (!validTypes.includes(data.type)) {
      console.log('Invalid type provided:', data.type)
      return NextResponse.json({
        error: 'Type must be one of: urgent, important, event'
      }, { status: 400 })
    }

    // Prepare the data for creation
    const createData = {
      title: data.title,
      content: data.content,
      date: new Date(data.date),
      type: data.type,
      isActive: data.isActive !== undefined ? data.isActive : true
    }
    console.log('Prepared create data:', createData)

    // Try to create the new announcement
    try {
      const announcement = await prisma.announcement.create({
        data: createData
      })

      console.log('Successfully created announcement:', announcement)
      return NextResponse.json(announcement)
    } catch (createError: any) {
      console.error('Error creating announcement:', createError.message)

      // If the table doesn't exist, return a mock announcement
      if (createError.message.includes('does not exist in the current database')) {
        console.log('Announcement table does not exist yet, returning mock data')
        const mockAnnouncement = {
          id: 'mock-' + Date.now(),
          ...createData,
          date: createData.date.toISOString(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }

        console.log('Returning mock announcement:', mockAnnouncement)
        return NextResponse.json(mockAnnouncement)
      }

      // Re-throw the error
      throw createError
    }
  } catch (error: any) {
    console.error('Error creating announcement:', error)
    console.error('Error stack:', error.stack)

    // Check if it's a Prisma error
    if (error.code) {
      console.error('Prisma error code:', error.code)
    }

    return NextResponse.json({
      error: error.message || 'Failed to create announcement'
    }, { status: 500 })
  }
}

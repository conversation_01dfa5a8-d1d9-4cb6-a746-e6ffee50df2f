'use client'

import React, { useState, useEffect } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { Button } from '@/app/components/ui/button'
import { Input } from '@/app/components/ui/input'
import { Label } from '@/app/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/app/components/ui/card'
import { Badge } from '@/app/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/app/components/ui/table'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/app/components/ui/dialog'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/app/components/ui/alert-dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/ui/select'
import { useToast } from '@/app/components/ui/use-toast'
import { Search, UserPlus, Trash2, RefreshCw, Users, AlertCircle, CheckCircle } from 'lucide-react'

interface User {
  id: string
  name: string
  email: string
  role: string
}

interface Student {
  id: string
  sid: string
  name: string
  className: string
  fatherName: string
  age: number
  gender: string
}

interface ParentStudentRelationship {
  id: string
  parentId: string
  studentId: string
  parent: User
  student: Student
  createdAt: string
}

export function ParentStudentLinking() {
  const [isLinkDialogOpen, setIsLinkDialogOpen] = useState(false)
  const [selectedParent, setSelectedParent] = useState<string>('')
  const [studentSid, setStudentSid] = useState('')
  const [foundStudent, setFoundStudent] = useState<Student | null>(null)
  const [isSearching, setIsSearching] = useState(false)
  const [deleteRelationshipId, setDeleteRelationshipId] = useState<string | null>(null)

  // Reset form when dialog closes
  const handleDialogClose = (open: boolean) => {
    setIsLinkDialogOpen(open)
    if (!open) {
      setSelectedParent('')
      setStudentSid('')
      setFoundStudent(null)
    }
  }

  // Cleanup effect to prevent focus conflicts
  useEffect(() => {
    return () => {
      // Cleanup when component unmounts
      setIsLinkDialogOpen(false)
      setDeleteRelationshipId(null)
    }
  }, [])

  const { toast } = useToast()
  const queryClient = useQueryClient()

  // Fetch all parent users
  const { data: parents, isLoading: isLoadingParents } = useQuery({
    queryKey: ['users', 'parents'],
    queryFn: async () => {
      const res = await fetch('/api/users')
      if (!res.ok) throw new Error('Failed to fetch users')
      const users = await res.json()
      return users.filter((user: User) => user.role === 'PARENT')
    }
  })

  // Fetch all parent-student relationships
  const { data: relationships, isLoading: isLoadingRelationships, refetch: refetchRelationships } = useQuery({
    queryKey: ['parent-student-relationships'],
    queryFn: async () => {
      const res = await fetch('/api/parent-students')
      if (!res.ok) throw new Error('Failed to fetch relationships')
      return res.json()
    }
  })

  // Search student by SID
  const searchStudentBySid = async () => {
    if (!studentSid.trim()) {
      toast({
        title: 'Error',
        description: 'Please enter a Student ID (SID)',
        variant: 'destructive'
      })
      return
    }

    setIsSearching(true)
    try {
      const res = await fetch(`/api/students/by-sid?sid=${encodeURIComponent(studentSid)}`)
      const data = await res.json()
      
      if (!res.ok) {
        throw new Error(data.error || 'Student not found')
      }

      setFoundStudent(data.student)
      toast({
        title: 'Success',
        description: 'Student found successfully'
      })
    } catch (error) {
      console.error('Error searching student:', error)
      setFoundStudent(null)
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to find student',
        variant: 'destructive'
      })
    } finally {
      setIsSearching(false)
    }
  }

  // Create parent-student relationship
  const linkMutation = useMutation({
    mutationFn: async (data: { parentId: string; studentId: string }) => {
      const res = await fetch('/api/parent-students', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })
      
      if (!res.ok) {
        const error = await res.json()
        throw new Error(error.error || 'Failed to create relationship')
      }
      
      return res.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['parent-student-relationships'] })
      handleDialogClose(false)
      toast({
        title: 'Success',
        description: 'Parent-student relationship created successfully'
      })
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to create relationship',
        variant: 'destructive'
      })
    }
  })

  // Delete parent-student relationship
  const deleteMutation = useMutation({
    mutationFn: async (relationshipId: string) => {
      const res = await fetch(`/api/parent-students?id=${relationshipId}`, {
        method: 'DELETE'
      })
      
      if (!res.ok) {
        const error = await res.json()
        throw new Error(error.error || 'Failed to delete relationship')
      }
      
      return res.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['parent-student-relationships'] })
      setDeleteRelationshipId(null)
      toast({
        title: 'Success',
        description: 'Parent-student relationship deleted successfully'
      })
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to delete relationship',
        variant: 'destructive'
      })
    }
  })

  const handleCreateLink = () => {
    if (!selectedParent || !foundStudent) {
      toast({
        title: 'Error',
        description: 'Please select a parent and find a student',
        variant: 'destructive'
      })
      return
    }

    linkMutation.mutate({
      parentId: selectedParent,
      studentId: foundStudent.id
    })
  }

  const handleDeleteRelationship = (relationshipId: string) => {
    // Close any open dialogs first to prevent focus conflicts
    setIsLinkDialogOpen(false)
    setDeleteRelationshipId(relationshipId)
  }

  const confirmDelete = () => {
    if (deleteRelationshipId) {
      deleteMutation.mutate(deleteRelationshipId)
    }
  }

  return (
    <div key="parent-student-linking" className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-semibold">Parent-Student Linking</h3>
          <p className="text-sm text-gray-500">Link parents to their children using Student ID (SID)</p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => refetchRelationships()}
            disabled={isLoadingRelationships}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoadingRelationships ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Dialog open={isLinkDialogOpen} onOpenChange={handleDialogClose}>
            <DialogTrigger asChild>
              <Button size="sm">
                <UserPlus className="h-4 w-4 mr-2" />
                Link Parent to Student
              </Button>
            </DialogTrigger>
            <DialogContent
              className="sm:max-w-[500px]"
              onInteractOutside={(e) => e.preventDefault()}
              onEscapeKeyDown={(e) => e.preventDefault()}
            >
              <DialogHeader>
                <DialogTitle>Link Parent to Student</DialogTitle>
                <DialogDescription>
                  Select a parent and search for a student by their SID to create a relationship.
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="parent-select">Select Parent</Label>
                  <Select value={selectedParent} onValueChange={setSelectedParent}>
                    <SelectTrigger>
                      <SelectValue placeholder="Choose a parent user..." />
                    </SelectTrigger>
                    <SelectContent>
                      {parents?.map((parent: User) => (
                        <SelectItem key={parent.id} value={parent.id}>
                          {parent.name} ({parent.email})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="student-sid">Student ID (SID)</Label>
                  <div className="flex gap-2">
                    <Input
                      id="student-sid"
                      placeholder="Enter student SID (e.g., 1A1, 2B3)"
                      value={studentSid}
                      onChange={(e) => setStudentSid(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && searchStudentBySid()}
                    />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={searchStudentBySid}
                      disabled={isSearching}
                    >
                      <Search className={`h-4 w-4 ${isSearching ? 'animate-spin' : ''}`} />
                    </Button>
                  </div>
                </div>

                {foundStudent && (
                  <Card className="bg-green-50 border-green-200">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-sm flex items-center">
                        <CheckCircle className="h-4 w-4 mr-2 text-green-600" />
                        Student Found
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div><strong>Name:</strong> {foundStudent.name}</div>
                        <div><strong>SID:</strong> {foundStudent.sid}</div>
                        <div><strong>Class:</strong> {foundStudent.className}</div>
                        <div><strong>Father:</strong> {foundStudent.fatherName}</div>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>

              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => handleDialogClose(false)}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleCreateLink}
                  disabled={!selectedParent || !foundStudent || linkMutation.isPending}
                >
                  {linkMutation.isPending ? 'Creating...' : 'Create Link'}
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Relationships Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Users className="h-5 w-5 mr-2" />
            Parent-Student Relationships
          </CardTitle>
          <CardDescription>
            Manage existing parent-student relationships
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoadingRelationships ? (
            <div className="flex justify-center items-center py-8">
              <RefreshCw className="h-6 w-6 animate-spin text-gray-400 mr-2" />
              <span>Loading relationships...</span>
            </div>
          ) : relationships?.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Users className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No parent-student relationships found</p>
              <p className="text-sm">Create the first relationship using the button above</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Parent</TableHead>
                  <TableHead>Student</TableHead>
                  <TableHead>Class</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {relationships?.map((relationship: ParentStudentRelationship) => (
                  <TableRow key={relationship.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{relationship.parent.name}</div>
                        <div className="text-sm text-gray-500">{relationship.parent.email}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{relationship.student.name}</div>
                        <div className="text-sm text-gray-500">SID: {relationship.student.sid}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{relationship.student.className}</Badge>
                    </TableCell>
                    <TableCell>
                      {new Date(relationship.createdAt).toLocaleDateString()}
                    </TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteRelationship(relationship.id)}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog
        open={!!deleteRelationshipId}
        onOpenChange={(open) => {
          if (!open) {
            setDeleteRelationshipId(null)
          }
        }}
      >
        <AlertDialogContent
          onInteractOutside={(e) => e.preventDefault()}
          onEscapeKeyDown={(e) => e.preventDefault()}
        >
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center">
              <AlertCircle className="h-5 w-5 mr-2 text-red-600" />
              Delete Relationship
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this parent-student relationship? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              className="bg-red-600 hover:bg-red-700"
              disabled={deleteMutation.isPending}
            >
              {deleteMutation.isPending ? 'Deleting...' : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}

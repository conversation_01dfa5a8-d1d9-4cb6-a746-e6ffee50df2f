import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const className = searchParams.get('className')

    if (!className) {
      return NextResponse.json(
        { error: 'Class name is required' },
        { status: 400 }
      )
    }

    console.log(`Fetching available students for class: ${className}`)

    // Get all students in the specified class
    const students = await prisma.student.findMany({
      where: {
        className: className,
      },
      select: {
        id: true,
        name: true,
        sid: true,
        className: true,
        academicYear: true,
      },
      orderBy: [
        {
          sid: 'asc',
        },
        {
          name: 'asc',
        },
      ],
    })

    console.log(`Found ${students.length} students in class ${className}`)

    return NextResponse.json(students)
  } catch (error) {
    console.error('Error fetching available students:', error)
    return NextResponse.json(
      { error: 'Failed to fetch available students' },
      { status: 500 }
    )
  }
}

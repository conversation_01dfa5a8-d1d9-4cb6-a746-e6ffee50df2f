const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// List of common subjects by grade level
const subjectsByGrade = {
  // Elementary (1-5)
  elementary: [
    'Islamic Education',
    'Arabic',
    'Somali',
    'Amharic',
    'English',
    'Mathematics',
    'General Science',
    'Social Studies',
    'Citizenship',
    'HPE',
    'ICT',
    'CTE',
    'PVT',
  ],
  
  // Middle School (6-8)
  middle: [
    'Islamic Education',
    'Arabic',
    'Somali',
    'Amharic',
    'English',
    'Mathematics',
    'Biology',
    'Chemistry',
    'Physics',
    'Geography',
    'History',
    'Citizenship',
    'HPE',
    'ICT',
    'CTE',
    'PVT',
  ],
  
  // High School (9-12)
  high: [
    'Islamic Education',
    'Arabic',
    'Somali',
    'Amharic',
    'English',
    'Mathematics',
    'Biology',
    'Chemistry',
    'Physics',
    'Geography',
    'History',
    'Economics',
    'Business Studies',
    'Accounting',
    'Computer Science',
    'HPE',
    'CTE',
    'PVT',
  ]
};

// Function to determine grade level from class name
function getGradeLevel(className) {
  // Extract the numeric part from the class name (e.g., "1A" -> "1")
  const gradeMatch = className.match(/\d+/);
  if (!gradeMatch) return 'elementary'; // Default to elementary if no number found
  
  const grade = parseInt(gradeMatch[0]);
  if (grade >= 1 && grade <= 5) return 'elementary';
  if (grade >= 6 && grade <= 8) return 'middle';
  return 'high';
}

async function seedSubjects() {
  try {
    console.log('Starting to seed subjects...');
    
    // Get all classes
    const classes = await prisma.class.findMany();
    console.log(`Found ${classes.length} classes`);
    
    // Track total subjects added
    let totalSubjectsAdded = 0;
    
    // For each class, add appropriate subjects
    for (const cls of classes) {
      console.log(`Processing class ${cls.name}...`);
      
      // Determine grade level
      const gradeLevel = getGradeLevel(cls.name);
      const subjects = subjectsByGrade[gradeLevel];
      
      console.log(`Class ${cls.name} is in grade level: ${gradeLevel}`);
      
      // Check existing subjects for this class
      const existingSubjects = await prisma.subject.findMany({
        where: { classId: cls.id },
        select: { name: true }
      });
      
      const existingSubjectNames = existingSubjects.map(s => s.name);
      console.log(`Class ${cls.name} already has ${existingSubjectNames.length} subjects`);
      
      // Add subjects that don't already exist
      const subjectsToAdd = subjects.filter(s => !existingSubjectNames.includes(s) && 
                                               !existingSubjectNames.includes(`${s} (${cls.name})`));
      
      if (subjectsToAdd.length === 0) {
        console.log(`No new subjects to add for class ${cls.name}`);
        continue;
      }
      
      console.log(`Adding ${subjectsToAdd.length} subjects to class ${cls.name}...`);
      
      // Create subjects for this class
      const createdSubjects = await Promise.all(
        subjectsToAdd.map(subjectName => 
          prisma.subject.create({
            data: {
              name: `${subjectName} (${cls.name})`,
              classId: cls.id
            }
          })
        )
      );
      
      totalSubjectsAdded += createdSubjects.length;
      
      // Update class total subjects count
      await prisma.class.update({
        where: { id: cls.id },
        data: { totalSubjects: existingSubjectNames.length + createdSubjects.length }
      });
      
      console.log(`Added ${createdSubjects.length} subjects to class ${cls.name}`);
    }
    
    console.log(`Seeding completed. Added ${totalSubjectsAdded} subjects in total.`);
  } catch (error) {
    console.error('Error seeding subjects:', error);
  } finally {
    await prisma.$disconnect();
  }
}

seedSubjects()
  .then(() => console.log('Seeding completed successfully'))
  .catch(e => {
    console.error('Error during seeding:', e);
    process.exit(1);
  });

'use client'

import React, { useState, useEffect, useRef } from 'react'
import dynamic from 'next/dynamic'
import Sidebar from '../../components/Sidebar'
import { Card } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Label } from '../../components/ui/label'
import { Checkbox } from '../../components/ui/checkbox'
import {
  Table, TableBody, TableCaption, TableCell,
  TableHead, TableHeader, TableRow
} from '../../components/ui/table'
import {
  RecursionSafeDialog,
  RecursionSafeDialogContent,
  RecursionSafeDialogHeader,
  RecursionSafeDialogTitle,
  RecursionSafeDialogDescription,
  RecursionSafeDialogFooter
} from '../../components/RecursionSafeDialog'
import {
  DropdownMenu, DropdownMenuContent, DropdownMenuItem,
  DropdownMenuTrigger
} from '../../components/ui/dropdown-menu'
import {
  Globe, ImageIcon, Plus, MoreHorizontal, Pencil, Trash2,
  ArrowUp, ArrowDown, Eye, EyeOff, AlertCircle, Upload
} from 'lucide-react'
import { useQuery, useMutation, QueryClient, QueryClientProvider } from '@tanstack/react-query'
import Link from 'next/link'
import { useToast } from '../../components/ui/use-toast'
import { Toaster } from '../../components/ui/toaster'

// Create a client
const queryClient = new QueryClient()

// Import Header component with no SSR to avoid hydration issues
const Header = dynamic(() => import('../../components/Header'), {
  ssr: false
})

// Type definition for hero slide
interface HeroSlide {
  id: string;
  title: string;
  subtitle: string;
  imageUrl: string;
  ctaText: string;
  ctaLink: string;
  isActive: boolean;
  order: number;
  createdAt: string;
  updatedAt: string;
}

function HeroSlidesManagement() {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false)
  const [isMounted, setIsMounted] = useState(false)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [currentSlide, setCurrentSlide] = useState<HeroSlide | null>(null)
  const [imageFile, setImageFile] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [formData, setFormData] = useState({
    title: '',
    subtitle: '',
    imageUrl: '',
    ctaText: '',
    ctaLink: '',
    isActive: true
  })
  const { toast } = useToast()

  // Fetch hero slides from the API
  const { data: heroSlides, isLoading, refetch } = useQuery<HeroSlide[]>({
    queryKey: ['heroSlides'],
    queryFn: async () => {
      try {
        const res = await fetch('/api/website-management/hero-slides')
        if (!res.ok) {
          throw new Error('Failed to fetch hero slides')
        }
        return res.json()
      } catch (error) {
        // If API doesn't exist yet, return mock data
        return [
          {
            id: '1',
            title: 'Welcome to Alfalah Islamic School',
            subtitle: 'Nurturing minds, enriching souls, building futures',
            imageUrl: '/images/banner.jpg',
            ctaText: 'Apply Now',
            ctaLink: '/dashboard',
            isActive: true,
            order: 0,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          },
          {
            id: '2',
            title: 'Academic Excellence',
            subtitle: 'Providing quality education with Islamic values since 1995',
            imageUrl: '/images/banner2.jpg',
            ctaText: 'Learn More',
            ctaLink: '/website/academics',
            isActive: true,
            order: 1,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          },
          {
            id: '3',
            title: 'Join Our Community',
            subtitle: 'A supportive environment for students to thrive',
            imageUrl: '/images/banner3.jpg',
            ctaText: 'Contact Us',
            ctaLink: '/website/contact',
            isActive: true,
            order: 2,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }
        ]
      }
    },
    staleTime: 60000, // 1 minute
    refetchOnWindowFocus: false,
  })

  // Add hero slide mutation
  const addSlideMutation = useMutation({
    mutationFn: async (newSlide: Omit<HeroSlide, 'id' | 'createdAt' | 'updatedAt' | 'order'>) => {
      const res = await fetch('/api/website-management/hero-slides', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newSlide),
      })

      if (!res.ok) {
        const error = await res.json()
        throw new Error(error.message || 'Failed to add hero slide')
      }

      return res.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['heroSlides'] })
      queryClient.invalidateQueries({ queryKey: ['websiteStats'] })
      setIsAddDialogOpen(false)
      resetForm()
      toast({
        title: 'Success',
        description: 'Hero slide added successfully',
        variant: 'default',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to add hero slide',
        variant: 'destructive',
      })
    }
  })

  // Update hero slide mutation
  const updateSlideMutation = useMutation({
    mutationFn: async (updatedSlide: Partial<HeroSlide> & { id: string }) => {
      const res = await fetch(`/api/website-management/hero-slides/${updatedSlide.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedSlide),
      })

      if (!res.ok) {
        const error = await res.json()
        throw new Error(error.message || 'Failed to update hero slide')
      }

      return res.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['heroSlides'] })
      setIsEditDialogOpen(false)
      resetForm()
      toast({
        title: 'Success',
        description: 'Hero slide updated successfully',
        variant: 'default',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update hero slide',
        variant: 'destructive',
      })
    }
  })

  // Delete hero slide mutation
  const deleteSlideMutation = useMutation({
    mutationFn: async (id: string) => {
      const res = await fetch(`/api/website-management/hero-slides/${id}`, {
        method: 'DELETE',
      })

      if (!res.ok) {
        const error = await res.json()
        throw new Error(error.message || 'Failed to delete hero slide')
      }

      return res.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['heroSlides'] })
      queryClient.invalidateQueries({ queryKey: ['websiteStats'] })
      setIsDeleteDialogOpen(false)
      toast({
        title: 'Success',
        description: 'Hero slide deleted successfully',
        variant: 'default',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete hero slide',
        variant: 'destructive',
      })
    }
  })

  useEffect(() => {
    setIsMounted(true)
  }, [])

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  const toggleMobileSidebar = () => {
    setMobileSidebarOpen(!mobileSidebarOpen)
  }

  const resetForm = () => {
    setFormData({
      title: '',
      subtitle: '',
      imageUrl: '',
      ctaText: '',
      ctaLink: '',
      isActive: true
    })
    setCurrentSlide(null)
    setImageFile(null)
    setImagePreview(null)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleCheckboxChange = (checked: boolean) => {
    setFormData(prev => ({ ...prev, isActive: checked }))
  }

  // Handle image upload
  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || !e.target.files[0]) return;

    const file = e.target.files[0];

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast({
        title: 'Invalid File',
        description: 'Please upload an image file',
        variant: 'destructive',
      });
      return;
    }

    // Set the file for reference
    setImageFile(file);

    // Create preview immediately for better UX
    const reader = new FileReader();
    reader.onload = (event) => {
      if (event.target?.result) {
        setImagePreview(event.target.result as string);
      }
    };
    reader.readAsDataURL(file);

    // Upload the file to the server
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('/api/website-management/upload-image', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: `HTTP error ${response.status}` }));
        throw new Error(errorData.error || 'Failed to upload image');
      }

      const data = await response.json();

      // Update the form data with the uploaded image URL
      setFormData(prev => ({ ...prev, imageUrl: data.url }));

      toast({
        title: 'Success',
        description: 'Image uploaded successfully',
        variant: 'default',
      });
    } catch (error) {
      console.error('Error uploading image:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to upload image',
        variant: 'destructive',
      });
    }
  };

  const handleAddSlide = () => {
    addSlideMutation.mutate(formData)
  }

  const handleUpdateSlide = () => {
    if (!currentSlide) return

    updateSlideMutation.mutate({
      id: currentSlide.id,
      ...formData
    })
  }

  const handleDeleteSlide = () => {
    if (!currentSlide) return

    deleteSlideMutation.mutate(currentSlide.id)
  }

  const openEditDialog = (slide: HeroSlide) => {
    setCurrentSlide(slide)
    setFormData({
      title: slide.title,
      subtitle: slide.subtitle,
      imageUrl: slide.imageUrl,
      ctaText: slide.ctaText,
      ctaLink: slide.ctaLink,
      isActive: slide.isActive
    })
    // Set the image preview to the existing image URL
    setImagePreview(slide.imageUrl)
    // Reset the image file since we're using an existing image
    setImageFile(null)
    setIsEditDialogOpen(true)
  }

  const openDeleteDialog = (slide: HeroSlide) => {
    setCurrentSlide(slide)
    setIsDeleteDialogOpen(true)
  }

  // Only show loading while mounting
  if (!isMounted || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-600"></div>
      </div>
    )
  }

  return (
    <div className="flex h-screen bg-gray-100 dark:bg-gray-900">
      <Sidebar
        isCollapsed={sidebarCollapsed}
        toggleSidebar={toggleSidebar}
        isMobileOpen={mobileSidebarOpen}
      />

      <main className={`main-content ${sidebarCollapsed ? 'sidebar-collapsed' : ''} w-full h-screen overflow-y-auto`}>
        <Header toggleMobileSidebar={toggleMobileSidebar} />
        <Toaster />

        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-2xl font-semibold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent inline-block">
                Hero Slides Management
              </h1>
              <p className="text-gray-600 dark:text-gray-300 mt-1">
                Manage the banner slides displayed on the homepage
              </p>
            </div>
            <div className="flex space-x-2">
              <Link href="/website" target="_blank">
                <Button variant="outline" size="sm" className="flex items-center">
                  <Globe className="h-4 w-4 mr-1" />
                  <span>View Website</span>
                </Button>
              </Link>
              <Button onClick={() => setIsAddDialogOpen(true)} className="flex items-center">
                <Plus className="h-4 w-4 mr-1" />
                <span>Add Slide</span>
              </Button>
            </div>
          </div>

          <Card className="overflow-hidden border-0 shadow-md">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[80px]">Order</TableHead>
                  <TableHead className="w-[100px]">Image</TableHead>
                  <TableHead>Title</TableHead>
                  <TableHead>Subtitle</TableHead>
                  <TableHead>CTA</TableHead>
                  <TableHead className="w-[100px]">Status</TableHead>
                  <TableHead className="w-[100px] text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {heroSlides && heroSlides.length > 0 ? (
                  heroSlides.map((slide) => (
                    <TableRow key={slide.id}>
                      <TableCell className="font-medium">{slide.order + 1}</TableCell>
                      <TableCell>
                        <div className="relative h-12 w-20 rounded overflow-hidden">
                          <img
                            src={slide.imageUrl}
                            alt={slide.title}
                            className="object-cover w-full h-full"
                          />
                        </div>
                      </TableCell>
                      <TableCell>{slide.title}</TableCell>
                      <TableCell className="max-w-[200px] truncate">{slide.subtitle}</TableCell>
                      <TableCell>{slide.ctaText}</TableCell>
                      <TableCell>
                        {slide.isActive ? (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                            Active
                          </span>
                        ) : (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                            Inactive
                          </span>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">Open menu</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => openEditDialog(slide)}>
                              <Pencil className="h-4 w-4 mr-2" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => openDeleteDialog(slide)}>
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => updateSlideMutation.mutate({ id: slide.id, isActive: !slide.isActive })}>
                              {slide.isActive ? (
                                <>
                                  <EyeOff className="h-4 w-4 mr-2" />
                                  Deactivate
                                </>
                              ) : (
                                <>
                                  <Eye className="h-4 w-4 mr-2" />
                                  Activate
                                </>
                              )}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      <div className="flex flex-col items-center justify-center text-gray-500 dark:text-gray-400">
                        <ImageIcon className="h-12 w-12 mb-2 opacity-50" />
                        <p>No hero slides found</p>
                        <Button
                          variant="link"
                          onClick={() => setIsAddDialogOpen(true)}
                          className="mt-2"
                        >
                          Add your first slide
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </Card>
        </div>
      </main>

      {/* Add Slide Dialog */}
      <RecursionSafeDialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen} maxWidth="max-w-[600px]">
        <RecursionSafeDialogHeader>
          <RecursionSafeDialogTitle>Add New Hero Slide</RecursionSafeDialogTitle>
          <RecursionSafeDialogDescription>
            Create a new slide for the homepage banner. All fields are required.
          </RecursionSafeDialogDescription>
        </RecursionSafeDialogHeader>
        <RecursionSafeDialogContent>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="title">Title</Label>
                <Input
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  placeholder="Enter slide title"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="subtitle">Subtitle</Label>
                <Input
                  id="subtitle"
                  name="subtitle"
                  value={formData.subtitle}
                  onChange={handleInputChange}
                  placeholder="Enter slide subtitle"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="image">Post Image</Label>
              <div className="flex items-center gap-4">
                <div className="relative h-20 w-32 rounded overflow-hidden bg-gray-200">
                  {imagePreview ? (
                    <img
                      src={imagePreview}
                      alt="Preview"
                      className="h-full w-full object-cover"
                    />
                  ) : (
                    <div className="h-full w-full flex items-center justify-center bg-blue-100 text-blue-600">
                      <ImageIcon className="h-6 w-6" />
                    </div>
                  )}
                </div>
                <div className="flex-1">
                  <Input
                    id="image"
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    ref={fileInputRef}
                    className="hidden"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => fileInputRef.current?.click()}
                    className="w-full"
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    Upload Image
                  </Button>
                  <p className="text-xs text-gray-500 mt-2">
                    Recommended image size: 1920x800 pixels
                  </p>
                </div>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="ctaText">Button Text</Label>
                <Input
                  id="ctaText"
                  name="ctaText"
                  value={formData.ctaText}
                  onChange={handleInputChange}
                  placeholder="Enter button text"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="ctaLink">Button Link</Label>
                <Input
                  id="ctaLink"
                  name="ctaLink"
                  value={formData.ctaLink}
                  onChange={handleInputChange}
                  placeholder="Enter button link"
                />
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="isActive"
                checked={formData.isActive}
                onCheckedChange={handleCheckboxChange}
              />
              <Label htmlFor="isActive">Active</Label>
            </div>
          </div>
        </RecursionSafeDialogContent>
        <RecursionSafeDialogFooter>
          <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
            Cancel
          </Button>
          <Button onClick={handleAddSlide} disabled={addSlideMutation.isPending}>
            {addSlideMutation.isPending ? 'Adding...' : 'Add Slide'}
          </Button>
        </RecursionSafeDialogFooter>
      </RecursionSafeDialog>

      {/* Edit Slide Dialog */}
      <RecursionSafeDialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen} maxWidth="max-w-[600px]">
        <RecursionSafeDialogHeader>
          <RecursionSafeDialogTitle>Edit Hero Slide</RecursionSafeDialogTitle>
          <RecursionSafeDialogDescription>
            Update the slide information. All fields are required.
          </RecursionSafeDialogDescription>
        </RecursionSafeDialogHeader>
        <RecursionSafeDialogContent>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-title">Title</Label>
                <Input
                  id="edit-title"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  placeholder="Enter slide title"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-subtitle">Subtitle</Label>
                <Input
                  id="edit-subtitle"
                  name="subtitle"
                  value={formData.subtitle}
                  onChange={handleInputChange}
                  placeholder="Enter slide subtitle"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-image">Post Image</Label>
              <div className="flex items-center gap-4">
                <div className="relative h-20 w-32 rounded overflow-hidden bg-gray-200">
                  {imagePreview ? (
                    <img
                      src={imagePreview}
                      alt="Preview"
                      className="h-full w-full object-cover"
                    />
                  ) : (
                    <div className="h-full w-full flex items-center justify-center bg-blue-100 text-blue-600">
                      <ImageIcon className="h-6 w-6" />
                    </div>
                  )}
                </div>
                <div className="flex-1">
                  <Input
                    id="edit-image"
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    ref={fileInputRef}
                    className="hidden"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => fileInputRef.current?.click()}
                    className="w-full"
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    Upload New Image
                  </Button>
                  <p className="text-xs text-gray-500 mt-2">
                    Recommended image size: 1920x800 pixels
                  </p>
                </div>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-ctaText">Button Text</Label>
                <Input
                  id="edit-ctaText"
                  name="ctaText"
                  value={formData.ctaText}
                  onChange={handleInputChange}
                  placeholder="Enter button text"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-ctaLink">Button Link</Label>
                <Input
                  id="edit-ctaLink"
                  name="ctaLink"
                  value={formData.ctaLink}
                  onChange={handleInputChange}
                  placeholder="Enter button link"
                />
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="edit-isActive"
                checked={formData.isActive}
                onCheckedChange={handleCheckboxChange}
              />
              <Label htmlFor="edit-isActive">Active</Label>
            </div>
          </div>
        </RecursionSafeDialogContent>
        <RecursionSafeDialogFooter>
          <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
            Cancel
          </Button>
          <Button onClick={handleUpdateSlide} disabled={updateSlideMutation.isPending}>
            {updateSlideMutation.isPending ? 'Updating...' : 'Update Slide'}
          </Button>
        </RecursionSafeDialogFooter>
      </RecursionSafeDialog>

      {/* Delete Confirmation Dialog */}
      <RecursionSafeDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen} maxWidth="max-w-[425px]">
        <RecursionSafeDialogHeader>
          <RecursionSafeDialogTitle>Confirm Deletion</RecursionSafeDialogTitle>
          <RecursionSafeDialogDescription>
            Are you sure you want to delete this hero slide? This action cannot be undone.
          </RecursionSafeDialogDescription>
        </RecursionSafeDialogHeader>
        <RecursionSafeDialogContent>
          <div className="flex items-center space-x-2 py-4 text-amber-600 dark:text-amber-400">
            <AlertCircle className="h-5 w-5" />
            <p className="text-sm">This will permanently remove the slide from the website.</p>
          </div>
        </RecursionSafeDialogContent>
        <RecursionSafeDialogFooter>
          <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleDeleteSlide}
            disabled={deleteSlideMutation.isPending}
          >
            {deleteSlideMutation.isPending ? 'Deleting...' : 'Delete Slide'}
          </Button>
        </RecursionSafeDialogFooter>
      </RecursionSafeDialog>

      {/* Overlay for mobile menu */}
      {mobileSidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 md:hidden z-40"
          onClick={toggleMobileSidebar}
        ></div>
      )}
    </div>
  )
}

// Export a wrapper component that provides the QueryClient
export default function HeroSlidesManagementPage() {
  return (
    <QueryClientProvider client={queryClient}>
      <HeroSlidesManagement />
    </QueryClientProvider>
  )
}

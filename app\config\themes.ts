export type ThemeColor = 'red' | 'blue' | 'green';
export type ThemeMode = 'light' | 'dark';

export interface ThemeColors {
  primary: string;
  secondary: string;
  background: string;
  text: string;
  accent: string;
}

type ThemeConfig = {
  [key in ThemeColor]: {
    [mode in ThemeMode]: ThemeColors;
  };
};

export const themes: ThemeConfig = {
  red: {
    light: {
      primary: '#ff4d4d',
      secondary: '#ff8080',
      background: '#ffffff',
      text: '#333333',
      accent: '#ff1a1a'
    },
    dark: {
      primary: '#cc0000',
      secondary: '#ff4d4d',
      background: '#1a1a1a',
      text: '#ffffff',
      accent: '#ff1a1a'
    }
  },
  blue: {
    light: {
      primary: '#4d4dff',
      secondary: '#8080ff',
      background: '#ffffff',
      text: '#333333',
      accent: '#1a1aff'
    },
    dark: {
      primary: '#0000cc',
      secondary: '#4d4dff',
      background: '#1a1a1a',
      text: '#ffffff',
      accent: '#1a1aff'
    }
  },
  green: {
    light: {
      primary: '#4dff4d',
      secondary: '#80ff80',
      background: '#ffffff',
      text: '#333333',
      accent: '#1aff1a'
    },
    dark: {
      primary: '#00cc00',
      secondary: '#4dff4d',
      background: '#1a1a1a',
      text: '#ffffff',
      accent: '#1aff1a'
    }
  }
}; 
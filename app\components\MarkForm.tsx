'use client'

import { useState, useEffect } from 'react'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/app/components/ui/select'
import { Button } from '@/app/components/ui/button'
import { Input } from '@/app/components/ui/input'
import { Label } from '@/app/components/ui/label'
import { Textarea } from '@/app/components/ui/textarea'
import { Loader2, Users, BookOpen, UserCheck, GraduationCap } from 'lucide-react'
import { toast } from '@/app/components/ui/use-toast'

// Types based on Prisma schema
interface Class {
  id: string
  name: string
  totalStudents: number
  totalSubjects: number
  hasSubjects: boolean
}

interface Subject {
  id: string
  name: string
  classId: string
}

interface Student {
  id: string
  name: string
  sid: string
  className: string
  academicYear: string
}

interface MarkFormProps {
  isEditMode?: boolean
  initialData?: any
  onSubmit: (markData: any) => Promise<void>
  onCancel: () => void
  currentClass?: string
  currentSubject?: string
}

// Academic years and terms based on Prisma schema
const academicYears = [
  '2020-2021', '2021-2022', '2022-2023', '2023-2024',
  '2024-2025', '2025-2026', '2026-2027', '2027-2028'
]

const terms = [
  'First Semester',
  'Second Semester'
]

export function MarkForm({
  isEditMode = false,
  initialData,
  onSubmit,
  onCancel,
  currentClass,
  currentSubject
}: MarkFormProps) {
  // Form data state matching Prisma Mark model
  const [formData, setFormData] = useState({
    id: '',
    studentId: '',
    studentName: '',
    className: currentClass || '',
    subject: currentSubject || '',
    term: 'First Semester',
    academicYear: '2024-2025',
    totalMarks: 100,
    marks: 0, // Using 'marks' to match Prisma schema
    remarks: ''
  })

  const [isSubmitting, setIsSubmitting] = useState(false)

  // Simple state for data
  const [classes, setClasses] = useState<Class[]>([])
  const [subjects, setSubjects] = useState<Subject[]>([])
  const [students, setStudents] = useState<Student[]>([])
  const [isLoadingClasses, setIsLoadingClasses] = useState(false)
  const [isLoadingSubjects, setIsLoadingSubjects] = useState(false)
  const [isLoadingStudents, setIsLoadingStudents] = useState(false)

  // Fetch classes
  const fetchClasses = async () => {
    setIsLoadingClasses(true)
    try {
      const res = await fetch('/api/classes/for-marks')
      if (res.ok) {
        const data = await res.json()
        setClasses(data)
      }
    } catch (error) {
      console.error('Error fetching classes:', error)
    } finally {
      setIsLoadingClasses(false)
    }
  }

  // Fetch subjects for selected class
  const fetchSubjects = async (className: string) => {
    if (!className) {
      setSubjects([])
      return
    }
    setIsLoadingSubjects(true)
    try {
      const res = await fetch(`/api/subjects/for-class?className=${className}`)
      if (res.ok) {
        const data = await res.json()
        setSubjects(data)
      } else {
        setSubjects([])
      }
    } catch (error) {
      console.error('Error fetching subjects:', error)
      setSubjects([])
    } finally {
      setIsLoadingSubjects(false)
    }
  }

  // Fetch students for selected class
  const fetchStudents = async (className: string) => {
    if (!className) {
      setStudents([])
      return
    }
    setIsLoadingStudents(true)
    try {
      const res = await fetch(`/api/students?class=${className}`)
      if (res.ok) {
        const data = await res.json()
        setStudents(data)
      } else {
        setStudents([])
      }
    } catch (error) {
      console.error('Error fetching students:', error)
      setStudents([])
    } finally {
      setIsLoadingStudents(false)
    }
  }

  // Load initial data
  useEffect(() => {
    fetchClasses()
  }, [])

  // Initialize form with initial data if editing
  useEffect(() => {
    if (isEditMode && initialData) {
      setFormData(prev => ({
        ...prev,
        id: initialData.id || '',
        studentId: initialData.studentId || '',
        studentName: initialData.studentName || '',
        className: initialData.className || initialData.class || currentClass || '',
        subject: initialData.subject || currentSubject || '',
        term: initialData.term || 'First Semester',
        academicYear: initialData.academicYear || '2024-2025',
        totalMarks: initialData.totalMarks || 100,
        marks: initialData.marks || initialData.obtainedMarks || 0,
        remarks: initialData.remarks || ''
      }))
    }
  }, [isEditMode, initialData?.id, currentClass, currentSubject])

  // Handle class change
  useEffect(() => {
    if (formData.className) {
      fetchSubjects(formData.className)
      fetchStudents(formData.className)
    }
  }, [formData.className])

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: name === 'totalMarks' || name === 'marks'
        ? parseInt(value) || 0
        : value
    }))
  }

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))

    // Clear student when class changes
    if (name === 'className') {
      setFormData(prev => ({
        ...prev,
        studentId: '',
        studentName: ''
      }))
    }
  }

  // Handle student selection
  const handleStudentSelect = (studentId: string) => {
    const selectedStudent = students.find(s => s.id === studentId)
    if (selectedStudent) {
      setFormData(prev => ({
        ...prev,
        studentId: selectedStudent.id,
        studentName: selectedStudent.name
      }))
    }
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validate required fields
    if (!formData.studentId || !formData.className || !formData.subject || !formData.term) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields",
        variant: "destructive"
      })
      return
    }

    if (formData.marks > formData.totalMarks) {
      toast({
        title: "Validation Error", 
        description: "Obtained marks cannot be greater than total marks",
        variant: "destructive"
      })
      return
    }

    setIsSubmitting(true)
    try {
      // Prepare data for submission matching Prisma schema
      const markData = {
        ...formData,
        // Ensure we use the correct field names for the API
        obtainedMarks: formData.marks, // For backward compatibility
        class: formData.className // For backward compatibility
      }

      await onSubmit(markData)
      
      toast({
        title: "Success",
        description: isEditMode ? "Mark updated successfully" : "Mark added successfully"
      })
    } catch (error) {
      console.error('Error submitting mark:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to save mark",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Class and Student Selection */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Class */}
        <div className="grid gap-2">
          <Label htmlFor="className" className="text-sm font-medium">
            Class <span className="text-red-500">*</span>
          </Label>
          <Select
            value={formData.className}
            onValueChange={(value) => handleSelectChange('className', value)}
            disabled={isLoadingClasses}
          >
            <SelectTrigger>
              {isLoadingClasses ? (
                <div className="flex items-center">
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Loading classes...
                </div>
              ) : (
                <>
                  <Users className="mr-2 h-4 w-4 text-gray-400" />
                  <SelectValue placeholder="Select class" />
                </>
              )}
            </SelectTrigger>
            <SelectContent>
              {classes && classes.length > 0 ? (
                classes.map((cls) => (
                  <SelectItem key={cls.id} value={cls.name}>
                    <div className="flex items-center justify-between w-full">
                      <span>{cls.name}</span>
                      <span className="text-xs text-gray-500 ml-2">
                        {cls.totalSubjects} subjects
                      </span>
                    </div>
                  </SelectItem>
                ))
              ) : (
                <div className="p-2 text-center text-sm text-gray-500">
                  No classes found
                </div>
              )}
            </SelectContent>
          </Select>
        </div>

        {/* Student */}
        <div className="grid gap-2">
          <Label htmlFor="studentId" className="text-sm font-medium">
            Student <span className="text-red-500">*</span>
          </Label>
          <Select
            value={formData.studentId}
            onValueChange={handleStudentSelect}
            disabled={!formData.className || isLoadingStudents}
          >
            <SelectTrigger>
              {isLoadingStudents ? (
                <div className="flex items-center">
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Loading students...
                </div>
              ) : (
                <>
                  <UserCheck className="mr-2 h-4 w-4 text-gray-400" />
                  <SelectValue placeholder="Select student" />
                </>
              )}
            </SelectTrigger>
            <SelectContent>
              {students && students.length > 0 ? (
                students.map((student) => (
                  <SelectItem key={student.id} value={student.id}>
                    <div className="flex items-center justify-between w-full">
                      <span>{student.name}</span>
                      <span className="text-xs text-gray-500 ml-2 font-mono">
                        {student.sid}
                      </span>
                    </div>
                  </SelectItem>
                ))
              ) : (
                <div className="p-2 text-center text-sm text-gray-500">
                  {formData.className ? 'No students found in this class' : 'Select a class first'}
                </div>
              )}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Subject and Term */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Subject */}
        <div className="grid gap-2">
          <Label htmlFor="subject" className="text-sm font-medium">
            Subject <span className="text-red-500">*</span>
          </Label>
          <Select
            value={formData.subject}
            onValueChange={(value) => handleSelectChange('subject', value)}
            disabled={!formData.className || isLoadingSubjects}
          >
            <SelectTrigger>
              {isLoadingSubjects ? (
                <div className="flex items-center">
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Loading subjects...
                </div>
              ) : (
                <>
                  <BookOpen className="mr-2 h-4 w-4 text-gray-400" />
                  <SelectValue placeholder="Select subject" />
                </>
              )}
            </SelectTrigger>
            <SelectContent>
              {subjects && subjects.length > 0 ? (
                subjects.map((subject) => (
                  <SelectItem key={subject.id} value={subject.name}>
                    {subject.name}
                  </SelectItem>
                ))
              ) : (
                <div className="p-2 text-center text-sm text-gray-500">
                  {formData.className 
                    ? 'No subjects assigned to this class. Please add subjects in Class Management first.' 
                    : 'Select a class first'
                  }
                </div>
              )}
            </SelectContent>
          </Select>
        </div>

        {/* Semester */}
        <div className="grid gap-2">
          <Label htmlFor="term" className="text-sm font-medium">
            Semester <span className="text-red-500">*</span>
          </Label>
          <Select
            value={formData.term}
            onValueChange={(value) => handleSelectChange('term', value)}
          >
            <SelectTrigger>
              <GraduationCap className="mr-2 h-4 w-4 text-gray-400" />
              <SelectValue placeholder="Select semester" />
            </SelectTrigger>
            <SelectContent>
              {terms.map(term => (
                <SelectItem key={term} value={term}>{term}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Academic Year */}
      <div className="grid grid-cols-1 gap-4">
        <div className="grid gap-2">
          <Label htmlFor="academicYear" className="text-sm font-medium">
            Academic Year <span className="text-red-500">*</span>
          </Label>
          <Select
            value={formData.academicYear}
            onValueChange={(value) => handleSelectChange('academicYear', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select academic year" />
            </SelectTrigger>
            <SelectContent>
              {academicYears.map(year => (
                <SelectItem key={year} value={year}>{year}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Marks */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="grid gap-2">
          <Label htmlFor="totalMarks" className="text-sm font-medium">
            Total Marks <span className="text-red-500">*</span>
          </Label>
          <Input
            id="totalMarks"
            name="totalMarks"
            type="number"
            min="1"
            max="1000"
            value={formData.totalMarks}
            onChange={handleInputChange}
            required
          />
        </div>

        <div className="grid gap-2">
          <Label htmlFor="marks" className="text-sm font-medium">
            Obtained Marks <span className="text-red-500">*</span>
          </Label>
          <Input
            id="marks"
            name="marks"
            type="number"
            min="0"
            max={formData.totalMarks}
            value={formData.marks}
            onChange={handleInputChange}
            required
          />
        </div>
      </div>

      {/* Remarks */}
      <div className="grid gap-2">
        <Label htmlFor="remarks" className="text-sm font-medium">
          Remarks (Optional)
        </Label>
        <Textarea
          id="remarks"
          name="remarks"
          value={formData.remarks}
          onChange={handleInputChange}
          placeholder="Enter any remarks..."
          rows={3}
        />
      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-2 pt-4">
        <Button variant="outline" type="button" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? (
            <div className="flex items-center">
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {isEditMode ? 'Updating...' : 'Adding...'}
            </div>
          ) : (
            isEditMode ? 'Update Mark' : 'Add Mark'
          )}
        </Button>
      </div>
    </form>
  )
}

"use client";

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { Button } from '@/app/components/ui/button';
import { useRouter } from 'next/navigation';

// Define the feature interface
interface Feature {
  title: string;
  description: string;
}

// Define the featured content interface
interface FeaturedContent {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  features: Feature[];
  isActive: boolean;
}

// Default fallback image URL
const FALLBACK_IMAGE_URL = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9IiMwMDdiZmYiLz48dGV4dCB4PSI1MCUiIHk9IjUwJSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjI0IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkZlYXR1cmVkIEltYWdlPC90ZXh0Pjwvc3ZnPg==';

// Fallback featured content in case API fails
const fallbackFeaturedContent = {
  id: '1',
  title: 'Why Choose Alfalah Islamic School?',
  description: 'At Alfalah Islamic School, we provide a unique educational experience that combines academic excellence with Islamic values. Our comprehensive approach to education nurtures both the mind and soul, preparing students for success in this world and the hereafter.',
  imageUrl: '/images/banner4.jpg',
  features: [
    {
      title: 'Academic Excellence',
      description: 'Rigorous curriculum designed to challenge and inspire students to reach their full potential.'
    },
    {
      title: 'Islamic Values',
      description: 'Integration of Islamic principles and values throughout the educational experience.'
    },
    {
      title: 'Qualified Teachers',
      description: 'Dedicated educators who are experts in their fields and committed to student success.'
    },
    {
      title: 'Supportive Community',
      description: 'A welcoming environment where students feel valued, supported, and encouraged.'
    }
  ],
  isActive: true
};

// Color classes for feature titles
const featureColors = [
  'text-blue-600',
  'text-green-600',
  'text-amber-600',
  'text-purple-600',
  'text-red-600',
  'text-teal-600',
  'text-indigo-600',
  'text-pink-600'
];

export default function FeaturedSection() {
  const [featuredContent, setFeaturedContent] = useState<FeaturedContent>(fallbackFeaturedContent);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  // Fetch featured content from API
  useEffect(() => {
    const fetchFeaturedContent = async () => {
      try {
        const response = await fetch('/api/website-management/featured-content');
        if (response.ok) {
          const data = await response.json();
          // Filter only active featured content
          const activeFeaturedContent = data.filter((content: any) => content.isActive);
          if (activeFeaturedContent.length > 0) {
            // Parse the features JSON if it's a string
            const content = activeFeaturedContent[0];

            // Make sure features is an array
            if (content.features) {
              try {
                // If features is a string, parse it as JSON
                if (typeof content.features === 'string') {
                  content.features = JSON.parse(content.features);
                }
                // If features is already an object but not an array, wrap it in an array
                else if (typeof content.features === 'object' && !Array.isArray(content.features)) {
                  content.features = [content.features];
                }
                // If features is neither a string nor an object, set it to an empty array
                else if (!Array.isArray(content.features)) {
                  console.warn('Features is not an array or parseable JSON:', content.features);
                  content.features = [];
                }
              } catch (e) {
                console.error('Error parsing features:', e);
                content.features = [];
              }
            } else {
              content.features = [];
            }

            // Check if the image URL is valid or use fallback
            if (!content.imageUrl || content.imageUrl.trim() === '') {
              content.imageUrl = FALLBACK_IMAGE_URL;
            }

            setFeaturedContent(content); // Use the first active featured content with parsed features
          }
        }
      } catch (error) {
        console.error('Error fetching featured content:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchFeaturedContent();
  }, []);

  // Show loading state
  if (isLoading) {
    return (
      <section className="py-20 bg-gray-50 dark:bg-gray-800">
        <div className="container mx-auto px-4 flex justify-center items-center h-[400px]">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-20 bg-gray-50 dark:bg-gray-800">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Image Column */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.7 }}
            viewport={{ once: true }}
            className="relative h-[400px] rounded-lg overflow-hidden shadow-xl"
          >
            <Image
              src={featuredContent.imageUrl || FALLBACK_IMAGE_URL}
              alt={featuredContent.title}
              fill
              className="object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex items-end p-6">
              <div className="text-white">
                <h3 className="text-xl font-bold mb-2">Excellence in Education</h3>
                <p className="text-sm text-gray-200">Our students consistently achieve outstanding academic results</p>
              </div>
            </div>
          </motion.div>

          {/* Content Column */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.7 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            <div>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">{featuredContent.title}</h2>
              <div className="w-20 h-1 bg-blue-600 rounded-full mb-6"></div>
            </div>

            <p className="text-gray-600 dark:text-gray-300">
              {featuredContent.description}
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-8">
              {Array.isArray(featuredContent.features) ? (
                featuredContent.features.map((feature, index) => (
                  <div key={index} className="bg-white dark:bg-gray-900 p-4 rounded-lg shadow-md">
                    <div className={`${featureColors[index % featureColors.length]} font-bold text-lg mb-2`}>
                      {feature.title}
                    </div>
                    <p className="text-gray-600 dark:text-gray-400 text-sm">
                      {feature.description}
                    </p>
                  </div>
                ))
              ) : (
                <div className="col-span-2 text-center text-gray-500 dark:text-gray-400">
                  No features available
                </div>
              )}
            </div>

            <div className="pt-4">
              <Button
                onClick={() => router.push('/website/about')}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                Learn More About Us
              </Button>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}

import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Capitalizes the first letter of each word in a name
 * @param name The name to capitalize
 * @returns The capitalized name
 */
export function capitalizeName(name: string): string {
  if (!name) return '';
  
  // Split the name into words and capitalize each one
  return name
    .split(' ')
    .map(word => {
      if (word.length === 0) return word;
      // Capitalize the first letter and keep the rest of the word as is
      return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
    })
    .join(' ');
} 
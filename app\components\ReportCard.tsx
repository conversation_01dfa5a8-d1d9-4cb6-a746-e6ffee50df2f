"use client"

import React, { useState, useRef, useEffect } from 'react'
import { Button } from './ui/button'
import {
  FileText,
  Calendar,
  Printer,
  Download,
  CheckCircle,
  Search,
  User
} from 'lucide-react'
import { useQuery } from '@tanstack/react-query'
import { calculatePromotionStatus, getPromotionPolicyDescription, getPromotionStatusText, PromotionPolicyData } from '../lib/promotion-policy'
import { useAppSettings } from '../contexts/app-settings-context'

export function ReportCard() {
  const { settings } = useAppSettings()
  const [step, setStep] = useState<'form' | 'preview'>('form')
  const [isGenerating, setIsGenerating] = useState(false)
  const [isGenerated, setIsGenerated] = useState(false)
  const printRef = useRef<HTMLDivElement>(null)

  // Get current academic year (e.g., 2023-2024)
  const getCurrentAcademicYear = () => {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth();

    // If we're in the second half of the year (July-December), academic year is current-next
    // Otherwise it's previous-current
    if (month >= 6) { // July or later
      return `${year}-${year + 1}`;
    } else {
      return `${year - 1}-${year}`;
    }
  };

  // Form state
  const [formData, setFormData] = useState({
    studentId: '',
    studentName: '',
    class: '',
    academicYear: getCurrentAcademicYear(),
    semester: 'First Semester'
  })

  // State for report mode (student or class)
  const [reportMode, setReportMode] = useState<'student' | 'class'>('student');

  // State for student report
  const [studentSid, setStudentSid] = useState<string>('');
  const [selectedStudent, setSelectedStudent] = useState<any>(null);
  const [reportData, setReportData] = useState<any>(null);

  // State for class report
  const [classReportData, setClassReportData] = useState<any>(null);

  // Fetch classes from the database
  const { data: classes, isLoading: isLoadingClasses } = useQuery({
    queryKey: ['classes'],
    queryFn: async () => {
      // Fetch classes without requiring a year parameter
      const res = await fetch('/api/classes')
      if (!res.ok) throw new Error('Failed to fetch classes')
      const data = await res.json()
      return Array.isArray(data) ? data : []
    }
  })

  // Fetch students from the database
  const { data: students, isLoading: isLoadingStudents } = useQuery({
    queryKey: ['students', formData.class],
    queryFn: async () => {
      // If class is selected, fetch students for that class
      const url = formData.class
        ? `/api/students?class=${formData.class}`
        : '/api/students'

      const res = await fetch(url)
      if (!res.ok) throw new Error('Failed to fetch students')
      return res.json()
    },
    enabled: !!classes // Only fetch students when classes are loaded
  });

  // Fetch report card data
  const {
    data: fetchedReportData,
    isLoading: isLoadingReport,
    refetch: refetchReport,
    isError: isReportError,
    error: reportError
  } = useQuery({
    queryKey: ['reportCard', reportMode, studentSid, formData.class, formData.academicYear, formData.semester],
    queryFn: async () => {
      // Build query parameters
      const params = new URLSearchParams();
      params.append('reportType', reportMode);
      params.append('academicYear', formData.academicYear);
      params.append('semester', formData.semester);

      if (reportMode === 'student' && studentSid) {
        params.append('studentId', studentSid);
        console.log('Sending studentId:', studentSid);
      } else if (reportMode === 'class' && formData.class) {
        params.append('className', formData.class);
        console.log('Sending className:', formData.class);
      } else {
        throw new Error('Missing required parameters');
      }

      const url = `/api/reportcard?${params.toString()}`;
      console.log('Fetching report card from URL:', url);

      const res = await fetch(url);

      if (!res.ok) {
        const errorData = await res.json().catch(() => ({}));
        console.error('Report card API error:', errorData);

        // Create a custom error object with the response data
        const error = new Error(errorData.error || 'Failed to fetch report card data');
        (error as any).response = { data: errorData };
        throw error;
      }

      return res.json();
    },
    enabled: false // Don't fetch automatically, we'll trigger it manually
  });

  // Update report data when fetched
  useEffect(() => {
    if (fetchedReportData) {
      if (reportMode === 'student') {
        setReportData(fetchedReportData);
      } else {
        setClassReportData(fetchedReportData);
      }
      setIsGenerated(true);
      setStep('preview');
    }
  }, [fetchedReportData, reportMode]);

  // Extract class names from the fetched classes
  const classesList = Array.isArray(classes)
    ? classes.map((cls: any) => cls.name || cls)
    : []

  // Update selected student when studentSid changes
  useEffect(() => {
    if (studentSid && students) {
      const student = students.find((s: any) => s.id === studentSid);
      if (student) {
        setSelectedStudent(student);
        setFormData(prev => ({
          ...prev,
          studentName: student.name,
          class: student.className
        }));
      }
    }
  }, [studentSid, students]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData({
      ...formData,
      [name]: value
    })
  }

  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [promotionPolicy, setPromotionPolicy] = useState<PromotionPolicyData | null>(null);

  // Fetch promotion policy on component mount
  useEffect(() => {
    const fetchPromotionPolicy = async () => {
      try {
        const response = await fetch('/api/promotion-policy');
        if (response.ok) {
          const policy = await response.json();
          setPromotionPolicy(policy);
        }
      } catch (error) {
        console.error('Error fetching promotion policy:', error);
      }
    };

    fetchPromotionPolicy();
  }, []);

  const handleGenerateReportCard = async () => {
    setIsGenerating(true)
    setErrorMessage(null)

    try {
      // Validate required fields based on report mode
      if (reportMode === 'student') {
        if (!studentSid || !formData.studentName) {
          throw new Error('Student information is required')
        }
      } else if (reportMode === 'class') {
        if (!formData.class) {
          throw new Error('Class selection is required')
        }
      }

      // Trigger the report data fetch
      await refetchReport()
    } catch (error) {
      console.error('Error generating report card:', error)

      // Extract error message from the API response if available
      let message = 'Failed to generate report card';

      if (error.response && error.response.data && error.response.data.error) {
        message = error.response.data.error;
      } else if (error.message) {
        message = error.message;
      }

      // Set error message to display in the UI
      setErrorMessage(message);
    } finally {
      setIsGenerating(false)
    }
  }

  // Function to handle printing
  const handlePrint = () => {
    // Create a new window for printing
    const printWindow = window.open('', '_blank');

    if (!printWindow) {
      alert('Please allow pop-ups to print');
      return;
    }

    // For single student report
    // Get the HTML content of the report card
    const reportCardHTML = printRef.current?.innerHTML || '';

    // Create a complete HTML document with proper styling
    const htmlContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Report Card - ${formData.studentName || 'Student'}</title>
          <style>
            @page {
              size: A4 portrait;
              margin: 0;
            }
            body {
              font-family: Arial, sans-serif;
              margin: 0;
              padding: 20px;
              -webkit-print-color-adjust: exact !important;
              print-color-adjust: exact !important;
              color-adjust: exact !important;
            }
            .report-card {
              width: 794px;
              min-height: 1123px;
              margin: 0 auto;
              padding: 20px;
              box-sizing: border-box;
              font-size: 12px;
            }
            .header {
              text-align: center;
              margin-bottom: 20px;
              border-bottom: 1px solid #ccc;
              padding-bottom: 10px;
            }
            .logo {
              width: 50px;
              height: 50px;
              margin: 0 auto 5px;
              display: block;
            }
            .school-name {
              font-size: 16px;
              font-weight: bold;
              margin: 5px 0;
            }
            .report-title {
              font-size: 14px;
              font-weight: bold;
              margin: 5px 0;
            }
            .academic-year {
              font-size: 12px;
              color: #0369a1;
              margin: 5px 0 10px;
            }
            .student-info {
              display: flex;
              flex-wrap: wrap;
              margin-bottom: 20px;
            }
            .student-info-item {
              width: 50%;
              margin-bottom: 10px;
              font-size: 12px;
            }
            .student-info-label {
              font-weight: bold;
            }
            .section-title {
              font-size: 14px;
              font-weight: bold;
              margin: 15px 0 10px;
            }
            table {
              width: 100%;
              border-collapse: collapse;
              margin-bottom: 20px;
            }
            th, td {
              border: 1px solid #ccc;
              padding: 6px 8px;
              text-align: center;
              font-size: 12px;
            }
            th {
              background-color: #f5f5f5;
            }
            .subject-column {
              text-align: left;
            }
            .total-row, .average-row, .rank-row {
              font-weight: bold;
              background-color: #f5f5f5;
            }
            .grading-system {
              margin: 20px 0;
            }
            .grading-table {
              width: 100%;
              border-collapse: collapse;
            }
            .grading-table th, .grading-table td {
              border: 1px solid #ccc;
              padding: 4px 8px;
              text-align: center;
              font-size: 11px;
            }
            .grade-a {
              color: #16a34a;
            }
            .grade-b {
              color: #2563eb;
            }
            .grade-c {
              color: #ca8a04;
            }
            .grade-d {
              color: #dc2626;
            }
            .signatures {
              display: flex;
              justify-content: space-between;
              margin-top: 30px;
              padding-top: 10px;
              border-top: 1px solid #ccc;
            }
            .signature-block {
              width: 45%;
            }
            .signature-title {
              font-weight: bold;
              font-size: 12px;
              margin-bottom: 5px;
            }
            .signature-line {
              font-size: 12px;
            }
            .text-center {
              text-align: center;
            }
            .font-bold {
              font-weight: bold;
            }
            img {
              display: block;
              margin: 0 auto;
            }
          </style>
        </head>
        <body>
          <div class="report-card">
            <div class="header">
              <img src="${settings.schoolLogo || '/images/logo.png'}" alt="School Logo" class="logo">
              <div class="school-name">${settings.schoolName}</div>
              <div class="report-title">Student Report Card</div>
              <div class="academic-year">${formData.semester} - ${formData.academicYear}</div>
            </div>

            <div class="student-info">
              <div class="student-info-item">
                <span class="student-info-label">Name:</span> ${reportData?.student?.name || formData.studentName}
              </div>
              <div class="student-info-item">
                <span class="student-info-label">Class:</span> ${reportData?.student?.className || formData.class}
              </div>
              <div class="student-info-item">
                <span class="student-info-label">Father Name:</span> ${reportData?.student?.fatherName || '-'}
              </div>
              <div class="student-info-item">
                <span class="student-info-label">GrandFather Name:</span> ${reportData?.student?.gfName || '-'}
              </div>
              <div class="student-info-item">
                <span class="student-info-label">Gender:</span> ${reportData?.student?.gender || '-'}
              </div>
              <div class="student-info-item">
                <span class="student-info-label">Academic Year:</span> ${formData.academicYear}
              </div>
              <div class="student-info-item">
                <span class="student-info-label">Semester:</span> ${formData.semester || "Annual"}
              </div>
              ${reportData?.attendance ? `
              <div class="student-info-item">
                <span class="student-info-label">Attendance:</span> Present: ${reportData.attendance.present} / ${reportData.attendance.totalDays} days (${reportData.attendance.percentage}%)
              </div>
              ` : ''}
            </div>

            <div class="section-title">Academic Performance</div>
            <table>
              <thead>
                <tr>
                  <th class="subject-column">Subject</th>
                  ${formData.semester === 'Annual' ? `
                    <th>First Semester</th>
                    <th>Second Semester</th>
                    <th>Average</th>
                  ` : `
                    <th>First Semester</th>
                  `}
                </tr>
              </thead>
              <tbody>
                ${reportData && reportData.subjects ?
                  reportData.subjects.map((subject: any) => `
                    <tr>
                      <td class="subject-column">${subject.name}</td>
                      ${formData.semester === 'Annual' ? `
                        <td>${subject['First Semester'] || '-'}</td>
                        <td>${subject['Second Semester'] || '-'}</td>
                        <td>${subject.average || '-'}</td>
                      ` : `
                        <td>${subject[formData.semester] || '-'}</td>
                      `}
                    </tr>
                  `).join('') :
                  `
                    <tr>
                      <td colspan="${formData.semester === 'Annual' ? 4 : 2}" class="text-center">No data available</td>
                    </tr>
                  `
                }

                ${reportData && reportData.summary ? `
                  <tr class="total-row">
                    <td class="subject-column">Total</td>
                    ${formData.semester === 'Annual' ? `
                      <td>${reportData.subjects.reduce((sum: number, subject: any) => sum + (subject['First Semester'] || 0), 0)}</td>
                      <td>${reportData.subjects.reduce((sum: number, subject: any) => sum + (subject['Second Semester'] || 0), 0)}</td>
                      <td>${reportData.summary.totalMarks}</td>
                    ` : `
                      <td>${reportData.summary.totalMarks}</td>
                    `}
                  </tr>
                  <tr class="average-row">
                    <td class="subject-column">Average</td>
                    ${formData.semester === 'Annual' ? `
                      <td>${reportData.subjects.length > 0 ?
                        Math.round(reportData.subjects.reduce((sum: number, subject: any) => sum + (subject['First Semester'] || 0), 0) /
                        reportData.subjects.filter((s: any) => s['First Semester']).length) : '-'}</td>
                      <td>${reportData.subjects.length > 0 ?
                        Math.round(reportData.subjects.reduce((sum: number, subject: any) => sum + (subject['Second Semester'] || 0), 0) /
                        reportData.subjects.filter((s: any) => s['Second Semester']).length) : '-'}</td>
                      <td>${reportData.summary.average}</td>
                    ` : `
                      <td>${reportData.summary.average}</td>
                    `}
                  </tr>
                  <tr class="rank-row">
                    <td class="subject-column">Rank</td>
                    ${formData.semester === 'Annual' ? `
                      <td>${reportData.summary.firstSemesterRank || '-'}</td>
                      <td>${reportData.summary.secondSemesterRank || '-'}</td>
                      <td>${reportData.summary.rank || 1}</td>
                    ` : `
                      <td>${reportData.summary.rank || 1}</td>
                    `}
                  </tr>
                ` : ''}
              </tbody>
            </table>

            <div class="section-title" style="font-size: 14px; font-weight: bold; margin: 15px 0 10px; border-bottom: 1px solid #ccc; padding-bottom: 5px;">Grading System</div>
            <table class="grading-table" style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
              <tr style="background-color: #f5f5f5;">
                <th style="border: 1px solid #ccc; padding: 6px 8px; text-align: center; font-size: 12px;">90.00 - 100.00</th>
                <th style="border: 1px solid #ccc; padding: 6px 8px; text-align: center; font-size: 12px;">80.00 - 89.99</th>
                <th style="border: 1px solid #ccc; padding: 6px 8px; text-align: center; font-size: 12px;">60.00 - 79.99</th>
                <th style="border: 1px solid #ccc; padding: 6px 8px; text-align: center; font-size: 12px;">50.00 - 59.99</th>
                <th style="border: 1px solid #ccc; padding: 6px 8px; text-align: center; font-size: 12px;">0.00 - 49.99</th>
              </tr>
              <tr>
                <td style="border: 1px solid #ccc; padding: 6px 8px; text-align: center; font-size: 12px; color: #16a34a;">Excellent</td>
                <td style="border: 1px solid #ccc; padding: 6px 8px; text-align: center; font-size: 12px; color: #2563eb;">Very Good</td>
                <td style="border: 1px solid #ccc; padding: 6px 8px; text-align: center; font-size: 12px; color: #3b82f6;">Good</td>
                <td style="border: 1px solid #ccc; padding: 6px 8px; text-align: center; font-size: 12px; color: #ca8a04;">Satisfactory</td>
                <td style="border: 1px solid #ccc; padding: 6px 8px; text-align: center; font-size: 12px; color: #dc2626;">Failure</td>
              </tr>
            </table>

            <div class="section-title" style="font-size: 14px; font-weight: bold; margin: 15px 0 10px; border-bottom: 1px solid #ccc; padding-bottom: 5px;">Promotion Policy</div>
            <div style="padding: 10px; background-color: #f9fafb; border: 1px solid #e5e7eb; border-radius: 4px; margin-bottom: 20px;">
              <p style="font-size: 12px; color: #4b5563; margin: 0;">
                <span style="font-weight: bold;">Promotion Criteria:</span> ${getPromotionPolicyDescription(promotionPolicy)}
              </p>
            </div>

            ${reportData && reportData.subjects ? `
            <div class="promotion-status" style="text-align: center; margin: 20px 0; padding: 15px; background-color: #f9fafb; border: 1px solid #e5e7eb; border-radius: 4px;">
              <div style="font-weight: bold; font-size: 14px; margin-bottom: 10px; color: #1f2937;">Student Status</div>
              ${(() => {
                const isPromoted = calculatePromotionStatusWithPolicy(reportData.subjects, formData.semester);
                const statusInfo = getPromotionStatusText(isPromoted);
                return !isPromoted ? `
                  <div style="display: inline-block; text-align: center;">
                    <img src="/images/detained.png" alt="Detained" style="width: 120px; height: 120px; transform: rotate(12deg); opacity: 0.8;">
                    <p style="font-weight: bold; color: #dc2626; margin-top: 10px; font-size: 16px;">${statusInfo.status}</p>
                    <p style="font-size: 12px; color: #666;">${statusInfo.description}</p>
                  </div>
                ` : `
                  <div style="display: inline-block; text-align: center;">
                    <img src="/images/promoted.png" alt="Promoted" style="width: 120px; height: 120px; opacity: 0.8;">
                    <p style="font-weight: bold; color: #16a34a; margin-top: 10px; font-size: 16px;">${statusInfo.status}</p>
                    <p style="font-size: 12px; color: #666;">${statusInfo.description}</p>
                  </div>
                `;
              })()}
            </div>
            ` : ''}

            <div class="signatures">
              <div class="signature-block">
                <div class="signature-title">Home Room Teacher:</div>
                <div class="signature-line">Signature: _________________</div>
              </div>
              <div class="signature-block">
                <div class="signature-title">Director:</div>
                <div class="signature-line">Signature: _________________</div>
              </div>
            </div>
          </div>


          <script>
            // Wait for images to load
            window.onload = function() {
              // Automatically open the print dialog when the page loads
              setTimeout(function() {
                window.print();
                // Close the window after printing (or if print is canceled)
                setTimeout(function() {
                  window.close();
                }, 500);
              }, 1000); // Wait 1 second for images to load
            };
          </script>
        </body>
      </html>
    `;

    // Use a safer approach than document.write
    printWindow.document.open();
    // @ts-ignore - TypeScript doesn't like document.write but it's the most reliable way for this use case
    printWindow.document.write(htmlContent);
    printWindow.document.close();
  };

  // Function to handle downloading PDF
  const handleDownloadPDF = () => {
    // Create a new window for printing
    const printWindow = window.open('', '_blank');

    if (!printWindow) {
      alert('Please allow pop-ups to download PDF');
      return;
    }

    // Get the HTML content of the report card
    const reportCardHTML = printRef.current?.innerHTML || '';

    // Create a complete HTML document with proper styling
    const htmlContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Report Card - ${formData.studentName || 'Student'}</title>
          <style>
            @page {
              size: A4 portrait;
              margin: 0;
            }
            body {
              font-family: Arial, sans-serif;
              margin: 0;
              padding: 20px;
              -webkit-print-color-adjust: exact !important;
              print-color-adjust: exact !important;
              color-adjust: exact !important;
            }
            .pdf-message {
              position: fixed;
              top: 10px;
              left: 50%;
              transform: translateX(-50%);
              background: #f0f9ff;
              border: 1px solid #bae6fd;
              color: #0369a1;
              padding: 8px 16px;
              border-radius: 4px;
              font-size: 14px;
              z-index: 9999;
              box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            @media print {
              .pdf-message {
                display: none;
              }
            }
            .report-card {
              width: 794px;
              min-height: 1123px;
              margin: 0 auto;
              padding: 20px;
              box-sizing: border-box;
              font-size: 12px;
            }
            .header {
              text-align: center;
              margin-bottom: 20px;
              border-bottom: 1px solid #ccc;
              padding-bottom: 10px;
            }
            .logo {
              width: 50px;
              height: 50px;
              margin: 0 auto 5px;
              display: block;
            }
            .school-name {
              font-size: 16px;
              font-weight: bold;
              margin: 5px 0;
            }
            .report-title {
              font-size: 14px;
              font-weight: bold;
              margin: 5px 0;
            }
            .academic-year {
              font-size: 12px;
              color: #0369a1;
              margin: 5px 0 10px;
            }
            .student-info {
              display: flex;
              flex-wrap: wrap;
              margin-bottom: 20px;
            }
            .student-info-item {
              width: 50%;
              margin-bottom: 10px;
              font-size: 12px;
            }
            .student-info-label {
              font-weight: bold;
            }
            .section-title {
              font-size: 14px;
              font-weight: bold;
              margin: 15px 0 10px;
            }
            table {
              width: 100%;
              border-collapse: collapse;
              margin-bottom: 20px;
            }
            th, td {
              border: 1px solid #ccc;
              padding: 6px 8px;
              text-align: center;
              font-size: 12px;
            }
            th {
              background-color: #f5f5f5;
            }
            .subject-column {
              text-align: left;
            }
            .total-row, .average-row, .rank-row {
              font-weight: bold;
              background-color: #f5f5f5;
            }
            .signatures {
              display: flex;
              justify-content: space-between;
              margin-top: 30px;
              padding-top: 10px;
              border-top: 1px solid #ccc;
            }
            .signature-block {
              width: 45%;
            }
            .signature-title {
              font-weight: bold;
              font-size: 12px;
              margin-bottom: 5px;
            }
            .signature-line {
              font-size: 12px;
            }
          </style>
        </head>
        <body>
          <div class="pdf-message">To save as PDF, select "Save as PDF" in the print dialog</div>
          <div class="report-card">
            ${reportCardHTML}
          </div>
          <script>
            window.onload = function() {
              setTimeout(function() {
                window.print();
              }, 1000);
            };
          </script>
        </body>
      </html>
    `;

    printWindow.document.open();
    // @ts-ignore - TypeScript doesn't like document.write but it's the most reliable way for this use case
    printWindow.document.write(htmlContent);
    printWindow.document.close();
  };

  // Calculate promotion status based on subject marks using configurable policy
  const calculatePromotionStatusWithPolicy = (subjects: any[], semester: string) => {
    console.log('=== PROMOTION CALCULATION DEBUG ===');
    console.log('Subjects:', subjects);
    console.log('Semester:', semester);
    console.log('Promotion Policy:', promotionPolicy);

    const result = calculatePromotionStatus(subjects, semester, promotionPolicy);

    // Debug calculation
    const failedSubjects = subjects.filter(subject => {
      let score = 0;
      if (semester === 'Annual') {
        score = subject.average ||
          (subject['First Semester'] && subject['Second Semester']
            ? (parseInt(subject['First Semester']) + parseInt(subject['Second Semester'])) / 2
            : (subject['First Semester'] || subject['Second Semester'] || 0));
      } else {
        score = subject[semester] || 0;
      }
      return score < (promotionPolicy?.passingScore || 50);
    });

    console.log('Failed subjects count:', failedSubjects.length);
    console.log('Failed subjects:', failedSubjects.map(s => ({ name: s.name || s.subject, score: semester === 'Annual' ? s.average : s[semester] })));
    console.log('Max allowed failures:', promotionPolicy?.maxFailedSubjects || 2);
    console.log('Detention threshold:', (promotionPolicy?.maxFailedSubjects || 2) + 1);
    console.log('Promotion result:', result);
    console.log('=== END DEBUG ===');

    return result;
  };

  // Calculate total marks based on real data from the API
  const calculateTotalMarks = () => {
    // Use real data from the API if available
    if (reportData && reportMode === 'student') {
      if (formData.semester === 'Annual') {
        // For annual view, calculate totals from both semesters
        let firstSemTotal = 0;
        let secondSemTotal = 0;
        let firstSemCount = 0;
        let secondSemCount = 0;

        reportData.subjects.forEach((subject: any) => {
          if (subject['First Semester']) {
            firstSemTotal += parseInt(subject['First Semester']);
            firstSemCount++;
          }
          if (subject['Second Semester']) {
            secondSemTotal += parseInt(subject['Second Semester']);
            secondSemCount++;
          }
        });

        return {
          total: reportData.summary.totalMarks || 0,
          obtained: reportData.summary.totalMarks || 0,
          percentage: reportData.summary.average || 0,
          firstSemTotal: firstSemTotal,
          secondSemTotal: secondSemTotal,
          annualAverage: reportData.summary.average || 0
        };
      } else {
        // For single semester view
        return {
          total: reportData.summary.totalMarks || 0,
          obtained: reportData.summary.totalMarks || 0,
          percentage: reportData.summary.average || 0,
          firstSemTotal: reportData.summary.totalMarks || 0,
          secondSemTotal: 0,
          annualAverage: reportData.summary.average || 0
        };
      }
    }

    // Return empty values if no data is available
    return {
      total: 0,
      obtained: 0,
      percentage: 0,
      firstSemTotal: 0,
      secondSemTotal: 0,
      annualAverage: 0
    };
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Student Report Card</h1>
          <p className="text-gray-600 mt-1">Generate and view student performance reports</p>
        </div>
        {isGenerated && (
          <Button
            variant="outline"
            onClick={() => setStep('form')}
            className="flex items-center gap-2 hover:bg-gray-50"
          >
            <Search className="h-4 w-4" />
            Generate New
          </Button>
        )}
      </div>

      {step === 'form' ? (
        <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
          <div className="p-6 border-b border-gray-100 bg-gradient-to-r from-blue-50 to-blue-100/50 text-center">
            <h2 className="text-lg font-semibold text-gray-800">Generate Report Card</h2>
            <p className="text-sm text-gray-600 mt-1">Fill in the details to generate a student report card</p>
          </div>

          <div className="p-6">
            <div className="w-full">
              <div className="flex justify-center mb-6 bg-gray-100/80 p-1 rounded-lg">
                <div
                  className={`flex items-center gap-2 px-6 py-2 cursor-pointer ${reportMode === 'student' ? 'bg-white shadow-sm' : 'bg-transparent'} rounded-md`}
                  onClick={() => setReportMode('student')}
                >
                  <User className="h-4 w-4" />
                  By Student
                </div>
                <div
                  className={`flex items-center gap-2 px-6 py-2 cursor-pointer ${reportMode === 'class' ? 'bg-white shadow-sm' : 'bg-transparent'} rounded-md`}
                  onClick={() => setReportMode('class')}
                >
                  <Calendar className="h-4 w-4" />
                  By Class
                </div>
              </div>

              {reportMode === 'student' ? (
                <div className="space-y-6">
                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg flex items-start space-x-3">
                    <div className="mt-0.5 bg-blue-100 p-2 rounded-md text-blue-700">
                      <User className="h-5 w-5" />
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-blue-900">Student Information</h3>
                      <p className="text-sm text-blue-700 mt-0.5">Enter the student ID to generate an individual report card</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-3">
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1" htmlFor="class">Class</label>
                        <select
                          id="studentClass"
                          value={formData.class}
                          onChange={(e) => {
                            setFormData((prev: any) => ({
                              ...prev,
                              class: e.target.value,
                              studentId: '',
                              studentName: ''
                            }));
                            setStudentSid('');
                          }}
                          className="h-9 w-full rounded-md border border-gray-300 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                          required
                          disabled={isLoadingClasses}
                        >
                          {isLoadingClasses ? (
                            <option value="">Loading classes...</option>
                          ) : (
                            <>
                              <option value="">Select a class</option>
                              {classesList.map((cls: string) => (
                                <option key={cls} value={cls}>{cls}</option>
                              ))}
                            </>
                          )}
                        </select>
                      </div>

                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1" htmlFor="studentId">Student</label>
                        <select
                          id="studentId"
                          value={formData.studentId}
                          onChange={(e) => {
                            const selectedStudent = students?.find((s: any) => s.sid === e.target.value || s.id === e.target.value);
                            if (selectedStudent) {
                              setFormData((prev: any) => ({
                                ...prev,
                                studentId: selectedStudent.sid || selectedStudent.id,
                                studentName: selectedStudent.name
                              }));
                              setStudentSid(selectedStudent.sid || selectedStudent.id);
                            }
                          }}
                          className="h-9 w-full rounded-md border border-gray-300 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                          required
                          disabled={isLoadingStudents || !formData.class}
                        >
                          <option value="">Select a student</option>
                          {!isLoadingStudents && students && students
                            .filter((student: any) => student.className === formData.class)
                            .map((student: any) => (
                              <option key={student.id} value={student.sid || student.id}>
                                {student.name} (SID: {student.sid || student.id})
                              </option>
                            ))
                          }
                        </select>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1" htmlFor="academicYear">Academic Year</label>
                        <select
                          id="academicYear"
                          name="academicYear"
                          value={formData.academicYear}
                          onChange={handleInputChange}
                          className="h-9 w-full rounded-md border border-gray-300 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                          required
                        >
                          <option value="2025-2026">2025-2026</option>
                          <option value="2024-2025">2024-2025</option>
                          <option value="2023-2024">2023-2024</option>
                          <option value="2022-2023">2022-2023</option>
                          <option value="2021-2022">2021-2022</option>
                          <option value="2020-2021">2020-2021</option>
                          <option value="2019-2020">2019-2020</option>
                        </select>
                      </div>

                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1" htmlFor="semester">Semester</label>
                        <div className="grid grid-cols-2 gap-2">
                          <div className="bg-white border border-gray-200 rounded-md p-2 flex items-center">
                            <input
                              type="radio"
                              id="firstSemester"
                              name="semester"
                              value="First Semester"
                              checked={formData.semester === 'First Semester'}
                              onChange={handleInputChange}
                              className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                            />
                            <label htmlFor="firstSemester" className="ml-2 text-sm text-gray-700">First Semester</label>
                          </div>

                          <div className="bg-white border border-gray-200 rounded-md p-2 flex items-center">
                            <input
                              type="radio"
                              id="annual"
                              name="semester"
                              value="Annual"
                              checked={formData.semester === 'Annual'}
                              onChange={handleInputChange}
                              className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                            />
                            <label htmlFor="annual" className="ml-2 text-sm text-gray-700">Annual</label>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="space-y-6">
                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg flex items-start space-x-3">
                    <div className="mt-0.5 bg-blue-100 p-2 rounded-md text-blue-700">
                      <Calendar className="h-5 w-5" />
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-blue-900">Class Information</h3>
                      <p className="text-sm text-blue-700 mt-0.5">Generate report cards for all students in a class</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1" htmlFor="classSelection">Class</label>
                      <select
                        id="classSelection"
                        value={formData.class}
                        onChange={(e) => {
                          setFormData((prev: any) => ({
                            ...prev,
                            class: e.target.value
                          }));
                        }}
                        className="h-9 w-full rounded-md border border-gray-300 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                        required
                        disabled={isLoadingClasses}
                      >
                        {isLoadingClasses ? (
                          <option value="">Loading classes...</option>
                        ) : (
                          <>
                            <option value="">Select a class</option>
                            {classesList.map((cls: string) => (
                              <option key={cls} value={cls}>{cls}</option>
                            ))}
                          </>
                        )}
                      </select>
                    </div>

                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1" htmlFor="academicYear">Academic Year</label>
                      <select
                        id="academicYear"
                        name="academicYear"
                        value={formData.academicYear}
                        onChange={handleInputChange}
                        className="h-9 w-full rounded-md border border-gray-300 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                        required
                      >
                        <option value="2025-2026">2025-2026</option>
                        <option value="2024-2025">2024-2025</option>
                        <option value="2023-2024">2023-2024</option>
                        <option value="2022-2023">2022-2023</option>
                        <option value="2021-2022">2021-2022</option>
                        <option value="2020-2021">2020-2021</option>
                        <option value="2019-2020">2019-2020</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1" htmlFor="semester">Semester</label>
                      <div className="grid grid-cols-2 gap-2">
                        <div className="bg-white border border-gray-200 rounded-md p-2 flex items-center">
                          <input
                            type="radio"
                            id="classFirstSemester"
                            name="semester"
                            value="First Semester"
                            checked={formData.semester === 'First Semester'}
                            onChange={handleInputChange}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                          />
                          <label htmlFor="classFirstSemester" className="ml-2 text-sm text-gray-700">First Semester</label>
                        </div>

                        <div className="bg-white border border-gray-200 rounded-md p-2 flex items-center">
                          <input
                            type="radio"
                            id="classAnnual"
                            name="semester"
                            value="Annual"
                            checked={formData.semester === 'Annual'}
                            onChange={handleInputChange}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                          />
                          <label htmlFor="classAnnual" className="ml-2 text-sm text-gray-700">Annual</label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {errorMessage && (
              <div className="mt-4 p-3 bg-red-50 border border-red-200 text-red-700 rounded-md flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
                <span>{errorMessage}</span>
              </div>
            )}

            <div className="mt-6 flex justify-center">
              <Button
                className="w-full md:w-1/2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 h-11 rounded-lg shadow-sm transition-all duration-200"
                onClick={handleGenerateReportCard}
                disabled={
                  (reportMode === 'student' && (!formData.studentId || !formData.studentName)) ||
                  (reportMode === 'class' && !formData.class) ||
                  !formData.academicYear ||
                  isGenerating
                }
              >
                {isGenerating ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-spin mr-2">
                      <Calendar className="h-5 w-5" />
                    </div>
                    <span>Generating...</span>
                  </div>
                ) : (
                  <div className="flex items-center justify-center">
                    {isGenerated ? (
                      <>
                        <CheckCircle className="h-5 w-5 mr-2" />
                        <span>View {reportMode === 'student' ? 'Report Card' : 'Class Reports'}</span>
                      </>
                    ) : (
                      <>
                        <FileText className="h-5 w-5 mr-2" />
                        <span>{reportMode === 'student' ? 'Generate Report Card' : 'Generate Class Reports'}</span>
                      </>
                    )}
                  </div>
                )}
              </Button>
            </div>
          </div>
        </div>
      ) : (
        <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
          <div className="p-6 border-b border-gray-100 bg-gradient-to-r from-blue-50 to-blue-100/50 flex justify-between items-center">
            <div>
              <h2 className="text-lg font-semibold text-gray-800">
                {reportMode === 'student' ? 'Student Report Card' : 'Class Report Cards'}
              </h2>
              <p className="text-sm text-gray-600 mt-1">
                {reportMode === 'student'
                  ? `${formData.studentName} - ${formData.class} - ${formData.academicYear} - ${formData.semester}`
                  : `${formData.class} - ${formData.academicYear} - ${formData.semester}`
                }
              </p>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={handlePrint}
                className="flex items-center gap-2 hover:bg-gray-50"
              >
                <Printer className="h-4 w-4" />
                Print
              </Button>
              <Button
                variant="outline"
                onClick={handleDownloadPDF}
                className="flex items-center gap-2 hover:bg-gray-50"
              >
                <Download className="h-4 w-4" />
                Download PDF
              </Button>
            </div>
          </div>

          <div className="p-6">
            {isLoadingReport ? (
              <div className="flex flex-col items-center justify-center py-12">
                <div className="animate-spin mb-4">
                  <Calendar className="h-8 w-8 text-blue-500" />
                </div>
                <p className="text-gray-600">Loading report card data...</p>
              </div>
            ) : isReportError ? (
              <div className="flex flex-col items-center justify-center py-12 text-center">
                <div className="bg-red-100 p-3 rounded-full mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <p className="text-red-600 font-medium mb-2">Failed to load report card</p>
                <p className="text-gray-600 max-w-md">
                  {reportError instanceof Error ? reportError.message : 'An unknown error occurred'}
                </p>
                <Button
                  variant="outline"
                  onClick={() => refetchReport()}
                  className="mt-4"
                >
                  Try Again
                </Button>
              </div>
            ) : reportMode === 'student' && reportData ? (
              <div ref={printRef} className="report-card-preview">
                <div className="header">
                  <img src={settings.schoolLogo || "/images/logo.png"} alt="School Logo" className="logo" />
                  <div className="school-name">{settings.schoolName}</div>
                  <div className="report-title">STUDENT PROGRESS REPORT</div>
                  <div className="academic-year">{formData.academicYear} - {formData.semester}</div>
                </div>

                <div className="student-info">
                  <div className="student-info-item">
                    <span className="student-info-label">Name:</span> {reportData.student?.name || 'N/A'}
                  </div>
                  <div className="student-info-item">
                    <span className="student-info-label">Father's Name:</span> {reportData.student?.fatherName || 'N/A'}
                  </div>
                  <div className="student-info-item">
                    <span className="student-info-label">Grandfather's Name:</span> {reportData.student?.grandfatherName || 'N/A'}
                  </div>
                  <div className="student-info-item">
                    <span className="student-info-label">Gender:</span> {reportData.student?.gender || 'N/A'}
                  </div>
                  <div className="student-info-item">
                    <span className="student-info-label">Class:</span> {reportData.student?.className || formData.class}
                  </div>
                  <div className="student-info-item">
                    <span className="student-info-label">Student ID:</span> {reportData.student?.sid || reportData.student?.id || 'N/A'}
                  </div>
                </div>

                <div className="section-title">Academic Performance</div>
                <table>
                  <thead>
                    <tr>
                      <th className="subject-column">Subject</th>
                      {formData.semester === 'Annual' ? (
                        <>
                          <th>First Semester</th>
                          <th>Second Semester</th>
                          <th>Average</th>
                        </>
                      ) : (
                        <th>Mark</th>
                      )}
                    </tr>
                  </thead>
                  <tbody>
                    {reportData.subjects && reportData.subjects.length > 0 ? (
                      reportData.subjects.map((subject: any, index: number) => (
                        <tr key={index}>
                          <td className="subject-column">{subject.name}</td>
                          {formData.semester === 'Annual' ? (
                            <>
                              <td>{subject['First Semester'] || '-'}</td>
                              <td>{subject['Second Semester'] || '-'}</td>
                              <td>
                                {subject['First Semester'] && subject['Second Semester']
                                  ? ((parseInt(subject['First Semester']) + parseInt(subject['Second Semester'])) / 2).toFixed(2)
                                  : '-'}
                              </td>
                            </>
                          ) : (
                            <td>{subject[formData.semester] || '-'}</td>
                          )}
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={formData.semester === 'Annual' ? 4 : 2} className="text-center py-4 text-gray-500">
                          No subject data available
                        </td>
                      </tr>
                    )}

                    <tr className="total-row">
                      <td className="subject-column">Total</td>
                      {formData.semester === 'Annual' ? (
                        <>
                          <td>{calculateTotalMarks().firstSemTotal}</td>
                          <td>{calculateTotalMarks().secondSemTotal}</td>
                          <td>{calculateTotalMarks().annualAverage}</td>
                        </>
                      ) : (
                        <td>{calculateTotalMarks().obtained}</td>
                      )}
                    </tr>

                    <tr className="rank-row">
                      <td className="subject-column">Rank</td>
                      {formData.semester === 'Annual' ? (
                        <>
                          <td>{reportData.summary?.firstSemesterRank || '-'}</td>
                          <td>{reportData.summary?.secondSemesterRank || '-'}</td>
                          <td>{reportData.summary?.rank || 1}</td>
                        </>
                      ) : (
                        <td>{reportData.summary?.rank || 1}</td>
                      )}
                    </tr>
                  </tbody>
                </table>

                <div className="section-title mt-8 text-lg font-bold text-gray-800 border-b border-gray-200 pb-2">Grading System</div>
                <table className="grading-table mt-4 w-full border-collapse">
                  <tbody>
                    <tr className="bg-gray-100">
                      <th className="border border-gray-300 p-2 text-center">90.00 - 100.00</th>
                      <th className="border border-gray-300 p-2 text-center">80.00 - 89.99</th>
                      <th className="border border-gray-300 p-2 text-center">60.00 - 79.99</th>
                      <th className="border border-gray-300 p-2 text-center">50.00 - 59.99</th>
                      <th className="border border-gray-300 p-2 text-center">0.00 - 49.99</th>
                    </tr>
                    <tr>
                      <td className="border border-gray-300 p-2 text-center text-green-600 font-medium">Excellent</td>
                      <td className="border border-gray-300 p-2 text-center text-blue-600 font-medium">Very Good</td>
                      <td className="border border-gray-300 p-2 text-center text-blue-500 font-medium">Good</td>
                      <td className="border border-gray-300 p-2 text-center text-yellow-600 font-medium">Satisfactory</td>
                      <td className="border border-gray-300 p-2 text-center text-red-600 font-medium">Failure</td>
                    </tr>
                  </tbody>
                </table>

                {/* Promotion Policy */}
                <div className="section-title mt-8 text-lg font-bold text-gray-800 border-b border-gray-200 pb-2">Promotion Policy</div>
                <div className="mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
                  <p className="text-sm text-gray-700 mb-2">
                    <span className="font-medium">Promotion Criteria:</span> {getPromotionPolicyDescription(promotionPolicy)}
                  </p>
                </div>

                {/* Promotion Status */}
                {reportData.subjects && reportData.subjects.length > 0 && (
                  <div className="text-center my-8 p-6 bg-gray-50 rounded-lg border border-gray-200">
                    <div className="text-lg font-bold text-gray-800 mb-4">Student Status</div>
                    {(() => {
                      const isPromoted = calculatePromotionStatusWithPolicy(reportData.subjects, formData.semester);
                      const statusInfo = getPromotionStatusText(isPromoted);
                      return !isPromoted ? (
                        <div>
                          <img src="/images/detained.png" alt="Detained" className="w-32 h-32 mx-auto transform rotate-12" />
                          <p className="font-bold text-xl text-red-600 mt-4">{statusInfo.status}</p>
                          <p className="text-gray-600 mt-2">{statusInfo.description}</p>
                        </div>
                      ) : (
                        <div>
                          <img src="/images/promoted.png" alt="Promoted" className="w-32 h-32 mx-auto" />
                          <p className="font-bold text-xl text-green-600 mt-4">{statusInfo.status}</p>
                          <p className="text-gray-600 mt-2">{statusInfo.description}</p>
                        </div>
                      );
                    })()}
                  </div>
                )}

                <div className="signatures">
                  <div className="signature-block">
                    <div className="signature-title">Home Room Teacher</div>
                    <div className="mt-10 border-t border-gray-300"></div>
                    <div className="signature-line">Signature</div>
                  </div>
                  <div className="signature-block">
                    <div className="signature-title">Director</div>
                    <div className="mt-10 border-t border-gray-300"></div>
                    <div className="signature-line">Signature</div>
                  </div>
                </div>
              </div>
            ) : reportMode === 'class' && classReportData ? (
              <div className="class-reports">
                <div className="mb-4 p-4 bg-blue-50 rounded-lg">
                  <h3 className="text-lg font-medium text-blue-800">Class Report Summary</h3>
                  <p className="text-sm text-blue-700 mt-1">
                    {classReportData.length} students in {formData.class} - {formData.academicYear} - {formData.semester}
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {classReportData.map((studentReport: any, index: number) => (
                    <div key={index} className="border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                      <div className="p-4 border-b border-gray-100 bg-gradient-to-r from-gray-50 to-gray-100">
                        <h4 className="font-medium text-gray-900">{studentReport.student?.name || 'Unknown Student'}</h4>
                        <p className="text-sm text-gray-600">ID: {studentReport.student?.sid || studentReport.student?.id || 'N/A'}</p>
                      </div>
                      <div className="p-4">
                        <div className="grid grid-cols-2 gap-2 text-sm">
                          <div className="text-gray-600">Average:</div>
                          <div className="font-medium text-gray-900">{studentReport.summary?.average || 'N/A'}</div>

                          <div className="text-gray-600">Rank:</div>
                          <div className="font-medium text-gray-900">{studentReport.summary?.rank || 'N/A'}</div>

                          <div className="text-gray-600">Status:</div>
                          <div className="font-medium">
                            {(() => {
                              const isPromoted = calculatePromotionStatusWithPolicy(studentReport.subjects, formData.semester);
                              const statusInfo = getPromotionStatusText(isPromoted);
                              return !isPromoted ? (
                                <span className="text-red-600">{statusInfo.status}</span>
                              ) : (
                                <span className="text-green-600">{statusInfo.status}</span>
                              );
                            })()}
                          </div>
                        </div>

                        <Button
                          variant="outline"
                          className="w-full mt-4 text-sm h-8"
                          onClick={() => {
                            // Set the student data and switch to student mode to view individual report
                            setReportMode('student');
                            setStudentSid(studentReport.student?.sid || studentReport.student?.id);
                            setFormData(prev => ({
                              ...prev,
                              studentId: studentReport.student?.sid || studentReport.student?.id,
                              studentName: studentReport.student?.name || 'Unknown Student'
                            }));
                            setReportData(studentReport);
                          }}
                        >
                          View Full Report
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-12">
                <div className="bg-yellow-100 p-3 rounded-full mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-yellow-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <p className="text-gray-800 font-medium mb-2">No Report Data Available</p>
                <p className="text-gray-600 max-w-md text-center">
                  There is no report card data available for the selected criteria. Please try different parameters or contact the administrator.
                </p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
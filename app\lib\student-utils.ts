import { prisma } from '@/lib/prisma';

/**
 * Generates a unique Student ID (SID) for a new student based on their class
 * Format: {className}{sequential number} e.g., 1A1, 1A2, 1B1, etc.
 */
export async function generateStudentSID(className: string): Promise<string> {
  // Get all students in the class, sorted by SID
  const classStudents = await prisma.student.findMany({
    where: { className },
    orderBy: { sid: 'asc' },
  });

  // If no students exist in this class, start with 1
  if (classStudents.length === 0) {
    return `${className}1`;
  }

  // Extract the highest number from existing SIDs
  const sidPattern = new RegExp(`^${className}(\\d+)$`);
  let highestNumber = 0;

  for (const student of classStudents) {
    const match = student.sid.match(sidPattern);
    if (match && match[1]) {
      const num = parseInt(match[1], 10);
      if (num > highestNumber) {
        highestNumber = num;
      }
    }
  }

  // Return the next SID in sequence
  return `${className}${highestNumber + 1}`;
}

/**
 * Regenerates SIDs for all students in a class
 * This is useful when students are deleted or when we need to reorder
 */
export async function regenerateClassSIDs(className: string): Promise<void> {
  // Get all students in the class, sorted by name
  const classStudents = await prisma.student.findMany({
    where: { className },
    orderBy: { name: 'asc' },
  });

  // Update each student with a new sequential SID
  for (let i = 0; i < classStudents.length; i++) {
    const newSID = `${className}${i + 1}`;
    await prisma.student.update({
      where: { id: classStudents[i].id },
      data: { sid: newSID },
    });
  }
}

/**
 * Bulk generates SIDs for a list of new students
 * Used during CSV import
 */
export async function bulkGenerateSIDs(
  students: { className: string }[]
): Promise<string[]> {
  // Group students by class
  const classCounts: Record<string, number> = {};

  // Get current highest SID for each class
  for (const student of students) {
    const { className } = student;
    if (!classCounts[className]) {
      const highestSID = await getHighestSIDForClass(className);
      classCounts[className] = highestSID;
    }
  }

  // Generate SIDs for each student
  const sids: string[] = [];

  for (const student of students) {
    const { className } = student;
    classCounts[className]++;
    sids.push(`${className}${classCounts[className]}`);
  }

  return sids;
}

/**
 * Gets the highest SID number for a class
 */
async function getHighestSIDForClass(className: string): Promise<number> {
  const classStudents = await prisma.student.findMany({
    where: { className },
    orderBy: { sid: 'desc' },
    take: 1,
  });

  if (classStudents.length === 0) {
    return 0;
  }

  const sidPattern = new RegExp(`^${className}(\\d+)$`);
  const match = classStudents[0].sid.match(sidPattern);

  if (match && match[1]) {
    return parseInt(match[1], 10);
  }

  return 0;
}

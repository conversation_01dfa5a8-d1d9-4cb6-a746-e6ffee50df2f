import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { marks } = body

    // Validate request body
    if (!marks || !Array.isArray(marks) || marks.length === 0) {
      return NextResponse.json(
        { error: 'Marks array is required and must not be empty' },
        { status: 400 }
      )
    }

    console.log(`Processing ${marks.length} marks in bulk submission`)

    // Validate each mark object
    for (const mark of marks) {
      if (!mark.studentId || !mark.className || !mark.subject || !mark.term || !mark.academicYear) {
        return NextResponse.json(
          { error: 'Each mark must have studentId, className, subject, term, and academicYear' },
          { status: 400 }
        )
      }

      if (typeof mark.marks !== 'number' || typeof mark.totalMarks !== 'number') {
        return NextResponse.json(
          { error: 'Marks and totalMarks must be numbers' },
          { status: 400 }
        )
      }

      if (mark.marks < 0 || mark.marks > mark.totalMarks) {
        return NextResponse.json(
          { error: `Invalid marks for student ${mark.studentName}: ${mark.marks}. Must be between 0 and ${mark.totalMarks}` },
          { status: 400 }
        )
      }
    }

    // Separate marks into creates and updates
    const marksToCreate = marks.filter(mark => !mark.markId)
    const marksToUpdate = marks.filter(mark => mark.markId)

    console.log(`Creating ${marksToCreate.length} new marks, updating ${marksToUpdate.length} existing marks`)

    // Perform bulk operations using transaction
    const results = await prisma.$transaction(async (tx) => {
      const createdMarks = []
      const updatedMarks = []

      // Create new marks
      for (const mark of marksToCreate) {
        // Check if mark already exists (prevent duplicates)
        const existingMark = await tx.mark.findFirst({
          where: {
            studentId: mark.studentId,
            className: mark.className,
            subject: mark.subject,
            term: mark.term,
            academicYear: mark.academicYear,
          }
        })

        if (existingMark) {
          // If mark exists, update it instead
          const updated = await tx.mark.update({
            where: { id: existingMark.id },
            data: {
              marks: mark.marks,
              totalMarks: mark.totalMarks,
              remarks: mark.remarks || '',
              updatedAt: new Date(),
            },
            include: {
              student: {
                select: {
                  name: true,
                  sid: true,
                },
              },
            },
          })
          updatedMarks.push(updated)
        } else {
          // Create new mark
          const created = await tx.mark.create({
            data: {
              studentId: mark.studentId,
              className: mark.className,
              subject: mark.subject,
              term: mark.term,
              academicYear: mark.academicYear,
              marks: mark.marks,
              totalMarks: mark.totalMarks,
              remarks: mark.remarks || '',
            },
            include: {
              student: {
                select: {
                  name: true,
                  sid: true,
                },
              },
            },
          })
          createdMarks.push(created)
        }
      }

      // Update existing marks
      for (const mark of marksToUpdate) {
        const updated = await tx.mark.update({
          where: { id: mark.markId },
          data: {
            marks: mark.marks,
            totalMarks: mark.totalMarks,
            remarks: mark.remarks || '',
            updatedAt: new Date(),
          },
          include: {
            student: {
              select: {
                name: true,
                sid: true,
              },
            },
          },
        })
        updatedMarks.push(updated)
      }

      return { createdMarks, updatedMarks }
    })

    const totalProcessed = results.createdMarks.length + results.updatedMarks.length

    console.log(`Successfully processed ${totalProcessed} marks (${results.createdMarks.length} created, ${results.updatedMarks.length} updated)`)

    // Return success response with processed marks info
    const processedMarksInfo = [
      ...results.createdMarks.map(mark => ({
        id: mark.id,
        studentName: mark.student.name,
        studentSid: mark.student.sid,
        marks: mark.marks,
        totalMarks: mark.totalMarks,
        subject: mark.subject,
        className: mark.className,
        term: mark.term,
        academicYear: mark.academicYear,
        action: 'created'
      })),
      ...results.updatedMarks.map(mark => ({
        id: mark.id,
        studentName: mark.student.name,
        studentSid: mark.student.sid,
        marks: mark.marks,
        totalMarks: mark.totalMarks,
        subject: mark.subject,
        className: mark.className,
        term: mark.term,
        academicYear: mark.academicYear,
        action: 'updated'
      }))
    ]

    return NextResponse.json({
      message: `Successfully processed ${totalProcessed} marks`,
      processedCount: totalProcessed,
      createdCount: results.createdMarks.length,
      updatedCount: results.updatedMarks.length,
      processedMarks: processedMarksInfo,
    })
  } catch (error) {
    console.error('Error submitting bulk marks:', error)
    
    // Handle specific Prisma errors
    if (error instanceof Error) {
      if (error.message.includes('Unique constraint failed')) {
        return NextResponse.json(
          { error: 'Duplicate mark entry detected. A mark already exists for one or more students.' },
          { status: 409 }
        )
      }
      if (error.message.includes('Foreign key constraint failed')) {
        return NextResponse.json(
          { error: 'Invalid student or class reference. Please check the data.' },
          { status: 400 }
        )
      }
    }

    return NextResponse.json(
      { error: 'Failed to submit bulk marks' },
      { status: 500 }
    )
  }
}

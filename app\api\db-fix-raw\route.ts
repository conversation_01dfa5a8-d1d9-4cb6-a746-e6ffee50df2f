import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

export async function GET() {
  let prisma: PrismaClient | null = null;

  try {
    console.log('Attempting to fix database schema using raw SQL...');
    
    // Create a new PrismaClient instance specifically for this operation
    prisma = new PrismaClient({
      log: ['query', 'info', 'warn', 'error'],
    });

    // Connect to the database
    await prisma.$connect();
    console.log('Database connection successful');

    // Try different SQL syntax to add the column
    try {
      // MySQL syntax
      const result1 = await prisma.$executeRawUnsafe(
        `ALTER TABLE bank_payment_voucher ADD COLUMN accountCode VARCHAR(255);`
      );
      console.log('MySQL syntax result:', result1);
    } catch (error1) {
      console.log('MySQL syntax failed:', error1);
      
      try {
        // Try alternative syntax
        const result2 = await prisma.$executeRawUnsafe(
          `ALTER TABLE bank_payment_voucher ADD accountCode VARCHAR(255);`
        );
        console.log('Alternative syntax result:', result2);
      } catch (error2) {
        console.log('Alternative syntax failed:', error2);
        
        try {
          // Try checking if column exists first
          const result3 = await prisma.$executeRawUnsafe(`
            SELECT COUNT(*) as count
            FROM information_schema.columns
            WHERE table_schema = DATABASE()
            AND table_name = 'bank_payment_voucher'
            AND column_name = 'accountCode';
          `);
          console.log('Column check result:', result3);
          
          // Try one more syntax if column doesn't exist
          const result4 = await prisma.$executeRawUnsafe(`
            ALTER TABLE bank_payment_voucher 
            ADD COLUMN IF NOT EXISTS accountCode VARCHAR(255) NULL;
          `);
          console.log('Final syntax result:', result4);
        } catch (error3) {
          console.log('Final attempt failed:', error3);
          throw error3;
        }
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Database schema update attempted with multiple methods',
    });
  } catch (error) {
    console.error('Error fixing database schema:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : null
    }, { status: 500 });
  } finally {
    // Clean up the connection
    if (prisma) {
      await prisma.$disconnect().catch(e => console.error('Error disconnecting from database:', e));
    }
  }
}

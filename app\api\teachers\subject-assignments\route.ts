import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const teacherId = searchParams.get('teacherId')
    
    console.log('Fetching subject assignments...', { teacherId })
    
    if (!teacherId) {
      return NextResponse.json(
        { error: 'Teacher ID is required' },
        { status: 400 }
      )
    }

    // Check if teacher exists in either Teacher table or User table with teacher role
    let teacher = await prisma.teacher.findUnique({
      where: { id: teacherId }
    })

    let teacherFromUser = null
    if (!teacher) {
      teacherFromUser = await prisma.user.findFirst({
        where: {
          id: teacherId,
          role: 'TEACHER',
          status: 'active'
        }
      })
    }

    if (!teacher && !teacherFromUser) {
      return NextResponse.json(
        { error: 'Teacher not found in either Teacher table or User table with teacher role' },
        { status: 404 }
      )
    }

    // Get subject assignments based on teacher source
    let assignments = []

    if (teacher) {
      // Teacher is from Teacher table - use SubjectTeacher model
      assignments = await prisma.subjectTeacher.findMany({
        where: { teacherId },
        include: {
          subject: {
            include: {
              class: true
            }
          }
        }
      })
    } else {
      // Teacher is from User table - use TeacherAssignment model
      const teacherAssignments = await prisma.teacherAssignment.findMany({
        where: {
          teacherId,
          isHRTeacher: false, // Only get subject assignments, not HR assignments
          subjectId: { not: null } // Only get assignments with subjects
        },
        include: {
          subject: {
            include: {
              class: true
            }
          }
        }
      })

      // Transform to match the expected format
      assignments = teacherAssignments.map(assignment => ({
        id: assignment.id,
        teacherId: assignment.teacherId,
        subjectId: assignment.subjectId,
        createdAt: assignment.createdAt,
        updatedAt: assignment.updatedAt,
        subject: assignment.subject
      }))
    }

    console.log(`Found ${assignments.length} subject assignments for teacher ${teacherId}`)
    
    return NextResponse.json(assignments)
  } catch (error) {
    console.error('Error fetching subject assignments:', error)
    return NextResponse.json(
      { error: 'Failed to fetch subject assignments' },
      { status: 500 }
    )
  }
}

export async function POST(request: Request) {
  try {
    console.log('Creating subject assignments...')
    const data = await request.json()
    
    const { teacherId, subjects } = data
    
    if (!teacherId || !subjects || !Array.isArray(subjects)) {
      return NextResponse.json(
        { error: 'Teacher ID and subjects array are required' },
        { status: 400 }
      )
    }

    // Check if teacher exists in either Teacher table or User table with teacher role
    let teacher = await prisma.teacher.findUnique({
      where: { id: teacherId }
    })

    let teacherFromUser = null
    if (!teacher) {
      teacherFromUser = await prisma.user.findFirst({
        where: {
          id: teacherId,
          role: 'TEACHER',
          status: 'active'
        }
      })
    }

    if (!teacher && !teacherFromUser) {
      return NextResponse.json(
        { error: 'Teacher not found in either Teacher table or User table with teacher role' },
        { status: 404 }
      )
    }

    const teacherName = teacher?.name || teacherFromUser?.name || 'Unknown Teacher'

    // Verify all subjects exist
    const subjectRecords = await prisma.subject.findMany({
      where: {
        id: {
          in: subjects
        }
      }
    })

    if (subjectRecords.length !== subjects.length) {
      return NextResponse.json(
        { error: 'One or more subjects not found' },
        { status: 404 }
      )
    }

    // Handle subject assignments based on teacher source
    let assignments = []

    if (teacher) {
      // Teacher is from Teacher table - use SubjectTeacher model
      // Delete existing subject assignments for this teacher
      await prisma.subjectTeacher.deleteMany({
        where: { teacherId }
      })

      // Create new subject assignments
      assignments = await Promise.all(
        subjects.map(subjectId =>
          prisma.subjectTeacher.create({
            data: {
              teacherId,
              subjectId
            },
            include: {
              subject: {
                include: {
                  class: true
                }
              },
              teacher: true
            }
          })
        )
      )
    } else {
      // Teacher is from User table - use TeacherAssignment model
      // Delete existing subject assignments for this teacher (excluding HR assignments)
      await prisma.teacherAssignment.deleteMany({
        where: {
          teacherId,
          isHRTeacher: false // Only delete subject assignments, not HR assignments
        }
      })

      // Create new subject assignments
      assignments = await Promise.all(
        subjects.map(subjectId =>
          prisma.teacherAssignment.create({
            data: {
              teacherId,
              subjectId,
              isHRTeacher: false
            },
            include: {
              subject: {
                include: {
                  class: true
                }
              },
              teacher: {
                select: {
                  id: true,
                  name: true,
                  email: true
                }
              }
            }
          })
        )
      )
    }

    console.log(`Created ${assignments.length} subject assignments for teacher ${teacherName}`)
    
    return NextResponse.json({
      message: 'Subject assignments created successfully',
      assignments
    })
  } catch (error) {
    console.error('Error creating subject assignments:', error)
    return NextResponse.json(
      { error: 'Failed to create subject assignments' },
      { status: 500 }
    )
  }
}

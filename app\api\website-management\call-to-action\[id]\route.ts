import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET a specific call to action
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id
    
    const callToAction = await prisma.callToAction.findUnique({
      where: { id }
    })
    
    if (!callToAction) {
      return NextResponse.json({
        error: 'Call to action not found'
      }, { status: 404 })
    }
    
    return NextResponse.json(callToAction)
  } catch (error) {
    console.error('Error fetching call to action:', error)
    return NextResponse.json({
      error: error.message || 'Failed to fetch call to action'
    }, { status: 500 })
  }
}

// PUT (update) a call to action
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id
    const data = await request.json()
    
    // Check if the call to action exists
    const existingCallToAction = await prisma.callToAction.findUnique({
      where: { id }
    })
    
    if (!existingCallToAction) {
      return NextResponse.json({
        error: 'Call to action not found'
      }, { status: 404 })
    }
    
    // Update the call to action
    const updatedCallToAction = await prisma.callToAction.update({
      where: { id },
      data: {
        title: data.title !== undefined ? data.title : existingCallToAction.title,
        description: data.description !== undefined ? data.description : existingCallToAction.description,
        primaryBtnText: data.primaryBtnText !== undefined ? data.primaryBtnText : existingCallToAction.primaryBtnText,
        primaryBtnLink: data.primaryBtnLink !== undefined ? data.primaryBtnLink : existingCallToAction.primaryBtnLink,
        secondaryBtnText: data.secondaryBtnText !== undefined ? data.secondaryBtnText : existingCallToAction.secondaryBtnText,
        secondaryBtnLink: data.secondaryBtnLink !== undefined ? data.secondaryBtnLink : existingCallToAction.secondaryBtnLink,
        isActive: data.isActive !== undefined ? data.isActive : existingCallToAction.isActive
      }
    })
    
    return NextResponse.json(updatedCallToAction)
  } catch (error) {
    console.error('Error updating call to action:', error)
    return NextResponse.json({
      error: error.message || 'Failed to update call to action'
    }, { status: 500 })
  }
}

// DELETE a call to action
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id
    
    // Check if the call to action exists
    const existingCallToAction = await prisma.callToAction.findUnique({
      where: { id }
    })
    
    if (!existingCallToAction) {
      return NextResponse.json({
        error: 'Call to action not found'
      }, { status: 404 })
    }
    
    // Delete the call to action
    await prisma.callToAction.delete({
      where: { id }
    })
    
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting call to action:', error)
    return NextResponse.json({
      error: error.message || 'Failed to delete call to action'
    }, { status: 500 })
  }
}

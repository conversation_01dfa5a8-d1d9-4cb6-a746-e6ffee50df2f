"use client";

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { FaCheckCircle, FaFileAlt, FaIdCard, FaUserGraduate, FaStethoscope, FaBook } from 'react-icons/fa';
import { Button } from '@/app/components/ui/button';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/app/components/ui/accordion';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.5 },
  },
};

// Required documents data
const requiredDocuments = [
  {
    id: 1,
    title: "Completed Application Form",
    description: "The official application form must be completed in full, signed by parents/guardians, and submitted with the non-refundable application fee.",
    icon: <FaFileAlt className="text-4xl text-blue-600" />,
  },
  {
    id: 2,
    title: "Identification Documents",
    description: "Birth certificate, passport, or other legal proof of identity and age. For non-citizens, appropriate visa or residency documentation is required.",
    icon: <FaIdCard className="text-4xl text-green-600" />,
  },
  {
    id: 3,
    title: "Academic Records",
    description: "Transcripts, report cards, and standardized test scores from the previous two years. For kindergarten applicants, any preschool evaluations if available.",
    icon: <FaUserGraduate className="text-4xl text-purple-600" />,
  },
  {
    id: 4,
    title: "Health Records",
    description: "Immunization records, health examination form completed by a physician, and information about any medical conditions or special needs.",
    icon: <FaStethoscope className="text-4xl text-red-600" />,
  },
  {
    id: 5,
    title: "Recommendation Letters",
    description: "Two letters of recommendation: one from a current teacher and one from another adult who knows the student well (not a family member).",
    icon: <FaFileAlt className="text-4xl text-amber-600" />,
  },
  {
    id: 6,
    title: "Student Work Samples",
    description: "For grades 3 and above, samples of recent academic work, including writing samples and math work. For arts-focused students, relevant portfolios.",
    icon: <FaBook className="text-4xl text-indigo-600" />,
  },
];

// FAQ data
const faqs = [
  {
    question: "What are the age requirements for admission?",
    answer: "For Kindergarten, students must be 5 years old by September 1 of the academic year. For other grades, appropriate age requirements apply, typically aligning with standard grade-level ages. In exceptional cases, we may consider students whose birthdays fall shortly after the cutoff date, based on readiness assessments and previous academic performance."
  },
  {
    question: "Is prior Islamic education required?",
    answer: "No, prior Islamic education is not required for admission. We welcome students from all backgrounds. Our curriculum is designed to accommodate students with varying levels of Islamic knowledge. New students will receive appropriate support to integrate into our Islamic studies program."
  },
  {
    question: "Do you accept mid-year transfers?",
    answer: "Yes, we accept mid-year transfers on a case-by-case basis, depending on space availability and the student's academic standing. Mid-year applicants follow the same application process but may have an expedited timeline. We work closely with families to ensure a smooth transition for mid-year students."
  },
  {
    question: "What kind of entrance assessment will my child take?",
    answer: "Entrance assessments vary by grade level. For Kindergarten and lower elementary, assessments focus on basic readiness skills, language development, and social interaction. For upper elementary through high school, assessments cover grade-appropriate content in reading, writing, mathematics, and for high school, additional subject areas. The assessment is designed to determine proper grade placement and identify any areas needing support."
  },
  {
    question: "Are there English language proficiency requirements?",
    answer: "Students should have sufficient English proficiency to participate in classroom instruction. For non-native English speakers, we conduct an English language assessment as part of the admissions process. We offer English language support for students who need additional assistance, but our primary instruction is in English."
  },
  {
    question: "Do you offer financial aid or scholarships?",
    answer: "Yes, we offer need-based financial aid and merit scholarships. Financial aid applications are reviewed separately from admission applications and do not affect admission decisions. Merit scholarships are available for students demonstrating exceptional academic achievement, leadership, or Islamic character. Separate applications are required for both financial aid and scholarships."
  },
  {
    question: "What if my child has special learning needs?",
    answer: "We evaluate each student's needs individually. We can accommodate mild to moderate learning differences and provide reasonable accommodations. Parents should disclose any diagnosed learning differences, IEPs, or special needs during the application process so we can determine if our resources can adequately support the student. For specific questions about your child's needs, please contact our admissions office."
  },
];

export default function RequirementsPage() {
  return (
    <div className="min-h-screen pt-20">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-blue-900 to-indigo-900 text-white">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{ backgroundImage: 'url(/images/pattern.svg)', backgroundSize: '20px' }}></div>
        </div>
        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center max-w-3xl mx-auto"
          >
            <h1 className="text-4xl md:text-5xl font-bold mb-6">Admission Requirements</h1>
            <p className="text-xl text-blue-100">
              Everything you need to know about qualifying for admission to Alfalah Islamic School
            </p>
          </motion.div>
        </div>
      </section>

      {/* Introduction Section */}
      <section className="py-16 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.7 }}
              viewport={{ once: true }}
              className="space-y-6 order-2 lg:order-1"
            >
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Admission Criteria</h2>
              <p className="text-gray-600 dark:text-gray-300">
                At Alfalah Islamic School, we seek students who will thrive in our academically rigorous, values-based educational environment. Our admission process is designed to identify students who:
              </p>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <FaCheckCircle className="text-green-600 mt-1 mr-2 flex-shrink-0" />
                  <span className="text-gray-600 dark:text-gray-300">Demonstrate academic readiness for their grade level</span>
                </li>
                <li className="flex items-start">
                  <FaCheckCircle className="text-green-600 mt-1 mr-2 flex-shrink-0" />
                  <span className="text-gray-600 dark:text-gray-300">Show potential for growth and development</span>
                </li>
                <li className="flex items-start">
                  <FaCheckCircle className="text-green-600 mt-1 mr-2 flex-shrink-0" />
                  <span className="text-gray-600 dark:text-gray-300">Exhibit good character and behavior</span>
                </li>
                <li className="flex items-start">
                  <FaCheckCircle className="text-green-600 mt-1 mr-2 flex-shrink-0" />
                  <span className="text-gray-600 dark:text-gray-300">Come from families who support our educational philosophy and Islamic values</span>
                </li>
              </ul>
              <p className="text-gray-600 dark:text-gray-300">
                We welcome students of all backgrounds who meet our admission criteria and whose families are committed to partnering with us in their child's education.
              </p>
              <div className="pt-4 flex flex-wrap gap-4">
                <Link href="/website/admissions/how-to-apply">
                  <Button variant="outline">
                    How to Apply
                  </Button>
                </Link>
                <Link href="/website/admissions/tuition-fees">
                  <Button variant="outline">
                    Tuition & Fees
                  </Button>
                </Link>
                <Link href="/website/admissions/forms">
                  <Button variant="outline">
                    Download Forms
                  </Button>
                </Link>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.7 }}
              viewport={{ once: true }}
              className="order-1 lg:order-2"
            >
              <div className="relative h-[400px] rounded-lg overflow-hidden shadow-xl">
                <Image
                  src="/images/portrait.jpg" // Replace with actual image
                  alt="Students at Alfalah Islamic School"
                  fill
                  className="object-cover"
                />
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Required Documents */}
      <section id="required-documents" className="py-16 bg-gray-50 dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Required Documents</h2>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              The following documents must be submitted as part of the application process
            </p>
          </motion.div>

          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          >
            {requiredDocuments.map((doc) => (
              <motion.div
                key={doc.id}
                variants={itemVariants}
                className="bg-white dark:bg-gray-900 p-6 rounded-lg shadow-md flex flex-col h-full"
              >
                <div className="mb-4">{doc.icon}</div>
                <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">{doc.title}</h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">{doc.description}</p>
              </motion.div>
            ))}
          </motion.div>

          <div className="mt-8 text-center">
            <p className="text-gray-600 dark:text-gray-300 italic">
              Note: Additional documents may be requested during the application process as needed.
            </p>
          </div>
        </div>
      </section>

      {/* Academic Requirements */}
      <section id="academic-requirements" className="py-16 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Academic Requirements</h2>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Academic standards for admission vary by grade level
            </p>
          </motion.div>

          <div className="max-w-4xl mx-auto">
            <div className="space-y-8">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                viewport={{ once: true }}
                className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg shadow-md"
              >
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Elementary School (Grades K-5)</h3>
                <ul className="space-y-3">
                  <li className="flex items-start">
                    <FaCheckCircle className="text-green-600 mt-1 mr-2 flex-shrink-0" />
                    <span className="text-gray-600 dark:text-gray-300">Kindergarten: Demonstration of school readiness through assessment</span>
                  </li>
                  <li className="flex items-start">
                    <FaCheckCircle className="text-green-600 mt-1 mr-2 flex-shrink-0" />
                    <span className="text-gray-600 dark:text-gray-300">Grades 1-5: Grade-appropriate performance in reading, writing, and mathematics</span>
                  </li>
                  <li className="flex items-start">
                    <FaCheckCircle className="text-green-600 mt-1 mr-2 flex-shrink-0" />
                    <span className="text-gray-600 dark:text-gray-300">Satisfactory conduct and effort reports from previous school</span>
                  </li>
                  <li className="flex items-start">
                    <FaCheckCircle className="text-green-600 mt-1 mr-2 flex-shrink-0" />
                    <span className="text-gray-600 dark:text-gray-300">Successful completion of entrance assessment</span>
                  </li>
                </ul>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                viewport={{ once: true }}
                className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg shadow-md"
              >
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Middle School (Grades 6-8)</h3>
                <ul className="space-y-3">
                  <li className="flex items-start">
                    <FaCheckCircle className="text-green-600 mt-1 mr-2 flex-shrink-0" />
                    <span className="text-gray-600 dark:text-gray-300">Minimum C average (2.0 GPA) in core academic subjects</span>
                  </li>
                  <li className="flex items-start">
                    <FaCheckCircle className="text-green-600 mt-1 mr-2 flex-shrink-0" />
                    <span className="text-gray-600 dark:text-gray-300">Satisfactory performance on standardized tests (if available)</span>
                  </li>
                  <li className="flex items-start">
                    <FaCheckCircle className="text-green-600 mt-1 mr-2 flex-shrink-0" />
                    <span className="text-gray-600 dark:text-gray-300">Positive recommendation from current teacher</span>
                  </li>
                  <li className="flex items-start">
                    <FaCheckCircle className="text-green-600 mt-1 mr-2 flex-shrink-0" />
                    <span className="text-gray-600 dark:text-gray-300">Successful completion of entrance assessment in reading, writing, and mathematics</span>
                  </li>
                  <li className="flex items-start">
                    <FaCheckCircle className="text-green-600 mt-1 mr-2 flex-shrink-0" />
                    <span className="text-gray-600 dark:text-gray-300">Good disciplinary record</span>
                  </li>
                </ul>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                viewport={{ once: true }}
                className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg shadow-md"
              >
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">High School (Grades 9-12)</h3>
                <ul className="space-y-3">
                  <li className="flex items-start">
                    <FaCheckCircle className="text-green-600 mt-1 mr-2 flex-shrink-0" />
                    <span className="text-gray-600 dark:text-gray-300">Minimum B- average (2.7 GPA) in core academic subjects</span>
                  </li>
                  <li className="flex items-start">
                    <FaCheckCircle className="text-green-600 mt-1 mr-2 flex-shrink-0" />
                    <span className="text-gray-600 dark:text-gray-300">Satisfactory performance on standardized tests</span>
                  </li>
                  <li className="flex items-start">
                    <FaCheckCircle className="text-green-600 mt-1 mr-2 flex-shrink-0" />
                    <span className="text-gray-600 dark:text-gray-300">Strong recommendations from current teachers</span>
                  </li>
                  <li className="flex items-start">
                    <FaCheckCircle className="text-green-600 mt-1 mr-2 flex-shrink-0" />
                    <span className="text-gray-600 dark:text-gray-300">Successful completion of comprehensive entrance assessment</span>
                  </li>
                  <li className="flex items-start">
                    <FaCheckCircle className="text-green-600 mt-1 mr-2 flex-shrink-0" />
                    <span className="text-gray-600 dark:text-gray-300">Personal interview with department heads</span>
                  </li>
                  <li className="flex items-start">
                    <FaCheckCircle className="text-green-600 mt-1 mr-2 flex-shrink-0" />
                    <span className="text-gray-600 dark:text-gray-300">Exemplary disciplinary record</span>
                  </li>
                  <li className="flex items-start">
                    <FaCheckCircle className="text-green-600 mt-1 mr-2 flex-shrink-0" />
                    <span className="text-gray-600 dark:text-gray-300">Evidence of extracurricular involvement and/or community service</span>
                  </li>
                </ul>
              </motion.div>
            </div>
          </div>
        </div>
      </section>

      {/* Frequently Asked Questions */}
      <section id="faqs" className="py-16 bg-gray-50 dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Frequently Asked Questions</h2>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Answers to common questions about our admission requirements
            </p>
          </motion.div>

          <div className="max-w-3xl mx-auto">
            <Accordion type="single" collapsible className="space-y-4">
              {faqs.map((faq, index) => (
                <AccordionItem 
                  key={index} 
                  value={`item-${index}`}
                  className="bg-white dark:bg-gray-900 rounded-lg shadow-md overflow-hidden"
                >
                  <AccordionTrigger className="px-6 py-4 text-left font-semibold text-gray-900 dark:text-white hover:no-underline">
                    {faq.question}
                  </AccordionTrigger>
                  <AccordionContent className="px-6 pb-4 text-gray-600 dark:text-gray-300">
                    {faq.answer}
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-12 bg-blue-600">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="text-white mb-6 md:mb-0">
              <h2 className="text-2xl font-bold mb-2">Ready to Begin the Application Process?</h2>
              <p className="text-blue-100">Start your journey with Alfalah Islamic School today.</p>
            </div>
            <div className="flex flex-col sm:flex-row gap-4">
              <Link href="/website/admissions/how-to-apply">
                <Button className="bg-white text-blue-600 hover:bg-blue-50">
                  How to Apply
                </Button>
              </Link>
              <Link href="/website/contact">
                <Button variant="outline" className="border-white text-white hover:bg-blue-700">
                  Contact Admissions
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

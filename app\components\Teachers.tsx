"use client"

import React, { useState } from 'react'
import { <PERSON><PERSON> } from './ui/button'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/table'
import { Input } from './ui/input'
import { Search, Edit, Trash2, SlidersHorizontal, Plus, Mail, Phone, User, BookOpen } from 'lucide-react'
import {
  RecursionSafeDialog,
  RecursionSafeDialogContent,
  RecursionSafeDialogHeader,
  RecursionSafeDialogTitle,
  RecursionSafeDialogDescription,
  RecursionSafeDialogFooter
} from "./RecursionSafeDialog"

import { Label } from "./ui/label"
import { CustomSelect } from './ui/custom-select'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { formatTeacherName } from "@/app/utils/formatters"
import { useAuth } from '../contexts/auth-context'
import { usePermissionDeniedDialog } from './PermissionDeniedDialog'

interface Teacher {
  id: string
  name: string
  fatherName: string
  gender: string
  email: string
  subject: string
  mobile: string
}

// List of subjects
const subjectsList = [
  'Arabic', 'Quran', 'Islamic Education', 'Terbia', 'English', 'Somali', 'Amharic',
  'Maths', 'Chemistry', 'Biology', 'Physics', 'IT', 'Agriculture', 'Economics',
  'Geography', 'History', 'CTE', 'Citizenship', 'HPE', 'General Science', 'Social Science'
]

export function Teachers() {
  const queryClient = useQueryClient()
  const { user } = useAuth()
  const { showDialog, PermissionDialog } = usePermissionDeniedDialog()
  const [searchTerm, setSearchTerm] = useState('')
  const [open, setOpen] = useState(false)
  const [isEditing, setIsEditing] = useState(false)
  const [newTeacher, setNewTeacher] = useState({
    id: '',
    name: '',
    fatherName: '',
    gender: '',
    email: '',
    subject: '',
    mobile: ''
  })

  // Fetch teachers
  const { data: teachers, isLoading } = useQuery({
    queryKey: ['teachers'],
    queryFn: async () => {
      const res = await fetch('/api/teachers')
      if (!res.ok) throw new Error('Failed to fetch teachers')
      return res.json()
    }
  })

  // Add teacher mutation
  const addTeacherMutation = useMutation({
    mutationFn: async (newTeacher: Omit<Teacher, 'id'>) => {
      const res = await fetch('/api/teachers', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newTeacher),
      })
      if (!res.ok) throw new Error('Failed to add teacher')
      return res.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teachers'] })
    },
  })

  // Update teacher mutation
  const updateTeacherMutation = useMutation({
    mutationFn: async (updatedTeacher: Teacher) => {
      const res = await fetch(`/api/teachers/${updatedTeacher.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updatedTeacher),
      })
      if (!res.ok) throw new Error('Failed to update teacher')
      return res.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teachers'] })
    },
  })

  // Delete teacher mutation
  const deleteTeacherMutation = useMutation({
    mutationFn: async (id: string) => {
      const res = await fetch(`/api/teachers/${id}`, {
        method: 'DELETE',
      })
      if (!res.ok) throw new Error('Failed to delete teacher')
      return res.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teachers'] })
    },
  })

  const filteredTeachers = (teachers ?? []).filter((teacher: { name: string; subject: string; email: string }) =>
    teacher.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    teacher.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
    teacher.email.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleDelete = (id: string) => {
    deleteTeacherMutation.mutate(id)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setNewTeacher({
      ...newTeacher,
      [name]: name.includes('name') ? formatTeacherName(value) : value
    })
  }

  const handleSelectChange = (name: string, value: string) => {
    setNewTeacher({
      ...newTeacher,
      [name]: value
    })
  }

  const handleEdit = (teacher: Teacher) => {
    setNewTeacher({
      id: teacher.id || '',
      name: teacher.name || '',
      fatherName: teacher.fatherName || '',
      gender: teacher.gender || '',
      email: teacher.email || '',
      subject: teacher.subject || '',
      mobile: teacher.mobile || ''
    })
    setIsEditing(true)
    setOpen(true)
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (isEditing) {
      updateTeacherMutation.mutate({
        id: newTeacher.id,
        name: newTeacher.name,
        fatherName: newTeacher.fatherName,
        gender: newTeacher.gender,
        email: newTeacher.email,
        subject: newTeacher.subject,
        mobile: newTeacher.mobile
      })
    } else {
      addTeacherMutation.mutate({
        name: newTeacher.name,
        fatherName: newTeacher.fatherName,
        gender: newTeacher.gender,
        email: newTeacher.email,
        subject: newTeacher.subject,
        mobile: newTeacher.mobile
      })
    }

    setNewTeacher({
      id: '',
      name: '',
      fatherName: '',
      gender: '',
      email: '',
      subject: '',
      mobile: ''
    })
    setIsEditing(false)
    setOpen(false)
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Teachers</h2>
        <Button
          variant="default"
          className="bg-indigo-600 hover:bg-indigo-700"
          onClick={() => {
            if (!user || !['SUPER_ADMIN', 'ADMIN', 'DATA_ENCODER'].includes(user.role)) {
              showDialog(
                'You do not have permission to add teachers',
                'Only Super Admin, Admin, and Data Encoder roles can add teachers.'
              );
              return;
            }
            setOpen(true);
          }}
        >
          <Plus className="mr-2 h-4 w-4" /> New Teacher
        </Button>
      </div>

      {/* Teacher Form Dialog */}
      <RecursionSafeDialog
        open={open}
        onOpenChange={(open) => {
          setOpen(open)
          if (!open) {
            setIsEditing(false)
            setNewTeacher({
              id: '',
              name: '',
              fatherName: '',
              gender: '',
              email: '',
              subject: '',
              mobile: ''
            })
          }
        }}
        maxWidth="max-w-2xl"
      >
        <RecursionSafeDialogHeader>
          <RecursionSafeDialogTitle>{isEditing ? 'Edit Teacher' : 'Add New Teacher'}</RecursionSafeDialogTitle>
          <RecursionSafeDialogDescription>
            {isEditing
              ? 'Edit the teacher details and click save when you\'re done.'
              : 'Fill in the teacher details and click save when you\'re done.'
            }
          </RecursionSafeDialogDescription>
        </RecursionSafeDialogHeader>

        <RecursionSafeDialogContent>
          <form onSubmit={handleSubmit} className="grid gap-6 py-4 px-2">
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="name" className="text-sm font-medium">
                    Teacher Name
                  </Label>
                  <div className="relative">
                    <User className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      id="name"
                      name="name"
                      placeholder="Full Name"
                      className="pl-9"
                      value={newTeacher.name}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                </div>
                <div className="grid gap-2">
                  <CustomSelect
                    label="Subject"
                    icon={BookOpen}
                    value={newTeacher.subject}
                    options={subjectsList}
                    onChange={(value) => handleSelectChange('subject', value as string)}
                    placeholder="Select Subject"
                    required
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="fatherName" className="text-sm font-medium">
                    Father's Name
                  </Label>
                  <Input
                    id="fatherName"
                    name="fatherName"
                    placeholder="Father's Name"
                    value={newTeacher.fatherName}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="gender" className="text-sm font-medium">
                    Gender
                  </Label>
                  <select
                    id="gender"
                    name="gender"
                    value={newTeacher.gender}
                    onChange={(e) => handleSelectChange('gender', e.target.value)}
                    required
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  >
                    <option value="">Select Gender</option>
                    <option value="Male">Male</option>
                    <option value="Female">Female</option>
                  </select>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="email" className="text-sm font-medium">
                    Email
                  </Label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      placeholder="Email Address"
                      className="pl-9"
                      value={newTeacher.email}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="mobile" className="text-sm font-medium">
                    Mobile Number
                  </Label>
                  <div className="relative">
                    <Phone className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      id="mobile"
                      name="mobile"
                      placeholder="******-567-8900"
                      className="pl-9"
                      value={newTeacher.mobile}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                </div>
              </div>

            <RecursionSafeDialogFooter>
              <Button variant="outline" onClick={() => {
                setOpen(false)
                setIsEditing(false)
                setNewTeacher({
                  id: '',
                  name: '',
                  fatherName: '',
                  gender: '',
                  email: '',
                  subject: '',
                  mobile: ''
                })
              }}>
                Cancel
              </Button>
              <Button type="submit">
                {isEditing ? 'Save Changes' : 'Add Teacher'}
              </Button>
            </RecursionSafeDialogFooter>
          </form>
        </RecursionSafeDialogContent>
      </RecursionSafeDialog>

      {(addTeacherMutation.isError || updateTeacherMutation.isError || deleteTeacherMutation.isError) && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
          <span className="block sm:inline">Failed to perform the requested action. Please try again.</span>
        </div>
      )}

      <div className="flex flex-col sm:flex-row justify-between items-center mb-6 gap-4">
        <div className="relative w-full sm:w-72">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search here..."
            className="pl-10"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="flex items-center gap-2 w-full sm:w-auto">
          <Button variant="outline" className="flex items-center gap-2">
            <SlidersHorizontal className="h-4 w-4" />
            Filters
          </Button>
          <div className="relative ml-2">
            <select className="bg-white border rounded-md py-2 px-4 pr-8 appearance-none text-sm focus:outline-none focus:ring-2 focus:ring-indigo-600">
              <option>Newest</option>
              <option>Oldest</option>
              <option>A-Z</option>
              <option>Z-A</option>
            </select>
            <div className="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <svg className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <input type="checkbox" className="h-4 w-4 rounded border-gray-300" />
              </TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Father Name</TableHead>
              <TableHead>Gender</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Subject</TableHead>
              <TableHead>Mobile</TableHead>
              <TableHead className="text-right">Action</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredTeachers.length > 0 ? (
              filteredTeachers.map((teacher: Teacher) => (
                <TableRow key={teacher.id}>
                  <TableCell>
                    <input type="checkbox" className="h-4 w-4 rounded border-gray-300" />
                  </TableCell>
                  <TableCell className="font-medium">
                    <div className="flex items-center gap-3">
                      <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 font-semibold">
                        {teacher.name.charAt(0)}
                      </div>
                      {teacher.name}
                    </div>
                  </TableCell>
                  <TableCell>{teacher.fatherName}</TableCell>
                  <TableCell>{teacher.gender}</TableCell>
                  <TableCell>{teacher.email}</TableCell>
                  <TableCell>
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      teacher.subject === 'Mathematics' || teacher.subject === 'Maths' ? 'bg-indigo-100 text-indigo-800' :
                      teacher.subject === 'English' ? 'bg-green-100 text-green-800' :
                      teacher.subject === 'Science' || teacher.subject === 'General Science' ? 'bg-blue-100 text-blue-800' :
                      teacher.subject === 'History' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-purple-100 text-purple-800'
                    }`}>
                      {teacher.subject}
                    </span>
                  </TableCell>
                  <TableCell>{teacher.mobile}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-indigo-600"
                        onClick={() => {
                          if (!user || !['SUPER_ADMIN', 'ADMIN', 'DATA_ENCODER'].includes(user.role)) {
                            showDialog(
                              'You do not have permission to edit teachers',
                              'Only Super Admin, Admin, and Data Encoder roles can edit teachers.'
                            );
                            return;
                          }
                          handleEdit(teacher);
                        }}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-red-600"
                        onClick={() => {
                          if (!user || !['SUPER_ADMIN', 'ADMIN', 'DATA_ENCODER'].includes(user.role)) {
                            showDialog(
                              'You do not have permission to delete teachers',
                              'Only Super Admin, Admin, and Data Encoder roles can delete teachers.'
                            );
                            return;
                          }
                          handleDelete(teacher.id);
                        }}
                        disabled={deleteTeacherMutation.isPending}
                      >
                        {deleteTeacherMutation.isPending ? (
                          <div className="animate-spin">...</div>
                        ) : (
                          <Trash2 className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-4 text-gray-500">
                  No teachers found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Permission Denied Dialog */}
      <PermissionDialog />
    </div>
  )
}

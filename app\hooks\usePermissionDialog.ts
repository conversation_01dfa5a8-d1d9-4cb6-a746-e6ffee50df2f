import { useState, useCallback, useEffect } from 'react'

interface UsePermissionDialogResult {
  isPermissionDialogOpen: boolean
  permissionMessage: string
  permissionTitle: string
  permissionResource: string
  permissionAction: string
  
  showPermissionDialog: (options: {
    message?: string;
    title?: string;
    resource?: string;
    action?: string;
  }) => void;
  
  hidePermissionDialog: () => void
  
  // Helper for API responses
  handlePermissionError: (error: any) => boolean
}

/**
 * Enhanced hook for handling permission denied dialogs across the application
 * 
 * @param defaultOptions Default options for the permission dialog
 * @returns Object with permission dialog state and functions to show/hide the dialog
 */
export function usePermissionDialog(
  defaultOptions = {
    message: 'You do not have permission to access this feature. Please contact your administrator if you believe this is an error.',
    title: 'Access Denied',
    resource: 'feature',
    action: 'access'
  }
): UsePermissionDialogResult {
  const [isPermissionDialogOpen, setIsPermissionDialogOpen] = useState(false)
  const [permissionMessage, setPermissionMessage] = useState(defaultOptions.message)
  const [permissionTitle, setPermissionTitle] = useState(defaultOptions.title)
  const [permissionResource, setPermissionResource] = useState(defaultOptions.resource)
  const [permissionAction, setPermissionAction] = useState(defaultOptions.action)

  const showPermissionDialog = useCallback((options: {
    message?: string;
    title?: string;
    resource?: string;
    action?: string;
  } = {}) => {
    if (options.message) setPermissionMessage(options.message)
    if (options.title) setPermissionTitle(options.title)
    if (options.resource) setPermissionResource(options.resource)
    if (options.action) setPermissionAction(options.action)
    
    setIsPermissionDialogOpen(true)
  }, [])

  const hidePermissionDialog = useCallback(() => {
    setIsPermissionDialogOpen(false)
  }, [])

  /**
   * Helper function to handle permission errors from API responses
   * Returns true if it was a permission error and was handled
   */
  const handlePermissionError = useCallback((error: any): boolean => {
    // Check if it's an Error object with a message
    const errorMessage = error instanceof Error 
      ? error.message 
      : typeof error === 'string'
        ? error
        : error?.error || 'An error occurred';
    
    // Check if it's a permission error
    if (
      errorMessage.includes('permission') || 
      errorMessage.includes('Access Denied') ||
      errorMessage.includes('Forbidden') ||
      (error?.status === 403)
    ) {
      // Extract resource and action if possible
      const permissionRegex = /permission to ([\w]+) ([\w\s]+)(?: for (.+))?/i;
      const matches = errorMessage.match(permissionRegex);
      
      if (matches) {
        const [, action, resource, context] = matches;
        
        showPermissionDialog({
          message: errorMessage,
          action: action || defaultOptions.action,
          resource: resource || defaultOptions.resource,
          title: 'Permission Denied'
        });
      } else {
        // If we can't parse the message, just show it as is
        showPermissionDialog({
          message: errorMessage,
          title: 'Permission Denied'
        });
      }
      
      return true;
    }
    
    return false;
  }, [defaultOptions.action, defaultOptions.resource, showPermissionDialog]);

  return {
    isPermissionDialogOpen,
    permissionMessage,
    permissionTitle,
    permissionResource,
    permissionAction,
    showPermissionDialog,
    hidePermissionDialog,
    handlePermissionError
  }
}

export default usePermissionDialog

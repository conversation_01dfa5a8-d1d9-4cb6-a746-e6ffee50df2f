"use client"

import React from 'react'
import DashboardLayout from '../components/DashboardLayout'
import { ReportCard } from '../components/ReportCardFixed'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import RoleGuard from '../components/RoleGuard'

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
})

// Access denied component
const AccessDenied = () => (
  <div className="flex items-center justify-center h-full">
    <div className="text-center">
      <h1 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h1>
      <p className="text-gray-600">You don't have permission to access this page.</p>
    </div>
  </div>
)

// Report card content
const ReportCardContent = () => (
  <div className="space-y-8">
    {/* Enhanced Header */}
    <div className="relative overflow-hidden rounded-3xl bg-gradient-to-r from-rose-600 via-pink-600 to-fuchsia-600 p-8 text-white shadow-2xl">
      <div className="absolute inset-0 bg-black/10"></div>
      <div className="relative flex items-center justify-between">
        <div>
          <h1 className="text-4xl font-bold tracking-tight mb-2 bg-gradient-to-r from-white to-rose-100 bg-clip-text text-transparent">
            Student Report Card
          </h1>
          <p className="text-rose-100 text-lg">
            Generate and manage comprehensive student performance reports and academic assessments
          </p>
        </div>
        <div className="hidden md:flex items-center gap-4">
          <div className="h-16 w-16 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center">
            <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
        </div>
      </div>
      {/* Decorative elements */}
      <div className="absolute -top-8 -right-8 w-32 h-32 bg-white/10 rounded-full blur-xl"></div>
      <div className="absolute -bottom-12 -left-12 w-40 h-40 bg-white/5 rounded-full blur-2xl"></div>
    </div>

    {/* Report Card Component */}
    <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border-0 overflow-hidden">
      <ReportCard />
    </div>
  </div>
)

export default function ReportCardPage() {
  return (
    <QueryClientProvider client={queryClient}>
      <DashboardLayout>
        <RoleGuard
          sidebarItems="reportcard"
          redirectOnAccessDenied={true}
          fallback={<AccessDenied />}
        >
          <ReportCardContent />
        </RoleGuard>
      </DashboardLayout>
    </QueryClientProvider>
  )
}
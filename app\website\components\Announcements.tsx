"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaBullhorn, FaCalendarAlt, FaArrowLeft, FaArrowRight } from 'react-icons/fa';

// Define the announcement interface
interface Announcement {
  id: string;
  title: string;
  content: string;
  date: string;
  type: string;
  isActive: boolean;
}

// Fallback announcements in case API fails
const fallbackAnnouncements = [
  {
    id: '1',
    title: 'School Closure Due to Weather',
    date: '2023-05-15T00:00:00.000Z',
    content: 'Due to severe weather conditions, the school will be closed on Monday, May 15th. Stay safe!',
    type: 'urgent',
    isActive: true
  },
  {
    id: '2',
    title: 'Parent-Teacher Conference',
    date: '2023-05-20T00:00:00.000Z',
    content: 'Parent-Teacher conferences will be held on May 20th. Please schedule your appointment online.',
    type: 'important',
    isActive: true
  },
  {
    id: '3',
    title: 'Annual Sports Day',
    date: '2023-06-10T00:00:00.000Z',
    content: 'Join us for our Annual Sports Day on June 10th. All parents are welcome to attend and cheer for their children.',
    type: 'event',
    isActive: true
  },
  {
    id: '4',
    title: 'Ramadan Fasting week',
    date: '2023-06-10T00:00:00.000Z',
    content: 'Join us for our Annual Sports Day on June 10th. All parents are welcome to attend and cheer for their children.',
    type: 'event',
    isActive: true
  }
];

export default function Announcements() {
  const [announcements, setAnnouncements] = useState<Announcement[]>(fallbackAnnouncements);
  const [currentAnnouncement, setCurrentAnnouncement] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch announcements from API
  useEffect(() => {
    const fetchAnnouncements = async () => {
      try {
        const response = await fetch('/api/website-management/announcements');
        if (response.ok) {
          const data = await response.json();
          // Filter only active announcements
          const activeAnnouncements = data.filter((announcement: Announcement) => announcement.isActive);
          if (activeAnnouncements.length > 0) {
            setAnnouncements(activeAnnouncements);
          }
        }
      } catch (error) {
        console.error('Error fetching announcements:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchAnnouncements();
  }, []);

  // Auto-advance announcements
  useEffect(() => {
    if (announcements.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentAnnouncement((prev) => (prev === announcements.length - 1 ? 0 : prev + 1));
    }, 7000);

    return () => clearInterval(interval);
  }, [announcements.length]);

  // Format date
  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('en-US', options);
  };

  // Handle navigation
  const goToPrev = () => {
    setCurrentAnnouncement((prev) => (prev === 0 ? announcements.length - 1 : prev - 1));
  };

  const goToNext = () => {
    setCurrentAnnouncement((prev) => (prev === announcements.length - 1 ? 0 : prev + 1));
  };

  // Get background color based on announcement type
  const getBackgroundColor = (type: string) => {
    switch (type) {
      case 'urgent':
        return 'bg-red-600';
      case 'important':
        return 'bg-amber-500';
      case 'event':
        return 'bg-green-600';
      default:
        return 'bg-blue-600';
    }
  };

  return (
    <div className="bg-gray-100 dark:bg-gray-800 py-4">
      <div className="container mx-auto px-4">
        <div className="relative bg-white dark:bg-gray-900 rounded-lg shadow-md overflow-hidden">
          {/* Announcement Carousel */}
          <div className="relative h-24 md:h-20">
            {announcements.map((announcement, index) => (
              <motion.div
                key={announcement.id}
                className="absolute inset-0 flex items-center px-4 md:px-6"
                initial={{ opacity: 0, x: 50 }}
                animate={{
                  opacity: currentAnnouncement === index ? 1 : 0,
                  x: currentAnnouncement === index ? 0 : 50,
                  zIndex: currentAnnouncement === index ? 10 : 0,
                }}
                transition={{ duration: 0.5 }}
              >
                <div className={`flex-shrink-0 w-12 h-12 rounded-full ${getBackgroundColor(announcement.type)} flex items-center justify-center mr-4`}>
                  <FaBullhorn className="text-white text-xl" />
                </div>
                <div className="flex-grow">
                  <h3 className="font-semibold text-gray-900 dark:text-white">{announcement.title}</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-1">{announcement.content}</p>
                  <div className="flex items-center mt-1">
                    <FaCalendarAlt className="text-gray-400 text-xs mr-1" />
                    <span className="text-xs text-gray-500 dark:text-gray-400">{formatDate(announcement.date)}</span>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Navigation Controls */}
          <div className="absolute right-4 top-1/2 transform -translate-y-1/2 flex space-x-2">
            <button
              onClick={goToPrev}
              className="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center text-gray-600 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
              aria-label="Previous announcement"
            >
              <FaArrowLeft className="text-xs" />
            </button>
            <button
              onClick={goToNext}
              className="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center text-gray-600 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
              aria-label="Next announcement"
            >
              <FaArrowRight className="text-xs" />
            </button>
          </div>

          {/* Indicators */}
          <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-1">
            {announcements.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentAnnouncement(index)}
                className={`w-2 h-2 rounded-full transition-all duration-300 ${
                  currentAnnouncement === index ? 'bg-blue-600 w-4' : 'bg-gray-300 dark:bg-gray-600'
                }`}
                aria-label={`Go to announcement ${index + 1}`}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

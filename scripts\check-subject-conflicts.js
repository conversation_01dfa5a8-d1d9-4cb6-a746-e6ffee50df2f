// Script to check for subject conflicts in the database
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    console.log('🔍 Checking Subject Database Conflicts...\n');

    // Get all subjects
    const allSubjects = await prisma.subject.findMany({
      include: {
        class: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        name: 'asc'
      }
    });

    console.log(`📊 Total subjects in database: ${allSubjects.length}\n`);

    // Group subjects by name to find conflicts
    const subjectGroups = {};
    allSubjects.forEach(subject => {
      if (!subjectGroups[subject.name]) {
        subjectGroups[subject.name] = [];
      }
      subjectGroups[subject.name].push({
        id: subject.id,
        className: subject.class.name,
        classId: subject.classId
      });
    });

    // Check for subjects that should be in multiple classes but can't due to unique constraint
    const problematicSubjects = ['Arabic', 'HPE', 'Social Science', 'General Science', 'Mathematics', 'English', 'Quran'];
    
    console.log('🚨 PROBLEMATIC SUBJECTS ANALYSIS:\n');
    
    problematicSubjects.forEach(subjectName => {
      const instances = subjectGroups[subjectName] || [];
      console.log(`📚 ${subjectName}:`);
      if (instances.length === 0) {
        console.log('   ❌ Not found in database');
      } else if (instances.length === 1) {
        console.log(`   ⚠️  Only exists for class: ${instances[0].className}`);
        console.log(`   🔒 Cannot be added to other classes due to unique constraint`);
      } else {
        console.log(`   ✅ Exists for ${instances.length} classes:`);
        instances.forEach(instance => {
          console.log(`      - ${instance.className}`);
        });
      }
      console.log('');
    });

    // Get all classes
    const allClasses = await prisma.class.findMany({
      select: {
        id: true,
        name: true,
        totalSubjects: true
      },
      orderBy: {
        name: 'asc'
      }
    });

    console.log('🏫 CLASSES AND THEIR SUBJECT COUNTS:\n');
    for (const cls of allClasses) {
      const actualSubjects = await prisma.subject.count({
        where: { classId: cls.id }
      });
      
      const mismatch = cls.totalSubjects !== actualSubjects;
      console.log(`📋 ${cls.name}:`);
      console.log(`   Recorded count: ${cls.totalSubjects}`);
      console.log(`   Actual count: ${actualSubjects}`);
      if (mismatch) {
        console.log(`   ⚠️  MISMATCH DETECTED!`);
      }
      console.log('');
    }

    console.log('💡 SOLUTION NEEDED:');
    console.log('   The current database schema has a unique constraint on subject names,');
    console.log('   preventing the same subject from being assigned to multiple classes.');
    console.log('   This needs to be fixed by changing the schema to allow duplicate subject names.');

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();

"use client";

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { FaFileAlt, FaClipboardList, FaMoneyBillWave, FaFileDownload, FaCalendarAlt, FaQuestionCircle, FaGraduationCap, FaUsers, FaChalkboardTeacher, FaFlask, FaGlobe, FaPrayingHands } from 'react-icons/fa';
import { Button } from '@/app/components/ui/button';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.5 },
  },
};

// Admissions sections data
const admissionsSections = [
  {
    id: 1,
    title: "How to Apply",
    description: "A step-by-step guide to joining the Alfalah Islamic School community.",
    icon: <FaFileAlt className="text-4xl text-blue-600" />,
    link: "/website/admissions/how-to-apply",
    color: "bg-blue-50 dark:bg-blue-900/20",
    borderColor: "border-blue-200 dark:border-blue-800",
    hoverColor: "hover:bg-blue-100 dark:hover:bg-blue-900/30",
  },
  {
    id: 2,
    title: "Admission Requirements",
    description: "Everything you need to know about qualifying for admission to our school.",
    icon: <FaClipboardList className="text-4xl text-green-600" />,
    link: "/website/admissions/requirements",
    color: "bg-green-50 dark:bg-green-900/20",
    borderColor: "border-green-200 dark:border-green-800",
    hoverColor: "hover:bg-green-100 dark:hover:bg-green-900/30",
  },
  {
    id: 3,
    title: "Tuition & Fees",
    description: "Investment in your child's education for the current academic year.",
    icon: <FaMoneyBillWave className="text-4xl text-purple-600" />,
    link: "/website/admissions/tuition-fees",
    color: "bg-purple-50 dark:bg-purple-900/20",
    borderColor: "border-purple-200 dark:border-purple-800",
    hoverColor: "hover:bg-purple-100 dark:hover:bg-purple-900/30",
  },
  {
    id: 4,
    title: "Forms & Documents",
    description: "Download all the forms you need for admission and enrollment.",
    icon: <FaFileDownload className="text-4xl text-amber-600" />,
    link: "/website/admissions/forms",
    color: "bg-amber-50 dark:bg-amber-900/20",
    borderColor: "border-amber-200 dark:border-amber-800",
    hoverColor: "hover:bg-amber-100 dark:hover:bg-amber-900/30",
  },
];

// FAQ data
const faqs = [
  {
    question: "When should I apply for admission?",
    answer: "We recommend applying as early as possible, as spaces fill quickly. Our priority application deadline is March 1 for the following academic year. After this date, applications are considered on a rolling basis as space permits."
  },
  {
    question: "What grade levels does Alfalah Islamic School offer?",
    answer: "We offer a comprehensive education from Kindergarten through 12th grade, with dedicated programs for Elementary (K-5), Middle School (6-8), and High School (9-12)."
  },
  {
    question: "Is financial aid available?",
    answer: "Yes, we offer need-based financial aid to qualifying families. Financial aid applications are reviewed separately from admission applications and do not affect admission decisions. The deadline for financial aid applications is March 15 for priority consideration."
  },
  {
    question: "Do you accept mid-year transfers?",
    answer: "Yes, we accept mid-year transfers on a case-by-case basis, depending on space availability and the student's academic standing. Please contact our admissions office for more information."
  },
  {
    question: "What is the average class size?",
    answer: "Our average class size is 18-22 students, allowing for personalized attention and differentiated instruction to meet the needs of all learners."
  },
];

// Key dates
const keyDates = [
  {
    date: "November 1, 2023",
    event: "Applications Open for 2024-2025",
  },
  {
    date: "January 20, 2024",
    event: "Open House",
  },
  {
    date: "March 1, 2024",
    event: "Priority Application Deadline",
  },
  {
    date: "March 15-30, 2024",
    event: "Entrance Assessments",
  },
  {
    date: "April 15, 2024",
    event: "Admission Decisions Sent",
  },
  {
    date: "May 1, 2024",
    event: "Enrollment Deposit Due",
  },
];

export default function AdmissionsPage() {
  return (
    <div className="min-h-screen pt-20">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-blue-900 to-indigo-900 text-white">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{ backgroundImage: 'url(/images/pattern.svg)', backgroundSize: '20px' }}></div>
        </div>
        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center max-w-3xl mx-auto"
          >
            <h1 className="text-4xl md:text-5xl font-bold mb-6">Admissions</h1>
            <p className="text-xl text-blue-100">
              Join our community of learners at Alfalah Islamic School
            </p>
          </motion.div>
        </div>
      </section>

      {/* Introduction Section */}
      <section className="py-16 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.7 }}
              viewport={{ once: true }}
              className="space-y-6"
            >
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Welcome to Alfalah Islamic School</h2>
              <p className="text-gray-600 dark:text-gray-300">
                Thank you for your interest in Alfalah Islamic School. We are dedicated to providing an exceptional educational experience that combines academic excellence with Islamic values.
              </p>
              <p className="text-gray-600 dark:text-gray-300">
                Our admissions process is designed to identify students who will thrive in our challenging academic environment and whose families share our commitment to Islamic education. We welcome applications from families of all backgrounds who support our mission and values.
              </p>
              <p className="text-gray-600 dark:text-gray-300">
                We invite you to explore our website, schedule a visit, and contact our admissions team with any questions. We look forward to guiding you through the admissions process and welcoming you to our school community.
              </p>
              <div className="pt-4">
                <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                  Apply Now
                </Button>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.7 }}
              viewport={{ once: true }}
            >
              <div className="relative h-[400px] rounded-lg overflow-hidden shadow-xl">
                <Image
                  src="/images/school.jpg" // Replace with actual image
                  alt="Students at Alfalah Islamic School"
                  fill
                  className="object-cover"
                />
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Admissions Sections */}
      <section className="py-16 bg-gray-50 dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Admissions Information</h2>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Everything you need to know about joining our school community
            </p>
          </motion.div>

          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
          >
            {admissionsSections.map((section) => (
              <motion.div key={section.id} variants={itemVariants}>
                <Link href={section.link}>
                  <div className={`h-full p-6 rounded-lg border ${section.borderColor} ${section.color} ${section.hoverColor} transition-colors duration-300 flex flex-col`}>
                    <div className="mb-4">{section.icon}</div>
                    <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">{section.title}</h3>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">{section.description}</p>
                    <div className="mt-auto pt-4">
                      <span className="text-blue-600 dark:text-blue-400 text-sm font-medium">Learn more →</span>
                    </div>
                  </div>
                </Link>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Key Dates */}
      <section className="py-16 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.7 }}
              viewport={{ once: true }}
            >
              <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-8 border border-blue-200 dark:border-blue-800">
                <div className="flex items-center mb-6">
                  <FaCalendarAlt className="text-4xl text-blue-600 mr-4" />
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Key Admissions Dates</h2>
                </div>
                <div className="space-y-4">
                  {keyDates.map((item, index) => (
                    <div key={index} className="flex">
                      <div className="w-1/3 font-semibold text-gray-900 dark:text-white">{item.date}</div>
                      <div className="w-2/3 text-gray-600 dark:text-gray-300">{item.event}</div>
                    </div>
                  ))}
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.7 }}
              viewport={{ once: true }}
              className="space-y-6"
            >
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Visit Our Campus</h2>
              <p className="text-gray-600 dark:text-gray-300">
                The best way to experience Alfalah Islamic School is to visit our campus. We offer several opportunities for prospective families to tour our facilities, observe classes, and meet our teachers and students.
              </p>
              <div className="space-y-4">
                <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                  <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Open House Events</h3>
                  <p className="text-gray-600 dark:text-gray-300 text-sm">
                    Join us for an informative presentation about our school, followed by a campus tour and Q&A session with administrators and faculty.
                  </p>
                </div>
                <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                  <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Personal Tours</h3>
                  <p className="text-gray-600 dark:text-gray-300 text-sm">
                    Schedule a personalized tour of our campus at a time that works for your family. Tours are available Monday through Friday during school hours.
                  </p>
                </div>
                <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                  <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Shadow Days</h3>
                  <p className="text-gray-600 dark:text-gray-300 text-sm">
                    Middle and high school applicants can spend a day with a current student, attending classes and experiencing our school firsthand.
                  </p>
                </div>
              </div>
              <div className="pt-4">
                <Link href="/website/contact">
                  <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                    Schedule a Visit
                  </Button>
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="py-16 bg-gray-50 dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Why Choose Alfalah Islamic School?</h2>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Discover what sets our school apart and why families choose us for their children's education
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true }}
              className="bg-white dark:bg-gray-900 p-6 rounded-lg shadow-md"
            >
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center text-blue-600 dark:text-blue-400 mr-4">
                  <FaGraduationCap className="text-xl" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Academic Excellence</h3>
              </div>
              <p className="text-gray-600 dark:text-gray-300">
                Our rigorous curriculum meets or exceeds national standards, preparing students for success in college and beyond. Our graduates are accepted to top universities nationwide.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
              className="bg-white dark:bg-gray-900 p-6 rounded-lg shadow-md"
            >
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center text-green-600 dark:text-green-400 mr-4">
                  <FaPrayingHands className="text-xl" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Islamic Environment</h3>
              </div>
              <p className="text-gray-600 dark:text-gray-300">
                Our school provides an environment where Islamic values are practiced and celebrated. Students develop a strong Islamic identity and character while respecting diversity.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              viewport={{ once: true }}
              className="bg-white dark:bg-gray-900 p-6 rounded-lg shadow-md"
            >
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center text-purple-600 dark:text-purple-400 mr-4">
                  <FaChalkboardTeacher className="text-xl" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Dedicated Faculty</h3>
              </div>
              <p className="text-gray-600 dark:text-gray-300">
                Our teachers are experts in their fields and committed to the success of each student. Small class sizes allow for personalized attention and differentiated instruction.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              viewport={{ once: true }}
              className="bg-white dark:bg-gray-900 p-6 rounded-lg shadow-md"
            >
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-amber-100 dark:bg-amber-900/30 rounded-full flex items-center justify-center text-amber-600 dark:text-amber-400 mr-4">
                  <FaUsers className="text-xl" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Supportive Community</h3>
              </div>
              <p className="text-gray-600 dark:text-gray-300">
                Our school is a close-knit community where students, parents, and staff work together to create a nurturing and supportive environment for learning and growth.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.5 }}
              viewport={{ once: true }}
              className="bg-white dark:bg-gray-900 p-6 rounded-lg shadow-md"
            >
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center text-red-600 dark:text-red-400 mr-4">
                  <FaFlask className="text-xl" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Modern Facilities</h3>
              </div>
              <p className="text-gray-600 dark:text-gray-300">
                Our campus features state-of-the-art classrooms, science and computer labs, library, prayer spaces, and athletic facilities to support a well-rounded educational experience.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.6 }}
              viewport={{ once: true }}
              className="bg-white dark:bg-gray-900 p-6 rounded-lg shadow-md"
            >
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-indigo-100 dark:bg-indigo-900/30 rounded-full flex items-center justify-center text-indigo-600 dark:text-indigo-400 mr-4">
                  <FaGlobe className="text-xl" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Holistic Development</h3>
              </div>
              <p className="text-gray-600 dark:text-gray-300">
                We focus on developing the whole child—intellectually, spiritually, physically, and socially—through a comprehensive curriculum and extracurricular activities.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* FAQs */}
      <section className="py-16 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Frequently Asked Questions</h2>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Answers to common questions about our admissions process
            </p>
          </motion.div>

          <div className="max-w-3xl mx-auto">
            <div className="space-y-4">
              {faqs.map((faq, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6"
                >
                  <div className="flex items-start">
                    <FaQuestionCircle className="text-blue-600 mt-1 mr-3 flex-shrink-0" />
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">{faq.question}</h3>
                      <p className="text-gray-600 dark:text-gray-300">{faq.answer}</p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>

            <div className="text-center mt-8">
              <Link href="/website/contact">
                <Button variant="outline" className="flex items-center gap-2">
                  <FaQuestionCircle className="h-4 w-4" />
                  <span>Have More Questions? Contact Us</span>
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-12 bg-blue-600">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="text-white mb-6 md:mb-0">
              <h2 className="text-2xl font-bold mb-2">Ready to Begin Your Journey?</h2>
              <p className="text-blue-100">Start the application process or contact our admissions team for more information.</p>
            </div>
            <div className="flex flex-col sm:flex-row gap-4">
              <Link href="/website/admissions/how-to-apply">
                <Button className="bg-white text-blue-600 hover:bg-blue-50">
                  Apply Now
                </Button>
              </Link>
              <Link href="/website/contact">
                <Button variant="outline" className="border-white text-white hover:bg-blue-700">
                  Contact Admissions
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

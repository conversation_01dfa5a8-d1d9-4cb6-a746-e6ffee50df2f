'use client'

import { useEffect, useState } from 'react'
import { useAuth } from '../contexts/auth-context'
import { useRouter } from 'next/navigation'

interface ProtectedRouteProps {
  children: React.ReactNode
  allowedRoles?: string[]
}

export default function ProtectedRoute({ children, allowedRoles }: ProtectedRouteProps) {
  const { user, isLoading, isAuthenticated } = useAuth()
  const router = useRouter()
  const [isAuthorized, setIsAuthorized] = useState(false)

  useEffect(() => {
    // If not loading and not authenticated, redirect to login
    if (!isLoading && !isAuthenticated) {
      console.log('Not authenticated, redirecting to login')
      window.location.href = '/login'
      return
    }

    // If authenticated but not authorized for this role, redirect to unauthorized
    if (!isLoading && isAuthenticated && allowedRoles && user && !allowedRoles.includes(user.role)) {
      console.log('Not authorized for this role, redirecting to unauthorized')
      window.location.href = '/unauthorized'
      return
    }

    // If we reach here, user is authenticated and authorized
    if (!isLoading && isAuthenticated) {
      setIsAuthorized(true)
    }
  }, [isLoading, isAuthenticated, user, allowedRoles])

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-600"></div>
      </div>
    )
  }

  // Don't render anything until authorized
  if (!isAuthorized) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-600"></div>
      </div>
    )
  }

  // Render children if authorized
  return <>{children}</>
}
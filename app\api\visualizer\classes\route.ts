import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    // Fetch all classes without any filtering
    try {
      // Get all active classes with their relations to ensure we have complete data
      const classes = await prisma.class.findMany({
        select: {
          id: true,
          name: true,
          totalStudents: true,
          hrTeacherId: true
        },
        orderBy: {
          name: 'asc'
        }
      })

      console.log(`Successfully fetched ${classes.length} classes from database`)
      return NextResponse.json(classes)
    } catch (dbError) {
      console.error('Database error:', dbError)

      // If database query fails, return comprehensive mock data for development
      // This includes all possible class combinations from 1A-F to 8A-F
      const mockClasses = []

      // Generate classes 1-8 with sections A-F
      for (let grade = 1; grade <= 8; grade++) {
        for (let section of ['A', 'B', 'C', 'D', 'E', 'F']) {
          mockClasses.push({
            id: `${grade}${section}`,
            name: `${grade}${section}`,
            totalStudents: Math.floor(Math.random() * 30) + 20,
            hrTeacherId: null
          })
        }
      }

      console.log(`Returning ${mockClasses.length} mock classes`)
      return NextResponse.json(mockClasses)
    }
  } catch (error) {
    console.error('Error fetching classes:', error)
    return NextResponse.json(
      { error: 'Failed to fetch classes' },
      { status: 500 }
    )
  }
}

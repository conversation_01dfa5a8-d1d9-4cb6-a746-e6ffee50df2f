// Simple script to test database connection
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
  datasources: {
    db: {
      url: process.env.DATABASE_URL
    }
  }
});

async function main() {
  console.log('Testing database connection...');
  
  try {
    // Connect to the database
    await prisma.$connect();
    console.log('Successfully connected to the database');
    
    // Try to fetch some data
    const students = await prisma.student.findMany({
      take: 5
    });
    console.log(`Found ${students.length} students`);
    if (students.length > 0) {
      console.log('Sample student:', students[0]);
    }
    
    const classes = await prisma.class.findMany({
      take: 5
    });
    console.log(`Found ${classes.length} classes`);
    if (classes.length > 0) {
      console.log('Sample class:', classes[0]);
    }
    
    const teachers = await prisma.teacher.findMany({
      take: 5
    });
    console.log(`Found ${teachers.length} teachers`);
    if (teachers.length > 0) {
      console.log('Sample teacher:', teachers[0]);
    }
    
    console.log('Database test completed successfully');
  } catch (error) {
    console.error('Error testing database connection:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();

"use client";

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { FaBook, FaChalkboardTeacher, FaCalendarAlt, FaGraduationCap, FaFlask, FaCalculator, FaGlobe, FaPalette, FaUsers, FaPrayingHands } from 'react-icons/fa';
import { Button } from '@/app/components/ui/button';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.5 },
  },
};

// Academic sections data
const academicSections = [
  {
    id: 1,
    title: "Curriculum",
    description: "Explore our comprehensive curriculum that integrates academic excellence with Islamic values.",
    icon: <FaBook className="text-4xl text-blue-600" />,
    link: "/website/academics/curriculum",
    color: "bg-blue-50 dark:bg-blue-900/20",
    borderColor: "border-blue-200 dark:border-blue-800",
    hoverColor: "hover:bg-blue-100 dark:hover:bg-blue-900/30",
  },
  {
    id: 2,
    title: "Departments",
    description: "Learn about our specialized academic departments and their dedicated faculty.",
    icon: <FaChalkboardTeacher className="text-4xl text-green-600" />,
    link: "/website/academics/departments",
    color: "bg-green-50 dark:bg-green-900/20",
    borderColor: "border-green-200 dark:border-green-800",
    hoverColor: "hover:bg-green-100 dark:hover:bg-green-900/30",
  },
  {
    id: 3,
    title: "Academic Calendar",
    description: "View important dates, holidays, and events for the current academic year.",
    icon: <FaCalendarAlt className="text-4xl text-purple-600" />,
    link: "/website/academics/calendar",
    color: "bg-purple-50 dark:bg-purple-900/20",
    borderColor: "border-purple-200 dark:border-purple-800",
    hoverColor: "hover:bg-purple-100 dark:hover:bg-purple-900/30",
  },
];

// Programs data
const programs = [
  {
    id: 1,
    title: "Elementary School",
    grades: "Kindergarten - Grade 5",
    description: "Our elementary program builds strong foundations in core subjects while nurturing Islamic character and values.",
    features: [
      "Phonics-based reading program",
      "Hands-on mathematics",
      "Quran memorization and Islamic studies",
      "Science exploration",
      "Social studies with Islamic perspective",
    ],
    image: "/images/portrait.jpg", // Replace with actual image
  },
  {
    id: 2,
    title: "Middle School",
    grades: "Grades 6-8",
    description: "Our middle school program deepens academic knowledge while supporting students through the critical early adolescent years.",
    features: [
      "Advanced language arts and literature",
      "Pre-algebra and algebra",
      "Laboratory sciences",
      "Islamic history and jurisprudence",
      "Critical thinking and research skills",
    ],
    image: "/images/portrait.jpg", // Replace with actual image
  },
  {
    id: 3,
    title: "High School",
    grades: "Grades 9-12",
    description: "Our high school program prepares students for college success while strengthening their Islamic identity and leadership skills.",
    features: [
      "College preparatory curriculum",
      "Advanced Placement courses",
      "Islamic leadership development",
      "Career exploration",
      "Community service opportunities",
    ],
    image: "/images/portrait.jpg", // Replace with actual image
  },
];

export default function AcademicsPage() {
  return (
    <div className="min-h-screen pt-20">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-blue-900 to-indigo-900 text-white">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{ backgroundImage: 'url(/images/pattern.svg)', backgroundSize: '20px' }}></div>
        </div>
        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center max-w-3xl mx-auto"
          >
            <h1 className="text-4xl md:text-5xl font-bold mb-6">Academics</h1>
            <p className="text-xl text-blue-100">
              Excellence in education guided by Islamic principles
            </p>
          </motion.div>
        </div>
      </section>

      {/* Introduction Section */}
      <section className="py-16 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.7 }}
              viewport={{ once: true }}
              className="space-y-6"
            >
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Academic Excellence with Islamic Values</h2>
              <p className="text-gray-600 dark:text-gray-300">
                At Alfalah Islamic School, we provide a rigorous academic program that meets or exceeds national standards while integrating Islamic principles throughout the curriculum.
              </p>
              <p className="text-gray-600 dark:text-gray-300">
                Our comprehensive approach to education nurtures the whole child—intellectually, spiritually, physically, and socially. We believe that knowledge and faith go hand in hand, and we strive to develop students who excel academically while embodying Islamic values.
              </p>
              <p className="text-gray-600 dark:text-gray-300">
                Our dedicated teachers are experts in their fields and committed to helping each student reach their full potential. Small class sizes allow for personalized attention and differentiated instruction to meet the needs of all learners.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.7 }}
              viewport={{ once: true }}
            >
              <div className="relative h-[400px] rounded-lg overflow-hidden shadow-xl">
                <Image
                  src="/images/portrait.jpg" // Replace with actual image
                  alt="Students learning at Alfalah Islamic School"
                  fill
                  className="object-cover"
                />
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Academic Sections */}
      <section className="py-16 bg-gray-50 dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Explore Our Academics</h2>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Learn more about our academic programs, departments, and resources
            </p>
          </motion.div>

          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="grid grid-cols-1 md:grid-cols-3 gap-6"
          >
            {academicSections.map((section) => (
              <motion.div key={section.id} variants={itemVariants}>
                <Link href={section.link}>
                  <div className={`h-full p-6 rounded-lg border ${section.borderColor} ${section.color} ${section.hoverColor} transition-colors duration-300 flex flex-col`}>
                    <div className="mb-4">{section.icon}</div>
                    <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">{section.title}</h3>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">{section.description}</p>
                    <div className="mt-auto pt-4">
                      <span className="text-blue-600 dark:text-blue-400 text-sm font-medium">Learn more →</span>
                    </div>
                  </div>
                </Link>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Academic Programs */}
      <section className="py-16 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Academic Programs</h2>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Our grade-level programs are designed to meet the developmental needs of students at each stage
            </p>
          </motion.div>

          <div className="space-y-12">
            {programs.map((program, index) => (
              <motion.div
                key={program.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.7, delay: index * 0.1 }}
                viewport={{ once: true }}
                className={`grid grid-cols-1 lg:grid-cols-2 gap-12 items-center ${
                  index % 2 === 1 ? 'lg:flex-row-reverse' : ''
                }`}
              >
                <div className={`order-2 ${index % 2 === 1 ? 'lg:order-1' : 'lg:order-2'}`}>
                  <div className="relative h-[300px] rounded-lg overflow-hidden shadow-xl">
                    <Image
                      src={program.image}
                      alt={program.title}
                      fill
                      className="object-cover"
                    />
                  </div>
                </div>

                <div className={`space-y-6 order-1 ${index % 2 === 1 ? 'lg:order-2' : 'lg:order-1'}`}>
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-1">{program.title}</h3>
                    <p className="text-blue-600 dark:text-blue-400 font-medium">{program.grades}</p>
                  </div>
                  <p className="text-gray-600 dark:text-gray-300">{program.description}</p>
                  <ul className="space-y-2">
                    {program.features.map((feature, i) => (
                      <li key={i} className="flex items-start">
                        <span className="text-green-600 mr-2">•</span>
                        <span className="text-gray-600 dark:text-gray-300">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <div className="pt-4">
                    <Link href="/website/academics/curriculum">
                      <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                        Learn More
                      </Button>
                    </Link>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Subject Areas */}
      <section className="py-16 bg-gray-50 dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Subject Areas</h2>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Our comprehensive curriculum covers all major subject areas with an Islamic perspective
            </p>
          </motion.div>

          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6"
          >
            <motion.div variants={itemVariants} className="bg-white dark:bg-gray-900 p-6 rounded-lg shadow-md text-center">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 mb-4">
                <FaBook className="text-2xl" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Language Arts</h3>
              <p className="text-sm text-gray-600 dark:text-gray-300">Reading, writing, grammar, vocabulary, and literature studies</p>
            </motion.div>

            <motion.div variants={itemVariants} className="bg-white dark:bg-gray-900 p-6 rounded-lg shadow-md text-center">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 mb-4">
                <FaCalculator className="text-2xl" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Mathematics</h3>
              <p className="text-sm text-gray-600 dark:text-gray-300">Arithmetic, algebra, geometry, statistics, and calculus</p>
            </motion.div>

            <motion.div variants={itemVariants} className="bg-white dark:bg-gray-900 p-6 rounded-lg shadow-md text-center">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 mb-4">
                <FaFlask className="text-2xl" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Science</h3>
              <p className="text-sm text-gray-600 dark:text-gray-300">Biology, chemistry, physics, and environmental science</p>
            </motion.div>

            <motion.div variants={itemVariants} className="bg-white dark:bg-gray-900 p-6 rounded-lg shadow-md text-center">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-amber-100 dark:bg-amber-900/30 text-amber-600 dark:text-amber-400 mb-4">
                <FaGlobe className="text-2xl" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Social Studies</h3>
              <p className="text-sm text-gray-600 dark:text-gray-300">History, geography, civics, and world cultures</p>
            </motion.div>

            <motion.div variants={itemVariants} className="bg-white dark:bg-gray-900 p-6 rounded-lg shadow-md text-center">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-emerald-100 dark:bg-emerald-900/30 text-emerald-600 dark:text-emerald-400 mb-4">
                <FaPrayingHands className="text-2xl" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Islamic Studies</h3>
              <p className="text-sm text-gray-600 dark:text-gray-300">Quran, hadith, fiqh, and Islamic history</p>
            </motion.div>

            <motion.div variants={itemVariants} className="bg-white dark:bg-gray-900 p-6 rounded-lg shadow-md text-center">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-pink-100 dark:bg-pink-900/30 text-pink-600 dark:text-pink-400 mb-4">
                <FaPalette className="text-2xl" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Arts & Music</h3>
              <p className="text-sm text-gray-600 dark:text-gray-300">Visual arts, music, drama, and Islamic art</p>
            </motion.div>

            <motion.div variants={itemVariants} className="bg-white dark:bg-gray-900 p-6 rounded-lg shadow-md text-center">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 mb-4">
                <FaGraduationCap className="text-2xl" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Technology</h3>
              <p className="text-sm text-gray-600 dark:text-gray-300">Computer science, coding, and digital literacy</p>
            </motion.div>

            <motion.div variants={itemVariants} className="bg-white dark:bg-gray-900 p-6 rounded-lg shadow-md text-center">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-indigo-100 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400 mb-4">
                <FaGlobe className="text-2xl" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Languages</h3>
              <p className="text-sm text-gray-600 dark:text-gray-300">Arabic and other foreign language options</p>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-12 bg-blue-600">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="text-white mb-6 md:mb-0">
              <h2 className="text-2xl font-bold mb-2">Ready to learn more?</h2>
              <p className="text-blue-100">Explore our curriculum or schedule a visit to see our classrooms in action.</p>
            </div>
            <div className="flex flex-col sm:flex-row gap-4">
              <Link href="/website/academics/curriculum">
                <Button className="bg-white text-blue-600 hover:bg-blue-50">
                  Explore Curriculum
                </Button>
              </Link>
              <Link href="/website/contact">
                <Button variant="outline" className="border-white text-white hover:bg-blue-700">
                  Schedule a Visit
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

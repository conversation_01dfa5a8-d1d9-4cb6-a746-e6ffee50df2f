'use client'

import React, { useState, useRef, useEffect } from 'react'
import Sidebar from '../../components/Sidebar'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Label } from '../../components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select'
import { Switch } from '../../components/ui/switch'
import { Checkbox } from '../../components/ui/checkbox'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '../../components/ui/dialog'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useToast } from '../../components/ui/use-toast'
import { useAppSettings } from '../../contexts/app-settings-context'
import {
  CreditCard,
  Upload,
  Download,
  User,
  School,
  Calendar,
  Hash,
  Loader2,
  QrCode,
  Search,
  FileText,
  Users,
  Camera,
  X
} from 'lucide-react'
import Image from 'next/image'
import dynamic from 'next/dynamic'
import html2canvas from 'html2canvas'
import { jsPDF } from 'jspdf'

// Import Header component with no SSR to avoid hydration issues
const Header = dynamic(() => import('../../components/Header'), {
  ssr: false
})

interface Student {
  id: string
  sid: string
  name: string
  fatherName: string
  className: string
  age: number
  gender: string
  academicYear: string
  photoUrl?: string
  selected?: boolean
}

interface Class {
  id: string
  name: string
}

export default function IDCardPage() {
  const { settings } = useAppSettings()
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false)
  const [selectedClass, setSelectedClass] = useState('')
  const [selectedStudent, setSelectedStudent] = useState('')
  const [studentPhoto, setStudentPhoto] = useState<string | null>(null)
  const [schoolLogo, setSchoolLogo] = useState<string | null>(null)
  const [isGenerating, setIsGenerating] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [formData, setFormData] = useState({
    fullName: '',
    fatherName: '',
    gfName: '',
    className: '',
    idNumber: '',
    age: '',
    schoolName: settings.schoolName,
    expiryDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toISOString().split('T')[0]
  })
  const [bulkGenerate, setBulkGenerate] = useState(false)
  const [selectedClassStudents, setSelectedClassStudents] = useState<Student[]>([])
  const [selectedStudentsForPhotos, setSelectedStudentsForPhotos] = useState<Student[]>([])
  const [isPhotoUploadDialogOpen, setIsPhotoUploadDialogOpen] = useState(false)
  const [currentPhotoStudent, setCurrentPhotoStudent] = useState<Student | null>(null)
  const [uploadingPhoto, setUploadingPhoto] = useState(false)
  const queryClient = useQueryClient()

  const cardRef = useRef<HTMLDivElement>(null)
  const studentPhotoRef = useRef<HTMLInputElement>(null)
  const schoolLogoRef = useRef<HTMLInputElement>(null)
  const { toast } = useToast()

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  const toggleMobileSidebar = () => {
    setMobileSidebarOpen(!mobileSidebarOpen)
  }

  // Update school name and logo when settings change
  useEffect(() => {
    setFormData(prev => ({
      ...prev,
      schoolName: settings.schoolName
    }))
    if (settings.schoolLogo) {
      setSchoolLogo(settings.schoolLogo)
    }
  }, [settings.schoolName, settings.schoolLogo])

  // Fetch classes
  const { data: classes = [], isLoading: isLoadingClasses } = useQuery({
    queryKey: ['classes'],
    queryFn: async () => {
      const res = await fetch('/api/classes')
      if (!res.ok) throw new Error('Failed to fetch classes')
      return res.json()
    }
  })

  // Fetch students based on selected class
  const { data: students = [], isLoading: isLoadingStudents } = useQuery({
    queryKey: ['students', selectedClass],
    queryFn: async () => {
      if (!selectedClass) return []
      const res = await fetch(`/api/students?class=${selectedClass}`)
      if (!res.ok) throw new Error('Failed to fetch students')
      return res.json()
    },
    enabled: !!selectedClass
  })

  // Filter students based on search query
  const filteredStudents = students.filter((student: Student) =>
    student.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    student.sid.toLowerCase().includes(searchQuery.toLowerCase())
  )

  // Handle student photo upload
  const handleStudentPhotoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0]

      // Check if file is an image
      if (!file.type.startsWith('image/')) {
        toast({
          title: 'Invalid File',
          description: 'Please upload an image file',
          variant: 'destructive',
        })
        return
      }

      // Create preview
      const reader = new FileReader()
      reader.onloadend = () => {
        setStudentPhoto(reader.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  // Handle school logo upload
  const handleSchoolLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0]

      // Check if file is an image
      if (!file.type.startsWith('image/')) {
        toast({
          title: 'Invalid File',
          description: 'Please upload an image file',
          variant: 'destructive',
        })
        return
      }

      // Create preview
      const reader = new FileReader()
      reader.onloadend = () => {
        setSchoolLogo(reader.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  // Handle student selection
  const handleStudentChange = (value: string) => {
    setSelectedStudent(value)

    const student = students.find((s: Student) => s.id === value)
    if (student) {
      setFormData({
        ...formData,
        fullName: student.name,
        fatherName: student.fatherName,
        gfName: student.gfName || '',
        className: student.className,
        idNumber: student.sid,
        age: student.age.toString()
      })
    }
  }

  // Handle class selection for bulk generation
  const handleClassChange = (value: string) => {
    setSelectedClass(value)

    if (bulkGenerate) {
      // When in bulk mode, load all students from the class
      const classStudents = students
        .filter((s: Student) => s.className === value)
        .map(student => ({ ...student, selected: false }))
      setSelectedClassStudents(classStudents)
    }
  }

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData({
      ...formData,
      [name]: value
    })
  }

  // Generate QR code data
  const qrCodeData = `ID:${formData.idNumber},Name:${formData.fullName},Class:${formData.className}`

  // Download ID card as image
  const handleDownload = async () => {
    if (!cardRef.current) return

    setIsGenerating(true)

    try {
      const canvas = await html2canvas(cardRef.current, {
        scale: 3, // Higher scale for better quality
        backgroundColor: null,
        logging: false
      })

      const image = canvas.toDataURL('image/png')
      const link = document.createElement('a')
      link.href = image
      link.download = `${formData.fullName.replace(/\s+/g, '_')}_ID_Card.png`
      link.click()

      toast({
        title: 'Success',
        description: 'ID Card downloaded successfully',
      })
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to download ID Card',
        variant: 'destructive',
      })
      console.error('Download error:', error)
    } finally {
      setIsGenerating(false)
    }
  }

  // Generate bulk ID cards as PDF
  const handleBulkDownload = async () => {
    if (selectedClassStudents.length === 0) {
      toast({
        title: 'No Students',
        description: 'No students found in the selected class',
        variant: 'destructive',
      })
      return
    }

    setIsGenerating(true)

    try {
      // Create PDF document (A4 size)
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4'
      })

      // A4 dimensions: 210mm x 297mm
      // Card dimensions: 85.6mm x 54mm
      // We'll fit 8 cards per page (2 columns, 4 rows)
      const cardWidth = 85.6
      const cardHeight = 54
      const margin = 10
      const colGap = 10
      const rowGap = 10

      // Calculate positions
      const positions = []
      for (let row = 0; row < 4; row++) {
        for (let col = 0; col < 2; col++) {
          positions.push({
            x: margin + col * (cardWidth + colGap),
            y: margin + row * (cardHeight + rowGap)
          })
        }
      }

      // Process students in batches of 8 (cards per page)
      const totalStudents = selectedClassStudents.length
      const totalPages = Math.ceil(totalStudents / 8)

      for (let page = 0; page < totalPages; page++) {
        if (page > 0) {
          pdf.addPage()
        }

        // Get students for this page
        const pageStudents = selectedClassStudents.slice(page * 8, (page + 1) * 8)

        // Generate cards for each student
        for (let i = 0; i < pageStudents.length; i++) {
          const student = pageStudents[i]
          const pos = positions[i]

          // Draw card background
          pdf.setFillColor(255, 255, 255)
          pdf.roundedRect(pos.x, pos.y, cardWidth, cardHeight, 3, 3, 'F')

          // Draw header background (blue gradient)
          pdf.setFillColor(59, 130, 246) // Blue color
          pdf.rect(pos.x, pos.y, cardWidth, cardHeight * 0.3, 'F')

          // Add school name
          pdf.setTextColor(255, 255, 255)
          pdf.setFont('helvetica', 'bold')
          pdf.setFontSize(12)
          pdf.text(formData.schoolName, pos.x + cardWidth / 2, pos.y + 8, { align: 'center' })

          // Add ID card title
          pdf.setFontSize(8)
          pdf.text('STUDENT IDENTIFICATION CARD', pos.x + cardWidth / 2, pos.y + 14, { align: 'center' })

          // Add student details
          pdf.setTextColor(0, 0, 0)
          pdf.setFont('times', 'bold')
          pdf.setFontSize(10)
          pdf.text(student.name, pos.x + cardWidth * 0.4, pos.y + cardHeight * 0.4)

          // Add father's name
          pdf.setFont('times', 'italic')
          pdf.setFontSize(8)
          pdf.text(`s/o ${student.fatherName}`, pos.x + cardWidth * 0.4, pos.y + cardHeight * 0.47)

          // Add other details
          pdf.setFont('helvetica', 'normal')
          pdf.setFontSize(8)

          // ID Number
          pdf.text('ID Number:', pos.x + cardWidth * 0.4, pos.y + cardHeight * 0.57)
          pdf.setFont('helvetica', 'bold')
          pdf.text(student.sid, pos.x + cardWidth * 0.6, pos.y + cardHeight * 0.57)

          // Class
          pdf.setFont('helvetica', 'normal')
          pdf.text('Class:', pos.x + cardWidth * 0.4, pos.y + cardHeight * 0.64)
          pdf.setFont('helvetica', 'bold')
          pdf.text(student.className, pos.x + cardWidth * 0.6, pos.y + cardHeight * 0.64)

          // Age
          pdf.setFont('helvetica', 'normal')
          pdf.text('Age:', pos.x + cardWidth * 0.4, pos.y + cardHeight * 0.71)
          pdf.setFont('helvetica', 'bold')
          pdf.text(`${student.age} years`, pos.x + cardWidth * 0.6, pos.y + cardHeight * 0.71)

          // Expiry date
          pdf.setFont('helvetica', 'normal')
          pdf.text('Valid Until:', pos.x + cardWidth * 0.4, pos.y + cardHeight * 0.85)
          pdf.setFont('helvetica', 'bold')

          const expiryDate = new Date()
          expiryDate.setFullYear(expiryDate.getFullYear() + 1)
          pdf.text(expiryDate.toLocaleDateString('en-US', {
            day: '2-digit',
            month: 'short',
            year: 'numeric'
          }), pos.x + cardWidth * 0.6, pos.y + cardHeight * 0.85)

          // Draw photo placeholder or add student photo if available
          if (student.photoUrl) {
            // If we have a photo URL, we'll add a note that the photo will be included
            // (In a real implementation, you would use a library that can add images to PDF)
            pdf.setDrawColor(0, 100, 0)
            pdf.setFillColor(230, 255, 230)
            pdf.roundedRect(pos.x + 10, pos.y + cardHeight * 0.4, cardWidth * 0.2, cardHeight * 0.4, 1, 1, 'FD')

            // Add a small indicator that photo is available
            pdf.setTextColor(0, 100, 0)
            pdf.setFontSize(6)
            pdf.text('PHOTO', pos.x + 10 + cardWidth * 0.1, pos.y + cardHeight * 0.6, { align: 'center' })
            pdf.text('AVAILABLE', pos.x + 10 + cardWidth * 0.1, pos.y + cardHeight * 0.65, { align: 'center' })
          } else {
            // Default placeholder for students without photos
            pdf.setDrawColor(200, 200, 200)
            pdf.setFillColor(240, 240, 240)
            pdf.roundedRect(pos.x + 10, pos.y + cardHeight * 0.4, cardWidth * 0.2, cardHeight * 0.4, 1, 1, 'FD')
          }

          // Draw QR code placeholder
          pdf.roundedRect(pos.x + 10, pos.y + cardHeight * 0.85 - 5, cardWidth * 0.2, cardWidth * 0.2, 1, 1, 'FD')
        }
      }

      // Save the PDF
      pdf.save(`${selectedClass}_ID_Cards.pdf`)

      toast({
        title: 'Success',
        description: `Generated ID cards for ${totalStudents} students`,
      })
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to generate bulk ID cards',
        variant: 'destructive',
      })
      console.error('Bulk download error:', error)
    } finally {
      setIsGenerating(false)
    }
  }

  // Upload photo mutation
  const uploadPhotoMutation = useMutation({
    mutationFn: async ({ studentId, photo }: { studentId: string; photo: File }) => {
      const formData = new FormData()
      formData.append('studentId', studentId)
      formData.append('photo', photo)

      const response = await fetch('/api/students/update-photo', {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to upload photo')
      }

      return response.json()
    },
    onSuccess: (data, variables) => {
      toast({
        title: 'Success',
        description: 'Photo uploaded successfully',
      })

      // Update the student in the list with the new photo URL
      setSelectedClassStudents(prev =>
        prev.map(student =>
          student.id === variables.studentId
            ? { ...student, photoUrl: data.url }
            : student
        )
      )

      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['students'] })

      // Move to the next student if in batch mode
      if (selectedStudentsForPhotos.length > 0) {
        const currentIndex = selectedStudentsForPhotos.findIndex(s => s.id === variables.studentId)
        if (currentIndex < selectedStudentsForPhotos.length - 1) {
          setCurrentPhotoStudent(selectedStudentsForPhotos[currentIndex + 1])
        } else {
          // We're done with all students
          setIsPhotoUploadDialogOpen(false)
          setCurrentPhotoStudent(null)
          setSelectedStudentsForPhotos([])
        }
      } else {
        setIsPhotoUploadDialogOpen(false)
        setCurrentPhotoStudent(null)
      }
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to upload photo',
        variant: 'destructive',
      })
    }
  })

  // Handle student selection for photo upload
  const handleStudentSelection = (studentId: string, isSelected: boolean) => {
    setSelectedClassStudents(prev =>
      prev.map(student =>
        student.id === studentId
          ? { ...student, selected: isSelected }
          : student
      )
    )
  }

  // Handle photo upload for selected students
  const handleUploadPhotosForSelected = () => {
    const selectedStudents = selectedClassStudents.filter(student => student.selected)
    if (selectedStudents.length === 0) {
      toast({
        title: 'No Students Selected',
        description: 'Please select at least one student',
        variant: 'destructive',
      })
      return
    }

    setSelectedStudentsForPhotos(selectedStudents)
    setCurrentPhotoStudent(selectedStudents[0])
    setIsPhotoUploadDialogOpen(true)
  }

  // Handle photo upload for a specific student
  const handleStudentPhotoUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!currentPhotoStudent) return

    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0]

      // Check if file is an image
      if (!file.type.startsWith('image/')) {
        toast({
          title: 'Invalid File',
          description: 'Please upload an image file',
          variant: 'destructive',
        })
        return
      }

      setUploadingPhoto(true)
      try {
        await uploadPhotoMutation.mutateAsync({
          studentId: currentPhotoStudent.id,
          photo: file
        })
      } finally {
        setUploadingPhoto(false)
      }
    }
  }

  // Load default school logo on mount
  useEffect(() => {
    setSchoolLogo('/images/logo.jpg')
  }, [])

  return (
    <div className="flex h-screen bg-gray-100 dark:bg-gray-900">
      <Sidebar
        isCollapsed={sidebarCollapsed}
        toggleSidebar={toggleSidebar}
        isMobileOpen={mobileSidebarOpen}
      />

      <main className={`main-content ${sidebarCollapsed ? 'sidebar-collapsed' : ''} w-full h-screen overflow-y-auto`}>
        <Header toggleMobileSidebar={toggleMobileSidebar} />

        <div className="container mx-auto px-4 py-6">
          <h1 className="text-2xl font-bold mb-6 text-gray-800 dark:text-white">
            Student ID Card Generator
          </h1>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Left side - Form */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <CreditCard className="mr-2 h-5 w-5 text-blue-500" />
                  ID Card Details
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Class and Student Selection */}
                  <div className="flex items-center justify-between mb-4">
                    <Label htmlFor="bulk-toggle" className="flex items-center space-x-2 cursor-pointer">
                      <Switch
                        id="bulk-toggle"
                        checked={bulkGenerate}
                        onCheckedChange={(checked) => {
                          setBulkGenerate(checked)
                          if (checked) {
                            setSelectedStudent('')
                            // Load all students from the class if a class is already selected
                            if (selectedClass) {
                              const classStudents = students.filter((s: Student) => s.className === selectedClass)
                              setSelectedClassStudents(classStudents)
                            }
                          } else {
                            setSelectedClassStudents([])
                          }
                        }}
                      />
                      <span className="text-sm font-medium">
                        {bulkGenerate ? 'Bulk Generation Mode' : 'Single ID Card Mode'}
                      </span>
                    </Label>

                    {bulkGenerate && (
                      <div className="text-xs text-gray-500 flex items-center">
                        <Users className="h-3 w-3 mr-1" />
                        <span>8 cards per page (A4)</span>
                      </div>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="class">Class</Label>
                      <Select
                        value={selectedClass}
                        onValueChange={bulkGenerate ? handleClassChange : setSelectedClass}
                      >
                        <SelectTrigger id="class" className={isLoadingClasses ? 'opacity-70' : ''}>
                          {isLoadingClasses ? (
                            <div className="flex items-center">
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                              <span>Loading classes...</span>
                            </div>
                          ) : (
                            <SelectValue placeholder="Select Class" />
                          )}
                        </SelectTrigger>
                        <SelectContent>
                          {classes.map((cls: Class) => (
                            <SelectItem key={cls.id} value={cls.name}>
                              {cls.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {!bulkGenerate && (
                      <div className="space-y-2">
                        <Label htmlFor="student">Student</Label>
                        <div className="relative">
                          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <Search className="h-4 w-4 text-gray-400" />
                          </div>
                          <Input
                            placeholder="Search students..."
                            className="pl-10"
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            disabled={!selectedClass || isLoadingStudents}
                          />
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Student List */}
                  {!bulkGenerate && selectedClass && filteredStudents.length > 0 && (
                    <div className="border rounded-md p-2 max-h-40 overflow-y-auto">
                      {filteredStudents.map((student: Student) => (
                        <div
                          key={student.id}
                          className={`p-2 cursor-pointer hover:bg-gray-100 rounded-md ${
                            selectedStudent === student.id ? 'bg-blue-50 border border-blue-200' : ''
                          }`}
                          onClick={() => handleStudentChange(student.id)}
                        >
                          <div className="flex items-center">
                            <User className="h-4 w-4 mr-2 text-gray-500" />
                            <span>{student.name}</span>
                            <span className="ml-2 text-xs text-gray-500">({student.sid})</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Bulk Students List */}
                  {bulkGenerate && selectedClass && (
                    <div className="border rounded-md p-2">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="text-sm font-medium">Students in {selectedClass}</h3>
                        <div className="flex items-center space-x-2">
                          <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                            {selectedClassStudents.length} students
                          </span>
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-xs h-7 px-2"
                            onClick={handleUploadPhotosForSelected}
                          >
                            <Camera className="h-3 w-3 mr-1" />
                            Upload Photos
                          </Button>
                        </div>
                      </div>

                      {selectedClassStudents.length > 0 ? (
                        <div className="max-h-60 overflow-y-auto">
                          <div className="grid grid-cols-2 gap-2">
                            {selectedClassStudents.map((student: Student) => (
                              <div
                                key={student.id}
                                className={`p-2 text-xs rounded-md flex items-start ${
                                  student.selected ? 'bg-blue-50 border border-blue-200' : 'bg-gray-50'
                                }`}
                              >
                                <Checkbox
                                  id={`student-${student.id}`}
                                  className="mr-2 mt-1"
                                  checked={student.selected || false}
                                  onCheckedChange={(checked) =>
                                    handleStudentSelection(student.id, checked as boolean)
                                  }
                                />
                                <div className="flex-1">
                                  <div className="flex items-center">
                                    {student.photoUrl ? (
                                      <div className="h-5 w-5 rounded-full overflow-hidden mr-1 bg-gray-200">
                                        <Image
                                          src={student.photoUrl}
                                          alt={student.name}
                                          width={20}
                                          height={20}
                                          className="object-cover"
                                        />
                                      </div>
                                    ) : (
                                      <User className="h-3 w-3 mr-1 text-gray-500" />
                                    )}
                                    <span className="truncate font-medium">{student.name}</span>
                                  </div>
                                  <div className="text-gray-500 ml-4 text-xs">ID: {student.sid}</div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      ) : (
                        <div className="text-center py-4 text-sm text-gray-500">
                          No students found in this class
                        </div>
                      )}
                    </div>
                  )}

                  {/* Photo Uploads */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="studentPhoto">Student Photo (Required)</Label>
                      <div className="flex items-center space-x-4">
                        <div className="flex-1">
                          <div
                            className="border-2 border-dashed rounded-md p-4 text-center cursor-pointer hover:bg-gray-50"
                            onClick={() => studentPhotoRef.current?.click()}
                          >
                            <Upload className="h-6 w-6 mx-auto text-gray-400 mb-2" />
                            <p className="text-sm text-gray-500">Click to upload photo</p>
                            <input
                              ref={studentPhotoRef}
                              type="file"
                              id="studentPhoto"
                              accept="image/*"
                              className="hidden"
                              onChange={handleStudentPhotoChange}
                            />
                          </div>
                        </div>
                        {studentPhoto && (
                          <div className="w-20 h-20 relative border rounded-md overflow-hidden">
                            <Image
                              src={studentPhoto}
                              alt="Student photo preview"
                              fill
                              className="object-cover"
                            />
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="schoolLogo">School Logo (Required)</Label>
                      <div className="flex items-center space-x-4">
                        <div className="flex-1">
                          <div
                            className="border-2 border-dashed rounded-md p-4 text-center cursor-pointer hover:bg-gray-50"
                            onClick={() => schoolLogoRef.current?.click()}
                          >
                            <Upload className="h-6 w-6 mx-auto text-gray-400 mb-2" />
                            <p className="text-sm text-gray-500">Click to upload logo</p>
                            <input
                              ref={schoolLogoRef}
                              type="file"
                              id="schoolLogo"
                              accept="image/*"
                              className="hidden"
                              onChange={handleSchoolLogoChange}
                            />
                          </div>
                        </div>
                        {schoolLogo && (
                          <div className="w-20 h-20 relative border rounded-md overflow-hidden">
                            <Image
                              src={schoolLogo}
                              alt="School logo preview"
                              fill
                              className="object-contain"
                            />
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Student Details */}
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="fullName">Full Name</Label>
                        <Input
                          id="fullName"
                          name="fullName"
                          value={formData.fullName}
                          onChange={handleInputChange}
                          placeholder="Student's full name"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="className">Class / Grade</Label>
                        <Input
                          id="className"
                          name="className"
                          value={formData.className}
                          onChange={handleInputChange}
                          placeholder="e.g. 10A"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="idNumber">ID Number</Label>
                        <Input
                          id="idNumber"
                          name="idNumber"
                          value={formData.idNumber}
                          onChange={handleInputChange}
                          placeholder="Student ID"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="age">Age</Label>
                        <Input
                          id="age"
                          name="age"
                          type="number"
                          value={formData.age}
                          onChange={handleInputChange}
                          placeholder="Student's age"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="schoolName">School Name</Label>
                        <Input
                          id="schoolName"
                          name="schoolName"
                          value={formData.schoolName}
                          onChange={handleInputChange}
                          placeholder="School name"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="expiryDate">Expiry Date</Label>
                        <Input
                          id="expiryDate"
                          name="expiryDate"
                          type="date"
                          value={formData.expiryDate}
                          onChange={handleInputChange}
                        />
                      </div>
                    </div>
                  </div>

                  {!bulkGenerate ? (
                    <Button
                      onClick={handleDownload}
                      disabled={isGenerating || !studentPhoto || !schoolLogo || !formData.fullName}
                      className="w-full mt-4"
                    >
                      {isGenerating ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Generating...
                        </>
                      ) : (
                        <>
                          <Download className="mr-2 h-4 w-4" />
                          Download ID Card (PNG)
                        </>
                      )}
                    </Button>
                  ) : (
                    <Button
                      onClick={handleBulkDownload}
                      disabled={isGenerating || selectedClassStudents.length === 0}
                      className="w-full mt-4 bg-green-600 hover:bg-green-700"
                    >
                      {isGenerating ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Generating Bulk Cards...
                        </>
                      ) : (
                        <>
                          <FileText className="mr-2 h-4 w-4" />
                          Generate {selectedClassStudents.length} ID Cards (PDF)
                        </>
                      )}
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Right side - Preview */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <CreditCard className="mr-2 h-5 w-5 text-blue-500" />
                  ID Card Preview
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex justify-center">
                  <div
                    ref={cardRef}
                    className="w-[85.6mm] h-[54mm] rounded-xl overflow-hidden shadow-lg bg-white"
                  >
                    {/* Header with gradient and school logo */}
                    <div className="h-[18mm] bg-gradient-to-r from-blue-600 to-blue-400 flex items-center px-4">
                      {schoolLogo ? (
                        <div className="w-10 h-10 relative mr-3">
                          <Image
                            src={schoolLogo}
                            alt="School logo"
                            fill
                            className="object-contain"
                          />
                        </div>
                      ) : (
                        <School className="w-10 h-10 text-white mr-3" />
                      )}
                      <div className="text-white">
                        <h3 className="font-bold text-lg leading-tight font-serif">{formData.schoolName}</h3>
                        <p className="text-xs opacity-90 font-serif">STUDENT IDENTIFICATION CARD</p>
                      </div>
                    </div>

                    {/* Card body */}
                    <div className="flex p-3 h-[36mm]">
                      {/* Left side - Photo */}
                      <div className="w-[22mm]">
                        <div className="w-full aspect-[3/4] rounded-md overflow-hidden border border-gray-200 shadow-sm bg-gray-50">
                          {studentPhoto ? (
                            <Image
                              src={studentPhoto}
                              alt="Student photo"
                              width={100}
                              height={133}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center bg-gray-100">
                              <User className="h-8 w-8 text-gray-400" />
                            </div>
                          )}
                        </div>

                        {/* QR Code */}
                        <div className="mt-1 w-full aspect-square bg-white flex items-center justify-center">
                          <QrCode className="w-full h-full text-gray-800 p-1" />
                        </div>
                      </div>

                      {/* Right side - Info */}
                      <div className="flex-1 pl-3 flex flex-col justify-between">
                        <div className="space-y-1">
                          <div className="text-sm font-semibold text-gray-800 font-serif">{formData.fullName || 'Student Name'}</div>
                          {(formData.fatherName || formData.gfName) && (
                            <div className="text-xs font-serif italic text-gray-600">
                              {formData.fatherName && `s/o ${formData.fatherName}`}
                              {formData.gfName && ` ${formData.fatherName ? ',' : ''} ${formData.gfName}`}
                            </div>
                          )}

                          <div className="text-xs flex mt-1">
                            <span className="text-gray-500 w-16">ID Number:</span>
                            <span className="font-medium font-serif">{formData.idNumber || 'ID12345'}</span>
                          </div>

                          <div className="text-xs flex">
                            <span className="text-gray-500 w-16">Class:</span>
                            <span className="font-medium font-serif">{formData.className || 'Class'}</span>
                          </div>

                          <div className="text-xs flex">
                            <span className="text-gray-500 w-16">Age:</span>
                            <span className="font-medium font-serif">{formData.age || '15'} years</span>
                          </div>
                        </div>

                        <div className="text-xs border-t pt-1 mt-1">
                          <div className="flex justify-between">
                            <span className="text-gray-500">Valid Until:</span>
                            <span className="font-medium font-serif">
                              {new Date(formData.expiryDate).toLocaleDateString('en-US', {
                                day: '2-digit',
                                month: 'short',
                                year: 'numeric'
                              })}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>

      {/* Photo Upload Dialog */}
      <Dialog open={isPhotoUploadDialogOpen} onOpenChange={setIsPhotoUploadDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Upload Student Photo</DialogTitle>
          </DialogHeader>

          {currentPhotoStudent && (
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <div className="w-16 h-16 relative rounded-full overflow-hidden bg-gray-100 border">
                  {currentPhotoStudent.photoUrl ? (
                    <Image
                      src={currentPhotoStudent.photoUrl}
                      alt={currentPhotoStudent.name}
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <User className="h-8 w-8 text-gray-400" />
                    </div>
                  )}
                </div>
                <div>
                  <h3 className="font-medium">{currentPhotoStudent.name}</h3>
                  <p className="text-sm text-gray-500">ID: {currentPhotoStudent.sid}</p>
                  <p className="text-sm text-gray-500">Class: {currentPhotoStudent.className}</p>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="studentPhotoUpload">Upload Photo</Label>
                <div className="flex items-center space-x-4">
                  <div className="flex-1">
                    <div
                      className="border-2 border-dashed rounded-md p-4 text-center cursor-pointer hover:bg-gray-50"
                      onClick={() => document.getElementById('studentPhotoUpload')?.click()}
                    >
                      <Upload className="h-6 w-6 mx-auto text-gray-400 mb-2" />
                      <p className="text-sm text-gray-500">Click to upload photo</p>
                      <input
                        id="studentPhotoUpload"
                        type="file"
                        accept="image/*"
                        className="hidden"
                        onChange={handleStudentPhotoUpload}
                        disabled={uploadingPhoto}
                      />
                    </div>
                  </div>
                  {currentPhotoStudent.photoUrl && (
                    <div className="w-20 h-20 relative border rounded-md overflow-hidden">
                      <Image
                        src={currentPhotoStudent.photoUrl}
                        alt="Student photo preview"
                        fill
                        className="object-cover"
                      />
                    </div>
                  )}
                </div>
              </div>

              <div className="text-xs text-gray-500 mt-2">
                Student {selectedStudentsForPhotos.findIndex(s => s.id === currentPhotoStudent.id) + 1} of {selectedStudentsForPhotos.length}
              </div>
            </div>
          )}

          <DialogFooter className="flex justify-between">
            <Button
              variant="outline"
              onClick={() => {
                setIsPhotoUploadDialogOpen(false)
                setCurrentPhotoStudent(null)
                setSelectedStudentsForPhotos([])
              }}
            >
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button
              onClick={() => {
                // Skip to the next student
                if (selectedStudentsForPhotos.length > 0 && currentPhotoStudent) {
                  const currentIndex = selectedStudentsForPhotos.findIndex(s => s.id === currentPhotoStudent.id)
                  if (currentIndex < selectedStudentsForPhotos.length - 1) {
                    setCurrentPhotoStudent(selectedStudentsForPhotos[currentIndex + 1])
                  } else {
                    // We're done with all students
                    setIsPhotoUploadDialogOpen(false)
                    setCurrentPhotoStudent(null)
                    setSelectedStudentsForPhotos([])
                  }
                }
              }}
              disabled={uploadingPhoto}
            >
              {uploadingPhoto ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Uploading...
                </>
              ) : (
                <>
                  Skip
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

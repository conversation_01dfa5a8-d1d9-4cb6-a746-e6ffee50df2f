"use client"

import React, { useState } from 'react'
import { <PERSON><PERSON> } from './ui/button'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/table'
import { Input } from './ui/input'
import { Search, Edit, Trash2, SlidersHorizontal, Plus, Users, BookOpen, UserCircle, Check } from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "./ui/dialog"
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "./ui/select"
import { Label } from "./ui/label"
import { capitalizeName } from "@/app/lib/utils"
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

interface Teacher {
  id: string
  name: string
  subject: string
}

interface Class {
  id: string
  name: string
  hrTeacherId: string | null
  hrTeacher?: Teacher
  totalStudents: number
  totalSubjects: number
  createdAt: string
  updatedAt: string
}

// List of classes grouped by grade
const classGroups = [
  { grade: 'Grade 1', classes: ['1A', '1B', '1C', '1D'] },
  { grade: 'Grade 2', classes: ['2A', '2B', '2C', '2D'] },
  { grade: 'Grade 3', classes: ['3A', '3B', '3C', '3D'] },
  { grade: 'Grade 4', classes: ['4A', '4B', '4C', '4D'] },
  { grade: 'Grade 5', classes: ['5A', '5B', '5C', '5D'] },
  { grade: 'Grade 6', classes: ['6A', '6B', '6C', '6D'] },
  { grade: 'Grade 7', classes: ['7A', '7B', '7C', '7D'] },
  { grade: 'Grade 8', classes: ['8A', '8B', '8C', '8D'] },
  { grade: 'Grade 9', classes: ['9A', '9B', '9C', '9D'] },
  { grade: 'Grade 10', classes: ['10A', '10B', '10C', '10D'] },
  { grade: 'Grade 11', classes: ['11A', '11B', '11C', '11D'] },
  { grade: 'Grade 12', classes: ['12A', '12B', '12C', '12D'] },
]

// Flat list of all classes
const classesList = classGroups.flatMap(group => group.classes)

// List of subjects
const subjectsList = [
  'Arabic', 'Quran', 'Islamic Education', 'Terbia', 'English', 'Somali', 'Amharic',
  'Maths', 'Chemistry', 'Biology', 'Physics', 'IT', 'Agriculture', 'Economics',
  'Geography', 'History', 'CTE', 'Citizenship', 'HPE', 'General Science', 'Social Science'
]

export function Classes() {
  const queryClient = useQueryClient()
  const [open, setOpen] = useState(false)
  const [isEditing, setIsEditing] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')

  // Fetch teachers for HR Teacher dropdown
  const { data: teachers } = useQuery({
    queryKey: ['teachers'],
    queryFn: async () => {
      const res = await fetch('/api/teachers')
      if (!res.ok) throw new Error('Failed to fetch teachers')
      return res.json()
    }
  })
  const [selectedSubjects, setSelectedSubjects] = useState<string[]>([])
  const [newClass, setNewClass] = useState({
    id: '',
    name: '',
    hrTeacherId: 'none',
    totalStudents: 0,
    totalSubjects: 0
  })

  // Fetch classes
  const { data: classes, isLoading } = useQuery({
    queryKey: ['classes'],
    queryFn: async () => {
      const res = await fetch('/api/classes')
      if (!res.ok) throw new Error('Failed to fetch classes')
      return res.json()
    }
  })

  // Create class mutation
  const createClassMutation = useMutation({
    mutationFn: async (newClass: Omit<Class, 'id' | 'createdAt' | 'updatedAt'>) => {
      const res = await fetch('/api/classes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newClass),
      })
      if (!res.ok) throw new Error('Failed to create class')
      return res.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['classes'] })
      setOpen(false)
      resetForm()
    },
  })

  // Update class mutation
  const updateClassMutation = useMutation({
    mutationFn: async (updatedClass: Partial<Class> & { id: string }) => {
      const res = await fetch(`/api/classes/${updatedClass.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedClass),
      })
      if (!res.ok) throw new Error('Failed to update class')
      return res.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['classes'] })
      setOpen(false)
      resetForm()
    },
  })

  // Delete class mutation
  const deleteClassMutation = useMutation({
    mutationFn: async (id: string) => {
      const res = await fetch(`/api/classes/${id}`, {
        method: 'DELETE',
      })
      if (!res.ok) throw new Error('Failed to delete class')
      return res.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['classes'] })
    },
  })

  // Filter classes based on search
  const filteredClasses = (classes ?? []).filter((cls: Class) =>
    cls.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleDelete = (id: string) => {
    deleteClassMutation.mutate(id)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setNewClass({
      ...newClass,
      [name]: value
    })
  }

  const handleSelectChange = (name: string, value: string) => {
    setNewClass({
      ...newClass,
      [name]: value
    })
  }

  const handleEdit = (cls: Class) => {
    setNewClass({
      id: cls.id,
      name: cls.name,
      hrTeacherId: cls.hrTeacherId || 'none',
      totalStudents: cls.totalStudents,
      totalSubjects: cls.totalSubjects
    })

    // In a real application, you would fetch the subjects for this class
    // and populate the selectedSubjects array
    // For now, we'll just set a few sample subjects based on the class name
    const sampleSubjects = cls.name.startsWith('1') ?
      ['Arabic', 'Quran', 'English', 'Maths'] :
      ['Arabic', 'Quran', 'English', 'Maths', 'Science']

    setSelectedSubjects(sampleSubjects)
    setIsEditing(true)
    setOpen(true)
  }

  const resetForm = () => {
    setNewClass({
      id: '',
      name: '',
      hrTeacherId: 'none',
      totalStudents: 0,
      totalSubjects: 0
    })
    setSelectedSubjects([])
    setIsEditing(false)
  }

  // Function to update student counts
  const updateStudentCounts = async () => {
    try {
      const res = await fetch('/api/classes/update-counts', {
        method: 'POST',
      })

      if (!res.ok) {
        throw new Error('Failed to update student counts')
      }

      // Refresh the classes data
      queryClient.invalidateQueries({ queryKey: ['classes'] })

      alert('Student counts updated successfully')
    } catch (error) {
      console.error('Error updating student counts:', error)
      alert('Failed to update student counts')
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (!newClass.name) {
      alert('Please fill in all required fields')
      return
    }

    // Set totalSubjects based on selected subjects count
    const totalSubjects = selectedSubjects.length

    // Handle the 'none' value for hrTeacherId
    const hrTeacherId = newClass.hrTeacherId === 'none' ? null : newClass.hrTeacherId || null

    if (isEditing) {
      updateClassMutation.mutate({
        id: newClass.id,
        name: newClass.name,
        hrTeacherId: hrTeacherId,
        totalSubjects: totalSubjects
      })
    } else {
      createClassMutation.mutate({
        name: newClass.name,
        hrTeacherId: hrTeacherId,
        totalStudents: 0,
        totalSubjects: totalSubjects
      })
    }

    // In a real application, you would also save the selected subjects
    // to the database here, creating the subject records for this class
    console.log('Selected subjects:', selectedSubjects)
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Classes</h2>
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Search className="h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search classes..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-64"
            />
          </div>
          <Button variant="outline" onClick={updateStudentCounts} className="mr-2">
            <SlidersHorizontal className="h-4 w-4 mr-2" />
            Update Student Counts
          </Button>
          <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Class
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[550px]">
              <DialogHeader>
                <DialogTitle>{isEditing ? 'Edit Class' : 'Add New Class'}</DialogTitle>
                <DialogDescription>
                  {isEditing ? 'Edit the class details and click save when you\'re done.' : 'Fill in the class details and click save when you\'re done.'}
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="grid gap-6 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="name" className="text-sm font-medium">
                      Class Name
                    </Label>
                    <div className="relative">
                      <Users className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Select
                        value={newClass.name}
                        onValueChange={(value) => handleSelectChange('name', value)}
                        required
                      >
                        <SelectTrigger className="pl-9">
                          <SelectValue placeholder="Select class" />
                        </SelectTrigger>
                        <SelectContent className="max-h-[300px] overflow-y-auto">
                          <div className="p-1 sticky top-0 bg-white z-10 border-b border-gray-100 flex items-center justify-center">
                            <p className="text-xs text-gray-500">Scroll to see more classes</p>
                          </div>
                          {classGroups.map((group) => (
                            <SelectGroup key={group.grade}>
                              <SelectLabel className="text-xs font-semibold">
                                {group.grade}
                              </SelectLabel>
                              {group.classes.map((cls) => (
                                <SelectItem key={cls} value={cls}>
                                  {cls}
                                </SelectItem>
                              ))}
                            </SelectGroup>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="hrTeacher" className="text-sm font-medium">
                      HR Teacher
                    </Label>
                    <div className="relative">
                      <UserCircle className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Select
                        value={newClass.hrTeacherId}
                        onValueChange={(value) => handleSelectChange('hrTeacherId', value)}
                      >
                        <SelectTrigger className="pl-9">
                          <SelectValue placeholder="Select HR Teacher" />
                        </SelectTrigger>
                        <SelectContent className="max-h-[300px] overflow-y-auto">
                          <SelectItem value="none">None</SelectItem>
                          {teachers?.map((teacher: { id: string; name: string; subject: string }) => (
                            <SelectItem key={teacher.id} value={teacher.id}>
                              {teacher.name} ({teacher.subject})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>

                <div className="grid gap-2">
                  <Label className="text-sm font-medium">
                    Subjects
                  </Label>
                  <div className="border rounded-md p-4 max-h-[200px] overflow-y-auto">
                    <div className="grid grid-cols-2 gap-3">
                      {subjectsList.map((subject) => (
                        <div key={subject} className="flex items-center space-x-2">
                          <div className="flex h-5 items-center">
                            <input
                              type="checkbox"
                              id={`subject-${subject}`}
                              className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600"
                              onChange={(e) => {
                                const isChecked = e.target.checked
                                const currentSubjects = [...selectedSubjects]

                                if (isChecked && !currentSubjects.includes(subject)) {
                                  currentSubjects.push(subject);
                                } else if (!isChecked && currentSubjects.includes(subject)) {
                                  const index = currentSubjects.indexOf(subject);
                                  currentSubjects.splice(index, 1);
                                }

                                setSelectedSubjects(currentSubjects);
                              }}
                              checked={selectedSubjects.includes(subject)}
                            />
                          </div>
                          <label htmlFor={`subject-${subject}`} className="text-sm font-medium text-gray-700">
                            {subject}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>
                  <div className="flex items-center mt-1">
                    <BookOpen className="h-4 w-4 text-gray-400 mr-1" />
                    <p className="text-xs text-gray-500">
                      Selected subjects: <span className="font-medium text-indigo-600">{selectedSubjects.length}</span>
                    </p>
                  </div>
                </div>

                <DialogFooter>
                  <Button variant="outline" onClick={() => {
                    setOpen(false);
                    resetForm();
                  }} type="button">
                    Cancel
                  </Button>
                  <Button type="submit" disabled={createClassMutation.isPending || updateClassMutation.isPending}>
                    {createClassMutation.isPending || updateClassMutation.isPending ?
                      'Saving...' :
                      isEditing ? 'Update Class' : 'Add Class'
                    }
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div className="overflow-x-auto">
        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            Loading...
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Class</TableHead>
                <TableHead>HR Teacher</TableHead>
                <TableHead>Total Students</TableHead>
                <TableHead>Total Subjects</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredClasses.length > 0 ? (
                filteredClasses.map((cls: Class) => (
                  <TableRow key={cls.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 font-semibold">
                          {cls.name.charAt(0)}
                        </div>
                        {cls.name}
                      </div>
                    </TableCell>
                    <TableCell>
                      {cls.hrTeacher ? (
                        <div className="flex items-center gap-2">
                          <div className="h-8 w-8 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-600">
                            <UserCircle className="h-4 w-4" />
                          </div>
                          <div>
                            {cls.hrTeacher.name || 'Unknown'}
                            <div className="text-xs text-gray-500">
                              {cls.hrTeacher.subject || ''}
                            </div>
                          </div>
                        </div>
                      ) : cls.hrTeacherId ? (
                        <div className="flex items-center gap-2">
                          <div className="h-8 w-8 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-600">
                            <UserCircle className="h-4 w-4" />
                          </div>
                          <div>
                            {teachers?.find(t => t.id === cls.hrTeacherId)?.name || 'Unknown'}
                            <div className="text-xs text-gray-500">
                              {teachers?.find(t => t.id === cls.hrTeacherId)?.subject || ''}
                            </div>
                          </div>
                        </div>
                      ) : (
                        <span className="text-gray-400 text-sm">Not assigned</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="bg-indigo-100 text-indigo-800 px-2 py-1 rounded-full text-xs font-medium w-fit">
                        {cls.totalStudents} Students
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium w-fit">
                        {cls.totalSubjects} Subjects
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-indigo-600"
                          onClick={() => handleEdit(cls)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-red-600"
                          onClick={() => handleDelete(cls.id)}
                          disabled={deleteClassMutation.isPending}
                        >
                          {deleteClassMutation.isPending ? (
                            <div className="animate-spin">...</div>
                          ) : (
                            <Trash2 className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={4} className="text-center py-4">
                    No classes found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        )}
      </div>
    </div>
  )
}

import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET all teacher permissions
export async function GET() {
  try {
    console.log('Fetching teacher permissions...')

    const permissions = await prisma.teacherPermission.findMany({
      select: {
        id: true,
        teacherId: true,
        classId: true,
        canViewAttendance: true,
        canTakeAttendance: true,
        canAddMarks: true,
        canEditMarks: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    })

    console.log(`Found ${permissions.length} teacher permissions`)

    return NextResponse.json({
      permissions,
      total: permissions.length
    })
  } catch (error) {
    console.error('Error fetching teacher permissions:', error)
    return NextResponse.json(
      { error: 'Failed to fetch teacher permissions' },
      { status: 500 }
    )
  }
}

// POST to create or update teacher permissions
export async function POST(request: Request) {
  try {
    console.log('Creating/updating teacher permissions...')
    const data = await request.json()

    const { teacherId, classId, permissions } = data

    if (!teacherId || !classId || !permissions) {
      return NextResponse.json(
        { error: 'Teacher ID, class ID, and permissions are required' },
        { status: 400 }
      )
    }

    // Check if teacher exists
    const teacher = await prisma.teacher.findUnique({
      where: { id: teacherId }
    })

    if (!teacher) {
      return NextResponse.json(
        { error: 'Teacher not found' },
        { status: 404 }
      )
    }

    // Check if class exists
    const classExists = await prisma.class.findUnique({
      where: { id: classId }
    })

    if (!classExists) {
      return NextResponse.json(
        { error: 'Class not found' },
        { status: 404 }
      )
    }

    // Upsert teacher permissions (create or update)
    const teacherPermission = await prisma.teacherPermission.upsert({
      where: {
        teacherId_classId: {
          teacherId,
          classId
        }
      },
      update: {
        canViewAttendance: permissions.canViewAttendance,
        canTakeAttendance: permissions.canTakeAttendance,
        canAddMarks: permissions.canAddMarks,
        canEditMarks: permissions.canEditMarks,
        updatedAt: new Date()
      },
      create: {
        teacherId,
        classId,
        canViewAttendance: permissions.canViewAttendance,
        canTakeAttendance: permissions.canTakeAttendance,
        canAddMarks: permissions.canAddMarks,
        canEditMarks: permissions.canEditMarks
      }
    })

    console.log('Teacher permissions saved successfully:', teacherPermission.id)

    return NextResponse.json({
      message: 'Teacher permissions saved successfully',
      permission: teacherPermission
    })
  } catch (error) {
    console.error('Error saving teacher permissions:', error)
    return NextResponse.json(
      { error: 'Failed to save teacher permissions' },
      { status: 500 }
    )
  }
}

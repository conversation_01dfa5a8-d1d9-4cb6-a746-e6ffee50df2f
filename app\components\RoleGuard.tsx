'use client'

import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/app/contexts/auth-context'
import PermissionDialog from './PermissionDialog'

interface RoleGuardProps {
  // Original role-based props
  roles?: string | string[]
  // New sidebar item based props
  sidebarItems?: string | string[]
  // Redirect to dashboard if access denied
  redirectOnAccessDenied?: boolean
  // Fallback UI to render if access denied
  fallback?: React.ReactNode
  // Children to render if access granted
  children: React.ReactNode
  // Whether to show a dialog when access is denied
  showDialog?: boolean
  // Custom dialog message
  dialogMessage?: string
  // Redirect delay in milliseconds (to allow user to read the dialog)
  redirectDelay?: number
  // Page or feature name for the dialog
  featureName?: string
}

/**
 * A component that conditionally renders its children based on user roles or sidebar access.
 *
 * @param roles - A single role string or array of roles (any one is sufficient)
 * @param sidebarItems - A single sidebar item ID or array of IDs (any one is sufficient)
 * @param redirectOnAccessDenied - Whether to redirect to dashboard if access denied
 * @param fallback - Optional element to render if user doesn't have the required access
 * @param children - Elements to render if user has the required access
 * @param showDialog - Whether to show a dialog when access is denied
 * @param dialogMessage - Custom dialog message
 * @param redirectDelay - Delay before redirecting (ms)
 * @param featureName - Name of the feature or page being accessed
 */
export function RoleGuard({
  roles,
  sidebarItems,
  redirectOnAccessDenied = false,
  fallback = null,
  children,
  showDialog = true,
  dialogMessage,
  redirectDelay = 3000, // 3 seconds default
  featureName = 'page'
}: RoleGuardProps) {
  const { user, hasSidebarAccess } = useAuth()
  const router = useRouter()
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [isRedirecting, setIsRedirecting] = useState(false)

  // If no user or no role, don't render children
  if (!user || !user.role) return <>{fallback}</>

  // Check if user has any of the required roles
  const hasRequiredRole = roles ? (
    Array.isArray(roles)
      ? roles.includes(user.role)
      : user.role === roles
  ) : true

  // Check if user has access to any of the required sidebar items
  const hasSidebarItemAccess = sidebarItems ? (
    Array.isArray(sidebarItems)
      ? sidebarItems.some(item => hasSidebarAccess(item))
      : hasSidebarAccess(sidebarItems)
  ) : true

  // User has access if they have the required role AND sidebar item access
  const hasAccess = hasRequiredRole && hasSidebarItemAccess

  // Generate a message for the dialog
  const getMessage = () => {
    if (dialogMessage) return dialogMessage;

    let message = `You do not have permission to access this ${featureName}.`;

    // Add information about required roles
    if (roles) {
      const roleText = Array.isArray(roles) ? roles.join(', ') : roles;
      message += `\n\nRequired role: ${roleText}`;
    }

    // Add information about required sidebar items
    if (sidebarItems) {
      const itemText = Array.isArray(sidebarItems) ? sidebarItems.join(', ') : sidebarItems;
      message += `\n\nRequired access to: ${itemText}`;
    }

    // Add redirect information
    if (redirectOnAccessDenied) {
      message += `\n\nYou will be redirected to the dashboard in ${redirectDelay/1000} seconds.`;
    }

    return message;
  };

  // Handle access denied
  useEffect(() => {
    if (!hasAccess) {
      if (showDialog && !isDialogOpen && !isRedirecting) {
        setIsDialogOpen(true);
      }

      if (redirectOnAccessDenied && !isRedirecting) {
        setIsRedirecting(true);

        // Set a timeout to redirect after the specified delay
        const redirectTimeout = setTimeout(() => {
          router.push('/dashboard');
        }, redirectDelay);

        // Clean up the timeout if the component unmounts
        return () => clearTimeout(redirectTimeout);
      }
    }
  }, [hasAccess, redirectOnAccessDenied, router, showDialog, isDialogOpen, isRedirecting, redirectDelay]);

  return (
    <>
      {/* Permission Denied Dialog */}
      {showDialog && (
        <PermissionDialog
          isOpen={isDialogOpen}
          onClose={() => setIsDialogOpen(false)}
          title="Access Denied"
          message={getMessage()}
          resource={featureName}
          action="access"
        />
      )}

      {/* Render children if user has access, otherwise render fallback */}
      {hasAccess ? <>{children}</> : <>{fallback}</>}
    </>
  );
}

export default RoleGuard

'use client'

import React, { useState, useEffect } from 'react'
import DashboardLayout from '../components/DashboardLayout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select'
import { Label } from '../components/ui/label'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs'
import { useQuery } from '@tanstack/react-query'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '../components/ui/dialog'
import { Checkbox } from '../components/ui/checkbox'
import { Button } from '../components/ui/button'
import { BarChart3, <PERSON><PERSON><PERSON> as PieChartIcon, Download, RefreshCw, TrendingUp, Users, Target, Award } from 'lucide-react'

// We'll import jsPDF directly in the generatePDF function to avoid SSR issues

// Create a client with configuration
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: true,
      refetchOnMount: true,
      staleTime: 10000, // Consider data stale after 10 seconds
      retry: 2,
    },
  },
})

// Define types
interface Mark {
  id: string
  studentId: string
  studentName: string
  gender: string
  className: string
  subject: string
  academicYear: string
  examType: string
  mark: number
}

interface RangeData {
  range: string
  maleCount: number
  femaleCount: number
  avgMark: number
  totalCount: number
}

interface ChartData {
  name: string
  male: number
  female: number
}

interface PieChartData {
  name: string
  value: number
  color: string
}

// Define the score ranges
const scoreRanges = [
  { min: 0, max: 50, label: '< 50', color: '#FF5252' },
  { min: 51, max: 60, label: '51-60', color: '#FFA726' },
  { min: 61, max: 74, label: '61-74', color: '#FFEB3B' },
  { min: 75, max: 89, label: '75-89', color: '#66BB6A' },
  { min: 90, max: 100, label: '> 90', color: '#2196F3' }
]

function Visualizer() {
  const [selectedSubject, setSelectedSubject] = useState<string>('all')
  const [isMounted, setIsMounted] = useState(false)
  const [refreshKey, setRefreshKey] = useState(0) // Add a refresh key to force refetch
  const [selectedSubjectsForPdf, setSelectedSubjectsForPdf] = useState<string[]>([])
  const [pdfDialogOpen, setPdfDialogOpen] = useState(false)
  const [generatingPdf, setGeneratingPdf] = useState(false)

  // Fetch classes for dropdown - using the visualizer-specific endpoint
  const { data: classes = [], isLoading: isClassesLoading, refetch: refetchClasses } = useQuery({
    queryKey: ['visualizer-classes', refreshKey],
    queryFn: async () => {
      console.log('Fetching classes for visualizer...')
      const res = await fetch('/api/visualizer/classes')
      if (!res.ok) {
        throw new Error('Failed to fetch classes')
      }
      const data = await res.json()
      console.log('Classes fetched:', data.length, 'classes')

      // Debug log to check if 8E is in the fetched data
      const has8E = data.some((cls: any) => cls.name === '8E')
      console.log('Class 8E found in API response:', has8E)

      if (data.length > 0) {
        console.log('Sample class names:', data.slice(0, 5).map((cls: any) => cls.name))
      }

      return data
    },
    staleTime: 60000,
  })

  // Fetch subjects for dropdown - using the visualizer-specific endpoint
  const { data: subjects = [], isLoading: isSubjectsLoading, refetch: refetchSubjects } = useQuery({
    queryKey: ['visualizer-subjects', refreshKey],
    queryFn: async () => {
      console.log('Fetching subjects for visualizer...')
      const res = await fetch('/api/visualizer/subjects')
      if (!res.ok) {
        throw new Error('Failed to fetch subjects')
      }
      const data = await res.json()
      console.log('Subjects fetched:', data.length, 'subjects')

      // Debug log to check if Maths (8E) is in the fetched data
      const hasMaths8E = data.some((subj: any) => subj.name === 'Maths (8E)')
      console.log('Maths (8E) found in API response:', hasMaths8E)

      // Check for subjects with "Maths" in the name
      const mathsSubjects = data.filter((subj: any) => subj.name.includes('Maths'))
      console.log('Subjects containing "Maths":', mathsSubjects.length)
      if (mathsSubjects.length > 0) {
        mathsSubjects.forEach((subj: any) => {
          console.log(`- ${subj.name} (ID: ${subj.id}, ClassID: ${subj.classId}, ClassName: ${subj.class?.name || 'N/A'})`)
        })
      }

      // Check for subjects associated with Class 8E
      const class8ESubjects = data.filter((subj: any) => subj.class && subj.class.name === '8E')
      console.log('Subjects for Class 8E:', class8ESubjects.length)
      if (class8ESubjects.length > 0) {
        class8ESubjects.forEach((subj: any) => {
          console.log(`- ${subj.name} (ID: ${subj.id}, ClassID: ${subj.classId})`)
        })
      }

      if (data.length > 0) {
        console.log('Sample subject names:', data.slice(0, 5).map((subj: any) => subj.name))
      }

      return data
    },
    staleTime: 60000,
  })

  // Fetch marks data
  const { data: marks = [], isLoading, refetch: refetchMarks } = useQuery({
    queryKey: ['marks', selectedSubject, refreshKey],
    queryFn: async () => {
      let url = '/api/marks'
      const params = new URLSearchParams()

      // Add visualizer flag to get the right format
      params.append('visualizer', 'true')

      if (selectedSubject !== 'all') {
        params.append('subject', selectedSubject)
      }

      url += `?${params.toString()}`

      const res = await fetch(url)
      if (!res.ok) {
        throw new Error('Failed to fetch marks')
      }
      return res.json()
    },
    staleTime: 30000,
  })

  useEffect(() => {
    setIsMounted(true)
  }, [])

  // Function to refresh all data
  const refreshAllData = async () => {
    console.log('Manually refreshing all data...')
    setRefreshKey(prev => prev + 1) // Increment refresh key to trigger refetch

    // First refetch subjects to ensure we get the latest data
    await refetchSubjects()

    // Then refetch the marks data
    refetchMarks()
  }

  // Function to toggle subject selection for PDF
  const toggleSubjectSelection = (subjectName: string) => {
    setSelectedSubjectsForPdf(prev =>
      prev.includes(subjectName)
        ? prev.filter(s => s !== subjectName)
        : [...prev, subjectName]
    )
  }

  // Function to select all subjects for PDF
  const selectAllSubjects = () => {
    setSelectedSubjectsForPdf(subjects.map((subj: any) => subj.name))
  }

  // Function to deselect all subjects for PDF
  const deselectAllSubjects = () => {
    setSelectedSubjectsForPdf([])
  }

  // Function to generate PDF
  const generatePDF = async () => {
    if (!isMounted || selectedSubjectsForPdf.length === 0) return

    try {
      console.log('Starting PDF generation...')
      setGeneratingPdf(true)

      // Dynamically import jsPDF and jspdf-autotable
      console.log('Importing jsPDF...')
      const jsPDFModule = await import('jspdf')
      const jsPDF = jsPDFModule.default
      console.log('jsPDF imported successfully')

      // Import autoTable
      console.log('Importing autoTable...')
      const autoTableModule = await import('jspdf-autotable')
      const autoTable = autoTableModule.default
      console.log('autoTable imported successfully')

      // Create a new jsPDF instance
      const doc = new jsPDF()

      // Set document properties
      doc.setProperties({
        title: 'Data Visualization Report',
        subject: 'Student Performance Data',
        author: 'School Management System',
        creator: 'Data Visualizer'
      })

      // Add title
      doc.setFontSize(18)
      doc.setTextColor(0, 51, 102)
      doc.text('Data Visualization Report', 105, 15, { align: 'center' })

      // Add subtitle with date
      doc.setFontSize(12)
      doc.setTextColor(100, 100, 100)
      doc.text(`Generated on ${new Date().toLocaleDateString()}`, 105, 22, { align: 'center' })

      // Add filter information
      doc.setFontSize(10)
      doc.setTextColor(80, 80, 80)
      doc.text(`Selected Subjects: ${selectedSubjectsForPdf.length}`, 105, 28, { align: 'center' })

      let yPosition = 35
      let subjectsWithData = 0

      // Process each selected subject
      for (const subjectName of selectedSubjectsForPdf) {
        // Filter marks for this subject
        const subjectMarks = marks.filter((mark: Mark) => mark.subject === subjectName)

        if (subjectMarks.length === 0) {
          // Skip if no data for this subject
          console.log(`No data for subject ${subjectName}, skipping...`)
          continue
        }

        subjectsWithData++

        // Calculate range data for this subject
        const subjectRangeData = scoreRanges.map(range => {
          const marksInRange = subjectMarks.filter((mark: Mark) =>
            mark.mark >= range.min && mark.mark <= range.max
          )

          const maleMarks = marksInRange.filter((mark: Mark) => mark.gender.toLowerCase() === 'male')
          const femaleMarks = marksInRange.filter((mark: Mark) => mark.gender.toLowerCase() === 'female')

          const totalMarks = marksInRange.reduce((sum: number, mark: Mark) => sum + mark.mark, 0)
          const avgMark = marksInRange.length > 0 ? totalMarks / marksInRange.length : 0

          return {
            range: range.label,
            maleCount: maleMarks.length,
            femaleCount: femaleMarks.length,
            avgMark: parseFloat(avgMark.toFixed(2)),
            totalCount: marksInRange.length
          }
        })

        // Add subject title
        if (yPosition > 250) {
          // Add a new page if we're running out of space
          doc.addPage()
          yPosition = 20
        }

        doc.setFontSize(14)
        doc.setTextColor(0, 102, 204)
        doc.text(`Subject: ${subjectName}`, 14, yPosition)
        yPosition += 8

        // Add table for this subject
        autoTable(doc, {
          startY: yPosition,
          head: [['Range', 'Male Count', 'Female Count', 'Avg Mark']],
          body: subjectRangeData.map(data => [
            data.range,
            data.maleCount,
            data.femaleCount,
            data.avgMark
          ]),
          theme: 'grid',
          headStyles: {
            fillColor: [0, 102, 204],
            textColor: [255, 255, 255],
            fontStyle: 'bold'
          },
          alternateRowStyles: {
            fillColor: [240, 240, 240]
          },
          margin: { top: 10 }
        })

        // Update yPosition for the next subject
        yPosition = (doc as any).lastAutoTable.finalY + 15
      }

      // Check if we have any data to display
      if (subjectsWithData === 0) {
        // No data for any selected subject
        console.log('No data available for any of the selected subjects')
        doc.setFontSize(14)
        doc.setTextColor(255, 0, 0)
        doc.text('No data available for the selected subjects.', 105, 50, { align: 'center' })
        doc.setFontSize(12)
        doc.setTextColor(100, 100, 100)
        doc.text('Please select different subjects or change the filter.', 105, 60, { align: 'center' })
      }

      // Save the PDF
      console.log('Saving PDF...')
      doc.save('data-visualization-report.pdf')
      console.log('PDF saved successfully')

      // Close the dialog
      setPdfDialogOpen(false)
      setGeneratingPdf(false)
      console.log('PDF generation completed')

    } catch (error) {
      console.error('Error generating PDF:', error)
      setGeneratingPdf(false)
    }
  }

  // Calculate range data
  const rangeData: RangeData[] = scoreRanges.map(range => {
    const marksInRange = marks.filter((mark: Mark) =>
      mark.mark >= range.min && mark.mark <= range.max
    )

    const maleMarks = marksInRange.filter((mark: Mark) => mark.gender.toLowerCase() === 'male')
    const femaleMarks = marksInRange.filter((mark: Mark) => mark.gender.toLowerCase() === 'female')

    const totalMarks = marksInRange.reduce((sum: number, mark: Mark) => sum + mark.mark, 0)
    const avgMark = marksInRange.length > 0 ? totalMarks / marksInRange.length : 0

    return {
      range: range.label,
      maleCount: maleMarks.length,
      femaleCount: femaleMarks.length,
      avgMark: parseFloat(avgMark.toFixed(2)),
      totalCount: marksInRange.length
    }
  })

  // Prepare data for bar chart
  const barChartData: ChartData[] = rangeData.map(data => ({
    name: data.range,
    male: data.maleCount,
    female: data.femaleCount
  }))

  // Prepare data for pie chart
  const pieChartData: PieChartData[] = rangeData.map((data, index) => ({
    name: data.range,
    value: data.totalCount,
    color: scoreRanges[index].color
  }))

  return (
    <DashboardLayout>
      {!isMounted ? (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-cyan-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading data visualizer...</p>
          </div>
        </div>
      ) : (
      <div className="space-y-8">
        {/* Enhanced Header */}
        <div className="relative overflow-hidden rounded-3xl bg-gradient-to-r from-cyan-600 via-teal-600 to-emerald-600 p-8 text-white shadow-2xl">
          <div className="absolute inset-0 bg-black/10"></div>
          <div className="relative flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-bold tracking-tight mb-2 bg-gradient-to-r from-white to-cyan-100 bg-clip-text text-transparent">
                Data Visualizer
              </h1>
              <p className="text-cyan-100 text-lg">
                Interactive data visualization and analytics for student performance insights
              </p>
            </div>
            <div className="hidden md:flex items-center gap-4">
              <div className="h-16 w-16 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center">
                <BarChart3 className="w-8 h-8" />
              </div>
            </div>
          </div>
          {/* Decorative elements */}
          <div className="absolute -top-8 -right-8 w-32 h-32 bg-white/10 rounded-full blur-xl"></div>
          <div className="absolute -bottom-12 -left-12 w-40 h-40 bg-white/5 rounded-full blur-2xl"></div>
        </div>

        {/* Enhanced Filters Section */}
        <Card className="border-0 shadow-xl bg-white/90 backdrop-blur-sm rounded-2xl overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-cyan-50 to-teal-50 border-b border-cyan-100 p-6">
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 rounded-full bg-cyan-500/20 flex items-center justify-center">
                <Target className="h-5 w-5 text-cyan-600" />
              </div>
              <div>
                <CardTitle className="text-xl text-cyan-700">Data Filters & Controls</CardTitle>
                <CardDescription className="text-cyan-600/70">
                  Configure visualization parameters and data sources
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            <div className="flex flex-col items-center mb-4">

              <div className="flex items-center justify-center mb-6 space-x-4">
                <Button
                  onClick={refreshAllData}
                  variant="outline"
                  className="h-12 px-6 border-2 border-cyan-200 hover:border-cyan-500 hover:bg-cyan-50 transition-all duration-200"
                  disabled={isClassesLoading || isSubjectsLoading || isLoading}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh Data
                </Button>

                <Dialog open={pdfDialogOpen} onOpenChange={setPdfDialogOpen}>
                  <DialogTrigger asChild>
                    <Button
                      className="h-12 px-6 bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200"
                      disabled={isClassesLoading || isLoading}
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Generate PDF Report
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-lg md:max-w-xl">
                    <DialogHeader>
                      <DialogTitle>Generate PDF Report</DialogTitle>
                      <DialogDescription>
                        Select one or more subjects to include in the PDF report.
                      </DialogDescription>
                    </DialogHeader>

                    <div className="py-4">
                      <div className="flex items-center justify-between mb-4">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={selectAllSubjects}
                          disabled={subjects.length === 0}
                        >
                          Select All
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={deselectAllSubjects}
                          disabled={selectedSubjectsForPdf.length === 0}
                        >
                          Deselect All
                        </Button>
                      </div>

                      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 max-h-[300px] overflow-y-auto p-3 border rounded-md">
                        {subjects.map((subject: any) => (
                          <div key={subject.id} className="flex items-center space-x-2 min-w-0">
                            <Checkbox
                              id={`subject-${subject.id}`}
                              checked={selectedSubjectsForPdf.includes(subject.name)}
                              onCheckedChange={() => toggleSubjectSelection(subject.name)}
                            />
                            <label
                              htmlFor={`subject-${subject.id}`}
                              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 whitespace-nowrap overflow-hidden text-ellipsis"
                              title={subject.name}
                            >
                              {subject.name}
                            </label>
                          </div>
                        ))}

                        {subjects.length === 0 && (
                          <div className="col-span-1 sm:col-span-2 md:col-span-3 text-center py-4 text-gray-500">
                            No subjects available
                          </div>
                        )}
                      </div>
                    </div>

                    <DialogFooter className="sm:justify-between">
                      <div className="text-sm text-gray-500">
                        {selectedSubjectsForPdf.length} subjects selected
                      </div>
                      <Button
                        onClick={generatePDF}
                        disabled={selectedSubjectsForPdf.length === 0 || generatingPdf}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        {generatingPdf ? (
                          <>
                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Generating...
                          </>
                        ) : (
                          'Generate PDF'
                        )}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>

              {selectedSubject !== 'all' && (
                <div className="flex items-center text-sm mt-2">
                  <span className="mr-2 text-gray-600 dark:text-gray-400">Active filter:</span>
                  <span className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 px-2 py-1 rounded-full text-xs mr-2">
                    Subject: {selectedSubject}
                  </span>
                  <button
                    onClick={() => {
                      setSelectedSubject('all');
                    }}
                    className="text-xs text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 flex items-center"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    Reset
                  </button>
                </div>
              )}
            </div>
            <div className="flex justify-center">
              <div className="w-64 md:w-80">
                <Label htmlFor="subject" className="mb-1 block text-center">Subject</Label>
                <Select value={selectedSubject} onValueChange={setSelectedSubject}>
                  <SelectTrigger className={`${isSubjectsLoading ? "opacity-70" : ""} w-full`}>
                    {isSubjectsLoading ? (
                      <div className="flex items-center">
                        <div className="animate-spin h-4 w-4 mr-2 border-2 border-blue-500 border-t-transparent rounded-full"></div>
                        <span>Loading subjects...</span>
                      </div>
                    ) : (
                      <SelectValue placeholder="Select Subject" />
                    )}
                  </SelectTrigger>
                <SelectContent className="max-h-[300px] overflow-y-auto relative">
                  <div className="sticky top-0 z-10 bg-white dark:bg-gray-950 border-b pb-1">
                    <SelectItem value="all" className="font-semibold text-blue-600">All Subjects</SelectItem>
                    <div className="py-1 px-2 text-xs text-gray-500">Select a subject to filter</div>
                  </div>
                  {subjects && subjects.length > 0 ? (
                    // First, ensure Maths (8E) is at the top if it exists
                    [...subjects]
                      .sort((a, b) => {
                        // Put Maths (8E) at the top
                        if (a.name === 'Maths (8E)') return -1;
                        if (b.name === 'Maths (8E)') return 1;
                        // Otherwise, maintain alphabetical order
                        return a.name.localeCompare(b.name);
                      })
                      .map((subject: any) => (
                        <SelectItem
                          key={subject.id}
                          value={subject.name}
                          className={`hover:bg-blue-50 transition-colors ${subject.name === 'Maths (8E)' ? 'bg-blue-50 font-semibold' : ''}`}
                        >
                          {subject.name}
                          {subject.class && (
                            <span className="text-xs text-gray-500 ml-1">
                              ({subject.class.name})
                            </span>
                          )}
                        </SelectItem>
                      ))
                  ) : (
                    <div className="p-2 text-sm text-gray-500">No subjects available</div>
                  )}
                  {subjects && subjects.length > 10 && (
                    <div className="py-1 px-2 text-xs text-gray-500 border-t mt-1">
                      Showing all {subjects.length} subjects
                    </div>
                  )}
                  <div className="sticky bottom-0 h-1 w-full bg-gradient-to-t from-white dark:from-gray-950 to-transparent"></div>
                </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Loading state */}
        {isLoading && (
          <div className="flex justify-center my-8">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-cyan-500"></div>
          </div>
        )}

        {!isLoading && (
          <>
            {/* Enhanced Table Display */}
            <Card className="mb-6 overflow-hidden border-0 shadow-xl bg-white/90 backdrop-blur-sm rounded-2xl">
                <div className="overflow-x-auto">
                  <table className="w-full text-sm text-left">
                    <thead className="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-800 dark:text-gray-400">
                      <tr>
                        <th scope="col" className="px-6 py-3">Range</th>
                        <th scope="col" className="px-6 py-3">Male Count</th>
                        <th scope="col" className="px-6 py-3">Female Count</th>
                        <th scope="col" className="px-6 py-3">Avg Mark</th>
                      </tr>
                    </thead>
                    <tbody>
                      {rangeData.map((data, index) => (
                        <tr
                          key={data.range}
                          className={`border-b dark:border-gray-700 hover:bg-blue-50 dark:hover:bg-gray-700 transition-colors ${
                            index % 2 === 0 ? 'bg-white dark:bg-gray-900' : 'bg-gray-50 dark:bg-gray-800'
                          }`}
                        >
                          <td className="px-6 py-4 font-medium" style={{ color: scoreRanges[index].color }}>
                            {data.range}
                          </td>
                          <td className="px-6 py-4">{data.maleCount}</td>
                          <td className="px-6 py-4">{data.femaleCount}</td>
                          <td className="px-6 py-4">{data.avgMark}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </Card>

            {/* Enhanced Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
              {/* Enhanced Bar Chart */}
              <Card className="border-0 shadow-xl bg-white/90 backdrop-blur-sm rounded-2xl overflow-hidden">
                  <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-100 p-6">
                    <div className="flex items-center gap-3">
                      <div className="h-10 w-10 rounded-full bg-blue-500/20 flex items-center justify-center">
                        <BarChart3 className="h-5 w-5 text-blue-600" />
                      </div>
                      <CardTitle className="text-xl text-blue-700">Gender Distribution by Score Range</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent className="p-6">
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        data={barChartData}
                        margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Bar dataKey="male" name="Male" fill="#2196F3" />
                        <Bar dataKey="female" name="Female" fill="#FF4081" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                  </CardContent>
                </Card>

                {/* Enhanced Pie Chart */}
                <Card className="border-0 shadow-xl bg-white/90 backdrop-blur-sm rounded-2xl overflow-hidden">
                  <CardHeader className="bg-gradient-to-r from-purple-50 to-pink-50 border-b border-purple-100 p-6">
                    <div className="flex items-center gap-3">
                      <div className="h-10 w-10 rounded-full bg-purple-500/20 flex items-center justify-center">
                        <PieChartIcon className="h-5 w-5 text-purple-600" />
                      </div>
                      <CardTitle className="text-xl text-purple-700">Overall Distribution Across Ranges</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent className="p-6">
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={pieChartData}
                          cx="50%"
                          cy="50%"
                          labelLine={true}
                          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                        >
                          {pieChartData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip formatter={(value) => [`${value} students`, 'Count']} />
                        <Legend />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                  </CardContent>
                </Card>
              </div>

            {/* Enhanced Summary Stats */}
            <Card className="border-0 shadow-xl bg-white/90 backdrop-blur-sm rounded-2xl overflow-hidden">
                <CardHeader className="bg-gradient-to-r from-emerald-50 to-teal-50 border-b border-emerald-100 p-6">
                  <div className="flex items-center gap-3">
                    <div className="h-10 w-10 rounded-full bg-emerald-500/20 flex items-center justify-center">
                      <TrendingUp className="h-5 w-5 text-emerald-600" />
                    </div>
                    <CardTitle className="text-xl text-emerald-700">Summary Statistics</CardTitle>
                  </div>
                </CardHeader>
                <CardContent className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <div className="bg-gradient-to-br from-blue-50 to-indigo-50 p-6 rounded-xl border border-blue-100 hover:shadow-lg transition-all duration-300">
                    <div className="flex items-center justify-between mb-2">
                      <p className="text-sm font-semibold text-blue-700">Total Students</p>
                      <Users className="h-5 w-5 text-blue-600" />
                    </div>
                    <p className="text-3xl font-bold text-blue-600">
                      {marks.length}
                    </p>
                  </div>
                  <div className="bg-gradient-to-br from-green-50 to-emerald-50 p-6 rounded-xl border border-green-100 hover:shadow-lg transition-all duration-300">
                    <div className="flex items-center justify-between mb-2">
                      <p className="text-sm font-semibold text-green-700">Average Mark</p>
                      <TrendingUp className="h-5 w-5 text-green-600" />
                    </div>
                    <p className="text-3xl font-bold text-green-600">
                      {marks.length > 0
                        ? (marks.reduce((sum: number, mark: Mark) => sum + mark.mark, 0) / marks.length).toFixed(2)
                        : 0}
                    </p>
                  </div>
                  <div className="bg-gradient-to-br from-purple-50 to-violet-50 p-6 rounded-xl border border-purple-100 hover:shadow-lg transition-all duration-300">
                    <div className="flex items-center justify-between mb-2">
                      <p className="text-sm font-semibold text-purple-700">Highest Mark</p>
                      <Award className="h-5 w-5 text-purple-600" />
                    </div>
                    <p className="text-3xl font-bold text-purple-600">
                      {marks.length > 0
                        ? Math.max(...marks.map((mark: Mark) => mark.mark))
                        : 0}
                    </p>
                  </div>
                  <div className="bg-gradient-to-br from-red-50 to-rose-50 p-6 rounded-xl border border-red-100 hover:shadow-lg transition-all duration-300">
                    <div className="flex items-center justify-between mb-2">
                      <p className="text-sm font-semibold text-red-700">Lowest Mark</p>
                      <Target className="h-5 w-5 text-red-600" />
                    </div>
                    <p className="text-3xl font-bold text-red-600">
                      {marks.length > 0
                        ? Math.min(...marks.map((mark: Mark) => mark.mark))
                        : 0}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </>
        )}
      </div>
      )}
    </DashboardLayout>
  )
}

// Export a wrapper component that provides the QueryClient
export default function VisualizerPage() {
  return (
    <QueryClientProvider client={queryClient}>
      <Visualizer />
    </QueryClientProvider>
  )
}

import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import {
  combineTeachers,
  filterTeachers,
  type TeacherFromTable,
  type UserWithTeacherRole
} from '@/app/utils/teacher-utils'

export async function GET(request: Request) {
  try {
    console.log('Fetching available teachers for HR assignment...')

    const { searchParams } = new URL(request.url)
    const searchTerm = searchParams.get('search') || ''

    // Get teachers from Teacher table
    const teachersFromTable = await prisma.teacher.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        subject: true,
        mobile: true,
        fatherName: true,
        gender: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: {
        name: 'asc',
      },
    }) as TeacherFromTable[]

    // Get users with teacher role
    const usersWithTeacherRole = await prisma.user.findMany({
      where: {
        role: 'TEACHER',
        status: 'active',
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        phone: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: {
        name: 'asc',
      },
    }) as UserWithTeacherRole[]

    console.log(`Found ${teachersFromTable.length} teachers from Teacher table`)
    console.log(`Found ${usersWithTeacherRole.length} users with teacher role`)

    // Combine and deduplicate teachers
    let unifiedTeachers = combineTeachers(teachersFromTable, usersWithTeacherRole)

    // Apply search filter if provided
    if (searchTerm) {
      unifiedTeachers = filterTeachers(unifiedTeachers, searchTerm)
    }

    console.log(`Returning ${unifiedTeachers.length} teachers available for HR assignment`)

    return NextResponse.json(unifiedTeachers)
  } catch (error) {
    console.error('Error fetching available teachers:', error)
    return NextResponse.json(
      { error: 'Failed to fetch available teachers' },
      { status: 500 }
    )
  }
}

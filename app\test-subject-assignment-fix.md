# Subject Assignment Fix

## Problem
The "Assign Subject" functionality was failing with:
```
Error in assignSubjects mutation: Error: Failed to create subject assignments
```

## Root Cause
The `/api/teachers/subject-assignments` endpoint was trying to use the `SubjectTeacher` model for all teachers, but this model has a foreign key constraint that only references the `Teacher` table. When trying to assign subjects to users with teacher role (from the `users` table), it failed because the foreign key constraint couldn't be satisfied.

## Database Schema Issue
The `SubjectTeacher` model:
```prisma
model SubjectTeacher {
  id        String   @id @default(cuid())
  subjectId String
  teacherId String
  teacher   Teacher  @relation(fields: [teacherId], references: [id]) // Only works with Teacher table
}
```

But we need to support users from the `User` table with teacher role.

## Solution Implemented

### 1. Updated Subject Assignment Logic
The API now uses different models based on teacher source:

**For teachers from `Teacher` table:**
- Uses `SubjectTeacher` model (traditional approach)
- Foreign key: `teacherId` → `Teacher.id`

**For users from `User` table:**
- Uses `TeacherAssignment` model with `isHRTeacher: false`
- Foreign key: `teacherId` → `User.id`
- Supports both subject assignments and HR teacher assignments

### 2. Updated GET Method
The API now fetches subject assignments from both sources:
- Checks `SubjectTeacher` table for teachers from `Teacher` table
- Checks `TeacherAssignment` table for users from `User` table
- Returns unified format for frontend consumption

### 3. Updated POST Method
The API now creates assignments in the appropriate table:
- Creates `SubjectTeacher` records for teachers from `Teacher` table
- Creates `TeacherAssignment` records for users from `User` table

## Implementation Details

### Assignment Creation Logic:
```javascript
if (teacher) {
  // Teacher from Teacher table - use SubjectTeacher model
  await prisma.subjectTeacher.create({
    data: { teacherId, subjectId }
  })
} else {
  // User from User table - use TeacherAssignment model
  await prisma.teacherAssignment.create({
    data: { teacherId, subjectId, isHRTeacher: false }
  })
}
```

### Assignment Retrieval Logic:
```javascript
if (teacher) {
  // Get from SubjectTeacher table
  assignments = await prisma.subjectTeacher.findMany({
    where: { teacherId }
  })
} else {
  // Get from TeacherAssignment table
  assignments = await prisma.teacherAssignment.findMany({
    where: { teacherId, isHRTeacher: false, subjectId: { not: null } }
  })
}
```

## Files Modified

- `app/api/teachers/subject-assignments/route.ts` - Main fix for both GET and POST methods

## Testing Steps

1. **Test with Teacher from Teacher table:**
   - Select a teacher without "(User)" indicator
   - Assign subjects to them
   - Should create records in `SubjectTeacher` table

2. **Test with User from User table:**
   - Select a teacher with "(User)" indicator
   - Assign subjects to them
   - Should create records in `TeacherAssignment` table with `isHRTeacher: false`

3. **Test subject display:**
   - Both teacher types should show their assigned subjects correctly
   - Subject assignments should persist and display properly

4. **Test subject removal:**
   - Removing subjects should work for both teacher types
   - Should clear assignments from the appropriate table

## Expected Behavior After Fix

1. ✅ "Assign Subject" should work for teachers from both sources
2. ✅ No more "Failed to create subject assignments" errors
3. ✅ Subject assignments should persist correctly in appropriate tables
4. ✅ Subject display should work for both teacher types
5. ✅ Subject removal should work for both assignment types

## Database Tables Used

**For Teacher table teachers:**
- `SubjectTeacher` table for subject assignments
- `HRTeacher` table for HR assignments

**For User table teachers:**
- `TeacherAssignment` table for both subject and HR assignments
- Uses `isHRTeacher` flag to distinguish between assignment types

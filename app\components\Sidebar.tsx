"use client"

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import { useAuth } from '../contexts/auth-context'
import {
  Home as HomeIcon,
  User as UserIcon,
  UserPlus as UserPlusIcon,
  ClipboardList,
  CalendarCheck,
  ListChecks,
  FileText,
  ShieldCheck,
  KeyRound,
  Settings,
  Menu as MenuIcon,
  <PERSON><PERSON>,
  BarChart,
  TrendingUp,
  Activity,
  DollarSign,
  Receipt,
  FileSpreadsheet,
  Printer,
  CreditCard,
  ChevronDown,
  Globe,
  Image,
  Bell,
  Link2,
  Newspaper,
  MessageSquare,
  MousePointer,
  Hash,
  Wallet
} from 'lucide-react'
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from './ui/accordion'
import { useAppSettings } from '../contexts/app-settings-context'

interface SidebarProps {
  isCollapsed: boolean
  toggleSidebar: () => void
  isMobileOpen: boolean
}

// Custom icons that aren't in Lucide
const BootstrapIcon: React.FC = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className="sidebar-icon"
  >
    <path d="M2 12a5 5 0 0 0 5 5 8 8 0 0 1 5 2 8 8 0 0 1 5-2 5 5 0 0 0 5-5V7H2Z"></path>
    <path d="M6 11V9h12v2"></path>
  </svg>
)

const AppsIcon: React.FC = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className="sidebar-icon"
  >
    <rect x="3" y="3" width="6" height="6" rx="1"></rect>
    <rect x="15" y="3" width="6" height="6" rx="1"></rect>
    <rect x="3" y="15" width="6" height="6" rx="1"></rect>
    <rect x="15" y="15" width="6" height="6" rx="1"></rect>
  </svg>
)

const Sidebar: React.FC<SidebarProps> = ({
  isCollapsed,
  toggleSidebar,
  isMobileOpen
}) => {
  const { user, hasSidebarAccess } = useAuth()
  const { settings } = useAppSettings()
  const [activeItem, setActiveItem] = useState('dashboard')
  const [expandedAccordions, setExpandedAccordions] = useState<string[]>([])

  // Get user initial and name from the authenticated user
  const userInitial = user?.name ? user.name.charAt(0).toUpperCase() : 'A'
  const userName = user?.name || 'User'

  const sidebarClass = `sidebar sidebar-bg ${isCollapsed ? 'collapsed' : ''} ${isMobileOpen ? 'mobile-open' : ''}`

  // Apply dynamic sidebar styles
  const sidebarStyle = {
    backgroundColor: settings.sidebarBgColor,
    color: settings.sidebarTextColor
  }

  const toggleAccordion = (value: string) => {
    if (isCollapsed) return

    setExpandedAccordions(prev =>
      prev.includes(value)
        ? prev.filter(item => item !== value)
        : [...prev, value]
    )
  }

  return (
    <aside className={sidebarClass} style={sidebarStyle}>
      <div className="py-4 px-4 flex items-center justify-between border-b border-white/10">
        <div className="flex items-center">
          <div className="w-8 h-8 rounded-md bg-white text-akademi-red flex items-center justify-center font-bold text-xl mr-3">
            {userInitial}
          </div>
          {!isCollapsed && <h1 className="text-xl font-bold sidebar-text">{userName}</h1>}
        </div>
        <button
          onClick={toggleSidebar}
          className="text-white p-1 rounded-md hover:bg-white/10"
        >
          <MenuIcon size={20} />
        </button>
      </div>

      <nav className="py-4">
        <Link href="/dashboard" className="block">
        <div
          className={`sidebar-item ${activeItem === 'dashboard' ? 'active' : ''}`}
          onClick={() => setActiveItem('dashboard')}
        >
          <HomeIcon size={20} className="sidebar-icon" />
          {!isCollapsed && <span className="sidebar-text">Dashboard</span>}
        </div>
        </Link>

        {/* Students Accordion */}
        <div className={`${isCollapsed ? 'collapsed-accordion' : ''}`}>
          {isCollapsed ? (
            <Link href="/students" className="block">
              <div
                className={`sidebar-item ${activeItem.startsWith('students') ? 'active' : ''}`}
                onClick={() => setActiveItem('students')}
              >
                <UserIcon size={20} className="sidebar-icon" />
              </div>
            </Link>
          ) : (
            <Accordion
              type="multiple"
              value={expandedAccordions}
              className="sidebar-accordion"
            >
              <AccordionItem value="students" className="border-none">
                <div
                  className={`sidebar-item accordion-trigger ${activeItem.startsWith('students') ? 'active' : ''}`}
                  onClick={() => toggleAccordion('students')}
                >
                  <div className="flex items-center">
                    <UserIcon size={20} className="sidebar-icon" />
                    <span className="sidebar-text">Students</span>
                  </div>
                  <ChevronDown
                    size={16}
                    className={`transition-transform duration-200 ${expandedAccordions.includes('students') ? 'rotate-180' : ''}`}
                  />
                </div>
                <AccordionContent className="accordion-content">
                  <Link href="/students" className="block">
                    <div
                      className={`sidebar-subitem ${activeItem === 'students' && !activeItem.includes('-') ? 'active' : ''}`}
                      onClick={() => setActiveItem('students')}
                    >
                      <UserIcon size={16} className="sidebar-subicon" />
                      <span className="sidebar-text">All Students</span>
                    </div>
                  </Link>
                  <Link href="/students/id-card" className="block">
                    <div
                      className={`sidebar-subitem ${activeItem === 'students-id-card' ? 'active' : ''}`}
                      onClick={() => setActiveItem('students-id-card')}
                    >
                      <CreditCard size={16} className="sidebar-subicon" />
                      <span className="sidebar-text">ID Card</span>
                    </div>
                  </Link>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          )}
        </div>

        <Link href="/teachers" className="block">
        <div
          className={`sidebar-item ${activeItem === 'teachers' ? 'active' : ''}`}
          onClick={() => setActiveItem('teachers')}
        >
          <UserPlusIcon size={20} className="sidebar-icon" />
          {!isCollapsed && <span className="sidebar-text">Teachers</span>}
        </div>
        </Link>

        <Link href="/classes" className="block">
        <div
          className={`sidebar-item ${activeItem === 'class' ? 'active' : ''}`}
          onClick={() => setActiveItem('class')}
        >
          <ClipboardList size={20} className="sidebar-icon" />
          {!isCollapsed && <span className="sidebar-text">Class</span>}
        </div>
        </Link>

        <Link href="/attendance" className="block">
        <div
          className={`sidebar-item ${activeItem === 'attendance' ? 'active' : ''}`}
          onClick={() => setActiveItem('attendance')}
        >
          <CalendarCheck size={20} className="sidebar-icon" />
          {!isCollapsed && <span className="sidebar-text">Attendance</span>}
        </div>
        </Link>

        <Link href="/marklist" className="block">
        <div
          className={`sidebar-item ${activeItem === 'marklist' ? 'active' : ''}`}
          onClick={() => setActiveItem('marklist')}
        >
          <ListChecks size={20} className="sidebar-icon" />
          {!isCollapsed && <span className="sidebar-text">Mark List</span>}
        </div>
        </Link>

        <Link href="/reportcard" className="block">
        <div
          className={`sidebar-item ${activeItem === 'reportcard' ? 'active' : ''}`}
          onClick={() => setActiveItem('reportcard')}
        >
          <FileText size={20} className="sidebar-icon" />
          {!isCollapsed && <span className="sidebar-text">Report Card</span>}
        </div>
        </Link>



        <Link href="/roles" className="block">
        <div
          className={`sidebar-item ${activeItem === 'role' ? 'active' : ''}`}
          onClick={() => setActiveItem('role')}
        >
          <ShieldCheck size={20} className="sidebar-icon" />
          {!isCollapsed && <span className="sidebar-text">Role</span>}
        </div>
        </Link>

        <Link href="/visualizer" className="block">
        <div
          className={`sidebar-item ${activeItem === 'visualizer' ? 'active' : ''}`}
          onClick={() => setActiveItem('visualizer')}
        >
          <BarChart size={20} className="sidebar-icon" />
          {!isCollapsed && <span className="sidebar-text">Visualizer</span>}
        </div>
        </Link>

        {hasSidebarAccess('analytics') && (
          <Link href="/analytics" className="block">
          <div
            className={`sidebar-item ${activeItem === 'analytics' ? 'active' : ''}`}
            onClick={() => setActiveItem('analytics')}
          >
            <TrendingUp size={20} className="sidebar-icon" />
            {!isCollapsed && <span className="sidebar-text">Analytics</span>}
          </div>
          </Link>
        )}

        {hasSidebarAccess('progress-manager') && (
          <Link href="/progress-manager" className="block">
          <div
            className={`sidebar-item ${activeItem === 'progress-manager' ? 'active' : ''}`}
            onClick={() => setActiveItem('progress-manager')}
          >
            <Activity size={20} className="sidebar-icon" />
            {!isCollapsed && <span className="sidebar-text">Progress Manager</span>}
          </div>
          </Link>
        )}

        {/* Accounting Accordion */}
        <div className={`${isCollapsed ? 'collapsed-accordion' : ''}`}>
          {isCollapsed ? (
            <Link href="/accounting" className="block">
              <div
                className={`sidebar-item ${activeItem.startsWith('accounting') ? 'active' : ''}`}
                onClick={() => setActiveItem('accounting')}
              >
                <DollarSign size={20} className="sidebar-icon" />
              </div>
            </Link>
          ) : (
            <Accordion
              type="multiple"
              value={expandedAccordions}
              className="sidebar-accordion"
            >
              <AccordionItem value="accounting" className="border-none">
                <div
                  className={`sidebar-item accordion-trigger ${activeItem.startsWith('accounting') ? 'active' : ''}`}
                  onClick={() => toggleAccordion('accounting')}
                >
                  <div className="flex items-center">
                    <DollarSign size={20} className="sidebar-icon" />
                    <span className="sidebar-text">Accounting</span>
                  </div>
                  <ChevronDown
                    size={16}
                    className={`transition-transform duration-200 ${expandedAccordions.includes('accounting') ? 'rotate-180' : ''}`}
                  />
                </div>
                <AccordionContent className="accordion-content">
                  <Link href="/accounting/fee-types" className="block">
                    <div
                      className={`sidebar-subitem ${activeItem === 'accounting-fee-types' ? 'active' : ''}`}
                      onClick={() => setActiveItem('accounting-fee-types')}
                    >
                      <CreditCard size={16} className="sidebar-subicon" />
                      <span className="sidebar-text">Fee Types</span>
                    </div>
                  </Link>
                  <Link href="/accounting/account-codes" className="block">
                    <div
                      className={`sidebar-subitem ${activeItem === 'accounting-account-codes' ? 'active' : ''}`}
                      onClick={() => setActiveItem('accounting-account-codes')}
                    >
                      <Hash size={16} className="sidebar-subicon" />
                      <span className="sidebar-text">Account Codes</span>
                    </div>
                  </Link>
                  <Link href="/accounting/fee-invoice" className="block">
                    <div
                      className={`sidebar-subitem ${activeItem === 'accounting-fee-invoice' ? 'active' : ''}`}
                      onClick={() => setActiveItem('accounting-fee-invoice')}
                    >
                      <Receipt size={16} className="sidebar-subicon" />
                      <span className="sidebar-text">Fee Invoice</span>
                    </div>
                  </Link>
                  <Link href="/accounting/invoices-report" className="block">
                    <div
                      className={`sidebar-subitem ${activeItem === 'accounting-invoices-report' ? 'active' : ''}`}
                      onClick={() => setActiveItem('accounting-invoices-report')}
                    >
                      <FileSpreadsheet size={16} className="sidebar-subicon" />
                      <span className="sidebar-text">Invoices Report</span>
                    </div>
                  </Link>
                  <Link href="/accounting/bank-payment-voucher" className="block">
                    <div
                      className={`sidebar-subitem ${activeItem === 'accounting-bank-payment-voucher' ? 'active' : ''}`}
                      onClick={() => setActiveItem('accounting-bank-payment-voucher')}
                    >
                      <CreditCard size={16} className="sidebar-subicon" />
                      <span className="sidebar-text">Bank Payment Voucher</span>
                    </div>
                  </Link>
                  <Link href="/accounting/journal-voucher" className="block">
                    <div
                      className={`sidebar-subitem ${activeItem === 'accounting-journal-voucher' ? 'active' : ''}`}
                      onClick={() => setActiveItem('accounting-journal-voucher')}
                    >
                      <FileText size={16} className="sidebar-subicon" />
                      <span className="sidebar-text">Journal Voucher</span>
                    </div>
                  </Link>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          )}
        </div>

        {/* Website Content Management Accordion */}
        <div className={`${isCollapsed ? 'collapsed-accordion' : ''}`}>
          {isCollapsed ? (
            <Link href="/website-management" className="block">
              <div
                className={`sidebar-item ${activeItem.startsWith('website') ? 'active' : ''}`}
                onClick={() => setActiveItem('website')}
              >
                <Globe size={20} className="sidebar-icon" />
              </div>
            </Link>
          ) : (
            <Accordion
              type="multiple"
              value={expandedAccordions}
              className="sidebar-accordion"
            >
              <AccordionItem value="website" className="border-none">
                <div
                  className={`sidebar-item accordion-trigger ${activeItem.startsWith('website') ? 'active' : ''}`}
                  onClick={() => toggleAccordion('website')}
                >
                  <div className="flex items-center">
                    <Globe size={20} className="sidebar-icon" />
                    <span className="sidebar-text">Website</span>
                  </div>
                  <ChevronDown
                    size={16}
                    className={`transition-transform duration-200 ${expandedAccordions.includes('website') ? 'rotate-180' : ''}`}
                  />
                </div>
                <AccordionContent className="accordion-content">
                  <Link href="/website-management/hero-slides" className="block">
                    <div
                      className={`sidebar-subitem ${activeItem === 'website-hero-slides' ? 'active' : ''}`}
                      onClick={() => setActiveItem('website-hero-slides')}
                    >
                      <Image size={16} className="sidebar-subicon" />
                      <span className="sidebar-text">Hero Slides</span>
                    </div>
                  </Link>
                  <Link href="/website-management/announcements" className="block">
                    <div
                      className={`sidebar-subitem ${activeItem === 'website-announcements' ? 'active' : ''}`}
                      onClick={() => setActiveItem('website-announcements')}
                    >
                      <Bell size={16} className="sidebar-subicon" />
                      <span className="sidebar-text">Announcements</span>
                    </div>
                  </Link>
                  <Link href="/website-management/quick-links" className="block">
                    <div
                      className={`sidebar-subitem ${activeItem === 'website-quick-links' ? 'active' : ''}`}
                      onClick={() => setActiveItem('website-quick-links')}
                    >
                      <Link2 size={16} className="sidebar-subicon" />
                      <span className="sidebar-text">Quick Links</span>
                    </div>
                  </Link>
                  <Link href="/website-management/featured-content" className="block">
                    <div
                      className={`sidebar-subitem ${activeItem === 'website-featured-content' ? 'active' : ''}`}
                      onClick={() => setActiveItem('website-featured-content')}
                    >
                      <Image size={16} className="sidebar-subicon" />
                      <span className="sidebar-text">Featured Content</span>
                    </div>
                  </Link>
                  <Link href="/website-management/news-events" className="block">
                    <div
                      className={`sidebar-subitem ${activeItem === 'website-news-events' ? 'active' : ''}`}
                      onClick={() => setActiveItem('website-news-events')}
                    >
                      <Newspaper size={16} className="sidebar-subicon" />
                      <span className="sidebar-text">News & Events</span>
                    </div>
                  </Link>
                  <Link href="/website-management/academic-calendar" className="block">
                    <div
                      className={`sidebar-subitem ${activeItem === 'website-academic-calendar' ? 'active' : ''}`}
                      onClick={() => setActiveItem('website-academic-calendar')}
                    >
                      <CalendarCheck size={16} className="sidebar-subicon" />
                      <span className="sidebar-text">Academic Calendar</span>
                    </div>
                  </Link>
                  <Link href="/website-management/testimonials" className="block">
                    <div
                      className={`sidebar-subitem ${activeItem === 'website-testimonials' ? 'active' : ''}`}
                      onClick={() => setActiveItem('website-testimonials')}
                    >
                      <MessageSquare size={16} className="sidebar-subicon" />
                      <span className="sidebar-text">Testimonials</span>
                    </div>
                  </Link>
                  <Link href="/website-management/tuition-fees" className="block">
                    <div
                      className={`sidebar-subitem ${activeItem === 'website-tuition-fees' ? 'active' : ''}`}
                      onClick={() => setActiveItem('website-tuition-fees')}
                    >
                      <Wallet size={16} className="sidebar-subicon" />
                      <span className="sidebar-text">Tuition & Fees</span>
                    </div>
                  </Link>
                  <Link href="/website-management/call-to-action" className="block">
                    <div
                      className={`sidebar-subitem ${activeItem === 'website-call-to-action' ? 'active' : ''}`}
                      onClick={() => setActiveItem('website-call-to-action')}
                    >
                      <MousePointer size={16} className="sidebar-subicon" />
                      <span className="sidebar-text">Call to Action</span>
                    </div>
                  </Link>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          )}
        </div>

        {/* User Settings Accordion */}
        <div className={`${isCollapsed ? 'collapsed-accordion' : ''}`}>
          {isCollapsed ? (
            <Link href="/profile" className="block">
              <div
                className={`sidebar-item ${activeItem === 'profile' || activeItem === 'account' ? 'active' : ''}`}
                onClick={() => setActiveItem('profile')}
              >
                <Settings size={20} className="sidebar-icon" />
              </div>
            </Link>
          ) : (
            <Accordion
              type="multiple"
              value={expandedAccordions}
              className="sidebar-accordion"
            >
              <AccordionItem value="user-settings" className="border-none">
                <div
                  className={`sidebar-item accordion-trigger ${activeItem === 'profile' || activeItem === 'account' ? 'active' : ''}`}
                  onClick={() => toggleAccordion('user-settings')}
                >
                  <div className="flex items-center">
                    <Settings size={20} className="sidebar-icon" />
                    <span className="sidebar-text">User Settings</span>
                  </div>
                  <ChevronDown
                    size={16}
                    className={`transition-transform duration-200 ${expandedAccordions.includes('user-settings') ? 'rotate-180' : ''}`}
                  />
                </div>
                <AccordionContent className="accordion-content">
                  <Link href="/profile" className="block">
                    <div
                      className={`sidebar-subitem ${activeItem === 'profile' ? 'active' : ''}`}
                      onClick={() => setActiveItem('profile')}
                    >
                      <UserIcon size={16} className="sidebar-subicon" />
                      <span className="sidebar-text">Profile Settings</span>
                    </div>
                  </Link>
                  <Link href="/account" className="block">
                    <div
                      className={`sidebar-subitem ${activeItem === 'account' ? 'active' : ''}`}
                      onClick={() => setActiveItem('account')}
                    >
                      <KeyRound size={16} className="sidebar-subicon" />
                      <span className="sidebar-text">Account Settings</span>
                    </div>
                  </Link>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          )}
        </div>

        <Link href="/settings" className="block">
        <div
          className={`sidebar-item ${activeItem === 'setting' ? 'active' : ''}`}
          onClick={() => setActiveItem('setting')}
        >
          <Settings size={20} className="sidebar-icon" />
          {!isCollapsed && <span className="sidebar-text">System Settings</span>}
        </div>
        </Link>

        <Link href="/theme-demo" className="block">
        <div
          className={`sidebar-item ${activeItem === 'theme-demo' ? 'active' : ''}`}
          onClick={() => setActiveItem('theme-demo')}
        >
          <Palette size={20} className="sidebar-icon" />
          {!isCollapsed && <span className="sidebar-text">Theme Demo</span>}
        </div>
        </Link>
      </nav>
    </aside>
  )
}

export default Sidebar;
import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const className = searchParams.get('className')

    if (!className) {
      return NextResponse.json(
        { error: 'Class name is required' },
        { status: 400 }
      )
    }

    console.log(`Fetching available subjects for class: ${className}`)

    // First, find the class to get its ID
    const classData = await prisma.class.findFirst({
      where: { name: className },
      select: { id: true, name: true }
    })

    if (!classData) {
      return NextResponse.json(
        { error: `Class '${className}' not found` },
        { status: 404 }
      )
    }

    // Get subjects assigned to this class
    const subjects = await prisma.subject.findMany({
      where: {
        classId: classData.id,
      },
      select: {
        id: true,
        name: true,
        classId: true,
      },
      orderBy: {
        name: 'asc',
      },
    })

    console.log(`Found ${subjects.length} subjects for class ${className}`)

    return NextResponse.json(subjects)
  } catch (error) {
    console.error('Error fetching available subjects:', error)
    return NextResponse.json(
      { error: 'Failed to fetch available subjects' },
      { status: 500 }
    )
  }
}

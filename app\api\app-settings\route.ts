import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// GET - Fetch app settings
export async function GET() {
  try {
    // Try to get existing settings
    let settings = await prisma.appSettings.findFirst()
    
    // If no settings exist, create default ones
    if (!settings) {
      settings = await prisma.appSettings.create({
        data: {
          schoolName: 'School Management System',
          schoolLogo: null,
          semesterCount: 2,
          defaultAcademicYear: '2024-2025',
          sidebarBgColor: '#1f2937',
          sidebarTextColor: '#ffffff'
        }
      })
    }

    return NextResponse.json(settings)
  } catch (error) {
    console.error('Error fetching app settings:', error)
    return NextResponse.json(
      { error: 'Failed to fetch app settings' },
      { status: 500 }
    )
  }
}

// POST - Create new app settings
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate required fields
    if (!body.schoolName) {
      return NextResponse.json(
        { error: 'School name is required' },
        { status: 400 }
      )
    }

    // Check if settings already exist
    const existingSettings = await prisma.appSettings.findFirst()
    if (existingSettings) {
      return NextResponse.json(
        { error: 'App settings already exist. Use PUT to update.' },
        { status: 400 }
      )
    }

    const settings = await prisma.appSettings.create({
      data: {
        schoolName: body.schoolName,
        schoolLogo: body.schoolLogo || null,
        semesterCount: body.semesterCount || 2,
        defaultAcademicYear: body.defaultAcademicYear || '2024-2025',
        sidebarBgColor: body.sidebarBgColor || '#1f2937',
        sidebarTextColor: body.sidebarTextColor || '#ffffff'
      }
    })

    return NextResponse.json(settings)
  } catch (error) {
    console.error('Error creating app settings:', error)
    return NextResponse.json(
      { error: 'Failed to create app settings' },
      { status: 500 }
    )
  }
}

// PUT - Update app settings
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate required fields
    if (!body.schoolName) {
      return NextResponse.json(
        { error: 'School name is required' },
        { status: 400 }
      )
    }

    // Get the first (and should be only) settings record
    const existingSettings = await prisma.appSettings.findFirst()
    
    if (!existingSettings) {
      return NextResponse.json(
        { error: 'No app settings found. Use POST to create.' },
        { status: 404 }
      )
    }

    const settings = await prisma.appSettings.update({
      where: { id: existingSettings.id },
      data: {
        schoolName: body.schoolName,
        schoolLogo: body.schoolLogo,
        semesterCount: body.semesterCount || 2,
        defaultAcademicYear: body.defaultAcademicYear || '2024-2025',
        sidebarBgColor: body.sidebarBgColor || '#1f2937',
        sidebarTextColor: body.sidebarTextColor || '#ffffff'
      }
    })

    return NextResponse.json(settings)
  } catch (error) {
    console.error('Error updating app settings:', error)
    return NextResponse.json(
      { error: 'Failed to update app settings' },
      { status: 500 }
    )
  }
}

// DELETE - Delete app settings (optional, for admin purposes)
export async function DELETE() {
  try {
    const existingSettings = await prisma.appSettings.findFirst()
    
    if (!existingSettings) {
      return NextResponse.json(
        { error: 'No app settings found' },
        { status: 404 }
      )
    }

    await prisma.appSettings.delete({
      where: { id: existingSettings.id }
    })

    return NextResponse.json({ message: 'App settings deleted successfully' })
  } catch (error) {
    console.error('Error deleting app settings:', error)
    return NextResponse.json(
      { error: 'Failed to delete app settings' },
      { status: 500 }
    )
  }
}

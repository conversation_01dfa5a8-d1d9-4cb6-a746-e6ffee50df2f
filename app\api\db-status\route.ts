import { NextResponse } from 'next/server';
import { prisma, isUsingMockPrisma, checkDatabaseConnection } from '@/lib/prisma';

export async function GET() {
  try {
    // Check if we're using the mock client
    const usingMock = isUsingMockPrisma();

    // Check database connection
    const connectionStatus = await checkDatabaseConnection();

    // Get database URL (masked for security)
    let dbUrl = 'Not set';
    try {
      if (process.env.DATABASE_URL) {
        const urlParts = process.env.DATABASE_URL.split('://');
        if (urlParts.length > 1) {
          const protocol = urlParts[0];
          const restParts = urlParts[1].split('@');
          if (restParts.length > 1) {
            dbUrl = `${protocol}://****@${restParts[1]}`;
          } else {
            dbUrl = `${protocol}://****`;
          }
        }
      }
    } catch (urlError) {
      console.error('Error masking database URL:', urlError);
      dbUrl = 'Error parsing URL';
    }

    // Get counts safely
    const getCounts = async () => {
      try {
        // Check if prisma is properly initialized
        if (!prisma || typeof prisma !== 'object') {
          return { error: 'Prisma client is not properly initialized' };
        }

        // Helper function to safely get count
        const safeCount = async (model: string, countFn: () => Promise<number>) => {
          try {
            if (typeof countFn !== 'function') {
              return `Error: ${model} count method not available`;
            }
            return await countFn();
          } catch (e) {
            return `Error: ${e instanceof Error ? e.message : 'Unknown error'}`;
          }
        };

        return {
          users: await safeCount('user', () =>
            prisma.user && typeof prisma.user.count === 'function' ?
            prisma.user.count() : Promise.reject('Count method not available')),

          classes: await safeCount('class', () =>
            prisma.class && typeof prisma.class.count === 'function' ?
            prisma.class.count() : Promise.reject('Count method not available')),

          students: await safeCount('student', () =>
            prisma.student && typeof prisma.student.count === 'function' ?
            prisma.student.count() : Promise.reject('Count method not available')),

          teachers: await safeCount('teacher', () =>
            prisma.teacher && typeof prisma.teacher.count === 'function' ?
            prisma.teacher.count() : Promise.reject('Count method not available')),

          subjects: await safeCount('subject', () =>
            prisma.subject && typeof prisma.subject.count === 'function' ?
            prisma.subject.count() : Promise.reject('Count method not available')),

          // Calculate performance based on marks if available
          performance: await (async () => {
            try {
              if (prisma.mark && typeof prisma.mark.findMany === 'function') {
                // Get all marks
                const marks = await prisma.mark.findMany();

                // Calculate average
                if (marks.length > 0) {
                  const sum = marks.reduce((acc, curr) => acc + (curr.marks || 0), 0);
                  const avg = sum / marks.length;
                  return `${Math.round(avg)}%`;
                }
              }
              return 'N/A';
            } catch (e) {
              console.error('Error calculating performance:', e);
              return 'N/A';
            }
          })(),

          // Website content models
          heroSlides: await safeCount('heroSlide', () =>
            prisma.heroSlide && typeof prisma.heroSlide.count === 'function' ?
            prisma.heroSlide.count() : Promise.reject('Count method not available')),

          announcements: await safeCount('announcement', () =>
            prisma.announcement && typeof prisma.announcement.count === 'function' ?
            prisma.announcement.count() : Promise.reject('Count method not available')),

          testimonials: await safeCount('testimonial', () =>
            prisma.testimonial && typeof prisma.testimonial.count === 'function' ?
            prisma.testimonial.count() : Promise.reject('Count method not available')),

          callToActions: await safeCount('callToAction', () =>
            prisma.callToAction && typeof prisma.callToAction.count === 'function' ?
            prisma.callToAction.count() : Promise.reject('Count method not available')),
        };
      } catch (countError) {
        console.error('Error getting counts:', countError);
        return {
          error: countError instanceof Error ? countError.message : 'Error getting counts'
        };
      }
    };

    // Get counts
    const counts = await getCounts();

    return NextResponse.json({
      status: connectionStatus.connected ? 'connected' : 'disconnected',
      usingMockClient: usingMock,
      databaseUrl: dbUrl,
      error: connectionStatus.error,
      environment: process.env.NODE_ENV || 'development',
      counts,
      message: usingMock
        ? 'Using mock Prisma client. Database connection failed or not configured.'
        : 'Using real Prisma client with database connection.',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error checking database status:', error);

    // Provide a safe response even if everything fails
    return NextResponse.json({
      status: 'error',
      message: 'Failed to check database status',
      error: error instanceof Error ? error.message : 'Unknown error',
      suggestion: 'Please check your database connection and ensure the MySQL server is running.',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

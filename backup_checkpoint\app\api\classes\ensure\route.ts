import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function POST(request: Request) {
  try {
    const data = await request.json()
    const { className } = data
    
    if (!className) {
      return NextResponse.json(
        { error: 'Class name is required' },
        { status: 400 }
      )
    }
    
    // Check if the class already exists
    let classRecord = await prisma.class.findFirst({
      where: { name: className },
    })
    
    // If the class doesn't exist, create it
    if (!classRecord) {
      classRecord = await prisma.class.create({
        data: {
          name: className,
          totalStudents: 0,
          totalSubjects: 0,
        },
      })
      
      return NextResponse.json({
        message: `Class '${className}' created successfully`,
        classRecord,
      })
    }
    
    return NextResponse.json({
      message: `Class '${className}' already exists`,
      classRecord,
    })
  } catch (error) {
    console.error('Error ensuring class exists:', error)
    return NextResponse.json(
      { error: 'Failed to ensure class exists', details: error.message },
      { status: 500 }
    )
  }
}

import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// Mock data for payment receipts (used as fallback)
const mockPaymentReceipts = [
  {
    id: '1',
    studentName: "<PERSON>",
    className: "8E",
    month: "September 2023",
    bankName: "Commercial Bank of Ethiopia",
    receiptImageUrl: "/images/sample-receipt.jpg",
    submittedAt: new Date('2023-09-05'),
    status: 'verified',
    createdAt: new Date('2023-09-05T10:30:00Z'),
    updatedAt: new Date('2023-09-05T10:30:00Z')
  },
  {
    id: '2',
    studentName: "Fatima Ali",
    className: "7A",
    month: "October 2023",
    bankName: "Dashen Bank",
    receiptImageUrl: "/images/sample-receipt.jpg",
    submittedAt: new Date('2023-10-03'),
    status: 'pending',
    createdAt: new Date('2023-10-03T11:15:00Z'),
    updatedAt: new Date('2023-10-03T11:15:00Z')
  },
  {
    id: '3',
    studentName: "<PERSON>",
    className: "10B",
    month: "October 2023",
    bankName: "Awash Bank",
    receiptImageUrl: "/images/sample-receipt.jpg",
    submittedAt: new Date('2023-10-07'),
    status: 'rejected',
    createdAt: new Date('2023-10-07T14:20:00Z'),
    updatedAt: new Date('2023-10-07T14:20:00Z')
  }
];

// Helper function to check if a model exists in Prisma
function isModelAvailable(modelName: string): boolean {
  try {
    return Object.prototype.hasOwnProperty.call(prisma, modelName);
  } catch (error) {
    console.error(`Error checking if model ${modelName} exists:`, error);
    return false;
  }
}

// GET all payment receipts
export async function GET(request: Request) {
  try {
    // Check if PaymentReceipt model is available
    if (!isModelAvailable('paymentReceipt')) {
      console.warn('PaymentReceipt model is not available in Prisma client. Using mock data.');
      return NextResponse.json(mockPaymentReceipts);
    }

    try {
      // Fetch payment receipts
      const paymentReceipts = await prisma.paymentReceipt.findMany({
        orderBy: {
          submittedAt: 'desc',
        },
      })

      return NextResponse.json(paymentReceipts)
    } catch (dbError) {
      console.error('Database error fetching payment receipts:', dbError);

      // If there's a database error, fall back to mock data
      console.warn('Falling back to mock payment receipt data');
      return NextResponse.json(mockPaymentReceipts);
    }
  } catch (error) {
    console.error('Error fetching payment receipts:', error)

    // Return mock data as a fallback
    console.warn('Error in payment receipts API, returning mock data');
    return NextResponse.json(mockPaymentReceipts);
  }
}

// CREATE a new payment receipt
export async function POST(request: Request) {
  try {
    const data = await request.json()

    // Validate required fields
    if (!data.studentName || !data.className || !data.month || !data.bankName || !data.receiptImageUrl) {
      return NextResponse.json(
        { error: 'Student name, class, month, bank name, and receipt image URL are required' },
        { status: 400 }
      )
    }

    // Check if PaymentReceipt model is available
    if (!isModelAvailable('paymentReceipt')) {
      console.warn('PaymentReceipt model is not available in Prisma client. Using mock data.');

      // Return a mock success response
      return NextResponse.json({
        id: Date.now().toString(),
        studentName: data.studentName,
        className: data.className,
        month: data.month,
        bankName: data.bankName,
        receiptImageUrl: data.receiptImageUrl,
        submittedAt: new Date(),
        status: 'pending',
        createdAt: new Date(),
        updatedAt: new Date()
      }, { status: 201 })
    }

    // Create the payment receipt
    const paymentReceipt = await prisma.paymentReceipt.create({
      data: {
        studentName: data.studentName,
        className: data.className,
        month: data.month,
        bankName: data.bankName,
        receiptImageUrl: data.receiptImageUrl,
        status: 'pending',
      }
    })

    return NextResponse.json(paymentReceipt, { status: 201 })
  } catch (error) {
    console.error('Error creating payment receipt:', error)
    return NextResponse.json(
      { error: 'Failed to create payment receipt' },
      { status: 500 }
    )
  }
}

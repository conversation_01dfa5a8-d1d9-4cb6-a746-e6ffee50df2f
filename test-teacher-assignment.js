// Test script to verify teacher assignment API fix
const fetch = require('node-fetch');

async function testTeacherAssignmentAPI() {
  try {
    console.log('Testing teacher assignment API...');
    
    // First, get teachers
    console.log('1. Fetching teachers...');
    const teachersResponse = await fetch('http://localhost:3001/api/users/teachers');
    const teachers = await teachersResponse.json();
    console.log(`Found ${teachers.length} teachers`);
    
    if (teachers.length === 0) {
      console.log('No teachers found. Cannot test assignment.');
      return;
    }
    
    // Get subjects
    console.log('2. Fetching subjects...');
    const subjectsResponse = await fetch('http://localhost:3001/api/subjects');
    const subjects = await subjectsResponse.json();
    console.log(`Found ${subjects.length} subjects`);
    
    if (subjects.length === 0) {
      console.log('No subjects found. Cannot test assignment.');
      return;
    }
    
    // Test creating a teacher assignment
    const testTeacher = teachers[0];
    const testSubject = subjects[0];
    
    console.log(`3. Testing assignment of teacher "${testTeacher.name}" to subject "${testSubject.name}"`);
    
    const assignmentData = {
      teacherId: testTeacher.id,
      subjectId: testSubject.id,
      isHRTeacher: false
    };
    
    const assignmentResponse = await fetch('http://localhost:3001/api/teacher-assignments/user-assignments', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(assignmentData)
    });
    
    if (assignmentResponse.ok) {
      const result = await assignmentResponse.json();
      console.log('✅ SUCCESS: Teacher assignment created successfully!');
      console.log('Assignment ID:', result.assignment.id);
      
      // Clean up - delete the test assignment
      const deleteResponse = await fetch(`http://localhost:3001/api/teacher-assignments/user-assignments/${result.assignment.id}`, {
        method: 'DELETE'
      });
      
      if (deleteResponse.ok) {
        console.log('✅ Test assignment cleaned up successfully');
      }
    } else {
      const error = await assignmentResponse.json();
      console.log('❌ FAILED: Teacher assignment failed');
      console.log('Error:', error);
    }
    
  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

testTeacherAssignmentAPI();

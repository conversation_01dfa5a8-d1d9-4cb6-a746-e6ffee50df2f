import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET all teacher permissions for users with TEACHER role
export async function GET() {
  try {
    console.log('Fetching all teacher permissions (alternative implementation)')

    // Fetch all teacher permissions
    const permissions = await prisma.teacherPermission.findMany({
      orderBy: {
        createdAt: 'desc'
      }
    })

    console.log(`Found ${permissions.length} teacher permissions`)
    return NextResponse.json({ permissions })
  } catch (error) {
    console.error('Error fetching teacher permissions:', error)
    return NextResponse.json(
      { error: 'Failed to fetch teacher permissions' },
      { status: 500 }
    )
  }
}

// POST to create or update teacher permissions for a user with TEACHER role
export async function POST(request: Request) {
  try {
    console.log('Creating/updating teacher permissions (alternative implementation)')

    // Get the request body
    const data = await request.json()
    console.log('Received data:', JSON.stringify(data, null, 2))

    // Validate required fields
    if (!data.teacherId || !data.classId || !data.permissions) {
      return NextResponse.json(
        { error: 'Teacher ID, Class ID, and permissions are required' },
        { status: 400 }
      )
    }

    // Check if the user with TEACHER role exists
    const teacher = await prisma.user.findFirst({
      where: { 
        id: data.teacherId,
        role: 'TEACHER'
      }
    })

    if (!teacher) {
      return NextResponse.json(
        { error: 'Teacher not found or user is not a teacher' },
        { status: 404 }
      )
    }

    // Check if the class exists
    const classObj = await prisma.class.findUnique({
      where: { id: data.classId }
    })

    if (!classObj) {
      return NextResponse.json(
        { error: 'Class not found' },
        { status: 404 }
      )
    }

    // Use a transaction to ensure atomicity
    const result = await prisma.$transaction(async (tx) => {
      // Check if permissions already exist
      const existingPermissions = await tx.teacherPermission.findFirst({
        where: {
          teacherId: data.teacherId,
          classId: data.classId
        }
      })

      if (existingPermissions) {
        // Update existing permissions
        console.log(`Updating permissions with ID: ${existingPermissions.id}`)
        const updated = await tx.teacherPermission.update({
          where: {
            id: existingPermissions.id
          },
          data: {
            canViewAttendance: data.permissions.canViewAttendance,
            canTakeAttendance: data.permissions.canTakeAttendance,
            canAddMarks: data.permissions.canAddMarks,
            canEditMarks: data.permissions.canEditMarks
          }
        })
        return { 
          result: updated, 
          isNew: false 
        }
      } else {
        // Create new permissions
        console.log('Creating new permissions')
        const created = await tx.teacherPermission.create({
          data: {
            teacherId: data.teacherId,
            classId: data.classId,
            canViewAttendance: data.permissions.canViewAttendance,
            canTakeAttendance: data.permissions.canTakeAttendance,
            canAddMarks: data.permissions.canAddMarks,
            canEditMarks: data.permissions.canEditMarks
          }
        })
        return { 
          result: created, 
          isNew: true 
        }
      }
    })

    console.log(`${result.isNew ? 'Created' : 'Updated'} permissions for teacher ${teacher.name} in class ${classObj.name}`)
    
    return NextResponse.json({
      message: result.isNew ? 'Permissions created successfully' : 'Permissions updated successfully',
      permissions: result.result
    })
  } catch (error) {
    console.error('Error creating/updating teacher permissions:', error)
    // Log more detailed error information
    if (error instanceof Error) {
      console.error('Error message:', error.message)
      console.error('Error stack:', error.stack)
    }
    return NextResponse.json(
      { error: 'Failed to create/update teacher permissions', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    )
  }
}

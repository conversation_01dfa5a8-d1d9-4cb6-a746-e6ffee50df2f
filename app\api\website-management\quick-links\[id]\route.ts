import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET a specific quick link
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id
    
    const quickLink = await prisma.quickLink.findUnique({
      where: { id }
    })
    
    if (!quickLink) {
      return NextResponse.json({
        error: 'Quick link not found'
      }, { status: 404 })
    }
    
    return NextResponse.json(quickLink)
  } catch (error) {
    console.error('Error fetching quick link:', error)
    return NextResponse.json({
      error: error.message || 'Failed to fetch quick link'
    }, { status: 500 })
  }
}

// PUT (update) a quick link
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id
    const data = await request.json()
    
    // Check if the quick link exists
    const existingQuickLink = await prisma.quickLink.findUnique({
      where: { id }
    })
    
    if (!existingQuickLink) {
      return NextResponse.json({
        error: 'Quick link not found'
      }, { status: 404 })
    }
    
    // Update the quick link
    const updatedQuickLink = await prisma.quickLink.update({
      where: { id },
      data: {
        title: data.title !== undefined ? data.title : existingQuickLink.title,
        description: data.description !== undefined ? data.description : existingQuickLink.description,
        icon: data.icon !== undefined ? data.icon : existingQuickLink.icon,
        url: data.url !== undefined ? data.url : existingQuickLink.url,
        color: data.color !== undefined ? data.color : existingQuickLink.color,
        borderColor: data.borderColor !== undefined ? data.borderColor : existingQuickLink.borderColor,
        hoverColor: data.hoverColor !== undefined ? data.hoverColor : existingQuickLink.hoverColor,
        isActive: data.isActive !== undefined ? data.isActive : existingQuickLink.isActive,
        order: data.order !== undefined ? data.order : existingQuickLink.order
      }
    })
    
    return NextResponse.json(updatedQuickLink)
  } catch (error) {
    console.error('Error updating quick link:', error)
    return NextResponse.json({
      error: error.message || 'Failed to update quick link'
    }, { status: 500 })
  }
}

// DELETE a quick link
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id
    
    // Check if the quick link exists
    const existingQuickLink = await prisma.quickLink.findUnique({
      where: { id }
    })
    
    if (!existingQuickLink) {
      return NextResponse.json({
        error: 'Quick link not found'
      }, { status: 404 })
    }
    
    // Delete the quick link
    await prisma.quickLink.delete({
      where: { id }
    })
    
    // Reorder remaining quick links to ensure no gaps
    const remainingQuickLinks = await prisma.quickLink.findMany({
      orderBy: {
        order: 'asc'
      }
    })
    
    // Update order for all remaining quick links
    for (let i = 0; i < remainingQuickLinks.length; i++) {
      await prisma.quickLink.update({
        where: { id: remainingQuickLinks[i].id },
        data: { order: i }
      })
    }
    
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting quick link:', error)
    return NextResponse.json({
      error: error.message || 'Failed to delete quick link'
    }, { status: 500 })
  }
}

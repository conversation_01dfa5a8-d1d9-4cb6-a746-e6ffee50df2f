import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET a specific payment
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id
    
    const payment = await prisma.payment.findUnique({
      where: { id },
      include: {
        student: {
          select: {
            id: true,
            sid: true,
            name: true,
            className: true,
          },
        },
        feeType: true,
      },
    })
    
    if (!payment) {
      return NextResponse.json(
        { error: 'Payment not found' },
        { status: 404 }
      )
    }
    
    return NextResponse.json(payment)
  } catch (error) {
    console.error('Error fetching payment:', error)
    return NextResponse.json(
      { error: 'Failed to fetch payment' },
      { status: 500 }
    )
  }
}

// UPDATE a payment
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id
    const data = await request.json()
    
    // Check if payment exists
    const existingPayment = await prisma.payment.findUnique({
      where: { id },
    })
    
    if (!existingPayment) {
      return NextResponse.json(
        { error: 'Payment not found' },
        { status: 404 }
      )
    }
    
    // Validate required fields
    if (!data.amount || !data.paymentMethod || !data.status) {
      return NextResponse.json(
        { error: 'Amount, payment method, and status are required' },
        { status: 400 }
      )
    }
    
    // Update the payment
    const updatedPayment = await prisma.payment.update({
      where: { id },
      data: {
        amount: parseFloat(data.amount),
        paymentDate: data.paymentDate ? new Date(data.paymentDate) : undefined,
        paymentMethod: data.paymentMethod,
        transferId: data.transferId || null,
        status: data.status,
        notes: data.notes || null,
      },
      include: {
        student: {
          select: {
            id: true,
            sid: true,
            name: true,
            className: true,
          },
        },
        feeType: true,
      },
    })
    
    return NextResponse.json(updatedPayment)
  } catch (error) {
    console.error('Error updating payment:', error)
    return NextResponse.json(
      { error: 'Failed to update payment' },
      { status: 500 }
    )
  }
}

// DELETE a payment
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id
    
    // Check if payment exists
    const existingPayment = await prisma.payment.findUnique({
      where: { id },
    })
    
    if (!existingPayment) {
      return NextResponse.json(
        { error: 'Payment not found' },
        { status: 404 }
      )
    }
    
    // Delete the payment
    await prisma.payment.delete({
      where: { id },
    })
    
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting payment:', error)
    return NextResponse.json(
      { error: 'Failed to delete payment' },
      { status: 500 }
    )
  }
}

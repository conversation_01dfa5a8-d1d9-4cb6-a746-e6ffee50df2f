const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function main() {
  try {
    console.log('=== Testing Teacher Permissions Display ===\n')

    // 1. Get all teacher permissions with details
    console.log('1. Fetching all teacher permissions...')
    const permissions = await prisma.teacherPermission.findMany({
      select: {
        id: true,
        teacherId: true,
        classId: true,
        canViewAttendance: true,
        canTakeAttendance: true,
        canAddMarks: true,
        canEditMarks: true,
      },
      orderBy: [
        { teacherId: 'asc' },
        { classId: 'asc' }
      ],
    })

    console.log(`Found ${permissions.length} teacher permissions`)

    // 2. Get teacher and class details
    const teacherIds = [...new Set(permissions.map(p => p.teacherId))]
    const classIds = [...new Set(permissions.map(p => p.classId))]

    const [teachers, classes] = await Promise.all([
      prisma.teacher.findMany({
        where: { id: { in: teacherIds } },
        select: { id: true, name: true, email: true, subject: true }
      }),
      prisma.class.findMany({
        where: { id: { in: classIds } },
        select: { id: true, name: true }
      })
    ])

    console.log(`Found ${teachers.length} teachers and ${classes.length} classes`)

    // 3. Create lookup maps
    const teacherMap = new Map(teachers.map(t => [t.id, t]))
    const classMap = new Map(classes.map(c => [c.id, c]))

    // 4. Display teacher permissions grouped by teacher
    console.log('\n2. Teacher Permissions Summary:')
    console.log('=' .repeat(80))

    const teacherPermissionsMap = new Map()
    
    // Group permissions by teacher
    permissions.forEach(perm => {
      if (!teacherPermissionsMap.has(perm.teacherId)) {
        teacherPermissionsMap.set(perm.teacherId, [])
      }
      teacherPermissionsMap.get(perm.teacherId).push(perm)
    })

    // Display each teacher and their permissions
    for (const [teacherId, teacherPerms] of teacherPermissionsMap) {
      const teacher = teacherMap.get(teacherId)
      if (!teacher) continue

      console.log(`\n📚 ${teacher.name} (${teacher.subject})`)
      console.log(`   Email: ${teacher.email}`)
      console.log(`   ID: ${teacherId}`)
      console.log(`   Classes with permissions: ${teacherPerms.length}`)

      teacherPerms.forEach((perm, index) => {
        const cls = classMap.get(perm.classId)
        console.log(`   ${index + 1}. Class: ${cls ? cls.name : perm.classId}`)
        console.log(`      - View Attendance: ${perm.canViewAttendance ? '✅' : '❌'}`)
        console.log(`      - Take Attendance: ${perm.canTakeAttendance ? '✅' : '❌'}`)
        console.log(`      - Add Marks: ${perm.canAddMarks ? '✅' : '❌'}`)
        console.log(`      - Edit Marks: ${perm.canEditMarks ? '✅' : '❌'}`)
      })
    }

    // 5. Test the API response format
    console.log('\n3. Testing API Response Format...')
    const permissionsWithDetails = permissions.map(permission => ({
      id: permission.id,
      teacherId: permission.teacherId,
      classId: permission.classId,
      teacher: teacherMap.get(permission.teacherId) || null,
      class: classMap.get(permission.classId) || null,
      canViewAttendance: permission.canViewAttendance,
      canTakeAttendance: permission.canTakeAttendance,
      canAddMarks: permission.canAddMarks,
      canEditMarks: permission.canEditMarks,
    }))

    console.log('Sample API response format:')
    if (permissionsWithDetails.length > 0) {
      console.log(JSON.stringify(permissionsWithDetails[0], null, 2))
    }

    // 6. Test multiple class assignment for Emily Davis
    console.log('\n4. Testing Multiple Class Assignment for Emily Davis...')
    const emilyTeacher = await prisma.teacher.findFirst({
      where: { name: { contains: 'Emily Davis' } }
    })

    if (emilyTeacher) {
      console.log(`Found Emily Davis: ${emilyTeacher.id}`)
      
      // Get all classes
      const allClasses = await prisma.class.findMany({
        select: { id: true, name: true },
        orderBy: { name: 'asc' }
      })

      console.log(`Available classes: ${allClasses.length}`)
      
      // Test assigning Emily to multiple classes (1A, 2A, 2B)
      const targetClasses = allClasses.filter(cls => 
        ['1A', '2A', '2B'].includes(cls.name)
      )

      console.log(`Target classes for Emily: ${targetClasses.map(c => c.name).join(', ')}`)

      // Check current permissions for Emily
      const emilyCurrentPermissions = await prisma.teacherPermission.findMany({
        where: { teacherId: emilyTeacher.id },
        include: {
          // We'll get class details separately
        }
      })

      console.log(`Emily's current permissions: ${emilyCurrentPermissions.length} classes`)
      
      for (const perm of emilyCurrentPermissions) {
        const cls = allClasses.find(c => c.id === perm.classId)
        console.log(`  - ${cls ? cls.name : perm.classId}: View=${perm.canViewAttendance}, Take=${perm.canTakeAttendance}, Add=${perm.canAddMarks}, Edit=${perm.canEditMarks}`)
      }

      // Test the batch assignment API format
      console.log('\n5. Testing Batch Assignment API Format...')
      const batchAssignmentData = {
        teacherId: emilyTeacher.id,
        classIds: targetClasses.map(c => c.id),
        permissions: {
          canViewAttendance: true,
          canTakeAttendance: true,
          canAddMarks: true,
          canEditMarks: true
        }
      }

      console.log('Batch assignment data format:')
      console.log(JSON.stringify(batchAssignmentData, null, 2))

    } else {
      console.log('Emily Davis not found')
    }

    // 7. Summary for UI display
    console.log('\n6. Summary for UI Display:')
    console.log('=' .repeat(50))
    
    console.log('\nTeachers that should appear in Teacher Permissions tab:')
    for (const teacher of teachers) {
      const teacherPerms = permissions.filter(p => p.teacherId === teacher.id)
      const classNames = teacherPerms.map(p => {
        const cls = classMap.get(p.classId)
        return cls ? cls.name : p.classId
      }).join(', ')
      
      console.log(`✅ ${teacher.name} → Classes: ${classNames || 'None'}`)
    }

  } catch (error) {
    console.error('Error:', error)
  } finally {
    await prisma.$disconnect()
  }
}

main()

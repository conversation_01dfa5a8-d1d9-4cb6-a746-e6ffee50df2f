import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { formatTeacherName } from '@/app/utils/formatters'

export async function GET() {
  try {
    const teachers = await prisma.teacher.findMany({
      select: {
        id: true,
        name: true,
        fatherName: true,
        gender: true,
        email: true,
        subject: true,
        mobile: true,
      },
    })
    return NextResponse.json(teachers)
  } catch (error) {
    console.error('Error fetching teachers:', error)
    return NextResponse.json(
      { error: 'Failed to fetch teachers' },
      { status: 500 }
    )
  }
}

export async function POST(request: Request) {
  try {
    const data = await request.json()
    const teacher = await prisma.teacher.create({
      data: {
        name: formatTeacher<PERSON><PERSON>(data.name),
        fatherName: formatTeacherName(data.fatherName),
        gender: data.gender,
        email: data.email,
        subject: data.subject,
        mobile: data.mobile,
      },
    })
    return NextResponse.json(teacher)
  } catch (error) {
    console.error('Error creating teacher:', error)
    return NextResponse.json(
      { error: 'Failed to create teacher' },
      { status: 500 }
    )
  }
}

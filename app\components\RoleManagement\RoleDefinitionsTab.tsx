'use client'

import React, { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import {
  Table, TableBody, TableCell, TableHead,
  TableHeader, TableRow
} from '@/app/components/ui/table'
import { Button } from '@/app/components/ui/button'
import { Input } from '@/app/components/ui/input'
import { Textarea } from '@/app/components/ui/textarea'
import {
  Dialog, DialogContent, DialogDescription, DialogFooter,
  DialogHeader, DialogTitle, DialogTrigger
} from '@/app/components/ui/dialog'
import {
  AlertDialog, AlertDialogAction, AlertDialogCancel,
  AlertDialogContent, AlertDialogDescription, AlertDialogFooter,
  AlertDialogHeader, AlertDialogTitle
} from '@/app/components/ui/alert-dialog'
import {
  Card, CardContent, CardDescription, CardFooter,
  CardHeader, CardTitle
} from '@/app/components/ui/card'
import { Badge } from '@/app/components/ui/badge'
import { Checkbox } from '@/app/components/ui/checkbox'
import { Label } from '@/app/components/ui/label'
// Removed dropdown menu imports as they're no longer needed
import {
  Accordion, AccordionContent, AccordionItem, AccordionTrigger
} from '@/app/components/ui/accordion'
import {
  Plus, RefreshCw, Edit, Trash2,
  ShieldAlert, ShieldCheck, Shield, UserCog, XCircle
} from 'lucide-react'

// Types
interface Role {
  id: string
  name: string
  description: string
  permissions: string[]
  systemDefined: boolean
}

interface Permission {
  id: string
  name: string
  description: string
}

interface PermissionGroup {
  [key: string]: Permission[]
}

export function RoleDefinitionsTab() {
  const queryClient = useQueryClient()
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [selectedRole, setSelectedRole] = useState<Role | null>(null)
  const [newRole, setNewRole] = useState({
    name: '',
    description: '',
    permissions: [] as string[]
  })

  // Fetch roles
  const {
    data: roles = [],
    isLoading: isLoadingRoles,
    error: rolesError,
    refetch: refetchRoles
  } = useQuery({
    queryKey: ['roles'],
    queryFn: async () => {
      const response = await fetch('/api/roles')
      if (!response.ok) {
        throw new Error('Failed to fetch roles')
      }
      return response.json()
    }
  })

  // Fetch permissions
  const {
    data: permissionsData = [],
    isLoading: isLoadingPermissions
  } = useQuery({
    queryKey: ['permissions'],
    queryFn: async () => {
      const response = await fetch('/api/permissions')
      if (!response.ok) {
        throw new Error('Failed to fetch permissions')
      }
      return response.json()
    }
  })

  // Group permissions by category
  const permissions = React.useMemo(() => {
    const grouped: PermissionGroup = {};

    if (Array.isArray(permissionsData)) {
      permissionsData.forEach((permission: any) => {
        if (!grouped[permission.category]) {
          grouped[permission.category] = [];
        }
        grouped[permission.category].push({
          id: permission.name, // Use name as ID for simplicity
          name: permission.name,
          description: permission.description
        });
      });
    }

    return grouped;
  }, [permissionsData]);

  // Create role mutation
  const createRole = useMutation({
    mutationFn: async (role: { name: string, description: string, permissions: string[] }) => {
      const response = await fetch('/api/roles', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(role),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create role')
      }

      return response.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['roles'] })
      setIsCreateDialogOpen(false)
      resetNewRole()
    },
    onError: (error: Error) => {
      console.error('Error creating role:', error)
      alert(`Failed to create role: ${error.message}`)
    }
  })

  // Update role mutation
  const updateRole = useMutation({
    mutationFn: async (role: Role) => {
      const response = await fetch(`/api/roles/${role.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: role.name,
          description: role.description,
          permissions: role.permissions
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update role')
      }

      return response.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['roles'] })
      setIsEditDialogOpen(false)
      setSelectedRole(null)
    },
    onError: (error: Error) => {
      console.error('Error updating role:', error)
      alert(`Failed to update role: ${error.message}`)
    }
  })

  // Delete role mutation
  const deleteRole = useMutation({
    mutationFn: async (roleId: string) => {
      const response = await fetch(`/api/roles/${roleId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete role')
      }

      return response.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['roles'] })
      setIsDeleteDialogOpen(false)
      setSelectedRole(null)
    },
    onError: (error: Error) => {
      console.error('Error deleting role:', error)
      alert(`Failed to delete role: ${error.message}`)
    }
  })

  // Reset new role form
  const resetNewRole = () => {
    setNewRole({
      name: '',
      description: '',
      permissions: []
    })
  }

  // Handle permission toggle
  const handlePermissionToggle = (permissionId: string, isChecked: boolean, isEdit: boolean = false) => {
    if (isEdit && selectedRole) {
      // For editing existing role
      if (isChecked) {
        setSelectedRole({
          ...selectedRole,
          permissions: [...selectedRole.permissions, permissionId]
        })
      } else {
        setSelectedRole({
          ...selectedRole,
          permissions: selectedRole.permissions.filter(p => p !== permissionId)
        })
      }
    } else {
      // For creating new role
      if (isChecked) {
        setNewRole({
          ...newRole,
          permissions: [...newRole.permissions, permissionId]
        })
      } else {
        setNewRole({
          ...newRole,
          permissions: newRole.permissions.filter(p => p !== permissionId)
        })
      }
    }
  }

  // Handle toggling all permissions in a category
  const handleToggleCategory = (category: string, isChecked: boolean, isEdit: boolean = false) => {
    if (!permissions[category]) return;

    const categoryPermissionIds = permissions[category].map((p: Permission) => p.id);

    if (isEdit && selectedRole) {
      // For editing existing role
      if (isChecked) {
        // Add all permissions from this category that aren't already included
        const newPermissions = [...selectedRole.permissions];
        categoryPermissionIds.forEach(id => {
          if (!newPermissions.includes(id)) {
            newPermissions.push(id);
          }
        });

        setSelectedRole({
          ...selectedRole,
          permissions: newPermissions
        });
      } else {
        // Remove all permissions from this category
        setSelectedRole({
          ...selectedRole,
          permissions: selectedRole.permissions.filter(id => !categoryPermissionIds.includes(id))
        });
      }
    } else {
      // For creating new role
      if (isChecked) {
        // Add all permissions from this category that aren't already included
        const newPermissions = [...newRole.permissions];
        categoryPermissionIds.forEach(id => {
          if (!newPermissions.includes(id)) {
            newPermissions.push(id);
          }
        });

        setNewRole({
          ...newRole,
          permissions: newPermissions
        });
      } else {
        // Remove all permissions from this category
        setNewRole({
          ...newRole,
          permissions: newRole.permissions.filter(id => !categoryPermissionIds.includes(id))
        });
      }
    }
  }

  // Check if all permissions in a category are selected
  const areCategoryPermissionsSelected = (category: string, isEdit: boolean = false): boolean => {
    if (!permissions[category]) return false;

    const categoryPermissionIds = permissions[category].map((p: Permission) => p.id);
    const currentPermissions = isEdit && selectedRole ? selectedRole.permissions : newRole.permissions;

    return categoryPermissionIds.every(id => currentPermissions.includes(id));
  }

  // Handle selecting all permissions
  const handleSelectAllPermissions = (isEdit: boolean = false) => {
    if (!permissions) return;

    // Get all permission IDs from all categories
    const allPermissionIds: string[] = [];
    Object.keys(permissions).forEach(category => {
      permissions[category].forEach((permission: Permission) => {
        allPermissionIds.push(permission.id);
      });
    });

    if (isEdit && selectedRole) {
      setSelectedRole({
        ...selectedRole,
        permissions: allPermissionIds
      });
    } else {
      setNewRole({
        ...newRole,
        permissions: allPermissionIds
      });
    }
  }

  // Handle deselecting all permissions
  const handleDeselectAllPermissions = (isEdit: boolean = false) => {
    if (isEdit && selectedRole) {
      setSelectedRole({
        ...selectedRole,
        permissions: []
      });
    } else {
      setNewRole({
        ...newRole,
        permissions: []
      });
    }
  }

  // Handle create role
  const handleCreateRole = () => {
    if (newRole.name.trim() === '' || newRole.description.trim() === '') {
      alert('Name and description are required')
      return
    }

    if (newRole.permissions.length === 0) {
      alert('At least one permission is required')
      return
    }

    createRole.mutate(newRole)
  }

  // Handle edit role
  const handleEditRole = () => {
    if (!selectedRole) return

    // For system-defined roles, only validate permissions
    if (selectedRole.systemDefined) {
      if (selectedRole.permissions.length === 0) {
        alert('At least one permission is required')
        return
      }
    } else {
      // For custom roles, validate all fields
      if (selectedRole.name.trim() === '' || selectedRole.description.trim() === '') {
        alert('Name and description are required')
        return
      }

      if (selectedRole.permissions.length === 0) {
        alert('At least one permission is required')
        return
      }
    }

    updateRole.mutate(selectedRole)
  }

  // Handle delete role
  const handleDeleteRole = () => {
    if (!selectedRole) return
    deleteRole.mutate(selectedRole.id)
  }

  // Render role icon
  const renderRoleIcon = (roleId: string) => {
    switch (roleId) {
      case 'admin':
        return <ShieldAlert className="h-5 w-5 text-purple-600" />
      case 'teacher':
      case 'hrteacher':
        return <ShieldCheck className="h-5 w-5 text-blue-600" />
      case 'staff':
        return <Shield className="h-5 w-5 text-indigo-600" />
      default:
        return <UserCog className="h-5 w-5 text-gray-600" />
    }
  }

  // Get permission name by ID
  const getPermissionName = (permissionId: string): string => {
    for (const category in permissions) {
      const found = permissions[category].find((p: Permission) => p.id === permissionId)
      if (found) return found.name
    }
    return permissionId
  }

  return (
    <div className="space-y-4">
      {/* Header and actions */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-medium">System Roles</h3>
          <p className="text-sm text-gray-500">Define roles and their permissions</p>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => refetchRoles()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button
            size="sm"
            onClick={() => {
              resetNewRole()
              setIsCreateDialogOpen(true)
            }}
          >
            <Plus className="h-4 w-4 mr-2" />
            New Role
          </Button>
        </div>
      </div>

      {/* Roles grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {isLoadingRoles ? (
          <Card className="col-span-full h-40 flex items-center justify-center">
            <RefreshCw className="h-5 w-5 animate-spin text-gray-400 mr-2" />
            <span>Loading roles...</span>
          </Card>
        ) : rolesError ? (
          <Card className="col-span-full h-40 flex items-center justify-center">
            <XCircle className="h-5 w-5 text-red-500 mr-2" />
            <span className="text-red-500">Error loading roles</span>
          </Card>
        ) : roles.length === 0 ? (
          <Card className="col-span-full h-40 flex items-center justify-center">
            <span className="text-gray-500">No roles defined</span>
          </Card>
        ) : (
          roles.map((role: Role) => (
            <Card key={role.id} className={role.systemDefined ? 'border-indigo-200' : ''}>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div className="flex items-center">
                    {renderRoleIcon(role.id)}
                    <CardTitle className="ml-2 text-lg">{role.name}</CardTitle>
                  </div>
                  <div className="flex items-center space-x-2">
                    {role.systemDefined && (
                      <Badge variant="outline" className="bg-indigo-50 text-indigo-700 border-indigo-200">
                        System
                      </Badge>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex items-center"
                      onClick={() => {
                        setSelectedRole(role)
                        setIsEditDialogOpen(true)
                      }}
                    >
                      <Edit className="h-4 w-4 mr-2" />
                      Edit
                    </Button>
                    {!role.systemDefined && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex items-center text-red-600 hover:text-red-700"
                        onClick={() => {
                          setSelectedRole(role)
                          setIsDeleteDialogOpen(true)
                        }}
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </Button>
                    )}
                  </div>
                </div>
                <CardDescription>{role.description}</CardDescription>
              </CardHeader>
              <CardContent className="pb-2">
                <div className="text-sm font-medium mb-1">Permissions:</div>
                <div className="flex flex-wrap gap-1">
                  {role.permissions.slice(0, 3).map((permission) => (
                    <Badge key={permission} variant="secondary" className="text-xs">
                      {getPermissionName(permission)}
                    </Badge>
                  ))}
                  {role.permissions.length > 3 && (
                    <Badge variant="outline" className="text-xs">
                      +{role.permissions.length - 3} more
                    </Badge>
                  )}
                </div>
              </CardContent>
              <CardFooter className="pt-2">
                <div className="text-xs text-gray-500">
                  {role.permissions.length} permission{role.permissions.length !== 1 ? 's' : ''}
                </div>
              </CardFooter>
            </Card>
          ))
        )}
      </div>

      {/* Create Role Dialog */}
      {isCreateDialogOpen && (
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New Role</DialogTitle>
            <DialogDescription>
              Define a new role and its permissions
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Name
              </Label>
              <Input
                id="name"
                value={newRole.name}
                onChange={(e) => setNewRole({ ...newRole, name: e.target.value })}
                className="col-span-3"
                placeholder="e.g., Principal"
              />
            </div>
            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="description" className="text-right pt-2">
                Description
              </Label>
              <Textarea
                id="description"
                value={newRole.description}
                onChange={(e) => setNewRole({ ...newRole, description: e.target.value })}
                className="col-span-3"
                placeholder="Brief description of this role"
                rows={2}
              />
            </div>

            <div className="grid grid-cols-4 items-start gap-4">
              <Label className="text-right pt-2">
                Permissions
              </Label>
              <div className="col-span-3 border rounded-md p-4 space-y-4">
                <div className="flex justify-end space-x-2 mb-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleSelectAllPermissions()}
                  >
                    Select All
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDeselectAllPermissions()}
                  >
                    Deselect All
                  </Button>
                </div>
                {isLoadingPermissions ? (
                  <div className="flex items-center justify-center h-20">
                    <RefreshCw className="h-5 w-5 animate-spin text-gray-400 mr-2" />
                    <span>Loading permissions...</span>
                  </div>
                ) : (
                  <Accordion type="multiple" className="w-full">
                    {Object.keys(permissions).map((category) => (
                      <AccordionItem key={category} value={category}>
                        <AccordionTrigger className="capitalize">
                          {category} ({permissions[category].length})
                        </AccordionTrigger>
                        <AccordionContent>
                          <div className="mb-2 pb-2 border-b border-gray-200">
                            <div className="flex items-center space-x-2">
                              <Checkbox
                                id={`new-category-${category}`}
                                checked={areCategoryPermissionsSelected(category)}
                                onCheckedChange={(checked) =>
                                  handleToggleCategory(category, checked as boolean)
                                }
                              />
                              <Label
                                htmlFor={`new-category-${category}`}
                                className="text-sm font-medium"
                              >
                                Select All {category.charAt(0).toUpperCase() + category.slice(1)} Permissions
                              </Label>
                            </div>
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-2 pt-2">
                            {permissions[category].map((permission: Permission) => (
                              <div key={permission.id} className="flex items-start space-x-2">
                                <Checkbox
                                  id={`new-${permission.id}`}
                                  checked={newRole.permissions.includes(permission.id)}
                                  onCheckedChange={(checked) =>
                                    handlePermissionToggle(permission.id, checked as boolean)
                                  }
                                />
                                <div className="grid gap-1.5">
                                  <Label
                                    htmlFor={`new-${permission.id}`}
                                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                  >
                                    {permission.name}
                                  </Label>
                                  <p className="text-xs text-gray-500">
                                    {permission.description}
                                  </p>
                                </div>
                              </div>
                            ))}
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    ))}
                  </Accordion>
                )}
                <div className="text-sm text-gray-500">
                  Selected: {newRole.permissions.length} permission{newRole.permissions.length !== 1 ? 's' : ''}
                </div>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleCreateRole}
              disabled={createRole.isPending}
            >
              {createRole.isPending ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Creating...
                </>
              ) : 'Create Role'}
            </Button>
          </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Edit Role Dialog */}
      {isEditDialogOpen && (
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {selectedRole?.systemDefined ? 'Edit Role Permissions' : 'Edit Role'}
              {selectedRole?.systemDefined && (
                <Badge variant="outline" className="bg-indigo-50 text-indigo-700 border-indigo-200 ml-2">
                  System
                </Badge>
              )}
            </DialogTitle>
            <DialogDescription>
              {selectedRole?.systemDefined
                ? 'Modify permissions for this system-defined role'
                : 'Modify role details and permissions'}
            </DialogDescription>
          </DialogHeader>

          {selectedRole && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-name" className="text-right">
                  Name
                </Label>
                <Input
                  id="edit-name"
                  value={selectedRole.name}
                  onChange={(e) => setSelectedRole({ ...selectedRole, name: e.target.value })}
                  className="col-span-3"
                  disabled={selectedRole.systemDefined}
                  readOnly={selectedRole.systemDefined}
                />
              </div>
              <div className="grid grid-cols-4 items-start gap-4">
                <Label htmlFor="edit-description" className="text-right pt-2">
                  Description
                </Label>
                <Textarea
                  id="edit-description"
                  value={selectedRole.description}
                  onChange={(e) => setSelectedRole({ ...selectedRole, description: e.target.value })}
                  className="col-span-3"
                  rows={2}
                  disabled={selectedRole.systemDefined}
                  readOnly={selectedRole.systemDefined}
                />
              </div>

              <div className="grid grid-cols-4 items-start gap-4">
                <Label className="text-right pt-2">
                  Permissions
                </Label>
                <div className="col-span-3 border rounded-md p-4 space-y-4">
                  <div className="flex justify-end space-x-2 mb-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleSelectAllPermissions(true)}
                    >
                      Select All
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeselectAllPermissions(true)}
                    >
                      Deselect All
                    </Button>
                  </div>
                  {isLoadingPermissions ? (
                    <div className="flex items-center justify-center h-20">
                      <RefreshCw className="h-5 w-5 animate-spin text-gray-400 mr-2" />
                      <span>Loading permissions...</span>
                    </div>
                  ) : (
                    <Accordion type="multiple" className="w-full">
                      {Object.keys(permissions).map((category) => (
                        <AccordionItem key={category} value={category}>
                          <AccordionTrigger className="capitalize">
                            {category} ({permissions[category].length})
                          </AccordionTrigger>
                          <AccordionContent>
                            <div className="mb-2 pb-2 border-b border-gray-200">
                              <div className="flex items-center space-x-2">
                                <Checkbox
                                  id={`edit-category-${category}`}
                                  checked={areCategoryPermissionsSelected(category, true)}
                                  onCheckedChange={(checked) =>
                                    handleToggleCategory(category, checked as boolean, true)
                                  }
                                />
                                <Label
                                  htmlFor={`edit-category-${category}`}
                                  className="text-sm font-medium"
                                >
                                  Select All {category.charAt(0).toUpperCase() + category.slice(1)} Permissions
                                </Label>
                              </div>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 pt-2">
                              {permissions[category].map((permission: Permission) => (
                                <div key={permission.id} className="flex items-start space-x-2">
                                  <Checkbox
                                    id={`edit-${permission.id}`}
                                    checked={selectedRole.permissions.includes(permission.id)}
                                    onCheckedChange={(checked) =>
                                      handlePermissionToggle(permission.id, checked as boolean, true)
                                    }
                                  />
                                  <div className="grid gap-1.5">
                                    <Label
                                      htmlFor={`edit-${permission.id}`}
                                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                    >
                                      {permission.name}
                                    </Label>
                                    <p className="text-xs text-gray-500">
                                      {permission.description}
                                    </p>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </AccordionContent>
                        </AccordionItem>
                      ))}
                    </Accordion>
                  )}
                  <div className="text-sm text-gray-500">
                    Selected: {selectedRole.permissions.length} permission{selectedRole.permissions.length !== 1 ? 's' : ''}
                  </div>
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleEditRole}
              disabled={updateRole.isPending}
            >
              {updateRole.isPending ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : 'Save Changes'}
            </Button>
          </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Delete Confirmation Dialog */}
      {isDeleteDialogOpen && (
        <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the role "{selectedRole?.name}".
              <br /><br />
              Users with this role will need to be reassigned.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteRole}
              className="bg-red-600 hover:bg-red-700"
            >
              {deleteRole.isPending ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}
    </div>
  )
}

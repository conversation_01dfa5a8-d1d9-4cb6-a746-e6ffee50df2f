"use client"

import React from 'react'
import { Users, UserPlus, Calendar, School } from 'lucide-react'

export const DashboardStats: React.FC = () => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {/* Students Card */}
      <div className="dashboard-card">
        <div className="card-icon" style={{ backgroundColor: '#f82649' }}>
          <Users size={24} />
        </div>
        <div className="card-content">
          <span className="card-title">Students</span>
          <span className="card-value">932</span>
        </div>
      </div>

      {/* Teachers Card */}
      <div className="dashboard-card">
        <div className="card-icon" style={{ backgroundColor: '#ff7043' }}>
          <UserPlus size={24} />
        </div>
        <div className="card-content">
          <span className="card-title">Teachers</span>
          <span className="card-value">754</span>
        </div>
      </div>

      {/* Events Card */}
      <div className="dashboard-card">
        <div className="card-icon" style={{ backgroundColor: '#ffb300' }}>
          <Calendar size={24} />
        </div>
        <div className="card-content">
          <span className="card-title">Events</span>
          <span className="card-value">40</span>
        </div>
      </div>

      {/* Class Rooms Card */}
      <div className="dashboard-card">
        <div className="card-icon" style={{ backgroundColor: '#3949ab' }}>
          <School size={24} />
        </div>
        <div className="card-content">
          <span className="card-title">Class</span>
          <span className="card-value">23</span>
        </div>
      </div>
    </div>
  )
} 
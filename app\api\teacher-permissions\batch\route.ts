import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { teacherId, classIds, permissions } = body

    // Validate required fields
    if (!teacherId || !classIds || !Array.isArray(classIds) || classIds.length === 0) {
      return NextResponse.json(
        { error: 'Teacher ID and at least one class ID are required' },
        { status: 400 }
      )
    }

    if (!permissions || typeof permissions !== 'object') {
      return NextResponse.json(
        { error: 'Permissions object is required' },
        { status: 400 }
      )
    }

    const { canViewAttendance, canTakeAttendance, canAddMarks, canEditMarks } = permissions

    // Validate permission values
    if (
      typeof canViewAttendance !== 'boolean' ||
      typeof canTakeAttendance !== 'boolean' ||
      typeof canAddMarks !== 'boolean' ||
      typeof canEditMarks !== 'boolean'
    ) {
      return NextResponse.json(
        { error: 'All permission values must be boolean' },
        { status: 400 }
      )
    }

    console.log(`Assigning permissions for teacher ${teacherId} to ${classIds.length} classes`)

    // Verify teacher exists in either Teacher table or User table with teacher role
    let teacher = await prisma.teacher.findUnique({
      where: { id: teacherId },
      select: { id: true, name: true, email: true }
    })

    let teacherFromUser = null
    if (!teacher) {
      teacherFromUser = await prisma.user.findFirst({
        where: {
          id: teacherId,
          role: 'TEACHER',
          status: 'active'
        },
        select: { id: true, name: true, email: true }
      })
    }

    if (!teacher && !teacherFromUser) {
      return NextResponse.json(
        { error: 'Teacher not found in either Teacher table or User table with teacher role' },
        { status: 404 }
      )
    }

    // Use the found teacher data
    const teacherData = teacher || {
      id: teacherFromUser!.id,
      name: teacherFromUser!.name,
      email: teacherFromUser!.email
    }

    // Verify all classes exist
    const classes = await prisma.class.findMany({
      where: { id: { in: classIds } },
      select: { id: true, name: true }
    })

    if (classes.length !== classIds.length) {
      const foundClassIds = classes.map(c => c.id)
      const missingClassIds = classIds.filter(id => !foundClassIds.includes(id))
      return NextResponse.json(
        { error: `Classes not found: ${missingClassIds.join(', ')}` },
        { status: 404 }
      )
    }

    // Use transaction to ensure all permissions are created/updated atomically
    const results = await prisma.$transaction(async (tx) => {
      const createdPermissions = []
      const updatedPermissions = []

      for (const classId of classIds) {
        // Use upsert to create or update permissions
        const permission = await tx.teacherPermission.upsert({
          where: {
            teacherId_classId: {
              teacherId,
              classId
            }
          },
          update: {
            canViewAttendance,
            canTakeAttendance,
            canAddMarks,
            canEditMarks,
            updatedAt: new Date()
          },
          create: {
            teacherId,
            classId,
            canViewAttendance,
            canTakeAttendance,
            canAddMarks,
            canEditMarks
          }
        })

        // Check if this was a create or update operation
        const existingPermission = await tx.teacherPermission.findFirst({
          where: {
            teacherId,
            classId,
            createdAt: { lt: new Date(Date.now() - 1000) } // Created more than 1 second ago
          }
        })

        if (existingPermission) {
          updatedPermissions.push({
            ...permission,
            className: classes.find(c => c.id === classId)?.name
          })
        } else {
          createdPermissions.push({
            ...permission,
            className: classes.find(c => c.id === classId)?.name
          })
        }
      }

      return { createdPermissions, updatedPermissions }
    })

    const totalProcessed = results.createdPermissions.length + results.updatedPermissions.length

    console.log(`Successfully processed ${totalProcessed} permissions (${results.createdPermissions.length} created, ${results.updatedPermissions.length} updated)`)

    // Prepare response data
    const processedPermissions = [
      ...results.createdPermissions.map(perm => ({
        id: perm.id,
        teacherId: perm.teacherId,
        classId: perm.classId,
        className: perm.className,
        canViewAttendance: perm.canViewAttendance,
        canTakeAttendance: perm.canTakeAttendance,
        canAddMarks: perm.canAddMarks,
        canEditMarks: perm.canEditMarks,
        action: 'created'
      })),
      ...results.updatedPermissions.map(perm => ({
        id: perm.id,
        teacherId: perm.teacherId,
        classId: perm.classId,
        className: perm.className,
        canViewAttendance: perm.canViewAttendance,
        canTakeAttendance: perm.canTakeAttendance,
        canAddMarks: perm.canAddMarks,
        canEditMarks: perm.canEditMarks,
        action: 'updated'
      }))
    ]

    return NextResponse.json({
      message: `Successfully processed permissions for ${teacherData.name} in ${totalProcessed} class${totalProcessed !== 1 ? 'es' : ''}`,
      teacher: {
        id: teacherData.id,
        name: teacherData.name,
        email: teacherData.email
      },
      processedCount: totalProcessed,
      createdCount: results.createdPermissions.length,
      updatedCount: results.updatedPermissions.length,
      permissions: processedPermissions,
      classes: classes.map(cls => ({
        id: cls.id,
        name: cls.name
      }))
    })
  } catch (error) {
    console.error('Error processing batch teacher permissions:', error)
    
    // Handle specific Prisma errors
    if (error instanceof Error) {
      if (error.message.includes('Foreign key constraint failed')) {
        return NextResponse.json(
          { error: 'Invalid teacher or class reference. Please check the data.' },
          { status: 400 }
        )
      }
      if (error.message.includes('Unique constraint failed')) {
        return NextResponse.json(
          { error: 'Duplicate permission entry detected.' },
          { status: 409 }
        )
      }
    }

    return NextResponse.json(
      { error: 'Failed to process teacher permissions' },
      { status: 500 }
    )
  }
}

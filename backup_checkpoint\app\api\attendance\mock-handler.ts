import { NextResponse } from 'next/server'
import { fallbackAttendance, getAttendanceForClassAndDate, getStudentsForClass } from '@/app/lib/fallback-data'

// Mock GET handler for attendance
export async function mockGetHandler(searchParams: URLSearchParams) {
  try {
    const className = searchParams.get('class')
    const date = searchParams.get('date')

    if (!className) {
      return NextResponse.json(
        { error: 'Class parameter is required' },
        { status: 400 }
      )
    }

    // Get attendance records for the class and date
    const records = getAttendanceForClassAndDate(className, date || '')

    return NextResponse.json(records)
  } catch (error) {
    console.error('Error in mock attendance GET handler:', error)
    return NextResponse.json(
      { error: 'Failed to fetch mock attendance records' },
      { status: 500 }
    )
  }
}

// Mock POST handler for attendance
export async function mockPostHandler(body: any) {
  try {
    const { date, className, records } = body

    if (!date || !className || !records) {
      return NextResponse.json(
        { error: 'Date, className, and records are required' },
        { status: 400 }
      )
    }

    // Create mock attendance records
    const mockRecords = records.map((record: any) => ({
      id: `mock-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      date,
      className,
      studentId: record.studentId,
      status: record.status,
      createdAt: new Date(),
      updatedAt: new Date()
    }))

    // Store in the fallback data (this will be lost on page refresh)
    if (!fallbackAttendance[className]) {
      fallbackAttendance[className] = {}
    }
    fallbackAttendance[className][date] = mockRecords

    return NextResponse.json(mockRecords)
  } catch (error) {
    console.error('Error in mock attendance POST handler:', error)
    return NextResponse.json(
      { error: 'Failed to save mock attendance records' },
      { status: 500 }
    )
  }
}

// Mock report generator
export async function mockReportHandler(searchParams: URLSearchParams) {
  try {
    const className = searchParams.get('class')
    const type = searchParams.get('type')
    
    if (!className || !type) {
      return NextResponse.json(
        { error: 'Class and type parameters are required' },
        { status: 400 }
      )
    }

    // Get students for the class
    const students = getStudentsForClass(className)
    
    // Generate mock report data
    const reportData = {
      summary: {
        totalStudents: students.length,
        presentCount: Math.floor(students.length * 0.8),
        absentCount: Math.floor(students.length * 0.15),
        permissionCount: Math.floor(students.length * 0.05),
        attendanceRate: 80,
        totalRecords: students.length,
      },
      dateRange: {
        start: new Date().toISOString(),
        end: new Date().toISOString(),
      },
      dailyBreakdown: [
        {
          date: new Date().toISOString(),
          total: students.length,
          present: Math.floor(students.length * 0.8),
          absent: Math.floor(students.length * 0.15),
          permission: Math.floor(students.length * 0.05),
        }
      ],
      studentBreakdown: students.map(student => ({
        studentId: student.id,
        studentName: student.name,
        present: Math.floor(Math.random() * 10),
        absent: Math.floor(Math.random() * 3),
        permission: Math.floor(Math.random() * 2),
        total: 10,
      })),
    }

    return NextResponse.json(reportData)
  } catch (error) {
    console.error('Error in mock report handler:', error)
    return NextResponse.json(
      { error: 'Failed to generate mock attendance report' },
      { status: 500 }
    )
  }
}

import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET a specific news/event
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id
    
    const newsEvent = await prisma.newsEvent.findUnique({
      where: { id }
    })
    
    if (!newsEvent) {
      return NextResponse.json({
        error: 'News/event not found'
      }, { status: 404 })
    }
    
    return NextResponse.json(newsEvent)
  } catch (error) {
    console.error('Error fetching news/event:', error)
    return NextResponse.json({
      error: error.message || 'Failed to fetch news/event'
    }, { status: 500 })
  }
}

// PUT (update) a news/event
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id
    const data = await request.json()
    
    // Check if the news/event exists
    const existingNewsEvent = await prisma.newsEvent.findUnique({
      where: { id }
    })
    
    if (!existingNewsEvent) {
      return NextResponse.json({
        error: 'News/event not found'
      }, { status: 404 })
    }
    
    // Validate category if provided
    if (data.category && data.category !== 'news' && data.category !== 'event') {
      return NextResponse.json({
        error: 'Category must be either "news" or "event"'
      }, { status: 400 })
    }
    
    // Update the news/event
    const updatedNewsEvent = await prisma.newsEvent.update({
      where: { id },
      data: {
        title: data.title !== undefined ? data.title : existingNewsEvent.title,
        content: data.content !== undefined ? data.content : existingNewsEvent.content,
        excerpt: data.excerpt !== undefined ? data.excerpt : existingNewsEvent.excerpt,
        imageUrl: data.imageUrl !== undefined ? data.imageUrl : existingNewsEvent.imageUrl,
        date: data.date !== undefined ? new Date(data.date) : existingNewsEvent.date,
        category: data.category !== undefined ? data.category : existingNewsEvent.category,
        tags: data.tags !== undefined ? data.tags : existingNewsEvent.tags,
        isActive: data.isActive !== undefined ? data.isActive : existingNewsEvent.isActive
      }
    })
    
    return NextResponse.json(updatedNewsEvent)
  } catch (error) {
    console.error('Error updating news/event:', error)
    return NextResponse.json({
      error: error.message || 'Failed to update news/event'
    }, { status: 500 })
  }
}

// DELETE a news/event
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id
    
    // Check if the news/event exists
    const existingNewsEvent = await prisma.newsEvent.findUnique({
      where: { id }
    })
    
    if (!existingNewsEvent) {
      return NextResponse.json({
        error: 'News/event not found'
      }, { status: 404 })
    }
    
    // Delete the news/event
    await prisma.newsEvent.delete({
      where: { id }
    })
    
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting news/event:', error)
    return NextResponse.json({
      error: error.message || 'Failed to delete news/event'
    }, { status: 500 })
  }
}

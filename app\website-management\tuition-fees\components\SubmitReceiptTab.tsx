"use client"

import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/app/components/ui/button'
import { Card, CardContent } from '@/app/components/ui/card'
import { Input } from '@/app/components/ui/input'
import { Label } from '@/app/components/ui/label'
import { Textarea } from '@/app/components/ui/textarea'
import { useToast } from '@/app/components/ui/use-toast'
import { Info, Save, Upload, FileImage, Eye, Trash } from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/app/components/ui/dialog'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/app/components/ui/table'
import { Switch } from '@/app/components/ui/switch'

// Define the payment receipt type
interface PaymentReceipt {
  id: string
  studentName: string
  className: string
  month: string
  bankName: string
  receiptImageUrl: string
  submittedAt: string
  status: 'pending' | 'verified' | 'rejected'
}

// Mock data for initial payment receipts
const initialPaymentReceipts: PaymentReceipt[] = [
  {
    id: '1',
    studentName: "<PERSON>",
    className: "8E",
    month: "September 2023",
    bankName: "Commercial Bank of Ethiopia",
    receiptImageUrl: "/images/sample-receipt.jpg",
    submittedAt: "2023-09-05",
    status: 'verified',
  },
  {
    id: '2',
    studentName: "Fatima Ali",
    className: "7A",
    month: "October 2023",
    bankName: "Dashen Bank",
    receiptImageUrl: "/images/sample-receipt.jpg",
    submittedAt: "2023-10-03",
    status: 'pending',
  },
  {
    id: '3',
    studentName: "Ibrahim Hassan",
    className: "10B",
    month: "October 2023",
    bankName: "Awash Bank",
    receiptImageUrl: "/images/sample-receipt.jpg",
    submittedAt: "2023-10-07",
    status: 'rejected',
  },
]

export default function SubmitReceiptTab() {
  const { toast } = useToast()
  const [paymentReceipts, setPaymentReceipts] = useState<PaymentReceipt[]>([])
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isStatusDialogOpen, setIsStatusDialogOpen] = useState(false)
  const [currentReceipt, setCurrentReceipt] = useState<PaymentReceipt | null>(null)
  const [showFormSettings, setShowFormSettings] = useState(true)
  const [formSettings, setFormSettings] = useState({
    enabled: true,
    title: "Submit Payment Receipt",
    description: "Upload your payment receipt or screenshot for verification",
    successMessage: "Payment receipt submitted successfully! Our finance team will review and confirm your payment."
  })
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingReceipts, setIsLoadingReceipts] = useState(true)

  // Fetch payment receipts on component mount
  useEffect(() => {
    fetchPaymentReceipts()
  }, [])

  // Function to fetch payment receipts from the API
  const fetchPaymentReceipts = async () => {
    setIsLoadingReceipts(true)
    try {
      const response = await fetch('/api/website-management/payment-receipts')

      // Check if the response is ok
      if (!response.ok) {
        console.warn('API returned an error status:', response.status)
        throw new Error('Failed to fetch payment receipts')
      }

      // Parse the response
      const data = await response.json()

      // Validate that we received an array
      if (Array.isArray(data)) {
        setPaymentReceipts(data)
      } else {
        console.warn('API did not return an array:', data)
        // Fallback to initial data
        setPaymentReceipts(initialPaymentReceipts)
      }
    } catch (error) {
      console.error('Error fetching payment receipts:', error)
      toast({
        title: "Error",
        description: "Failed to load payment receipts. Using sample data instead.",
        variant: "destructive",
      })
      // Fallback to initial data if API fails
      setPaymentReceipts(initialPaymentReceipts)
    } finally {
      setIsLoadingReceipts(false)
    }
  }

  const handleFormSettingsChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormSettings(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSwitchChange = (checked: boolean) => {
    setFormSettings(prev => ({
      ...prev,
      enabled: checked
    }))
  }

  const handleSaveSettings = async () => {
    setIsLoading(true)

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      // In a real implementation, you would save to the database here
      toast({
        title: "Settings Updated",
        description: "Payment receipt form settings have been updated successfully.",
        variant: "default",
      })
    } catch (error) {
      console.error('Error saving settings:', error)
      toast({
        title: "Error",
        description: "Failed to update settings. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const openViewDialog = (receipt: PaymentReceipt) => {
    setCurrentReceipt(receipt)
    setIsViewDialogOpen(true)
  }

  const openStatusDialog = (receipt: PaymentReceipt) => {
    setCurrentReceipt(receipt)
    setIsStatusDialogOpen(true)
  }

  const openDeleteDialog = (receipt: PaymentReceipt) => {
    setCurrentReceipt(receipt)
    setIsDeleteDialogOpen(true)
  }

  const handleStatusChange = async (status: 'pending' | 'verified' | 'rejected') => {
    if (!currentReceipt) return

    setIsLoading(true)

    try {
      // Call the API to update the receipt status
      const response = await fetch(`/api/website-management/payment-receipts/${currentReceipt.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status }),
      })

      if (!response.ok) {
        throw new Error('Failed to update receipt status')
      }

      const updatedReceipt = await response.json()

      // Update the local state
      setPaymentReceipts(prev =>
        prev.map(receipt =>
          receipt.id === currentReceipt.id
            ? { ...receipt, status }
            : receipt
        )
      )

      setIsStatusDialogOpen(false)

      toast({
        title: "Status Updated",
        description: `Receipt status has been updated to ${status}.`,
        variant: "default",
      })
    } catch (error) {
      console.error('Error updating status:', error)
      toast({
        title: "Error",
        description: "Failed to update status. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleDeleteReceipt = async () => {
    if (!currentReceipt) return

    setIsLoading(true)

    try {
      // Call the API to delete the receipt
      const response = await fetch(`/api/website-management/payment-receipts/${currentReceipt.id}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error('Failed to delete receipt')
      }

      // Update the local state
      setPaymentReceipts(prev =>
        prev.filter(receipt => receipt.id !== currentReceipt.id)
      )

      setIsDeleteDialogOpen(false)

      toast({
        title: "Receipt Deleted",
        description: `Payment receipt for ${currentReceipt.studentName} has been deleted.`,
        variant: "default",
      })

      // Refresh the receipts list
      fetchPaymentReceipts()
    } catch (error) {
      console.error('Error deleting receipt:', error)
      toast({
        title: "Error",
        description: "Failed to delete receipt. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'verified':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
      case 'rejected':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
      default:
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-start space-x-2 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <Info className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5" />
        <div className="text-sm text-gray-600 dark:text-gray-300">
          <p>Manage the payment receipt submission form and review submitted receipts. You can verify or reject submitted receipts.</p>
        </div>
      </div>

      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Payment Receipt Management</h3>
        <Button
          variant="outline"
          onClick={() => setShowFormSettings(!showFormSettings)}
        >
          {showFormSettings ? 'Hide Form Settings' : 'Show Form Settings'}
        </Button>
      </div>

      {showFormSettings && (
        <Card>
          <CardContent className="p-6 space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <h4 className="text-base font-medium">Form Settings</h4>
                <p className="text-sm text-gray-500">Configure the payment receipt submission form</p>
              </div>
              <div className="flex items-center space-x-2">
                <Label htmlFor="form-enabled">Enable Form</Label>
                <Switch
                  id="form-enabled"
                  checked={formSettings.enabled}
                  onCheckedChange={handleSwitchChange}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="title">Form Title</Label>
              <Input
                id="title"
                name="title"
                value={formSettings.title}
                onChange={handleFormSettingsChange}
                placeholder="Enter form title"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Form Description</Label>
              <Textarea
                id="description"
                name="description"
                value={formSettings.description}
                onChange={handleFormSettingsChange}
                rows={2}
                placeholder="Enter form description"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="successMessage">Success Message</Label>
              <Textarea
                id="successMessage"
                name="successMessage"
                value={formSettings.successMessage}
                onChange={handleFormSettingsChange}
                rows={2}
                placeholder="Message to display after successful submission"
              />
            </div>

            <div className="flex justify-end">
              <Button
                onClick={handleSaveSettings}
                disabled={isLoading}
                className="flex items-center"
              >
                {isLoading ? (
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                ) : (
                  <Save className="mr-2 h-4 w-4" />
                )}
                Save Settings
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Submitted Receipts</h3>
      </div>

      <Card>
        <CardContent className="p-0 overflow-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Student Name</TableHead>
                <TableHead>Class</TableHead>
                <TableHead>Month</TableHead>
                <TableHead>Bank</TableHead>
                <TableHead>Submitted</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoadingReceipts ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-10">
                    <div className="flex flex-col items-center justify-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600 mb-2"></div>
                      <span className="text-gray-500">Loading payment receipts...</span>
                    </div>
                  </TableCell>
                </TableRow>
              ) : paymentReceipts.length > 0 ? (
                paymentReceipts.map((receipt) => (
                  <TableRow key={receipt.id}>
                    <TableCell className="font-medium">{receipt.studentName}</TableCell>
                    <TableCell>{receipt.className}</TableCell>
                    <TableCell>{receipt.month}</TableCell>
                    <TableCell>{receipt.bankName}</TableCell>
                    <TableCell>{new Date(receipt.submittedAt).toLocaleDateString()}</TableCell>
                    <TableCell>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusBadgeClass(receipt.status)}`}>
                        {receipt.status.charAt(0).toUpperCase() + receipt.status.slice(1)}
                      </span>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => openViewDialog(receipt)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => openStatusDialog(receipt)}
                        >
                          {receipt.status === 'pending' ? (
                            <FileImage className="h-4 w-4" />
                          ) : (
                            <Upload className="h-4 w-4" />
                          )}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => openDeleteDialog(receipt)}
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-6 text-gray-500">
                    No payment receipts have been submitted yet.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* View Receipt Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>View Payment Receipt</DialogTitle>
            <DialogDescription>
              Receipt submitted by {currentReceipt?.studentName} for {currentReceipt?.month}
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <div className="aspect-video bg-gray-100 dark:bg-gray-800 rounded-md overflow-hidden flex items-center justify-center">
              {currentReceipt?.receiptImageUrl ? (
                <img
                  src={currentReceipt.receiptImageUrl}
                  alt="Payment Receipt"
                  className="max-w-full max-h-full object-contain"
                />
              ) : (
                <div className="text-gray-400 flex flex-col items-center">
                  <FileImage className="h-12 w-12 mb-2" />
                  <span>No image available</span>
                </div>
              )}
            </div>
            <div className="mt-4 grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Student</p>
                <p>{currentReceipt?.studentName}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Class</p>
                <p>{currentReceipt?.className}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Month</p>
                <p>{currentReceipt?.month}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Bank</p>
                <p>{currentReceipt?.bankName}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Submitted</p>
                <p>{currentReceipt?.submittedAt && new Date(currentReceipt.submittedAt).toLocaleDateString()}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Status</p>
                <p className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${currentReceipt && getStatusBadgeClass(currentReceipt.status)}`}>
                  {currentReceipt?.status.charAt(0).toUpperCase() + currentReceipt?.status.slice(1)}
                </p>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>
              Close
            </Button>
            <Button onClick={() => {
              setIsViewDialogOpen(false)
              if (currentReceipt) openStatusDialog(currentReceipt)
            }}>
              Update Status
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Update Status Dialog */}
      <Dialog open={isStatusDialogOpen} onOpenChange={setIsStatusDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Update Receipt Status</DialogTitle>
            <DialogDescription>
              Change the verification status for this payment receipt.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4 space-y-4">
            <div className="flex flex-col space-y-2">
              <Button
                variant={currentReceipt?.status === 'verified' ? 'default' : 'outline'}
                className="justify-start"
                onClick={() => handleStatusChange('verified')}
                disabled={isLoading}
              >
                <div className="w-4 h-4 rounded-full bg-green-500 mr-2"></div>
                Verified
              </Button>
              <Button
                variant={currentReceipt?.status === 'pending' ? 'default' : 'outline'}
                className="justify-start"
                onClick={() => handleStatusChange('pending')}
                disabled={isLoading}
              >
                <div className="w-4 h-4 rounded-full bg-yellow-500 mr-2"></div>
                Pending
              </Button>
              <Button
                variant={currentReceipt?.status === 'rejected' ? 'default' : 'outline'}
                className="justify-start"
                onClick={() => handleStatusChange('rejected')}
                disabled={isLoading}
              >
                <div className="w-4 h-4 rounded-full bg-red-500 mr-2"></div>
                Rejected
              </Button>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsStatusDialogOpen(false)}>
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this payment receipt? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteReceipt}
              disabled={isLoading}
            >
              {isLoading ? (
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
              ) : (
                <Trash className="mr-2 h-4 w-4" />
              )}
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

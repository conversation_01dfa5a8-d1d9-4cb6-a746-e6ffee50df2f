// <PERSON>ript to create super admin user after database reset
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');
const prisma = new PrismaClient();

async function main() {
  try {
    console.log('🔐 Creating Super Admin User...\n');

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (existingUser) {
      console.log('✅ Super admin user already exists!');
      console.log(`   Email: ${existingUser.email}`);
      console.log(`   Name: ${existingUser.name}`);
      console.log(`   Role: ${existingUser.role}`);
      console.log(`   Status: ${existingUser.status}`);
      return;
    }

    // Hash the password
    const hashedPassword = await bcrypt.hash('admin@123', 12);

    // Create the super admin user
    const superAdmin = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Israel Super Admin',
        role: 'SUPER_ADMIN',
        status: 'active',
        lastLogin: new Date(),
        appNotifications: true,
        emailNotifications: true,
        language: 'english',
        theme: 'system'
      }
    });

    console.log('🎉 Super Admin User Created Successfully!');
    console.log('');
    console.log('📋 Login Credentials:');
    console.log(`   Email: ${superAdmin.email}`);
    console.log(`   Password: admin@123`);
    console.log(`   Role: ${superAdmin.role}`);
    console.log(`   Status: ${superAdmin.status}`);
    console.log('');
    console.log('✅ You can now login to the application with these credentials');

    // Also create basic roles if they don't exist
    console.log('\n🔧 Setting up basic roles...');
    
    const roles = [
      { name: 'SUPER_ADMIN', description: 'Super Administrator with full access' },
      { name: 'ADMIN', description: 'Administrator with management access' },
      { name: 'TEACHER', description: 'Teacher with classroom access' },
      { name: 'UNIT_LEADER', description: 'Unit Leader with view-only access' },
      { name: 'DATA_ENCODER', description: 'Data Encoder with limited access' }
    ];

    for (const roleData of roles) {
      try {
        const existingRole = await prisma.role.findUnique({
          where: { name: roleData.name }
        });

        if (!existingRole) {
          await prisma.role.create({
            data: roleData
          });
          console.log(`   ✅ Created role: ${roleData.name}`);
        } else {
          console.log(`   ⚠️  Role already exists: ${roleData.name}`);
        }
      } catch (error) {
        console.log(`   ❌ Failed to create role ${roleData.name}: ${error.message}`);
      }
    }

    console.log('\n🎯 Setup Complete!');
    console.log('The application is ready for use with super admin access.');

  } catch (error) {
    console.error('❌ Error creating super admin:', error);
    
    if (error.code === 'P2002') {
      console.log('User with this email already exists');
    }
  } finally {
    await prisma.$disconnect();
  }
}

main();

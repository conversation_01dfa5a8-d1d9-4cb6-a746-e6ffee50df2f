"use client"

import React from 'react'
import { cn } from '@/lib/utils'

interface MobileContainerProps {
  children: React.ReactNode
  className?: string
  variant?: 'default' | 'card' | 'header' | 'content'
  padding?: 'none' | 'sm' | 'md' | 'lg'
}

export function MobileContainer({ 
  children, 
  className, 
  variant = 'default',
  padding = 'md'
}: MobileContainerProps) {
  const baseClasses = "w-full"
  
  const variantClasses = {
    default: "",
    card: "bg-white rounded-lg shadow-sm border overflow-hidden",
    header: "border-b border-gray-200 bg-white",
    content: "bg-gray-50 min-h-screen"
  }
  
  const paddingClasses = {
    none: "",
    sm: "p-3 sm:p-4",
    md: "p-4 sm:p-6",
    lg: "p-6 sm:p-8"
  }

  return (
    <div className={cn(
      baseClasses,
      variantClasses[variant],
      paddingClasses[padding],
      className
    )}>
      {children}
    </div>
  )
}

interface MobileHeaderProps {
  title: string
  subtitle?: string
  children?: React.ReactNode
  className?: string
}

export function MobileHeader({ title, subtitle, children, className }: MobileHeaderProps) {
  return (
    <MobileContainer variant="header" padding="md" className={className}>
      <div className="flex flex-col space-y-4 lg:flex-row lg:justify-between lg:items-center lg:space-y-0">
        <div className="flex-1">
          <h1 className="text-2xl lg:text-3xl font-bold text-gray-900">{title}</h1>
          {subtitle && (
            <p className="text-gray-600 text-sm lg:text-base mt-1">{subtitle}</p>
          )}
        </div>
        {children && (
          <div className="flex-shrink-0">
            {children}
          </div>
        )}
      </div>
    </MobileContainer>
  )
}

interface MobileButtonGroupProps {
  children: React.ReactNode
  className?: string
  orientation?: 'horizontal' | 'vertical' | 'responsive'
}

export function MobileButtonGroup({ 
  children, 
  className, 
  orientation = 'responsive' 
}: MobileButtonGroupProps) {
  const orientationClasses = {
    horizontal: "flex space-x-2",
    vertical: "flex flex-col space-y-2",
    responsive: "flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2"
  }

  return (
    <div className={cn(orientationClasses[orientation], className)}>
      {children}
    </div>
  )
}

interface MobileCardProps {
  children: React.ReactNode
  className?: string
  title?: string
  subtitle?: string
  headerActions?: React.ReactNode
}

export function MobileCard({ 
  children, 
  className, 
  title, 
  subtitle, 
  headerActions 
}: MobileCardProps) {
  return (
    <MobileContainer variant="card" padding="none" className={className}>
      {(title || subtitle || headerActions) && (
        <div className="p-4 sm:p-6 border-b border-gray-200">
          <div className="flex flex-col space-y-2 sm:flex-row sm:justify-between sm:items-start sm:space-y-0">
            <div className="flex-1">
              {title && (
                <h2 className="text-lg font-semibold text-gray-900">{title}</h2>
              )}
              {subtitle && (
                <p className="text-sm text-gray-600 mt-1">{subtitle}</p>
              )}
            </div>
            {headerActions && (
              <div className="flex-shrink-0">
                {headerActions}
              </div>
            )}
          </div>
        </div>
      )}
      <div className="p-4 sm:p-6">
        {children}
      </div>
    </MobileContainer>
  )
}

interface MobileTableContainerProps {
  children: React.ReactNode
  className?: string
}

export function MobileTableContainer({ children, className }: MobileTableContainerProps) {
  return (
    <div className={cn(
      "rounded-md border bg-white overflow-hidden",
      className
    )}>
      <div className="overflow-x-auto -webkit-overflow-scrolling-touch">
        {children}
      </div>
    </div>
  )
}

interface MobileSearchProps {
  placeholder?: string
  value: string
  onChange: (value: string) => void
  className?: string
  icon?: React.ReactNode
}

export function MobileSearch({ 
  placeholder = "Search...", 
  value, 
  onChange, 
  className,
  icon
}: MobileSearchProps) {
  return (
    <div className={cn("relative", className)}>
      {icon && (
        <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">
          {icon}
        </div>
      )}
      <input
        type="text"
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className={cn(
          "w-full h-12 px-4 text-base border border-gray-300 rounded-lg",
          "focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
          "bg-white placeholder-gray-500",
          icon && "pl-10"
        )}
      />
    </div>
  )
}

interface MobileFilterGridProps {
  children: React.ReactNode
  className?: string
  columns?: 1 | 2 | 3 | 4
}

export function MobileFilterGrid({ 
  children, 
  className, 
  columns = 2 
}: MobileFilterGridProps) {
  const gridClasses = {
    1: "grid grid-cols-1 gap-3",
    2: "grid grid-cols-1 sm:grid-cols-2 gap-3",
    3: "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3",
    4: "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3"
  }

  return (
    <div className={cn(gridClasses[columns], className)}>
      {children}
    </div>
  )
}

interface MobileEmptyStateProps {
  icon?: React.ReactNode
  title: string
  description?: string
  action?: React.ReactNode
  className?: string
}

export function MobileEmptyState({ 
  icon, 
  title, 
  description, 
  action, 
  className 
}: MobileEmptyStateProps) {
  return (
    <div className={cn(
      "flex flex-col items-center justify-center py-12 px-4 text-center",
      className
    )}>
      {icon && (
        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
          {icon}
        </div>
      )}
      <h3 className="text-lg font-medium text-gray-900 mb-2">{title}</h3>
      {description && (
        <p className="text-sm text-gray-600 mb-6 max-w-sm">{description}</p>
      )}
      {action && action}
    </div>
  )
}

interface MobileLoadingProps {
  message?: string
  className?: string
}

export function MobileLoading({ message = "Loading...", className }: MobileLoadingProps) {
  return (
    <div className={cn(
      "flex flex-col items-center justify-center py-12 px-4 text-center",
      className
    )}>
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4"></div>
      <p className="text-sm text-gray-600">{message}</p>
    </div>
  )
}

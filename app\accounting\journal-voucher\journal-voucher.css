/* Journal Voucher Page Styles */

/* Main content area */
.main-content {
  margin-left: 280px;
  transition: margin-left 0.3s ease;
}

.main-content.sidebar-collapsed {
  margin-left: 80px;
}

/* Form styles */
.form-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

@media (min-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr 1fr;
  }
}

/* Print styles */
@media print {
  .sidebar,
  .header,
  .action-buttons,
  .filter-section {
    display: none !important;
  }

  .main-content {
    margin-left: 0 !important;
    padding: 0 !important;
  }

  .card {
    box-shadow: none !important;
    border: none !important;
  }

  .print-header {
    display: block !important;
  }

  /* Journal Voucher History print styles */
  @page {
    size: landscape;
    margin: 1cm;
  }

  /* Hide dialog elements not needed for printing */
  .dialog-overlay,
  .dialog-close-button,
  .search-container,
  .print-export-buttons {
    display: none !important;
  }

  /* Show dialog content in full page */
  .dialog-content {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100% !important;
    max-width: 100% !important;
    max-height: 100% !important;
    transform: none !important;
    border: none !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    padding: 0 !important;
    margin: 0 !important;
    overflow: visible !important;
  }

  /* Table styles for print */
  table {
    width: 100% !important;
    border-collapse: collapse !important;
    page-break-inside: auto !important;
  }

  /* Ensure rows don't break across pages */
  tr {
    page-break-inside: avoid !important;
  }

  /* Set fixed widths for table cells to prevent overlapping */
  th, td {
    padding: 8px !important;
    text-align: left !important;
    border: 1px solid #ddd !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
  }

  /* Specific column widths */
  th:nth-child(1), td:nth-child(1) { width: 12% !important; } /* Voucher No. */
  th:nth-child(2), td:nth-child(2) { width: 10% !important; } /* Date */
  th:nth-child(3), td:nth-child(3) { width: 15% !important; } /* Paid To */
  th:nth-child(4), td:nth-child(4) { width: 10% !important; } /* Account Code */
  th:nth-child(5), td:nth-child(5) { width: 12% !important; } /* Amount */
  th:nth-child(6), td:nth-child(6) { width: 12% !important; } /* Payment Method */
  th:nth-child(7), td:nth-child(7) { width: 15% !important; } /* Reference */
  th:nth-child(8), td:nth-child(8) { width: 10% !important; } /* Status */

  /* Add a title for the printed page */
  .voucher-history-title {
    display: block !important;
    text-align: center !important;
    font-size: 24px !important;
    font-weight: bold !important;
    margin: 20px 0 !important;
  }

  /* Status badge styles for print */
  .status-badge {
    padding: 2px 6px !important;
    border-radius: 4px !important;
    font-weight: normal !important;
  }
}

/* Responsive adjustments for mobile */
@media (max-width: 767px) {
  .main-content {
    margin-left: 0;
  }

  .main-content.sidebar-collapsed {
    margin-left: 0;
  }
}

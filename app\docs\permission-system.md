# Permission Dialog System

This document explains how to use the permission dialog system to display consistent permission denied messages across the application.

## Overview

The permission dialog system provides a standardized way to show permission denied messages when a user attempts to access a feature they don't have permission for. This ensures a consistent user experience and clear communication about access restrictions.

## Components and Utilities

### 1. PermissionDialog Component

The `PermissionDialog` component displays a modal dialog when a user doesn't have permission to access a feature.

```tsx
import PermissionDialog from '@/app/components/PermissionDialog';

// In your component:
const [showDialog, setShowDialog] = useState(false);
const [errorMessage, setErrorMessage] = useState('');

// Then in your JSX:
<PermissionDialog
  isOpen={showDialog}
  onClose={() => setShowDialog(false)}
  message={errorMessage}
  resource="feature-name"
  action="view"
/>
```

### 2. usePermissionDialog Hook

The `usePermissionDialog` hook provides state and functions for managing permission dialogs.

```tsx
import { usePermissionDialog } from '@/app/hooks/usePermissionDialog';

// In your component:
const {
  isPermissionDialogOpen,
  permissionMessage,
  showPermissionDialog,
  hidePermissionDialog,
  handlePermissionError
} = usePermissionDialog();

// Show dialog manually:
showPermissionDialog({
  message: 'You do not have permission to access this feature',
  title: 'Access Denied',
  resource: 'feature',
  action: 'access'
});

// Handle API errors automatically:
try {
  const response = await fetch('/api/protected-resource');
  if (!response.ok) throw new Error('API error');
} catch (error) {
  // Returns true if it was a permission error and was handled
  const wasHandled = handlePermissionError(error);
  if (!wasHandled) {
    // Handle other types of errors
  }
}

// Then in your JSX:
<PermissionDialog
  isOpen={isPermissionDialogOpen}
  onClose={hidePermissionDialog}
  message={permissionMessage}
  resource="feature-name"
  action="view"
/>
```

### 3. Enhanced PermissionGuard Component

The `PermissionGuard` component now shows a dialog when access is denied.

```tsx
import { PermissionGuard } from '@/app/components/PermissionGuard';

// In your JSX:
<PermissionGuard
  permission="VIEW_MARKS"
  showDialog={true} // default is true
  resource="marks"
  action="view"
>
  {/* Protected content */}
</PermissionGuard>
```

### 4. Enhanced RoleGuard Component

The `RoleGuard` component now shows a dialog before redirecting when access is denied.

```tsx
import { RoleGuard } from '@/app/components/RoleGuard';

// In your JSX:
<RoleGuard
  roles="ADMIN"
  showDialog={true} // default is true
  redirectOnAccessDenied={true}
  redirectDelay={3000} // 3 seconds
  featureName="admin dashboard"
>
  {/* Protected content */}
</RoleGuard>
```

### 5. Permission Error Utilities

The `permission-errors.ts` file provides utilities for handling permission errors consistently.

```tsx
import { 
  createPermissionDeniedResponse, 
  PermissionErrors,
  isPermissionError,
  handleApiPermissionError
} from '@/app/lib/permission-errors';

// In API routes:
if (!hasPermission) {
  return createPermissionDeniedResponse('view', 'marks', 'class "Class 1"');
}

// In client components:
try {
  const response = await fetch('/api/protected-resource');
  // Handle response
} catch (error) {
  if (handleApiPermissionError(error, showPermissionDialog)) {
    // Error was handled as a permission error
    return;
  }
  // Handle other errors
}
```

## Best Practices

1. **Use standardized error messages**: Use the `PermissionErrors` constants for consistent messaging.

2. **Handle API permission errors**: Always check for permission errors in API responses and show the appropriate dialog.

3. **Provide context**: When showing permission dialogs, include the resource name and action to make the message clear.

4. **Use guards for declarative protection**: Use `PermissionGuard` and `RoleGuard` components to protect sections of your UI.

5. **Delay redirects**: When redirecting after a permission denial, add a delay so users can read the message.

## Examples

### Handling API Permission Errors

```tsx
const fetchData = async () => {
  try {
    const response = await fetch('/api/protected-data');
    
    if (!response.ok) {
      if (response.status === 403) {
        const errorData = await response.json();
        showPermissionDialog({
          message: errorData.error,
          resource: 'data',
          action: 'view'
        });
        return;
      }
      throw new Error('Failed to fetch data');
    }
    
    const data = await response.json();
    // Process data
  } catch (error) {
    // Handle other errors
  }
};
```

### Protecting a Page with RoleGuard

```tsx
export default function AdminPage() {
  return (
    <RoleGuard
      roles={['ADMIN', 'SUPER_ADMIN']}
      showDialog={true}
      redirectOnAccessDenied={true}
      featureName="Admin Dashboard"
    >
      <AdminDashboard />
    </RoleGuard>
  );
}
```

### Protecting a Feature with PermissionGuard

```tsx
<PermissionGuard
  permission={['EDIT_MARKS', 'ADD_MARKS']}
  resource="student marks"
  action="edit"
>
  <EditMarksForm />
</PermissionGuard>
```
